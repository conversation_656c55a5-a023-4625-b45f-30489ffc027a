<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <groupId>yyzone-guildrank</groupId>
    <artifactId>yyzone-guildrank</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>yyzone-guildrank</name>

    <modules>
       
        <module>yyzone-guildrank-app</module>
        <module>yyzone-guildrank-persist</module>
        <module>yyzone-guildrank-service</module>
        <module>yyzone-guildrank-manage</module>
        <module>yyzone-guildrank-api</module>
    </modules>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>YYEnt Public Repositories</name>
            <url>http://nexus.yy.com/music/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <distributionManagement>
        <repository>
            <id>yyent-release</id>
            <name>yyent-release Repository</name>
            <url>http://nexus.yy.com/music/content/repositories/yyent-release/</url>
        </repository>
        <snapshotRepository>
            <id>yyent</id>
            <name>yyent Repository</name>
            <url>http://nexus.yy.com/music/content/repositories/yyent/</url>
        </snapshotRepository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>public</id>
            <name>YYEnt Public Repositories</name>
            <url>http://nexus.yy.com/music/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <properties>
        <app.version>0.0.1-SNAPSHOT</app.version>
        <main.application>com.yy.yyzone.guildrank.Application</main.application>
        <admin.main.application>com.yy.yyzone.guildrank.manage.Application</admin.main.application>
        <java.version>1.8</java.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <yylive.starter.version>1.0.2.3</yylive.starter.version>
        <dubbo-spring-boot-starter.version>1.0.5.36-RELEASE</dubbo-spring-boot-starter.version>
        <dubbo-rpc-app-entmobserv.version>1.0.5.36-RELEASE</dubbo-rpc-app-entmobserv.version>
    </properties>

    <dependencyManagement>
          <dependencies>
                <dependency>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                    <version>3.3.2</version>
                </dependency>

                <dependency>
                    <groupId>com.yy.boot.component</groupId>
                    <artifactId>yy-spring-boot-dependencies</artifactId>
                    <version>${yylive.starter.version}</version>
                    <type>pom</type>
                    <scope>import</scope>
                </dependency>
          </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>
        <dependency>
            <artifactId>clients-daemon</artifactId>
            <groupId>com.yy.ent.clients</groupId>
            <version>4.2.9-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.googlecode.protobuf-java-format</groupId>
            <artifactId>protobuf-java-format</artifactId>
            <version>1.2</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>2.2.11</version>
        </dependency>
        <dependency>
            <groupId>com.yy.ent.mobile</groupId>
            <artifactId>mobile-metrics</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.kevinsawicki</groupId>
            <artifactId>http-request</artifactId>
            <version>6.0</version>
        </dependency>

        <dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
            <version>3.1</version>
        </dependency>

        <dependency>
            <groupId>com.yy.boot.component</groupId>
            <artifactId>yy-spring-boot-autoconfigure</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>


        <dependency>
            <groupId>com.yy.boot.component</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>


        <dependency>
            <groupId>com.yy.boot.component</groupId>
            <artifactId>lock-spring-boot-starter</artifactId>
        </dependency>


        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
            <version>${dubbo-spring-boot-starter.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-rpc-app-entmobserv</artifactId>
            <version>${dubbo-rpc-app-entmobserv.version}</version>
        </dependency>

        <dependency>
            <groupId>com.yy.java</groupId>
            <artifactId>webdb-core</artifactId>
            <version>1.0.12</version>
            <exclusions>
                <exclusion>
                    <groupId>com.yy.java</groupId>
                    <artifactId>webdb-thrift-v0.9.0</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.yy.java</groupId>
            <artifactId>webdb-thrift-v0.6.1</artifactId>
            <version>1.0.12</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.16.20</version>
        </dependency>

        <!--	bos bce sdk-->
        <dependency>
            <groupId>com.baidubce</groupId>
            <artifactId>bce-java-sdk</artifactId>
            <version>0.10.382</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>jdk.tools</artifactId>
                    <groupId>jdk.tools</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>hbase-client</artifactId>
                    <groupId>org.apache.hbase</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-codec</artifactId>
                    <groupId>commons-codec</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>httpclient</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>org.eclipse.paho.client.mqttv3</artifactId>
                    <groupId>org.eclipse.paho</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-core</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.3.2</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <showWarnings>true</showWarnings>
                    <encoding>utf-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.1</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>3.0.0</version>
            </plugin>
        </plugins>
    </build>

</project>