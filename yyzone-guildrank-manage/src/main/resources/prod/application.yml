yy:
  env: PRODUCTION
  app-name: yyzoneguildrankmanage
  datasource:
    yyzone-guildrank:
      primary: true
      basePackage: com.yy.yyzone.guildrank.db.gen.mapper,com.yy.yyzone.guildrank.db.custom.mapper

logging:
  name: yyzone-guildrank
  sms: false
  level:
    root: info

apollo:
  meta: http://nbcfg.yy.com
  cluster: default
  bootstrap:
    enabled: true
    namespaces: application,manage.yml     #yy内部默认namespace为application
app:
  id: 4ca797c7d2196f5e0529d4777cb140c9

spring:
  profiles:
    active: prod