package com.yy.yyzone.guildrank.manage.controller;

import com.yy.yyzone.guildrank.dto.guildrankconfig.ConfigLogDto;
import com.yy.yyzone.guildrank.dto.guildrankconfig.ConfigLogReq;
import com.yy.yyzone.guildrank.dto.guildrankconfig.GuildRankConfigDto;
import com.yy.yyzone.guildrank.manage.interceptor.LocalUserContext;
import com.yy.yyzone.guildrank.service.GuildRankConfigService;
import com.yy.yyzone.guildrank.util.PageResp;
import com.yy.yyzone.guildrank.util.Resp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 公会段位门槛设置（健康度）
 **/
@Slf4j
@RestController
@RequestMapping("/guildRank/config")
public class GuildRankConfigController extends BaseController {
    @Autowired
    private GuildRankConfigService configService;

    /**
     * 获取公会段位门槛设置
     *
     * @return
     */
    @GetMapping(value = "/getConfig")
    public Resp<GuildRankConfigDto> getConfig() {
        return Resp.createBySuccess(configService.getConfig(false));
    }

    /**
     * 保持公会段位门槛设置
     *
     * @return
     */
    @PostMapping(value = "/saveConfig")
    public Resp saveConfig(@Valid @RequestBody GuildRankConfigDto config, BindingResult result) {
        log.info("saveConfig config:{},passport:{}", config, getOperator());
        Resp resp = getBindingResult(result);
        if (!resp.checkSuccess()) {
            return resp;
        }
        resp = config.validate();
        log.info("saveConfig validate:{}", resp);
        if (!resp.checkSuccess()) {
            return resp;
        }
        return configService.save(config, getOperator());
    }

    /**
     * 变更记录
     *
     * @param req
     * @return
     */
    @GetMapping(value = "/logList")
    public PageResp<List<ConfigLogDto>> logList(ConfigLogReq req) {
        return configService.logList(req);
    }
}
