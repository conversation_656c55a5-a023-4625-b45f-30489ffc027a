package com.yy.yyzone.guildrank.manage.controller;

import com.yy.yyzone.guildrank.dto.guildranktaskconfig.*;
import com.yy.yyzone.guildrank.exceptions.VersionRuntimeException;
import com.yy.yyzone.guildrank.manage.interceptor.LocalUserContext;
import com.yy.yyzone.guildrank.service.GuildRankTaskConfigService;
import com.yy.yyzone.guildrank.util.Resp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * 公会等级任务配置
 */
@RestController
@Slf4j
@RequestMapping("/guild-rank-task")
public class GuildRankTaskConfigController {

    @Autowired
    private GuildRankTaskConfigService guildRankTaskConfigService;

    /**
     * 获取公会等级任务配置和版本号，version 在设置时候需要带上来，配置说明 https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/M-wK0zh99p/ysEIeEfRRJ/JKqaWTKelfn5yj
     *
     * @return
     */
    @GetMapping("/config")
    public Resp<GuildRankTaskConfigResp> getGuildRankTaskConfig() {
        Pair<Long, GuildRankAllTaskConfig> configAndVersion = guildRankTaskConfigService.getGuildRankTaskConfigAndVersion();
        return Resp.createBySuccess(new GuildRankTaskConfigResp(configAndVersion.getLeft(), configAndVersion.getRight()));
    }

    /**
     * 设置公会等级任务配置
     *
     * @param req
     * @return
     */
    @PostMapping("/config")
    public Resp<GuildRankTaskConfigResp> setGuildRankTaskConfig(@RequestBody @Valid AddGuildRankTaskConfigReq req) {
        String passport = LocalUserContext.get().getPassport();
        log.info("guildRankTaskConfig passport:{} req:{}", passport, req);
        try {
            Pair<GuildRankAllTaskConfig, Long> result = guildRankTaskConfigService.setGuildRankTaskConfig(req, passport);
            return Resp.createBySuccess(new GuildRankTaskConfigResp(result.getRight(), result.getLeft()));
        } catch (VersionRuntimeException e) {
            log.warn("setGuildRankTaskConfig version error:{}", e.getMessage(), e);
            return Resp.createByError(e.getMessage());
        }
    }

    /**
     * 查询公会等级任务活动配置历史
     *
     * @param startDate 格式 yyyy-MM-dd'T'HH:mm:ss
     * @param endDate 格式 yyyy-MM-dd'T'HH:mm:ss
     * @param operator
     * @return
     */
    @GetMapping("/config/history")
    public Resp<List<ConfigHistory<GuildRankAllTaskConfig>>> getGuildRankTaskConfigHistory(@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss") Date startDate,
                                                                   @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss") Date endDate,
                                                                   @RequestParam(required = false) String operator) {
        return Resp.createBySuccess(guildRankTaskConfigService.getGuildRankTaskConfigHistory(startDate,endDate,operator));
    }
}
