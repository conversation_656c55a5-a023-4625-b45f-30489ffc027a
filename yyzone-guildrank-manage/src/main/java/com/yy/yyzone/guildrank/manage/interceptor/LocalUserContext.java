/**
 * 
 */
package com.yy.yyzone.guildrank.manage.interceptor;


/**
* <AUTHOR> 
* @date 2019年7月11日 下午3:55:44
* 
*/
public class LocalUserContext {
	
    private final static ThreadLocal<OperAdmin> USER_THREAD_LOCAL = new ThreadLocal<>();

    public static void add(OperAdmin user) {
        USER_THREAD_LOCAL.set(user);
    }

    public static void clean() {
        USER_THREAD_LOCAL.remove();
    }

    public static OperAdmin get() {
        return USER_THREAD_LOCAL.get();
    }

    public static Long getUid(){
        if (USER_THREAD_LOCAL.get() != null) {
           return  USER_THREAD_LOCAL.get().getYyuid();
        }
        return 0L;
    }
}
