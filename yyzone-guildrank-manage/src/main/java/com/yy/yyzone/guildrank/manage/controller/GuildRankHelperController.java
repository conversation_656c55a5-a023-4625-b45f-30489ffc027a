package com.yy.yyzone.guildrank.manage.controller;

import com.yy.yyzone.guildrank.db.custom.mapper.BiExtMapper;
import com.yy.yyzone.guildrank.db.custom.model.KV;
import com.yy.yyzone.guildrank.service.*;
import com.yy.yyzone.guildrank.util.MyDateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@RestController
@RequestMapping("/guildRank/helper")
public class GuildRankHelperController extends BaseController {
    @Autowired
    private StarService starService;

    @Autowired
    private GoldChannelService goldChannelService;

    @Autowired
    private ScoreMallService scoreMallService;

    @Autowired
    private RankCheckV3Service rankCheckV3Service;

    @Autowired
    private RankCacheV3Service rankCacheV3Service;

    @Autowired
    private GuildRankV3Service rankV3Service;

    @Autowired
    private GuildService guildService;

    @Autowired
    private GuildRankWhitelistService rankWhitelistService;
    @Autowired
    private BiExtMapper biExtMapper;

    @Value("#{'${yy.env}'.equalsIgnoreCase('dev')||'${yy.env}'.equalsIgnoreCase('test')}")
    private Boolean isTest;

    /**
     * 发放积分
     *
     * @return
     */
    @GetMapping(value = "/grantPoint")
    public Object grantPoint() {
        log.info("grantPoint passport:{}", getOperator());
        return scoreMallService.grantPoint();
    }

    /**
     * 将bi等级数据由临时表同步至正式表
     *
     * @return
     */
    @GetMapping(value = "/syncBiData")
    public Object syncBiData(@DateTimeFormat(pattern = "yyyy-MM-dd") Date month,
                             @RequestParam(defaultValue = "true") boolean ignoreErr) {
        log.info("syncBiData month:{},ignoreErr:{},isTest:{},passport:{}", month, ignoreErr, isTest, getOperator());
        if (isTest) {
            int i = biExtMapper.insertSyncDetail();
            log.info("syncBiData insertSyncDetail count:{}", i);
        }

        if (month == null) {
            month = MyDateUtil.lastMonthEnd();
        }
        rankCheckV3Service.rankCheck(month, ignoreErr);
        rankCheckV3Service.syncNotMonthEndData();
        rankCacheV3Service.refreshRankCache();
        return "OK";
    }

    @GetMapping(value = "/refreshRank")
    public Object refreshRank() {
        rankCacheV3Service.refreshRankCache();
        return getRankData();
    }

    @GetMapping(value = "/getRankData")
    public Object getRankData() {
        Map<String, Object> map = new HashMap<>();
        map.put("allRank", rankCacheV3Service.allRank());
        map.put("getRankCurrentMonth", rankCacheV3Service.getRankCurrentMonth());
        map.put("hasUpdate", rankCacheV3Service.hasUpdate());
        return map;
    }

    @GetMapping(value = "/getStarCmp")
    public Object getStarCmp() {
        return guildService.getStarCmp();
    }

    @GetMapping(value = "/getAllAssociatedUser")
    public Object getAllAssociatedUser() {
        return goldChannelService.getAllAssociatedUser();
    }

    @GetMapping(value = "/getAllStarGoldGuild")
    public Object getAllStarGoldGuild() {
        return starService.getAllStarGoldGuild();
    }

    @GetMapping(value = "/getRankCount")
    public Object getRankCount(@DateTimeFormat(pattern = "yyyyMMdd") Date dt,
                               @RequestParam(defaultValue = "true") boolean tmp) {
        if (dt == null) {
            dt = DateUtils.addDays(DateUtils.truncate(new Date(), Calendar.DATE), -1);
        }
        String tbl = tmp
                ? "yy_dm_entity_guild_cmp_health_analysis_di_tmp"
                : "yy_dm_entity_guild_cmp_health_analysis_di";
        List<KV<Long, Integer>> kvs = biExtMapper.selectLevelCount(tbl, dt);
        Map<Long, Integer> countMap = KV.toMap(kvs);
        return countMap;
    }

    @GetMapping(value = "/getRadarChartData")
    public Object getRadarChartData(long uid) {
        return rankV3Service.getRadarChartData(uid);
    }

    @GetMapping(value = "/notifyResource")
    public Object notifyResource(@RequestParam(defaultValue = "false") boolean refresh) throws Exception {
        log.info("notifyResource refresh:{},passport:{}", refresh, getOperator());
        if (refresh) {
            rankCacheV3Service.refreshRankCache();
            TimeUnit.SECONDS.sleep(30);
        }
        rankWhitelistService.notifyResource();
        return "OK";
    }
}