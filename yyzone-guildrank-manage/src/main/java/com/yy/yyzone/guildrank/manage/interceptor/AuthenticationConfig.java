/**
 * 
 */
package com.yy.yyzone.guildrank.manage.interceptor;

import com.yy.ent.yadmin.sdk.interceptor.AuthenticationInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @date 2019年7月11日 下午3:35:10
 * 
 */
@Configuration
@Profile("!dev")
@Slf4j
public class AuthenticationConfig implements WebMvcConfigurer {

	@Override
	public void addInterceptors(InterceptorRegistry registry) {
		// 拦截所有请求
		registry.addInterceptor(authenticationInterceptor()).addPathPatterns("/**");
		
		registry.addInterceptor(loginUserInterceptor()).addPathPatterns("/**");
		log.info("AuthenticationConfig enable");
	}

	@Bean
	public AuthenticationInterceptor authenticationInterceptor() {
		return new AuthenticationInterceptor("./admin-config.properties");
	}

	@Bean
	public LoginUserInterceptor loginUserInterceptor() {
		return new LoginUserInterceptor();
	}

}
