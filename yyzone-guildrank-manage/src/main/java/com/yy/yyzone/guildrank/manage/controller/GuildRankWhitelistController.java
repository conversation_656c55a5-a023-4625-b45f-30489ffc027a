package com.yy.yyzone.guildrank.manage.controller;

import com.yy.yyzone.guildrank.dto.GuildRankWhilelistPageInfo;
import com.yy.yyzone.guildrank.dto.GuildRankWhilelistSaveInfo;
import com.yy.yyzone.guildrank.manage.interceptor.LocalUserContext;
import com.yy.yyzone.guildrank.service.GuildRankWhitelistService;
import com.yy.yyzone.guildrank.util.Resp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 公会段位白名单相关
 * <AUTHOR>
 * @date 2019/11/5
 *
 */
@RequestMapping("/guildRankWhitelist")
@RestController
public class GuildRankWhitelistController {
    private static final Logger LOGGER = LoggerFactory.getLogger(GuildRankWhitelistController.class);

    @Autowired
    private GuildRankWhitelistService guildRankWhitelistService;

    /**
     * 公会段位白名单列表
     * @param uids 公会uid ,分割
     * @param yynos 公会yy号 ,分割
     * @param month 月份 yyyy-MM
     * @param pageSize 分页大小
     * @param pageIndex 页数
     * @return
     */
    @RequestMapping("/queryWhitelist")
    public Resp<GuildRankWhilelistPageInfo> queryWhitelist(@RequestParam(required = false, defaultValue = "") String uids,
                                                           @RequestParam(required = false, defaultValue = "") String yynos,
                                                           @RequestParam(required = false, defaultValue = "") String month,
                                                           @RequestParam(required = false, defaultValue = "20") int pageSize,
                                                           @RequestParam(required = false, defaultValue = "1") int pageIndex) {
        Long uid = LocalUserContext.getUid();

        if (pageSize > 200) {
            pageSize = 200;
        }

        LOGGER.info("queryWhitelist uids:{},yynos:{},month:{},pageSize:{},pageIndex:{},uid:{}", uids, yynos, month, pageSize, pageIndex, uid);
        Resp<GuildRankWhilelistPageInfo> resp = guildRankWhitelistService.queryWhitelist(uids, yynos, month, pageIndex, pageSize);
        return resp;
    }

    /**
     * 保存公会段位白名单
     * @param info
     * @return
     */
    @RequestMapping("/saveWhitelist")
    public Resp<Long> saveWhitelist(GuildRankWhilelistSaveInfo info) {
        Long uid = LocalUserContext.getUid();
        LOGGER.info("saveWhitelist info:{},uid:{}", info, uid);

        Resp<Long> resp = guildRankWhitelistService.saveWhitelist(info, LocalUserContext.get().getPassport());
        return resp;
    }

    /**
     * 删除公会段位白名单
     * @param id
     * @return
     */
    @RequestMapping("/delWhitelist")
    public Resp<Boolean> delWhitelist(Long id) {
        Long uid = LocalUserContext.getUid();
        LOGGER.info("delWhitelist id:{},uid:{}", id, uid);

        Resp<Boolean> resp = guildRankWhitelistService.delWhitelist(id, LocalUserContext.get().getPassport());
        return resp;
    }

    /**
     * 能否编辑公会段位白名单
     * @return
     */
    @RequestMapping("/canEditWhitelist")
    public Resp<Boolean> canEditWhitelist() {
        boolean flag = guildRankWhitelistService.isMonthLastday();
        //return Resp.createBySuccess(flag ? "为了数据上传准确，每月最后一天不能编辑或删除白名单" : "", !flag);
        return Resp.createBySuccess("", true);
    }
}