/**
 * 
 */
package com.yy.yyzone.guildrank.manage.interceptor;
/**
 * 
 * 操作人管理员信息
* <AUTHOR> 
* @date 2019年7月2日 下午12:02:34
* 
*/
public class OperAdmin {
	
	private long yyuid;
	private String passport;//dw_xxxx
	public long getYyuid() {
		return yyuid;
	}
	public void setYyuid(long yyuid) {
		this.yyuid = yyuid;
	}
	public String getPassport() {
		return passport;
	}
	public void setPassport(String passport) {
		this.passport = passport;
	}
	@Override
	public String toString() {
		return "OperAdmin [yyuid=" + yyuid + ", passport=" + passport + "]";
	}

	public OperAdmin() {
	}

	public OperAdmin(long yyuid, String passport) {
		this.yyuid = yyuid;
		this.passport = passport;
	}
}
