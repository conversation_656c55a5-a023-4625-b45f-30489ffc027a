/**
 * 
 */
package com.yy.yyzone.guildrank.manage.interceptor;

import com.yy.ent.mobile.persist.db.gen.yadmin.authc.model.User;
import com.yy.ent.yadmin.sdk.util.LoginUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 将需要的登录者信息封装到本地线程
 * 
 * <AUTHOR>
 * @date 2019年7月11日 下午3:50:11
 * 
 */
public class LoginUserInterceptor implements HandlerInterceptor {

	Logger logger = LoggerFactory.getLogger(LoginUserInterceptor.class);

	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
			throws Exception {

		User user = LoginUtils.getUser(request);
		OperAdmin admin = new OperAdmin();
		if (user != null) {
			admin.setPassport(user.getPassport());
			admin.setYyuid(user.getUserId());
		} else {
			logger.info("当前没有登陆信息");
		}
		LocalUserContext.add(admin);
		logger.info("当前登陆账号 :{}", admin);
		return true;
	}
}
