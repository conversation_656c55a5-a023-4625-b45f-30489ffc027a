package com.yy.yyzone.guildrank.manage.controller;

import com.google.common.collect.Lists;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankRenewArtistCount;
import com.yy.yyzone.guildrank.dto.renewartistdata.DataImportExcelRow;
import com.yy.yyzone.guildrank.dto.renewartistdata.DataListDto;
import com.yy.yyzone.guildrank.dto.renewartistdata.DataSaveReq;
import com.yy.yyzone.guildrank.service.GuildRankRenewArtistDataService;
import com.yy.yyzone.guildrank.util.PageResp;
import com.yy.yyzone.guildrank.util.Resp;
import com.yy.yyzone.guildrank.util.excel.Columns;
import com.yy.yyzone.guildrank.util.excel.ExcelReadUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 公会段位续约金牌艺人数据
 **/
@Slf4j
@RestController
@RequestMapping("/guildRank/renewArtistData")
public class GuildRankRenewArtistDataController extends BaseController {
    @Autowired
    private GuildRankRenewArtistDataService dataService;

    /**
     * 列表
     *
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping(value = "/dataList")
    public PageResp<List<DataListDto>> dataList(@RequestParam(required = false, defaultValue = "1") int pageNum,
                                                @RequestParam(required = false, defaultValue = "20") int pageSize) {
        return dataService.dataList(pageNum, pageSize);
    }

    /**
     * 保存数据 文件参数名为file
     *
     * @param req
     * @param file
     * @return
     */
    @PostMapping(value = "/saveData")
    public Resp saveData(@RequestPart @Valid DataSaveReq req, @RequestPart(value = "file", required = false) MultipartFile file, BindingResult result) {
        log.info("saveData req:{},passport:{}", req, getOperator());
        Resp r = getBindingResult(result);
        if (!r.checkSuccess()) {
            return r;
        }
        if (file == null || file.isEmpty()) {
            return Resp.createByError("文件内容不能为空");
        }

        saveFile(file, "renewArtistData");

        List<DataImportExcelRow> rows = Lists.newArrayList();
        Columns superColumns = new Columns();
        superColumns.addColumn("mainGuildUid", Columns.ColumnType.CELL_TYPE_LONG, false, null);
        superColumns.addColumn("renewCount", Columns.ColumnType.CELL_TYPE_INT, false, null);
        try {
            Map<String, Object> map = ExcelReadUtil.getDataList(file, superColumns, DataImportExcelRow.class);
            if (((int) map.get(ExcelReadUtil.KEY_CODE)) != ExcelReadUtil.SUCCESS_CODE) {
                return Resp.createByError((String) map.get(ExcelReadUtil.KEY_MSG));
            }
            rows = (List<DataImportExcelRow>) map.get(ExcelReadUtil.KEY_DATA);
        } catch (Exception e) {
            log.error("getDataList exception", e);
            return Resp.createByError("解析Excel异常");
        }
        Resp resp = dataService.saveData(req, rows, getOperator());
        log.info("saveData resp", resp);
        return resp;
    }

    /**
     * 导出名单
     *
     * @param dataMonth 数据月 yyyyMM
     */
    @GetMapping(value = "/exportList")
    public void exportList(@DateTimeFormat(pattern = "yyyyMM") Date dataMonth) {
        List<GuildrankRenewArtistCount> list = dataService.getRenewArtistCount(dataMonth);
        List<String> columns = Lists.newArrayList("主体UID", "续约金牌艺人数");
        List<List<String>> values = Lists.newArrayListWithExpectedSize(list.size());
        for (GuildrankRenewArtistCount c : list) {
            List<String> val = Lists.newArrayListWithExpectedSize(columns.size());
            val.add(String.valueOf(c.getMainGuildUid()));
            val.add(String.valueOf(c.getRenewCount()));
            values.add(val);
        }
        export(columns, values, DateFormatUtils.format(dataMonth, "yyyyMM") + "名单");
    }
}
