package com.yy.yyzone.guildrank.manage.controller;

import com.yy.yyzone.guildrank.db.gen.model.GuildRankPlatformActivityTaskUploadHistory;
import com.yy.yyzone.guildrank.dto.guildranktaskconfig.GuildPlatformActivityTaskResultVO;
import com.yy.yyzone.guildrank.enums.GuildPlatformActivityTask;
import com.yy.yyzone.guildrank.manage.interceptor.LocalUserContext;
import com.yy.yyzone.guildrank.service.GuildPlatformActivityResultService;
import com.yy.yyzone.guildrank.util.PageResp;
import com.yy.yyzone.guildrank.util.Resp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.Size;
import java.io.IOException;
import java.net.URI;
import java.util.List;

/**
 * 公会等级平台活动结果
 */
@RestController
@RequestMapping("/guild-platform-activity/result")
@Slf4j
public class GuildPlatformActivityResultController {

    @Autowired
    private GuildPlatformActivityResultService service;

    /**
     * 平台结果上传
     * @param file
     * @return
     * @throws IOException
     */
    @PostMapping("/upload")
    public Resp<Void> uploadExcel(@RequestParam("file") MultipartFile file) throws IOException {
        String passport = LocalUserContext.get().getPassport();
        log.info("uploadExcel passport:{} file:{}", passport, file.getOriginalFilename());
        Pair<Integer,List<GuildPlatformActivityTaskResultVO>> dtAndExcelVOs = service.validateAndReadExcel(file);
        service.saveResults(dtAndExcelVOs.getRight(),dtAndExcelVOs.getLeft(), file, passport);
        return Resp.createBySuccess();
    }
    /**
     * 查询结果列表
     * @param dt    格式 yyyyMM 例如 202507
     * @param page 从 1 开始
     * @param pageSize
     * @param operator
     * @param uids
     * @param task 任务类型
     * @return
     */
    @GetMapping("/list")
    public PageResp<List<GuildPlatformActivityTaskResultVO>> getResults(
            @RequestParam(name = "dt",required = false) Integer dt,
            @RequestParam(value = "page", defaultValue = "1") int page,
            @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @RequestParam(value = "operator", required = false) String operator,
            @RequestParam(value = "uid", required = false)@Size(max = 200) List<Long> uids,
            @RequestParam(value = "task", required = false) GuildPlatformActivityTask task) {
        String passport = LocalUserContext.get().getPassport();
        log.info("getResults passport:{} dt:{} page:{} pageSize:{} operator:{} uids:{} taskType:{}",passport, dt, page, pageSize, operator, uids, task);
        return service.getResults(dt, page, pageSize, operator, uids, task);
    }
    /**
     * 上传历史
     * @param dt    格式 yyyyMM 例如 202507
     * @param operator 操作人
     * @return
     */
    @GetMapping("/upload-history")
    public Resp<List<GuildRankPlatformActivityTaskUploadHistory>> getUploadHistory(@RequestParam(name ="dt", required = false) Integer dt, @RequestParam(value = "operator", required = false) String operator) {
        String passport = LocalUserContext.get().getPassport();
        log.info("getUploadHistory passport:{} dt:{} operator:{}",passport, dt, operator);
        return Resp.createBySuccess(service.getUploadHistory(dt,operator));
    }

    /**
     * 点击下载上传历史Excel时候通过这个接口 302 下载
     * @param dt
     * @return
     */
    @GetMapping("/upload-history/download-path")
    public ResponseEntity getDownloadPath(@RequestParam(name ="dt", required = true) Integer dt) {
        String passport = LocalUserContext.get().getPassport();
        log.info("getDownloadPath passport:{} dt:{}",passport, dt);
        HttpHeaders headers = new HttpHeaders();
        headers.setLocation(URI.create(service.getDownloadPath(dt))); // 设置重定向的目标地址
        return new ResponseEntity<>(headers, HttpStatus.FOUND); // 返回 302 状态码
    }
}