package com.yy.yyzone.guildrank.manage.controller;

import com.yy.yyzone.guildrank.dto.GuildRankTasksExportReq;
import com.yy.yyzone.guildrank.dto.guildranktaskconfig.*;
import com.yy.yyzone.guildrank.manage.interceptor.LocalUserContext;
import com.yy.yyzone.guildrank.service.GuildRankTaskDistributionService;
import com.yy.yyzone.guildrank.util.MyDateUtil;
import com.yy.yyzone.guildrank.util.PageResp;
import com.yy.yyzone.guildrank.util.Resp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

@RestController
@Slf4j
@RequestMapping("/guild-rank/distribution")
public class GuildRankTaskDistributionController extends BaseController{
    @Autowired
    private GuildRankTaskDistributionService taskService;

    /**
     * 查看活动下发列表
     * @param uids
     * @param dtStart 格式 yyyyMM 202507
     * @param dtEnd   格式 yyyyMM 202507
     * @param task
     * @param pageNum 从 1开始
     * @param pageSize
     * @return
     */
    @GetMapping("/tasks")
    public PageResp<List<GuildRankTaskDistributionDetailsDTO>> listTasks(
            @RequestParam(required = false) @Size(min = 0,max = 100)  List<Long> uids,
            @RequestParam(required = false) @Size(min = 0,max = 100) List<Long> yys,
            @RequestParam(required = false) Integer dtStart,
            @RequestParam(required = false) Integer dtEnd,
            @RequestParam(required = false) String task,
            @RequestParam(defaultValue = "1") @Min(1) int pageNum,
            @RequestParam(defaultValue = "10") @Max(30) int pageSize) {
        log.info("listTasks uid:{} yys:{} dtStart:{} dtEnd:{} task:{} pageNum:{} pageSize:{}", uids, yys, dtStart, dtEnd, task, pageNum, pageSize);
        return taskService.listTasks(uids, yys, dtStart, dtEnd, task, pageNum, pageSize);
    }

    /**
     *
     * 任务列表下载
     */
    @GetMapping("/tasks/export")
    public void listTasksExport(@Valid GuildRankTasksExportReq req) {
        log.info("listTasksExport req:{}", req);
        int pageNum = 1;
        int pageSize = 30;
        List<GuildRankTaskDistributionAdminExportDTO> exportDTOS = new ArrayList<>();
        Integer dtStart = req.getDtStart() == null ? MyDateUtil.getTodayDt() : req.getDtStart();
        Integer dtEnd = req.getDtEnd() == null ? MyDateUtil.getTodayDt() : req.getDtEnd();
        PageResp<List<GuildRankTaskDistributionDetailsDTO>>  pageResp = taskService.listTasks(req.getUids(),req.getYys(), dtStart, dtEnd, req.getTask(), pageNum, pageSize);
        while (CollectionUtils.isNotEmpty(pageResp.getData())) {
            for(GuildRankTaskDistributionDetailsDTO guildRankTaskDistributionDTO : pageResp.getData()) {
                exportDTOS.add(GuildRankTaskDistributionAdminExportDTO.build(guildRankTaskDistributionDTO));
            }
            pageNum++;
            pageResp = taskService.listTasks(req.getUids(),req.getYys(), dtStart, dtEnd, req.getTask(), pageNum, pageSize);
        }
        export(exportDTOS, GuildRankTaskDistributionAdminExportDTO.class,"公会任务发放");
    }

    /**
     * 查看活动下发历史
     * onlineBind3And4Star 线上绑定3&4星主播数量
     * newHighWaistAnchor 新增高优&腰部主播
     * newAnchorBlueDiamond 新主播蓝钻任务
     * guildBlueDiamond 公会蓝钻任务
     * monthBlueIncome 自然月月累积蓝钻收入
     * springHeadline  春季头条任务
     * summerHeadline 夏季头条任务
     * guildRaceS1 公会赛S1任务
     * guildRaceS2 公会赛S2任务
     * guildRaceMain 公会赛正赛任务
     * personalRaceMain 个人赛正赛任务
     *
     * @param uids  uids=1,2,3,4
     * @param dtStart 格式 yyyyMM 202507
     * @param dtEnd  格式 yyyyMM 202507
     * @param task [onlineBind3And4Star, newHighWaistAnchor, newAnchorBlueDiamond, guildBlueDiamond , monthBlueIncome , platformActivity]
     * @param opType [add, update, delete]
     * @param operator dw_xiongrongliang2
     * @param pageNum 从 1开始
     * @param pageSize
     * @return
     */
    @GetMapping("/task/history")
    public PageResp<List<GuildRankTaskDistributionHistoryDTO>> listHistoryTasks(
            @RequestParam(required = false) List<Long> uids,
            @RequestParam(required = false) Integer dtStart,
            @RequestParam(required = false) Integer dtEnd,
            @RequestParam(required = false) String task,
            @RequestParam(required = false) String opType,
            @RequestParam(required = false) String operator,
            @RequestParam(defaultValue = "1") @Min(1) int pageNum,
            @RequestParam(defaultValue = "10") @Max(30) int pageSize) {
        log.info("listHistoryTasks uids:{} dtStart:{} dtEnd:{} taskType:{} opType:{} pageNum:{} pageSize:{}", uids, dtStart, dtEnd, task, opType, pageNum, pageSize);
        return taskService.listHistoryTasks(uids, dtStart, dtEnd, task, opType, operator, pageNum, pageSize);
    }

    /**
     * 创建下发任务
     * @param request
     * @return
     */
    @PostMapping("/task")
    public Resp<Void> createTasks(@Valid @RequestBody GuildRankTaskDistributionCreateRequest request) {
        String passport = LocalUserContext.get().getPassport();
        log.info("createTasks req:{} passport:{}", request, passport);
        taskService.createTasks(request, passport);
        return Resp.createBySuccess();
    }

    /**
     * 批量uid修改单个任务
     * @param request
     * @return
     */
    @PutMapping("/tasks")
    public Resp<Void> updateBatchUidTask(@Valid @RequestBody GuildRankTaskDistributionBatchUpdateRequest request) {
        String passport = LocalUserContext.get().getPassport();
        log.info("updateBatchUidTask req:{} passport:{}", request, passport);
        taskService.updateBatchUidTask(request, passport);
        return Resp.createBySuccess();
    }

    /**
     * 修改单个uid多个任务
     * @param request
     * @return
     */
    @PutMapping("/task")
    public Resp<Void> updateMultiTask(@Valid @RequestBody GuildRankTaskDistributionUpdateByOneUidRequest request) {
        String passport = LocalUserContext.get().getPassport();
        log.info("updateMultiTask req:{} passport:{}", request, passport);
        taskService.updateMultiTask(request, passport);
        return Resp.createBySuccess();
    }

    /**
     * 删除
     * @return
     */
    @PostMapping("/delete-task")
    public Resp<Void> deleteTask(@Valid @RequestBody GuildRankTaskDistributionDeleteRequest request) {
        String passport = LocalUserContext.get().getPassport();
        log.info("deleteTask req:{} passport:{}", request, passport);
        taskService.deleteTask(request, passport);
        return Resp.createBySuccess();
    }
}