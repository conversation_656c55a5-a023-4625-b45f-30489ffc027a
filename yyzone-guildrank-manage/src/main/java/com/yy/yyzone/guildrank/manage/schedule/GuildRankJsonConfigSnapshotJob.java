package com.yy.yyzone.guildrank.manage.schedule;

import com.yy.yyzone.guildrank.service.JsonConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class GuildRankJsonConfigSnapshotJob {

    @Autowired
    private JsonConfigService jsonConfigService;

    @Scheduled(cron = "30 50,53,56,58,59 23 * * ?")
    public void saveSnapshot(){
        log.info("saveSnapshot begin");
        jsonConfigService.saveSnapshot();
        log.info("saveSnapshot end");
    }
}
