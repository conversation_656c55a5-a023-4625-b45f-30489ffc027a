package com.yy.yyzone.guildrank.manage.handler;

import com.alibaba.excel.exception.ExcelAnalysisException;
import com.yy.yyzone.guildrank.util.Resp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

@ControllerAdvice
public class GlobalExceptionHandler {
    private static Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @ExceptionHandler({MethodArgumentNotValidException.class, BindException.class, MethodArgumentTypeMismatchException.class, MissingServletRequestParameterException.class})
    @ResponseBody
    public Resp handlerMethodValidationException(Exception exception) {
        logger.warn("handler {} msg={}",exception.getClass().getSimpleName(),exception.getMessage(), exception);
        return Resp.createByError("参数错误");
    }
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseBody
    public Resp illegalArgumentExceptionException(IllegalArgumentException exception) {
        logger.error("illegalArgumentExceptionException error: msg={}", exception.getMessage(), exception);
        return Resp.createByError(exception.getMessage());
    }

    @ExceptionHandler(ExcelAnalysisException.class)
    @ResponseBody
    public Resp excelAnalysisExceptionException(ExcelAnalysisException exception) {
        logger.error("excelAnalysisExceptionException error: msg={}", exception.getMessage(), exception);
        return Resp.createByError(exception.getMessage());
    }

    @ExceptionHandler(Exception.class)
    @ResponseBody
    public Resp commonException(Exception exception) {
        logger.error("commonException error msg={}", exception.getMessage(), exception);
        return Resp.createByError("服务正忙，请稍后重试");
    }
}
