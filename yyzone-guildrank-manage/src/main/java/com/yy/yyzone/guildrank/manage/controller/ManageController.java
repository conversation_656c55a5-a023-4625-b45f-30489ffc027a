package com.yy.yyzone.guildrank.manage.controller;

import com.google.common.collect.Lists;
import com.yy.yyzone.guildrank.db.custom.mapper.CustomMapper;
import com.yy.yyzone.guildrank.db.gen.mapper.GuildrankTemporaryChatMapper;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankTemporaryChat;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankTemporaryChatExample;
import com.yy.yyzone.guildrank.manage.interceptor.LocalUserContext;
import com.yy.yyzone.guildrank.util.ExportTextUtil;
import com.yy.yyzone.guildrank.util.PageResp;
import com.yy.yyzone.guildrank.util.Resp;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * demo代码
 *<AUTHOR> gen
 **/
@RestController
@RequestMapping("/guildRank")
public class ManageController {

    private static Logger logger = LoggerFactory.getLogger(ManageController.class);


    @Autowired
    private GuildrankTemporaryChatMapper guildrankTemporaryChatMapper;

    @Autowired
    CustomMapper customMapper;

    /**
     * 添加临时会话
     * @param yy 运营yy号
     * @param nick 运营昵称
     * @param guildUids 公会yy号 多个逗号隔开
     * @return
     */
    @RequestMapping("/addTemporaryChat")
    public Resp<String> addTemporaryChat(String yy, @RequestParam(required = false) String nick, String guildUids) {
        if (LocalUserContext.getUid() <= 0) {
            return Resp.createByError("请登陆");
        }
        if (StringUtils.isBlank(guildUids) || StringUtils.isBlank(yy)) {
            return Resp.createByError("请填写数据");
        }
        String[] guildUidArr = guildUids.split(",");
        List<Long> uids = Lists.newArrayList();
        for (String s : guildUidArr) {
            uids.add(Long.parseLong(s));
        }

        GuildrankTemporaryChatExample example = new GuildrankTemporaryChatExample();
        example.createCriteria().andStatusEqualTo(0).andGuilduidIn(uids);
        List<GuildrankTemporaryChat> list = guildrankTemporaryChatMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(list)) {
            return Resp.createByError(1, "重复公会UID：" + list.get(0).getGuilduid());
        }

        GuildrankTemporaryChatExample example2 = new GuildrankTemporaryChatExample();
        example2.createCriteria().andStatusEqualTo(0).andServiceyyEqualTo(Long.parseLong(yy));
        List<GuildrankTemporaryChat> list2 = guildrankTemporaryChatMapper.selectByExample(example2);
        if (CollectionUtils.isNotEmpty(list2)) {
            return Resp.createByError(2, "重复运营YY号");
        }

        int insertNum = customMapper.getMaxinsertNum() + 1;
        List<GuildrankTemporaryChat> addList = Lists.newArrayList();
        for (Long uid : uids) {
            GuildrankTemporaryChat item = new GuildrankTemporaryChat();
            item.setServiceyy(Long.parseLong(yy));
            item.setGuilduid(uid);
            item.setCreatetime(new Date());
            item.setUpdatetime(new Date());
            item.setAddby(LocalUserContext.get().getPassport());
            //item.setAddby("dw_xiaoxiang");
            item.setStatus(0);
            item.setInsertnum(insertNum);
            item.setBiztype(0);
            item.setServicenick(nick);
            addList.add(item);
        }
        int succ = customMapper.batchAddTemporaryChat(addList);
        return Resp.createBySuccessMassage("成功插入：" + succ + "条数据");
    }

    /**
     * 查询服务公会列表
     * @param offset
     * @param limit
     * @return
     */
    @RequestMapping("/listTemporaryChat")
    public PageResp<List<GuildrankTemporaryChat>> listTemporaryChat(int offset, int limit) {
        GuildrankTemporaryChatExample example = new GuildrankTemporaryChatExample();
        int total = guildrankTemporaryChatMapper.countByExample(example);
        List<GuildrankTemporaryChat> list = customMapper.listTemporaryChat(offset, limit);
        return PageResp.createBySuccess(list, total);
    }

    /**
     * 下载服务公会名单
     * @param insertNum 回传列表这个字段
     * @param response
     */
    @RequestMapping("/downTemporaryChat")
    public void downTemporaryChat(int insertNum, HttpServletResponse response) {
        GuildrankTemporaryChatExample example = new GuildrankTemporaryChatExample();
        example.createCriteria().andInsertnumEqualTo(insertNum);
        List<GuildrankTemporaryChat> list = guildrankTemporaryChatMapper.selectByExample(example);
        StringBuffer buffer = new StringBuffer();
        for (GuildrankTemporaryChat guildrankTemporaryChat : list) {
            buffer.append(guildrankTemporaryChat.getGuilduid()).append("\r\n");
        }
        ExportTextUtil.writeToTxt(response, buffer.toString(), "服务公会名单");
    }


    /**
     * 失效
     * @param insertNum 回传列表这个字段
     */
    @RequestMapping("/invalidateTemporaryChat")
    public Resp invalidate(int insertNum) {
        GuildrankTemporaryChatExample example = new GuildrankTemporaryChatExample();
        example.createCriteria().andInsertnumEqualTo(insertNum);
        GuildrankTemporaryChat record = new GuildrankTemporaryChat();
        record.setStatus(1);
        record.setUpdatetime(new Date());
        int count = guildrankTemporaryChatMapper.updateByExampleSelective(record, example);
        if (count > 0) {
            return Resp.createBySuccess();
        }
        return Resp.createByError();
    }
}
