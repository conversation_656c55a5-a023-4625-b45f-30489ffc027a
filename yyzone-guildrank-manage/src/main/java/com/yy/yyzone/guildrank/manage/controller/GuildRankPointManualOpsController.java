package com.yy.yyzone.guildrank.manage.controller;

import com.google.common.collect.Lists;
import com.yy.yyzone.guildrank.dto.manual.ManualOpsListVO;
import com.yy.yyzone.guildrank.dto.manual.ManualOpsLogListVO;
import com.yy.yyzone.guildrank.dto.manual.ScoreManualDelDTO;
import com.yy.yyzone.guildrank.dto.manual.ScoreManualModifyDTO;
import com.yy.yyzone.guildrank.service.GuildRankPointManualOpsService;
import com.yy.yyzone.guildrank.util.PageResp;
import com.yy.yyzone.guildrank.util.Resp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@RequestMapping("/point-manual-ops")
@RestController
public class GuildRankPointManualOpsController extends BaseController {

    @Autowired
    private GuildRankPointManualOpsService guildRankPointManualOpsService;

    /**
     * 分页查询
     *
     * @param mainGuildUids 主体uid，逗号分隔多个
     * @param yearMonth     年月，yyyy-MM 格式
     * @return
     */
    @GetMapping(value = "/page-list")
    public PageResp<List<ManualOpsListVO>> pageList(@RequestParam(name = "mainGuildUids", defaultValue = "") String mainGuildUids,
                                                    @RequestParam(name = "yearMonth", defaultValue = "") String yearMonth,
                                                    @RequestParam(name = "pageNum", defaultValue = "1") int pageNum,
                                                    @RequestParam(name = "pageSize", defaultValue = "20") int pageSize) {

        List<Long> uids = null;
        if (StringUtils.isNotBlank(mainGuildUids)) {
            String[] guildUidArr = mainGuildUids.split(",");
            uids = Lists.newArrayList();
            for (String s : guildUidArr) {
                uids.add(Long.parseLong(s));
            }
        }

        try {
            return guildRankPointManualOpsService.pageList(uids, yearMonth, pageNum, pageSize);
        } catch (Exception e) {
            log.error("pageList happen err, mainGuildUids = {}, yearMonth = {}", mainGuildUids, yearMonth, e);
            return PageResp.createByError("系统异常");
        }
    }

    /**
     * 新增或修改
     *
     * @param req 请求参数
     * @return 处理结果
     */
    @PostMapping(value = "/modify")
    public Resp<?> modify(@RequestBody @Valid ScoreManualModifyDTO req, BindingResult result) {
        String operator = getOperator();
        log.info("modify, operator = {}, ScoreManualModifyDTO = {}", operator, req);
        Resp<?> r = getBindingResult(result);
        if (!r.checkSuccess()) {
            return r;
        }
        try {
            return guildRankPointManualOpsService.addOrModify(req, operator);
        } catch (Exception e) {
            log.error("modify happen err, ScoreManualModifyDTO = {}", req, e);
            return Resp.createByError("系统异常");
        }
    }

    /**
     * 删除
     *
     * @param dto 请求参数
     * @return 处理结果
     */
    @PostMapping(value = "/del")
    public Resp<?> del(@RequestBody @Valid ScoreManualDelDTO dto, BindingResult result) {
        String operator = getOperator();
        Resp<?> r = getBindingResult(result);
        if (!r.checkSuccess()) {
            return r;
        }
        try {
            return guildRankPointManualOpsService.del(dto, operator);
        } catch (Exception e) {
            log.error("del happen err, ScoreManualDelDTO = {}", dto, e);
            return Resp.createByError("系统异常");
        }
    }

    /**
     * 分页查询 变更记录
     *
     * @param modifyType    变更类型，1-新增，2-删除
     * @param mainGuildUids 主体uid，逗号分隔多个
     * @return 变更记录
     */
    @GetMapping(value = "/change-log/page-list")
    public PageResp<List<ManualOpsLogListVO>> changeLogPageList(@RequestParam(name = "modifyType", defaultValue = "0") byte modifyType,
                                                                @RequestParam(name = "mainGuildUids", defaultValue = "") String mainGuildUids,
                                                                @RequestParam(name = "pageNum", defaultValue = "1") int pageNum,
                                                                @RequestParam(name = "pageSize", defaultValue = "20") int pageSize) {


        List<Long> uids = null;
        if (StringUtils.isNotBlank(mainGuildUids)) {
            String[] guildUidArr = mainGuildUids.split(",");
            uids = Lists.newArrayList();
            for (String s : guildUidArr) {
                uids.add(Long.parseLong(s));
            }
        }

        try {
            return guildRankPointManualOpsService.changeLogPageList(modifyType, uids, pageNum, pageSize);
        } catch (Exception e) {
            log.error("pageList happen err, mainGuildUids = {}, modifyType = {}", mainGuildUids, modifyType, e);
            return PageResp.createByError("系统异常");
        }
    }
}
