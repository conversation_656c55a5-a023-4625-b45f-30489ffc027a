syntax = "proto3";

package com.yy.yyzone.guildrank.yrpc.client;
import "uri.proto";
option java_outer_classname = "GuildTypeReqResp";

message batchGetGuildTypeReq {
	repeated uint32 owUids = 1;
	map<uint32,string> extendData =2;
	option (yyp.uri) = 1;
}
message batchGetGuildTypeResp {
	uint32 result = 1;
	map<uint32,uint32> types = 2;// 0-普通 1-星级 2-金牌
	option (yyp.uri) = 2;
}

service GuildTypeService {
	rpc batchGetGuildType(batchGetGuildTypeReq) returns (batchGetGuildTypeResp);
}
