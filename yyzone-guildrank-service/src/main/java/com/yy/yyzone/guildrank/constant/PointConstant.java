package com.yy.yyzone.guildrank.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

public interface PointConstant {
    String POINT_REMARK = "公会健康度积分";
    int POINT_VALID_MONTH = 12;

    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    enum Type {
        MONTHLY_RANK(0, "每月段位更新"),
        ;
        int value;
        String desc;
    }

    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    enum Status {
        TO_BE_GRANT(0, "待处理"),
        GRANTED(1, "已处理"),
        GRANTING(2, "处理中"),
        GRANT_FAIL(3, "处理失败"),
        ;
        int value;
        String desc;
    }
}
