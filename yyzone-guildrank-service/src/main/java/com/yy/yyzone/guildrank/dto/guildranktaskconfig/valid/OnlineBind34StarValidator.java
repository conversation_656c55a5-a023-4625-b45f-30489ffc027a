package com.yy.yyzone.guildrank.dto.guildranktaskconfig.valid;

import com.yy.yyzone.guildrank.dto.guildranktaskconfig.GuildRankTask;
import com.yy.yyzone.guildrank.dto.guildranktaskconfig.Indicator;
import com.yy.yyzone.guildrank.dto.guildranktaskconfig.IndicatorEnum;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.List;

/**
 * 线上绑定3&4星主播任务
 */
public class OnlineBind34StarValidator implements ConstraintValidator<OnlineBind34StarValid, GuildRankTask> {

    @Override
    public boolean isValid(GuildRankTask task, ConstraintValidatorContext ctx) {
        if (task == null) {
            return false;
        }
        // 1. indicators 必须 2 个，顺序固定
        List<Indicator> list = task.getScore().getIndicators();
        if (list == null || list.size() != 2) {
            return fail(ctx, "indicators 必须 2 个");
        }
        if (list.get(0).getIndicator() != IndicatorEnum.lastThreeMonthsOnlineBind3StarCount ||  list.get(1).getIndicator() != IndicatorEnum.lastThreeMonthsOnlineBind4StarCount) {
            return fail(ctx, "indicator 顺序必须是 onlineBind3StarCount, onlineBind4StarCount");
        }
        // 2. 不能出现 score，必须出现 unitsPerCount & scorePerUnit
        for (Indicator i : list) {
            if (i.getScore() != null) {
                return fail(ctx, "不允许直接配置 score");
            }
            if (i.getUnitsPerCount() == null || i.getScorePerUnit() == null) {
                return fail(ctx, "必须配置 unitsPerCount 和 scorePerUnit");
            }
        }
        // 3. 不允许出现 score
        if (task.getCondition().getRanges().stream().anyMatch(r -> r.getScore() != null)) {
            return fail(ctx, "range 不允许配置 score");
        }
        return true;
    }

    private boolean fail(ConstraintValidatorContext ctx, String msg) {
        ctx.disableDefaultConstraintViolation();
        ctx.buildConstraintViolationWithTemplate("线上绑定3&4星主播任 " + msg).addConstraintViolation();
        return false;
    }
}