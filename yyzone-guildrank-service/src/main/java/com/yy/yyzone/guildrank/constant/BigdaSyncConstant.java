package com.yy.yyzone.guildrank.constant;


import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

public interface BigdaSyncConstant {
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    enum Status {
        SYNC_DONE(0, "海度同步完成"),
        CHECK_DONE(1, "数据计算完成"),
        ;
        int value;
        String desc;
    }

    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    enum TableName {
        CMP_HEALTH_DI(0, "data_out.yy_dm_entity_guild_cmp_health_analysis_di", "yyyy-MM-dd"),
        ;
        int value;
        String name;
        String dtFmt;

        public static TableName of(String name) {
            if (StringUtils.isEmpty(name)) {
                return null;
            }
            for (TableName t : TableName.values()) {
                if (t.getName().equals(name)) {
                    return t;
                }
            }
            return null;
        }
    }
}
