package com.yy.yyzone.guildrank.dto.guildranktaskconfig.valid;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = NewHighWaistAnchorValidator.class)
public @interface NewHighWaistAnchorValid {
    String message() default "newHighWaistAnchor 配置不合法";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}