package com.yy.yyzone.guildrank.service;

import com.google.common.collect.Maps;
import com.yy.ent.mobile.metrics.MetricsStopWatch;
import com.yy.yyzone.guildrank.constant.RankConstant;
import com.yy.yyzone.guildrank.db.gen.mapper.DmYyGuildSegmentScoreMonthMapper;
import com.yy.yyzone.guildrank.db.gen.model.DmYyGuildSegmentScoreMonth;
import com.yy.yyzone.guildrank.db.gen.model.DmYyGuildSegmentScoreMonthExample;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 段位
 * <AUTHOR>
 * @date 2023/03/16
 **/
@Service
@Slf4j
public class RankCacheService {

    private ConcurrentHashMap<Long, Integer> currentRank;

    @Autowired
    private StarService starService;

    @Autowired
    private RankCheckService rankCheckService;

    @Autowired
    private DmYyGuildSegmentScoreMonthMapper dmYyGuildSegmentScoreMonthMapper;

    private final ReentrantLock lock = new ReentrantLock();
    public volatile int HAS_UPDATE = 0;

    // @Scheduled(cron = "0 0/10 * * * ?")
    public void reload() {
        MetricsStopWatch metricsStopWatch = MetricsStopWatch.startInternalWatch().uri("Rank/reload");
        lock.lock();
        try {
            boolean hasUpdate = rankCheckService.rankCheckDone(DateFormatUtils.format(DateUtils.addMonths(new Date(), -1),"yyyy-MM"));
            int month = hasUpdate ? RankConstant.LAST : RankConstant.PRE_LAST;
            String currentDate = DateFormatUtils.format(DateUtils.addMonths(new Date(), month), "yyyy-MM");
            log.info("RankCacheService reload hasUpdate:{},dt:{}", hasUpdate, currentDate);
            Map<Long, Integer> rankMap = getRankFromDB(currentDate);

            // 无段位的星级公会默认为一星
            List<Long> starGuildList = starService.getAllStarGoldGuild();
            starGuildList.stream().filter(uid -> !rankMap.containsKey(uid)).forEach(uid -> rankMap.put(uid, RankConstant.DEFAULT_RANK));

            currentRank = new ConcurrentHashMap<>(rankMap);
            HAS_UPDATE = hasUpdate ? RankConstant.UPDATE : RankConstant.NOT_UPDATE;
            metricsStopWatch.successCode().markDurationAndCode();
            log.info("reload rank, rankMap:{}", rankMap);
        } catch (Exception e){
            log.warn("reload fail", e);
            metricsStopWatch.failCode().markDurationAndCode();
        } finally {
            lock.unlock();
        }
    }

    // @PostConstruct
    public void init(){
        reload();
    }

    /**
     * 从db获取公会段位（数据来源BI）
     *
     * @param date 日期
     * @return map
     */
    private Map<Long, Integer> getRankFromDB(String date) {
        Map<Long, Integer> result = Maps.newHashMapWithExpectedSize(3_000);
        DmYyGuildSegmentScoreMonthExample example = new DmYyGuildSegmentScoreMonthExample();
        example.createCriteria().andDtEqualTo(date + "-01");
        example.setOrderByClause(" id asc ");
        List<DmYyGuildSegmentScoreMonth> list = dmYyGuildSegmentScoreMonthMapper.selectByExample(example);
        for (DmYyGuildSegmentScoreMonth data : list) {
            result.put(data.getSidOwnerid(), data.getPsSAll());
        }
        return result;
    }

    public Map<Long, Integer> batchQueryCurrentRank(List<Long> guildUidList) {
        MetricsStopWatch metricsStopWatch = MetricsStopWatch.startServerWatch().uri("batchQueryCurrentRank");
        if (CollectionUtils.isEmpty(guildUidList)) {
            return Collections.emptyMap();
        }

        Map<Long, Integer> result = Maps.newHashMapWithExpectedSize(guildUidList.size());
        guildUidList.stream().filter(Objects::nonNull).filter(currentRank::containsKey).forEach(uid -> result.put(uid, currentRank.get(uid)));
        metricsStopWatch.code(0).markDurationAndCode();
        return result;
    }

    public int queryCurrentRank(Long guildUid){
        MetricsStopWatch metricsStopWatch = MetricsStopWatch.startServerWatch().uri("currentRankV2");
        int rank = currentRank.getOrDefault(guildUid, 0);
        metricsStopWatch.code(0).markDurationAndCode();
        return rank;
    }

    public int getUpdateFlag(){
        return HAS_UPDATE;
    }
}
