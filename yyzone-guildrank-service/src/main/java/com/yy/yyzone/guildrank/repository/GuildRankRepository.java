package com.yy.yyzone.guildrank.repository;

import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.yyzone.guildrank.db.custom.mapper.BiExtMapper;
import com.yy.yyzone.guildrank.db.custom.mapper.GuildrankWhilelistExtMapper;
import com.yy.yyzone.guildrank.db.custom.model.KV;
import com.yy.yyzone.guildrank.db.gen.mapper.GuildrankHistoryMapper;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankHistory;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankHistoryExample;
import com.yy.yyzone.guildrank.util.MyDateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDate;
import java.util.*;

@Slf4j
@Repository
public class GuildRankRepository {
    private static final int BATCH_SIZE = 500;

    @Autowired
    private GuildrankHistoryMapper historyMapper;

    @Autowired
    private BiExtMapper biExtMapper;

    @Autowired
    private GuildrankWhilelistExtMapper whilelistExtMapper;

    @Autowired
    private StringRedisTemplate redisTemplate;

    public void saveHistory(Date month, List<GuildrankHistory> histories) {
        if (CollectionUtils.isEmpty(histories)) {
            return;
        }
        String lockKey = "g_rank_save:" + MyDateUtil.fmtMonth(month);
        Boolean b = redisTemplate.opsForValue().setIfAbsent(lockKey, String.valueOf(System.currentTimeMillis()), Duration.ofMinutes(1));
        if (!b) {
            log.info("saveHistory lock failed key:{}", lockKey);
            return;
        }

        try {
            Date now = new Date();
            for (GuildrankHistory h : histories) {
                h.setUpdateTime(now);
            }

            List<List<GuildrankHistory>> lists = Lists.partition(histories, BATCH_SIZE);
            int count1 = 0;
            for (List<GuildrankHistory> list : lists) {
                count1 += biExtMapper.insertOrUpdateBatchHistory(list);
            }
            GuildrankHistoryExample example = new GuildrankHistoryExample();
            example.createCriteria().andMonthEqualTo(month).andUpdateTimeNotEqualTo(now);
            int count2 = historyMapper.deleteByExample(example);
            log.info("saveHistory del month:{},now:{},count1:{},count2:{}", MyDateUtil.fmt(month), MyDateUtil.fmt(now), count1, count2);
        } catch (Exception e) {
            log.error("saveHistory exception", e);
        }
    }

    /**
     * 从db获取主体段位（数据来源BI）
     *
     * @param date 日期
     * @return map
     */
    @Transactional(rollbackFor = {RuntimeException.class, Exception.class})
    public Map<Long, Integer> getCmpRankFromDB(Date date,boolean switchToCmpStarLvl) {
        if(switchToCmpStarLvl) {
            List<KV<Long, Integer>> kvs = biExtMapper.selectCmpStarLevel(date);
            return KV.toMap(kvs);
        }else{
            List<KV<Long, Integer>> kvs = biExtMapper.selectDataCmpLevel(date);
            return KV.toMap(kvs);
        }
        // Map<Long, Integer> result = Maps.newHashMapWithExpectedSize(3_000);
        // YyDmEntityGuildCmpHealthAnalysisDiExample example = new YyDmEntityGuildCmpHealthAnalysisDiExample();
        // example.createCriteria().andDtEqualTo(date);
        // example.setOrderByClause(" id asc ");
        // List<YyDmEntityGuildCmpHealthAnalysisDi> list = diMapper.selectByExample(example);
        // for (YyDmEntityGuildCmpHealthAnalysisDi di : list) {
        //     result.put(di.getGuildCmpOwnrId(), di.getCmpStarLvl());
        // }
        // return result;
    }

    @Transactional(rollbackFor = {RuntimeException.class, Exception.class})
    public Map<Long,Integer> getWhitelistRank(String month) {
        return KV.toMap(whilelistExtMapper.selectRank(month));
    }

    public Map<Long, Integer> getRankFromHistory(Collection<Long> guildUids, Date month) {
        if (CollectionUtils.isEmpty(guildUids)) {
            return Collections.emptyMap();
        }
        Iterable<List<Long>> lists = Iterables.partition(guildUids, BATCH_SIZE);
        Map<Long, Integer> result = Maps.newHashMapWithExpectedSize(guildUids.size());
        for (List<Long> l : lists) {
            List<KV<Long, Integer>> kvs = biExtMapper.selectLevelFromHisotry(l, month);
            result.putAll(KV.toMap(kvs));
        }
        return result;
    }

    public Map<Long, Integer> getAllRankFromHistory(Date month) {
        List<KV<Long, Integer>> kvs = biExtMapper.selectLevelFromHisotry(null, month);
        return KV.toMap(kvs);
    }
}