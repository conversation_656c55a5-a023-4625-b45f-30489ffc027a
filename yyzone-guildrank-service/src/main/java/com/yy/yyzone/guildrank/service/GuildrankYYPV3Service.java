package com.yy.yyzone.guildrank.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.yyzone.guildrank.api.AllGuildRank;
import com.yy.yyzone.guildrank.api.GuildRank;
import com.yy.yyzone.guildrank.api.GuildRankI64;
import com.yy.yyzone.guildrank.constant.RankConstant;
import com.yy.yyzone.guildrank.repository.GuildRankRepository;
import com.yy.yyzone.guildrank.util.MyDateUtil;
import com.yy.yyzone.guildrank.util.YrpcUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class GuildrankYYPV3Service {
    @Autowired
    private RankCacheV3Service rankCacheV3Service;

    @Autowired
    private GuildRankRepository guildRankRepository;

    public GuildRank.CurrentRankResp queryCurrentRank(GuildRank.CurrentRankReq req) {
        int rank = rankCacheV3Service.queryCurrentRank(req.getUid());
        return GuildRank.CurrentRankResp.newBuilder().setLevel(rank).setResult(0).build();
    }

    public GuildRank.QueryCurrentRankResp queryCurrentRankByFrom(GuildRank.QueryCurrentRankReq req) {
        int rank = rankCacheV3Service.queryCurrentRank(req.getUid());
        return GuildRank.QueryCurrentRankResp.newBuilder().setResult(0).setLevel(rank).build();
    }

    public GuildRank.BatchQueryCurrentRankResp batchQueryCurrentRank(GuildRank.BatchQueryCurrentRankReq req) {
        List<Integer> uids = req.getUidList();
        List<Long> uidsLong = Lists.newArrayList();
        for (Integer uid : uids) {
            uidsLong.add(YrpcUtil.cppInt32ToLong(uid));
        }
        Map<Long, Integer> result = rankCacheV3Service.batchQueryCurrentRank(uidsLong);
        Map<Integer, Integer> resp = Maps.newHashMap();
        for (Map.Entry<Long, Integer> entry : result.entrySet()) {
            resp.put(entry.getKey().intValue(), entry.getValue());
        }
        return GuildRank.BatchQueryCurrentRankResp.newBuilder().setResult(0).putAllUidLevel(resp).build();
    }

    public GuildRank.BatchQueryGuildRankResp batchQueryGuildRank(GuildRank.BatchQueryGuildRankReq req) {
        List<Integer> uids = req.getUidList();
        List<Long> uidsLong = Lists.newArrayList();
        for (Integer uid : uids) {
            uidsLong.add(YrpcUtil.cppInt32ToLong(uid));
        }
        int isNew = rankCacheV3Service.hasUpdate() ? RankConstant.UPDATE : RankConstant.NOT_UPDATE;
        Map<Long, Integer> result = rankCacheV3Service.batchQueryCurrentRank(uidsLong);
        Map<Integer, Integer> resp = Maps.newHashMap();
        for (Map.Entry<Long, Integer> entry : result.entrySet()) {
            resp.put(entry.getKey().intValue(), entry.getValue());
        }
        return GuildRank.BatchQueryGuildRankResp.newBuilder().setResult(0)
                .setIsNew(isNew).putAllUidLevel(resp).build();
    }

    public AllGuildRank.QueryAllGuildRankByOrderResp queryAllGuildRankByOrder(AllGuildRank.QueryAllGuildRankByOrderReq req) {
        log.warn("queryAllGuildRankByOrder 32 req:{}", req);
        throw new UnsupportedOperationException("请使用64位接口113 << 8 | 111");
    }

    public AllGuildRank.QueryAllGuildRankResp queryAllGuildRank(AllGuildRank.QueryAllGuildRankReq req) {
        Date month = MyDateUtil.parseDate(req.getMonth());
        int isNew = 0;
        if (rankCacheV3Service.hasUpdate()) {
            isNew = 1;

            Map<Long, Integer> rankMap;
            if (DateUtils.truncatedEquals(rankCacheV3Service.getRankCurrentMonth(), month, Calendar.MONTH)) {
                rankMap = rankCacheV3Service.allRank();
            } else {
                rankMap = guildRankRepository.getAllRankFromHistory(month);
            }

            List<AllGuildRank.GuildRankVO> list = Lists.newArrayListWithExpectedSize(rankMap.size());
            for (Map.Entry<Long, Integer> e : rankMap.entrySet()) {
                Map<String, String> map = Maps.newHashMapWithExpectedSize(3);
                map.put("sidOwnerid", String.valueOf(e.getKey()));
                map.put("psSAll", String.valueOf(e.getValue()));
                map.put("dt", req.getMonth());
                list.add(AllGuildRank.GuildRankVO.newBuilder().putAllData(map).build());
            }

            return AllGuildRank.QueryAllGuildRankResp.newBuilder().setIsNew(isNew).addAllDataList(list).build();
        }
        return AllGuildRank.QueryAllGuildRankResp.newBuilder().setIsNew(isNew).build();
    }

    public GuildRankI64.BatchQueryCurrentRankResp64 batchQueryCurrentRankNew(GuildRankI64.BatchQueryCurrentRankReq64 req) {
        List<Long> uidsLong = req.getUidList();
        Map<Long, Integer> result = rankCacheV3Service.batchQueryCurrentRank(uidsLong);
        return GuildRankI64.BatchQueryCurrentRankResp64.newBuilder().setResult(0).putAllUidLevel(result).build();
    }

    public GuildRankI64.BatchQueryGuildRankResp64 batchQueryGuildRankNew(GuildRankI64.BatchQueryGuildRankReq64 req) {
        List<Long> uidsLong = req.getUidList();
        int isNew = rankCacheV3Service.hasUpdate() ? RankConstant.UPDATE : RankConstant.NOT_UPDATE;
        Map<Long, Integer> result = rankCacheV3Service.batchQueryCurrentRank(uidsLong);
        return GuildRankI64.BatchQueryGuildRankResp64.newBuilder().setResult(0)
                .setIsNew(isNew).putAllUidLevel(result).build();
    }
}