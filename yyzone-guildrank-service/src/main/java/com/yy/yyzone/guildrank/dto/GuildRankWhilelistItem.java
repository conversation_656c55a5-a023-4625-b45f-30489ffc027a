package com.yy.yyzone.guildrank.dto;

import lombok.Data;

import java.util.Date;

/**
 * 公会段位白名单列表信息
 * <AUTHOR>
 * @date 2019/11/5
 *
 */
@Data
public class GuildRankWhilelistItem {
    /**
     * id
     */
    private Long id;

    /**
     * 公会uid
     */
    private Long guildUid;


    /**
     * 公会yy号
     */
    private String guildYyno;

    /**
     * 公会等级
     */
    private Integer rank;

    /**
     * 操作人
     */
    private String oprator;

    /**
     * 备注
     */
    private String remark;

    /**
     * 月份2018-01
     */
    private String monthstr;

    /**
     * 操作时间
     */
    private Date optTime;

    /**
     * 能否删除
     */
    private boolean canDelete;

    /**
     * 能否编辑
     */
    private boolean canEdit;
}