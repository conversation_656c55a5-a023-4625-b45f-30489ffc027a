package com.yy.yyzone.guildrank.util;

import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2019/8/1
 */
public class MyDateUtil {
    public static String getFirstDay(int addMonth) {
        return DateFormatUtils.format(DateUtils.addMonths(new Date(), addMonth), "yyyy-MM") + "-01";
    }

    /**
     * 解析日期，不抛异常
     *
     * @param d
     * @param pattern    不传默认yyyy-MM-dd
     * @param defaultVal
     * @return
     */
    public static Date parseDate(String d, String pattern, Date defaultVal) {
        try {
            if (StringUtils.isBlank(pattern)) {
                pattern = "yyyy-MM-dd";
            }
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            return sdf.parse(d);
        } catch (Exception ex) {
            // ignored
        }

        return defaultVal;
    }

    public static Date parseDate(String source) {
        if (StringUtils.isBlank(source)) {
            return null;
        }
        List<SimpleDateFormat> fmts = Arrays.asList(
                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"),
                new SimpleDateFormat("yyyy-MM-dd"),
                new SimpleDateFormat("yyyy/MM/dd HH:mm:ss"),
                new SimpleDateFormat("yyyy/MM/dd"),
                new SimpleDateFormat("yyyy-MM"));
        for (DateFormat dateFormat : fmts) {
            try {
                return dateFormat.parse(source);
            } catch (Exception ex) {
                // ignored;
            }
        }
        return null;
    }

    public static Date parseMonth(String month) {
       try {
           return new SimpleDateFormat("yyyy-MM").parse(month);
       } catch (Exception ex) {
           // ignored;
       }
       return null;
    }

    public static String getTargetMonthStr(Date date) {
        date = date == null ? new Date() : date;
        return DateFormatUtils.format(date, "yyyy-MM");
    }

    public static String getCurrentMonthStr() {
        return getTargetMonthStr(new Date());
    }

    /**
     * 本月1号日期
     *
     * @return
     */
    public static Date currentMonth() {
        return DateUtils.truncate(new Date(), Calendar.MONTH);
    }

    /**
     * 获取月结束日期
     *
     * @param d
     * @return
     */
    public static Date monthEnd(Date d) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(d);
        int lastDay = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        calendar.set(Calendar.DAY_OF_MONTH, lastDay);
        return calendar.getTime();
    }

    /**
     * 上月1号日期
     *
     * @return
     */
    public static Date lastMonth() {
        return DateUtils.addMonths(currentMonth(), -1);
    }

    public static Date lastMonthEnd() {
        return DateUtils.addDays(currentMonth(), -1);
    }

    public static Date lastMonthEnd(Date d) {
        return DateUtils.addDays(DateUtils.truncate(d, Calendar.MONTH), -1);
    }

    public static Date monthEnd(int delta) {
        return DateUtils.addDays(DateUtils.addMonths(currentMonth(), delta + 1), -1);
    }

    /**
     * 是否月末日期
     *
     * @param d
     * @return
     */
    public static boolean isMonthEnd(Date d) {
        return DateUtils.truncatedEquals(d, monthEnd(d), Calendar.DATE);
    }

    public static void main(String[] args) {
        System.out.println(isMonthEnd(parseDate("2024-06-29")));
    }

    public static String fmt(Date d) {
        if (d == null) {
            return "";
        }

        return DateFormatUtils.format(d, "yyyy-MM-dd HH:mm:ss");
    }

    public static String fmtDate(Date d) {
        return fmtDate(d, "");
    }

    public static String fmtDate(Date d, String nullStr) {
        if (d == null) {
            return nullStr;
        }

        return DateFormatUtils.format(d, "yyyy-MM-dd");
    }

    public static String fmtMonth(Date d) {
        if (d == null) {
            return "";
        }

        return DateFormatUtils.format(d, "yyyy-MM");
    }

    public static String fmtDate(Collection<Date> ds) {
        if (CollectionUtils.isEmpty(ds)) {
            return "";
        }
        List<String> ss = Lists.newArrayList();
        for (Date d : ds) {
            ss.add(fmtDate(d));
        }

        return String.join(",", ss);
    }

    public static String fmtTime(Date d) {
        if (d == null) {
            return "";
        }

        return DateFormatUtils.format(d, "HH:mm:ss");
    }

    /**
     * 获取 yyyyMM 数字
     * @return
     */
    public static int getTodayDt(){
        return Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMM"));
    }
}