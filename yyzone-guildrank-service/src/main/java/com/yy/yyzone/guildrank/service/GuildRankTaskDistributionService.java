package com.yy.yyzone.guildrank.service;

import com.yy.yyzone.guildrank.constant.GuildRankTaskConstant;
import com.yy.yyzone.guildrank.db.gen.mapper.GuildRankTaskDistributionMapper;
import com.yy.yyzone.guildrank.db.gen.model.GuildRankPlatformActivityTaskResult;
import com.yy.yyzone.guildrank.db.gen.model.GuildRankTaskDistribution;
import com.yy.yyzone.guildrank.db.gen.model.GuildRankTaskDistributionUidAndDt;
import com.yy.yyzone.guildrank.db.gen.model.GuildRankTaskRequirementsAndResult;
import com.yy.yyzone.guildrank.dto.guildranktaskconfig.GuildRankTaskDetailsDTO;
import com.yy.yyzone.guildrank.dto.guildranktaskconfig.*;
import com.yy.yyzone.guildrank.util.PageResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Valid;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
@Slf4j
@Service
public class GuildRankTaskDistributionService {
    @Autowired
    private GuildRankTaskDistributionMapper distributiontaskMapper;
    @Autowired
    private UserInfoService userInfoService;
    @Autowired
    private GuildPlatformActivityResultService guildPlatformActivityResultService;
    @Autowired
    private GuildRankTaskRequirementsAndResultService guildRankTaskRequirementsAndResultService;
    @Autowired
    private GuildRankTaskConfigService guildRankTaskConfigService;

    private static final DateTimeFormatter MONTH_FORMATTER_INT = DateTimeFormatter.ofPattern("yyyyMM");

    private static final List<String> VALID_OP_TYPES = Arrays.asList("add", "update", "delete");

    private static int currentMonthInt() {
        return Integer.parseInt(LocalDate.now().format(MONTH_FORMATTER_INT));
    }

    private Long getTaskValueByType(String taskType,GuildRankTaskRequirementsAndResult guildRankTaskRequirementsAndResult ){
        if(guildRankTaskRequirementsAndResult != null){
            if(GuildRankTaskConstant.ONLINE_BIND_3_AND_4_STAR.equals(taskType) && guildRankTaskRequirementsAndResult.getAvgNewAidRat3Or4AidNum3mAim() != null){
                return guildRankTaskRequirementsAndResult.getAvgNewAidRat3Or4AidNum3mAim().longValue();
            }
            if(GuildRankTaskConstant.NEW_HIGH_WAIST_ANCHOR.equals(taskType) && guildRankTaskRequirementsAndResult.getAvgUpgrdHighWaistAidNum3mAim() != null){
                return guildRankTaskRequirementsAndResult.getAvgUpgrdHighWaistAidNum3mAim().longValue();
            }
            if(GuildRankTaskConstant.NEW_ANCHOR_BLUE_DIAMOND.equals(taskType) && guildRankTaskRequirementsAndResult.getNewAidIncmComRateAim() != null){
                return guildRankTaskRequirementsAndResult.getNewAidIncmComRateAim().longValue();
            }
            if(GuildRankTaskConstant.GUILD_BLUE_DIAMOND.equals(taskType) && guildRankTaskRequirementsAndResult.getGuildTaskIncmLvlAim() != null){
                return guildRankTaskRequirementsAndResult.getGuildTaskIncmLvlAim().longValue();
            }
            if(GuildRankTaskConstant.MONTH_BLUE_INCOME.equals(taskType) && guildRankTaskRequirementsAndResult.getAccumIncmAmtAim() != null){
                return guildRankTaskRequirementsAndResult.getAccumIncmAmtAim();
            }
        }
        return null;
    }
    public GuildRankTaskDTO getGuildTasks(Long uid, Date yearMonth){
        return this.getGuildTasks(uid,Integer.parseInt(DateFormatUtils.format(yearMonth,"yyyyMM")));
    }
    public GuildRankTaskDTO getGuildTasks(Long uid, int dt) {
        Pair<GuildRankAllTaskResultDTO,List<GuildRankTaskDetailsDTO>>  tasksResultAndDetails =  getTaskInfo(uid,dt);
        GuildRankTaskDTO result = null;
        if(tasksResultAndDetails != null){
            GuildRankAllTaskConfig  guildRankAllTaskConfig = guildRankTaskConfigService.getGuildRankTaskConfigSnapshot(dt);
            Integer totalScoreLimit = guildRankAllTaskConfig != null ? guildRankAllTaskConfig.getTaskConfig().getTotalScoreLimit() : null;
            result = new GuildRankTaskDTO(dt,totalScoreLimit,tasksResultAndDetails.getRight());
        }
        log.info("get guild task distribution uid:{} dt:{} result: {}", uid, dt, result);
        return result;
    }

    private Pair<GuildRankAllTaskResultDTO,List<GuildRankTaskDetailsDTO>> getTaskInfo(long currentUid,int currentDt){
        GuildRankTaskRequirementsAndResult guildRankTaskRequirementsAndResult = guildRankTaskRequirementsAndResultService.getByUidAndDt(currentUid, currentDt);
        GuildRankPlatformActivityTaskResult guildRankPlatformActivityTaskResult = guildPlatformActivityResultService.getResultByUidAndDt(currentUid, currentDt);
        List<GuildRankTaskDistribution> tasks = distributiontaskMapper.findTasksByUidAndDt(currentUid, currentDt);
        log.info("getTaskInfo uid: {} dt: {} guildRankTaskRequirementsAndResult:{} tasks: {}", currentUid, currentDt,guildRankTaskRequirementsAndResult,tasks);
        //如果没有设置值，就要把bi的写进去
        for(GuildRankTaskDistribution task : tasks){
            if(task.getTargetValue() == null){
                task.setTargetValue(getTaskValueByType(task.getTaskType(),guildRankTaskRequirementsAndResult));
            }
        }
        GuildRankAllTaskResultDTO guildRankAllTaskResultDTO = GuildRankAllTaskResultDTO.build(guildRankTaskRequirementsAndResult,guildRankPlatformActivityTaskResult);
        List<GuildRankTaskDetailsDTO> guildRankTaskDistributionDTOS = new ArrayList<>(tasks.size());
        for (GuildRankTaskDistribution task : tasks) {
            guildRankTaskDistributionDTOS.add(new GuildRankTaskDetailsDTO(task.getTaskType(), task.getTargetValue(), guildRankAllTaskResultDTO));
        }
        return Pair.of(guildRankAllTaskResultDTO,guildRankTaskDistributionDTOS);
    }

    public PageResp<List<GuildRankTaskDistributionDetailsDTO>>  listTasks(List<Long> queryUids,List<Long> yys, Integer dtStart, Integer dtEnd, String taskType, int pageNum, int pageSize) {
        long offset = (pageNum - 1) * (long) pageSize;
        List<Long> queryAllUids;
        if(CollectionUtils.isNotEmpty(yys)){
            queryAllUids = new ArrayList<>(yys.size() + (queryUids != null ? queryUids.size() : 0));
            if(CollectionUtils.isNotEmpty(queryUids)){
                queryAllUids.addAll(queryUids);
            }
            queryAllUids.addAll(userInfoService.batchGetUidByYys(yys));
        }else{
            queryAllUids = queryUids;
        }
        if(StringUtils.isNotEmpty(taskType)){
            if (!GuildRankTaskConstant.ALL_TASK_TYPES.contains(taskType)) {
                throw new IllegalArgumentException("无效的任务类型: " + taskType);
            }
        }
        // 查询去重的 uid 和 dt
        List<GuildRankTaskDistributionUidAndDt> uidDtList = distributiontaskMapper.findDistinctUidAndDt(queryUids, dtStart, dtEnd, taskType, pageSize, offset);
        long total = distributiontaskMapper.countDistinctUidAndDt(queryUids, dtStart, dtEnd, taskType);
        if(CollectionUtils.isNotEmpty(uidDtList)){
            List<Long> uids = uidDtList.stream().map(GuildRankTaskDistributionUidAndDt::getUid).collect(Collectors.toList());
            Map<Long, Long> userAndYys = userInfoService.batchGetYyByUid(uids);
            Map<Long, String> uidAndGuildNames =  userInfoService.batchGetGuildName(uids);
            // 根据 uid 和 dt 查询所有非历史任务
            List<GuildRankTaskDistributionDetailsDTO> result = new ArrayList<>(uidDtList.size());
            for (GuildRankTaskDistributionUidAndDt uidDt : uidDtList) {
                Long currentUid = uidDt.getUid();
                Integer currentDt =uidDt.getDt();
                GuildRankTaskDistributionDetailsDTO dto = new GuildRankTaskDistributionDetailsDTO();
                dto.setUid(currentUid);
                dto.setYy(userAndYys.get(currentUid));
                dto.setGuildName(uidAndGuildNames.get(currentUid));
                dto.setDt(currentDt);
                Pair<GuildRankAllTaskResultDTO,List<GuildRankTaskDetailsDTO>> tasksResultAndDetails =  getTaskInfo(currentUid,currentDt);
                dto.setAllTaskResult(tasksResultAndDetails.getLeft());
                dto.setAllTaskDetails(tasksResultAndDetails.getRight());
                result.add(dto);
            }
            return PageResp.createBySuccess(result,(int)total);
        }else{
            return PageResp.createBySuccess(Collections.emptyList(),(int)total);
        }
    }

    private static void checkDt(int dt){
        if(dt < currentMonthInt()){
            throw new IllegalArgumentException("dt只能是本月或者下一个月");
        }
    }

    public PageResp<List<GuildRankTaskDistributionHistoryDTO>>  listHistoryTasks(List<Long> uids, Integer dtStart, Integer dtEnd,
                                                                            String task, String opType,String operator, int pageNum, int pageSize) {
        if (opType != null && !VALID_OP_TYPES.contains(opType)) {
            throw new IllegalArgumentException("无效的操作类型: " + opType);
        }
        long offset = (pageNum - 1) * (long) pageSize;
        List<GuildRankTaskDistribution> tasks = distributiontaskMapper.findHistoryTasks(uids, dtStart, dtEnd, task, opType,operator, pageSize, offset);
        long total = distributiontaskMapper.countHistoryTasks(uids, dtStart, dtEnd, task, opType,operator);
        List<Long> previousIds = tasks.stream().map(GuildRankTaskDistribution::getPreviousId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, GuildRankTaskDistribution> previousIdToTaskMap = new HashMap<>();
        if(!previousIds.isEmpty()){
            List<GuildRankTaskDistribution> previousTasks = distributiontaskMapper.selectBatchIds(previousIds);
            for (GuildRankTaskDistribution previousTask : previousTasks) {
                previousIdToTaskMap.put(previousTask.getId(), previousTask);
            }
        }
        List<Long> guildUids = tasks.stream().map(GuildRankTaskDistribution::getUid).collect(Collectors.toList());
        Map<Long, String>   uidAndGuildNameMap = userInfoService.batchGetGuildName(guildUids);
        // 转换为历史记录 DTO
        List<GuildRankTaskDistributionHistoryDTO> historyTasks = tasks.stream().map(taskDistributionHistory -> {
            if(taskDistributionHistory.getPreviousId() != null){
                GuildRankTaskDistribution  previousTask = previousIdToTaskMap.get(taskDistributionHistory.getPreviousId());
                return GuildRankTaskDistributionHistoryDTO.build(uidAndGuildNameMap.get(taskDistributionHistory.getUid()),taskDistributionHistory,GuildRankTaskDistributionHistoryDTO.build(uidAndGuildNameMap.get(previousTask.getUid()),previousTask));
            }else{
                return GuildRankTaskDistributionHistoryDTO.build(uidAndGuildNameMap.get(taskDistributionHistory.getUid()),taskDistributionHistory);
            }
        }).collect(Collectors.toList());
        return PageResp.createBySuccess(historyTasks,(int)total);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void createTasks(@Valid GuildRankTaskDistributionCreateRequest request,String operator) {
        for (String taskType : request.getTasks()) {
            if (!GuildRankTaskConstant.ALL_TASK_TYPES.contains(taskType)) {
                throw new IllegalArgumentException("无效的任务类型: " + taskType);
            }
        }
        checkDt(request.getDt());
        Date now = new Date();
        for (Long uid : request.getUids()) {
            for (String taskType : request.getTasks()) {
                // 将已有记录标记为历史
                GuildRankTaskDistribution latestTask = distributiontaskMapper.findLatestByUidAndDtAndType(uid, request.getDt(), taskType);
                if(latestTask != null){
                    throw new RuntimeException("uid:"+uid +",task:"+ taskType+" ,已存在相同任务类型的记录,请进行修改");
                }
                // 创建新记录
                GuildRankTaskDistribution task = new GuildRankTaskDistribution();
                task.setUid(uid);
                task.setDt(request.getDt());
                task.setTaskType(taskType);
                task.setOperator(operator);
                task.setCreateTime(now);
                task.setUpdateTime(now);
                task.setHistory(false);
                task.setReason(request.getReason());
                task.setOpType("add");
                distributiontaskMapper.insert(task);
            }
        }
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void updateMultiTask(GuildRankTaskDistributionUpdateByOneUidRequest request, String operator){
        for(String task : request.getTaskAndTargetValues().keySet()){
            if (!GuildRankTaskConstant.ALL_TASK_TYPES.contains(task)) {
                throw new IllegalArgumentException("无效的任务: " + task);
            }
        }
        checkDt(request.getDt());
        for(Map.Entry<String,Long> taskAndTargetValue : request.getTaskAndTargetValues().entrySet()){
            updateTaskDistribution(request.getUid(),request.getDt(),taskAndTargetValue.getKey(),taskAndTargetValue.getValue(),operator);
        }
    }

    private void updateTaskDistribution(long uid,int dt,String taskType,long targetValue,String operator){
        GuildRankTaskDistribution latestTask = distributiontaskMapper.findLatestByUidAndDtAndType(uid, dt, taskType);
        // 将已有记录标记为历史
        distributiontaskMapper.markAsHistory(latestTask.getId());
        // 创建新记录
        GuildRankTaskDistribution task = new GuildRankTaskDistribution();
        task.setUid(uid);
        task.setDt(dt);
        task.setTaskType(taskType);
        task.setTargetValue(targetValue);
        task.setOperator(operator);
        task.setCreateTime(new Date());
        task.setUpdateTime(new Date());
        task.setHistory(false);
        task.setOpType("update");
        task.setPreviousId(latestTask.getId());
        distributiontaskMapper.insert(task);
    }


    @Transactional(rollbackFor = RuntimeException.class)
    public void updateBatchUidTask(GuildRankTaskDistributionBatchUpdateRequest request, String operator) {
        if (!GuildRankTaskConstant.ALL_TASK_TYPES.contains(request.getTask())) {
                throw new IllegalArgumentException("无效的任务: " + request.getTask());
        }
        checkDt(request.getDt());
        Date now = new Date();
        for(Long uid : request.getUids()){
            updateTaskDistribution(uid,request.getDt(),request.getTask(),request.getTargetValue(),operator);
        }
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteTask(GuildRankTaskDistributionDeleteRequest request, String operator) {
        if (!GuildRankTaskConstant.ALL_TASK_TYPES.contains(request.getTask())) {
            throw new IllegalArgumentException("无效的任务类型: " + request.getTask());
        }
        for(Long uid : request.getUids()){
            if (uid == null || uid <= 0) {
                throw new IllegalArgumentException("UID不能为空且必须为正数");
            }
        }
        checkDt(request.getDt());
        for(long uid : request.getUids()){
            GuildRankTaskDistribution task = distributiontaskMapper.findLatestByUidAndDtAndType(uid, request.getDt(), request.getTask());
            if (task != null) {
                // 标记为历史
                distributiontaskMapper.markAsHistory(task.getId());
                // 创建删除记录
                GuildRankTaskDistribution deleteTask = new GuildRankTaskDistribution();
                deleteTask.setUid(task.getUid());
                deleteTask.setDt(task.getDt());
                deleteTask.setTaskType(task.getTaskType());
                deleteTask.setTargetValue(task.getTargetValue());
                deleteTask.setOperator(operator);
                deleteTask.setCreateTime(new Date());
                deleteTask.setUpdateTime(new Date());
                deleteTask.setHistory(true);
                deleteTask.setOpType("delete");
                deleteTask.setReason(request.getReason());
                deleteTask.setPreviousId(task.getId());
                distributiontaskMapper.insert(deleteTask);
            }
        }
    }
}