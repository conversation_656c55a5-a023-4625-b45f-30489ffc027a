package com.yy.yyzone.guildrank.dto.guildranktaskconfig.valid;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = RangeListValidator.class)
public @interface ValidRanges {
    String message() default "范围必须完整、不重叠且 max ≥ min";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}