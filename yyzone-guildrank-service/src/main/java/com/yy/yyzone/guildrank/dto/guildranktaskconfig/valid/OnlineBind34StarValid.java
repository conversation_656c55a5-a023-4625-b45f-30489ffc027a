package com.yy.yyzone.guildrank.dto.guildranktaskconfig.valid;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Target(ElementType.FIELD)
@Retention(RUNTIME)
@Constraint(validatedBy = OnlineBind34StarValidator.class)
public @interface OnlineBind34StarValid {
    String message() default "onlineBind3And4Star 配置不合法";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}