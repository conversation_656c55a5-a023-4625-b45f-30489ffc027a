package com.yy.yyzone.guildrank.dto.renewartistdata;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class DataSaveReq {
    /**
     * id >0表示修改
     */
    private Long id;

    /**
     * 数据月
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyyMM")
    @NotNull(message = "数据月不能为空")
    private Date dataMonth;

    /**
     * 备注
     */
    private String remark;

    public boolean ifAdd() {
        return id == null || id <= 0;
    }
}