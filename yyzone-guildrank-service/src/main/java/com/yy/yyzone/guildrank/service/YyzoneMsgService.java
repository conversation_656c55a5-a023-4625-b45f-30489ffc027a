package com.yy.yyzone.guildrank.service;

import com.alibaba.fastjson.JSONObject;
import com.github.kevinsawicki.http.HttpRequest;
import com.yy.yyzone.guildrank.dto.SendMsgResult;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Generated;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * 系统消息
 *
 * <AUTHOR>
 * @date 2021/06/24
 **/
@Service
public class YyzoneMsgService {
    private static final Logger logger = LoggerFactory.getLogger(YyzoneMsgService.class);

    @Value("${msg.all.uri:}")
    private String sendMsgUri;

    @Value("${msg.all.path:}")
    private String sendMsgPath;

    public static final String MSG_APP_KEY = "yyzoneGuaranteed";
    public static final String MSG_APP_SIGN_SECRET = "alj:yzdoelvi";

    /**
     * yyzone发送系统消息
     *
     * @param uid    uid
     * @param msgTpl 模板id
     * @param data   json数据
     * @return bool
     */
    public boolean sendMsg(int msgTpl, long uid, String data) {
        try {
            String url = calGetUrl(uid, msgTpl, data);
            logger.info("sendMsg url:{}", url);
            String response = HttpRequest.get(url, false).body();
            logger.info("sendMsg resp:{}", response);
            if (StringUtils.isNotBlank(response)) {
                logger.info("sendMsg result:{}", response);
                return JSONObject.parseObject(response, SendMsgResult.class).getCode() == 0;
            }
        } catch (Exception e) {
            logger.error("sendMsg fail, uid:{}, msgTpl:{}, data:{}", uid, msgTpl, data, e);
        }
        return false;
    }

    private String calGetUrl(long uid, int msgTpl, String data) throws UnsupportedEncodingException {
        long timeMs = System.currentTimeMillis();
        String signParam = sendMsgPath + "&appId=" + MSG_APP_KEY + "&data=" + data + "&tplId=" + msgTpl + "&ts=" + timeMs + "&uid=" + uid;
        return sendMsgUri + sendMsgPath + "?uid=" + uid + "&tplId=" + msgTpl + "&data=" + URLEncoder.encode(data, "UTF-8") + "&appId="
                + MSG_APP_KEY + "&ts=" + timeMs + "&sign=" + culSign(signParam);
    }

    private String culSign(String param) {
        return DigestUtils.md5Hex(param + "&" + MSG_APP_SIGN_SECRET);
    }
}
