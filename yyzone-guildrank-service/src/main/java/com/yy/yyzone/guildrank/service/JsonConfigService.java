package com.yy.yyzone.guildrank.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yy.yyzone.guildrank.db.gen.mapper.JsonConfigSnapshotMapper;
import com.yy.yyzone.guildrank.db.gen.mapper.JsonConfigMapper;
import com.yy.yyzone.guildrank.db.gen.model.JsonConfig;
import com.yy.yyzone.guildrank.db.gen.model.JsonConfigSnapshot;
import com.yy.yyzone.guildrank.dto.guildranktaskconfig.ConfigHistory;
import com.yy.yyzone.guildrank.enums.JsonConfigNameEnum;
import com.yy.yyzone.guildrank.exceptions.VersionRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class JsonConfigService {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    private final JsonConfigMapper jsonConfigMapper;

    private final JsonConfigSnapshotMapper jsonConfigSnapshotMapper;

    public JsonConfigService(JsonConfigMapper jsonConfigMapper,JsonConfigSnapshotMapper jsonConfigSnapshotMapper) {
        this.jsonConfigMapper = jsonConfigMapper;
        this.jsonConfigSnapshotMapper = jsonConfigSnapshotMapper;
    }

    @PostConstruct
    public void init(){
        for(JsonConfigNameEnum jsonConfigNameEnum : JsonConfigNameEnum.values()){
            jsonConfigMapper.ignoreInsertOrUpdateConfigVersion(jsonConfigNameEnum.getName(),0);
        }
    }

    public JsonConfig getLatestConfigByName(JsonConfigNameEnum configName) {
        return jsonConfigMapper.getLatestConfigByName(configName.getName());
    }
    public <T> List<ConfigHistory<T>> getConfigHistoryByNameAndFilter(JsonConfigNameEnum configName, TypeReference<T> typeReference, Date startTime, Date endTime, String operator) {
        List<JsonConfig> configs = jsonConfigMapper.getConfigsByName(configName.getName(),startTime,endTime,operator);
        if(CollectionUtils.isEmpty(configs)){
            return Collections.emptyList();
        }
        return configs.stream().map(jsonConfig ->new ConfigHistory<T>(jsonConfig.getId(),getConfigFromJsonConfig(jsonConfig,typeReference),jsonConfig.getUpdateTime(),jsonConfig.getOperator()))
                .collect(Collectors.toList());
    }

    public <T> Pair<Long,T> getLatestConfigAndIdByName(JsonConfigNameEnum configName, TypeReference<T> typeReference) {
        JsonConfig jsonConfig = jsonConfigMapper.getLatestConfigByName(configName.getName());
        if (jsonConfig != null) {
            return Pair.of(jsonConfig.getId(),getConfigFromJsonConfig(jsonConfig,typeReference));
        }
        return Pair.of(0L,null);
    }

    public <T> T getLatestConfigSnapshot(int dt, JsonConfigNameEnum configName, TypeReference<T> typeReference) {
        int todayDt = Integer.parseInt(DateFormatUtils.format(new Date(),"yyyyMM"));
        if(todayDt == dt){
            return getLatestConfigAndIdByName(configName,typeReference).getRight();
        }else{
            JsonConfigSnapshot  jsonConfigSnapshot = jsonConfigSnapshotMapper.selectByDtAndConfigName(dt,configName.getName());
            if(jsonConfigSnapshot != null){
                JsonConfig jsonConfig = jsonConfigMapper.selectById(jsonConfigSnapshot.getConfigId());
                if(jsonConfig != null){
                    return getConfigFromJsonConfig(jsonConfig,typeReference);
                }
            }
            return null;
        }
    }


    private <T> T getConfigFromJsonConfig(JsonConfig jsonConfig, TypeReference<T> typeReference) {
        try {
            return OBJECT_MAPPER.readValue(jsonConfig.getConfig(), typeReference);
        }catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public JsonConfig addConfig(long oldVersion , Object config, JsonConfigNameEnum configName,String operator) {
        log.info("addConfig oldVersion:{} configName:{} config:{} operator:{}",oldVersion,configName,config,operator);
        try {
            JsonConfig jsonConfig = new JsonConfig();
            jsonConfig.setConfig(OBJECT_MAPPER.writeValueAsString(config));
            jsonConfig.setConfigName(configName.getName());
            jsonConfig.setOperator(operator);
            jsonConfigMapper.insertConfig(jsonConfig);
            if(jsonConfigMapper.updateConfigVersion(configName.getName(),jsonConfig.getId(),oldVersion) != 1) {
                log.info("addConfig fail");
                throw new VersionRuntimeException("版本号错误了,已经有人更新过了，请刷新");
            }
            log.info("addConfig success jsonConfig:{}",jsonConfig);
            return jsonConfig;
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public void saveSnapshot(){
        int dt = Integer.parseInt(DateFormatUtils.format(new Date(),"yyyyMM"));
        List<JsonConfigSnapshot> jsonConfigSnapshots = new ArrayList<>(JsonConfigNameEnum.values().length);
        for(JsonConfigNameEnum jsonConfigNameEnum : JsonConfigNameEnum.values()){
            JsonConfig jsonConfig = jsonConfigMapper.getLatestConfigByName(jsonConfigNameEnum.getName());
            JsonConfigSnapshot snapshot = new JsonConfigSnapshot();
            snapshot.setConfigId(jsonConfig.getId());
            snapshot.setConfigName(jsonConfigNameEnum.getName());
            snapshot.setDt(dt);
        }
        jsonConfigSnapshotMapper.batchInsert(jsonConfigSnapshots);
        log.info("saveSnapshot dt:{} jsonConfigs:{}",dt,jsonConfigSnapshots);
    }
}
