package com.yy.yyzone.guildrank.service;

import com.google.common.collect.Lists;
import com.yy.yyzone.guildrank.db.custom.mapper.CustomMapper;
import com.yy.yyzone.guildrank.db.gen.mapper.GuildrankRenewArtistCountMapper;
import com.yy.yyzone.guildrank.db.gen.mapper.GuildrankRenewArtistDataMapper;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankRenewArtistCount;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankRenewArtistCountExample;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankRenewArtistData;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankRenewArtistDataExample;
import com.yy.yyzone.guildrank.dto.renewartistdata.DataImportExcelRow;
import com.yy.yyzone.guildrank.dto.renewartistdata.DataListDto;
import com.yy.yyzone.guildrank.dto.renewartistdata.DataSaveReq;
import com.yy.yyzone.guildrank.util.MyDateUtil;
import com.yy.yyzone.guildrank.util.PageResp;
import com.yy.yyzone.guildrank.util.Resp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GuildRankRenewArtistDataService {
    @Autowired
    private GuildrankRenewArtistDataMapper renewArtistDataMapper;

    @Autowired
    private GuildrankRenewArtistCountMapper artistCountMapper;

    @Autowired
    private CustomMapper customMapper;

    public PageResp<List<DataListDto>> dataList(int pageNum, int pageSize) {
        GuildrankRenewArtistDataExample example = new GuildrankRenewArtistDataExample();
        int count = renewArtistDataMapper.countByExample(null);
        List<DataListDto> list = Lists.newArrayList();
        if (count > 0) {
            example.setOffset((pageNum - 1) * pageSize);
            example.setLimit(pageSize);
            example.setOrderByClause("data_month desc");
            list = renewArtistDataMapper.selectByExample(example).stream().map(l -> {
                DataListDto dto = new DataListDto();
                BeanUtils.copyProperties(l, dto);
                return dto;
            }).collect(Collectors.toList());
        }
        return PageResp.createBySuccess(list, count);
    }

    public List<GuildrankRenewArtistCount> getRenewArtistCount(Date month) {
        GuildrankRenewArtistCountExample example = new GuildrankRenewArtistCountExample();
        example.createCriteria().andDataMonthEqualTo(month);
        return artistCountMapper.selectByExample(example);
    }

    @Transactional(rollbackFor = {RuntimeException.class, Exception.class})
    public Resp saveData(DataSaveReq req, List<DataImportExcelRow> rows, String passport) {
        if (CollectionUtils.isEmpty(rows)) {
            return Resp.createByError("名单不能为空");
        }

        if (stopEdit(req.getDataMonth())) {
            return Resp.createByError("月末倒数第二日23:00:00后名单锁定，不可再编辑");
        }

        Date now = new Date();
        GuildrankRenewArtistData newData = new GuildrankRenewArtistData();
        newData.setRemark(req.getRemark());
        newData.setUpdatePassport(passport);
        newData.setUpdateTime(now);
        if (req.ifAdd()) {
            if (isDataExists(req.getDataMonth())) {
                return Resp.createByError("数据月已存在");
            }
            newData.setDataMonth(req.getDataMonth());
            renewArtistDataMapper.insertSelective(newData);
        } else {
            GuildrankRenewArtistData dbData = renewArtistDataMapper.selectByPrimaryKey(req.getId());
            if (dbData == null) {
                return Resp.createByError("数据不存在");
            }
            if (!dbData.getDataMonth().equals(req.getDataMonth())) {
                return Resp.createByError("不能修改数据月");
            }
            newData.setId(req.getId());
            renewArtistDataMapper.updateByPrimaryKeySelective(newData);
        }

        GuildrankRenewArtistCountExample example = new GuildrankRenewArtistCountExample();
        example.createCriteria().andDataMonthEqualTo(req.getDataMonth());
        int count = artistCountMapper.deleteByExample(example);
        log.info("saveData del GuildrankRenewArtistCount count:{}", count);

        List<GuildrankRenewArtistCount> counts = new ArrayList<>(rows.size());
        Set<Long> uids = new HashSet<>(rows.size());
        Set<Long> dupUids = new HashSet<>();
        for (DataImportExcelRow r : rows) {
            GuildrankRenewArtistCount c = new GuildrankRenewArtistCount();
            c.setDataMonth(req.getDataMonth());
            c.setMainGuildUid(r.getMainGuildUid());
            c.setRenewCount(r.getRenewCount());
            if (uids.contains(r.getMainGuildUid())) {
                dupUids.add(r.getMainGuildUid());
            }
            uids.add(r.getMainGuildUid());
            counts.add(c);
        }
        if (CollectionUtils.isNotEmpty(dupUids)) {
            return Resp.createByError("以下主体UID重复：" + StringUtils.join(dupUids, "、"));
        }

        int batchSize = 200;
        List<List<GuildrankRenewArtistCount>> lists = Lists.partition(counts, batchSize);
        int insertCount = 0;
        for (List<GuildrankRenewArtistCount> l : lists) {
            insertCount += customMapper.insertBatchRenewArtistCount(l);
        }
        log.info("saveData insertBatchRenewArtistCount count:{}", insertCount);

        return Resp.createBySuccess();
    }

    public boolean isDataExists(Date month) {
        GuildrankRenewArtistDataExample example = new GuildrankRenewArtistDataExample();
        example.createCriteria().andDataMonthEqualTo(month);
        example.setLimit(1);
        List<GuildrankRenewArtistData> list = renewArtistDataMapper.selectByExample(example);
        return !list.isEmpty();
    }

    private boolean stopEdit(Date month) {
        if (DateUtils.truncatedCompareTo(new Date(), month, Calendar.MONTH) > 0) {
            return true;
        }

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime max = now.with(TemporalAdjusters.lastDayOfMonth()).minusDays(1)
                .withHour(23).withMinute(0).withSecond(0).withNano(0);
        return !now.isBefore(max);
    }
}
