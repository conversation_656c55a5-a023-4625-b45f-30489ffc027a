package com.yy.yyzone.guildrank.dto.guildranktaskconfig.valid;
import com.yy.yyzone.guildrank.dto.guildranktaskconfig.*;
import org.apache.commons.collections.CollectionUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.List;
public class NewAnchorBlueDiamondValidator
        implements ConstraintValidator<NewAnchorBlueDiamondValid, GuildRankTask> {

    @Override
    public boolean isValid(GuildRankTask task, ConstraintValidatorContext ctx) {
        if (task == null) {
            return false;
        };
        List<Range> ranges = task.getCondition().getRanges();
        if(CollectionUtils.isEmpty(ranges)){
            return fail(ctx, "必须配置 ranges");
        }
        for(Range range : ranges){
            if (range.getScore() == null){
                return fail(ctx, "必须配置 condition.ranges.score");
            }
            if(CollectionUtils.isEmpty(range.getScore().getRangeIndicators())){
                return fail(ctx, "必须配置 condition.ranges.score.rangeIndicators");
            }
            if (range.getScore().getIndicators() != null) {
                return fail(ctx, "不允许配置 condition.ranges.score.indicators");
            }
            for(RangeIndicator rangeIndicator : range.getScore().getRangeIndicators()){
                if(!rangeIndicator.getIndicator().equals(IndicatorEnum.newAnchorBlueDiamondGuildLastMonthBlueDiamondTaskCompletionRate)){
                    return fail(ctx, "range.score.rangeIndicators.indicator 必须是 newAnchorBlueDiamondGuildLastMonthBlueDiamondTaskCompletionRate");
                }
            }
        }
        // score.indicators 必须为空
        if (task.getScore().getIndicators() != null && !task.getScore().getIndicators().isEmpty()) {
            return fail(ctx, "不允许配置 indicators");
        }
        return true;
    }

    private boolean fail(ConstraintValidatorContext ctx, String msg) {
        ctx.disableDefaultConstraintViolation();
        ctx.buildConstraintViolationWithTemplate("新主播蓝钻任务 " + msg).addConstraintViolation();
        return false;
    }
}