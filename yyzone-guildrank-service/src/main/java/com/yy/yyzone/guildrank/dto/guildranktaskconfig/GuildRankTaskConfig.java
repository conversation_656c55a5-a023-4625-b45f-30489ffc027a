package com.yy.yyzone.guildrank.dto.guildranktaskconfig;

import com.yy.yyzone.guildrank.dto.guildranktaskconfig.valid.*;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
public class GuildRankTaskConfig {
    /**
     * 总加分上限
     */
    @NotNull
    @Min(1)
    private Integer totalScoreLimit;
    /**
     * 线上绑定3&4星主播数量
     */
    @NotNull
    @Valid
    @OnlineBind34StarValid
    private GuildRankTask onlineBind3And4Star;
    /**
     * 新增高优&腰部主播
     */
    @NotNull
    @Valid
    @NewHighWaistAnchorValid
    private GuildRankTask newHighWaistAnchor;
    /**
     * 新主播蓝钻任务
     */
    @NotNull
    @Valid
    @NewAnchorBlueDiamondValid
    private GuildRankTask newAnchorBlueDiamond;
    /**
     * 公会蓝钻任务
     */
    @NotNull
    @Valid
    @GuildBlueDiamondValid
    private GuildRankTask guildBlueDiamond;
    /**
     * 自然月月累积蓝钻收入
     */
    @Valid
    @MonthBlueIncomeValid
    private GuildRankTask monthBlueIncome;
}
