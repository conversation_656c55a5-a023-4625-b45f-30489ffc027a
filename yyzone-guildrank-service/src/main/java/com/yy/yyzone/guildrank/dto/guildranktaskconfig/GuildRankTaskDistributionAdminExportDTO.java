package com.yy.yyzone.guildrank.dto.guildranktaskconfig;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class GuildRankTaskDistributionAdminExportDTO {
    @ExcelProperty("月份")
    private String month;
    @ExcelProperty("主体yy")
    private Long yy;
    @ExcelProperty("主体UID")
    private Long uid;
    @ExcelProperty("公会名")
    private String guildName;
    @ExcelProperty("主体名称")
    private String entreMaskName;
    @ExcelProperty("线上绑定3&4星主播任务详情")
    private String onlineBind3And4StarTaskDetail;
    @ExcelProperty("线上绑定3&4星主播任务完成情况")
    private String onlineBind3And4StarCompletion;
    @ExcelProperty("线上绑定3星主播情况")
    private String onlineBind3StarCompletion;
    @ExcelProperty("线上绑定4星主播情况")
    private String onlineBind4StarCompletion;
    @ExcelProperty("线上绑定3&4星主播任务得分")
    private Integer onlineBind3And4StarScore;

    @ExcelProperty("新增高优&腰部主播任务详情")
    private String newHighWaistAnchorTaskDetail;
    @ExcelProperty("新增高优&腰部主播任务完成情况")
    private String newHighWaistAnchorCompletion;
    @ExcelProperty("新增高优主播情况")
    private String newHighAnchorCompletion;
    @ExcelProperty("新增腰部主播情况")
    private String newWaistAnchorCompletion;

    @ExcelProperty("新增高优&腰部主播任务得分")
    private Integer newHighWaistAnchorScore;

    @ExcelProperty("新主播蓝钻任务详情")
    private String newAnchorBlueDiamondTaskDetail;
    @ExcelProperty("新主播蓝钻任务完成情况")
    private String newAnchorBlueDiamondCompletion;
    @ExcelProperty("新主播蓝钻任务得分")
    private Integer newAnchorBlueDiamondScore;

    @ExcelProperty("公会蓝钻任务详情")
    private String guildBlueDiamondTaskDetail;
    @ExcelProperty("公会蓝钻任务完成情况")
    private String guildBlueDiamondCompletion;
    @ExcelProperty("公会蓝钻任务得分")
    private Integer guildBlueDiamondScore;

    @ExcelProperty("自然月累计蓝钻收入任务详情")
    private String monthBlueIncomeTaskDetail;
    @ExcelProperty("自然月累计蓝钻收入任务完成情况")
    private String monthBlueIncomeCompletion;
    @ExcelProperty("自然月累计蓝钻收入任务得分")
    private Integer monthBlueIncomeScore;
    /**
     * 春季头条任务详情
     */
    @ExcelProperty("春季头条任务详情")
    private String springHeadlineTaskDetail;

    /**
     * 春季头条完成情况
     */
    @ExcelProperty("春季头条完成情况")
    private String springHeadlineCompletion;

    /**
     * 春季头条得分
     */
    @ExcelProperty("春季头条得分")
    private Integer springHeadlineScore;

    /**
     * 夏季头条任务详情
     */
    @ExcelProperty("夏季头条任务详情")
    private String summerHeadlineTaskDetail;

    /**
     * 夏季头条完成情况
     */
    @ExcelProperty("夏季头条完成情况")
    private String summerHeadlineCompletion;

    /**
     * 夏季头条得分
     */
    @ExcelProperty("夏季头条得分")
    private Integer summerHeadlineScore;

    /**
     * 公会赛S1任务详情
     */
    @ExcelProperty("公会赛S1任务详情")
    private String guildRaceS1TaskDetail;

    /**
     * 公会赛S1完成情况
     */
    @ExcelProperty("公会赛S1完成情况")
    private String guildRaceS1Completion;

    /**
     * 公会赛S1得分
     */
    @ExcelProperty("公会赛S1得分")
    private Integer guildRaceS1Score;

    /**
     * 公会赛S2任务详情
     */
    @ExcelProperty("公会赛S2任务详情")
    private String guildRaceS2TaskDetail;

    /**
     * 公会赛S2完成情况
     */
    @ExcelProperty("公会赛S2完成情况")
    private String guildRaceS2Completion;

    /**
     * 公会赛S2得分
     */
    @ExcelProperty("公会赛S2得分")
    private Integer guildRaceS2Score;

    /**
     * 公会赛正赛任务详情
     */
    @ExcelProperty("公会赛正赛任务详情")
    private String guildRaceMainTaskDetail;

    /**
     * 公会赛正赛完成情况
     */
    @ExcelProperty("公会赛正赛完成情况")
    private String guildRaceMainCompletion;

    /**
     * 公会赛正赛得分
     */
    @ExcelProperty("公会赛正赛得分")
    private Integer guildRaceMainScore;
    /**
     * 个人赛正赛任务详情
     */
    @ExcelProperty("个人赛正赛任务详情")
    private String personalRaceMainTaskDetail;

    /**
     * 个人赛正赛完成情况
     */
    @ExcelProperty("个人赛正赛完成情况")
    private String personalRaceMainCompletion;

    /**
     * 个人赛正赛得分
     */
    @ExcelProperty("个人赛正赛得分")
    private Integer personalRaceMainScore;

    public static GuildRankTaskDistributionAdminExportDTO build(GuildRankTaskDistributionDetailsDTO guildRankTaskDistributionDTO) {
        GuildRankTaskDistributionAdminExportDTO exportDTO = new GuildRankTaskDistributionAdminExportDTO();
        exportDTO.setMonth(guildRankTaskDistributionDTO.getDt().toString());
        exportDTO.setYy(guildRankTaskDistributionDTO.getYy());
        exportDTO.setUid(guildRankTaskDistributionDTO.getUid());
        exportDTO.setGuildName(guildRankTaskDistributionDTO.getGuildName());
        if (guildRankTaskDistributionDTO.getAllTaskResult() != null) {
            exportDTO.setEntreMaskName(guildRankTaskDistributionDTO.getAllTaskResult().getGuildCmpName());
            exportDTO.setOnlineBind3StarCompletion(guildRankTaskDistributionDTO.getAllTaskResult().getOnlineBind3StarCompletion());
            exportDTO.setOnlineBind4StarCompletion(guildRankTaskDistributionDTO.getAllTaskResult().getOnlineBind4StarCompletion());
            exportDTO.setNewHighAnchorCompletion(guildRankTaskDistributionDTO.getAllTaskResult().getNewHighAnchorCompletion());
            exportDTO.setNewWaistAnchorCompletion(guildRankTaskDistributionDTO.getAllTaskResult().getNewWaistAnchorCompletion());
            exportDTO.setOnlineBind3And4StarTaskDetail(guildRankTaskDistributionDTO.getAllTaskResult().getOnlineBind3And4StarTaskDetail());
            exportDTO.setOnlineBind3And4StarCompletion(guildRankTaskDistributionDTO.getAllTaskResult().getOnlineBind3And4StarCompletionForAdmin());
            exportDTO.setOnlineBind3And4StarScore(guildRankTaskDistributionDTO.getAllTaskResult().getOnlineBind3And4StarScore());
            exportDTO.setNewHighWaistAnchorTaskDetail(guildRankTaskDistributionDTO.getAllTaskResult().getNewHighWaistAnchorTaskDetail());
            exportDTO.setNewHighWaistAnchorCompletion(guildRankTaskDistributionDTO.getAllTaskResult().getNewHighWaistAnchorCompletionForAdmin());
            exportDTO.setNewHighWaistAnchorScore(guildRankTaskDistributionDTO.getAllTaskResult().getNewHighWaistAnchorScore());
            exportDTO.setNewAnchorBlueDiamondTaskDetail(guildRankTaskDistributionDTO.getAllTaskResult().getNewAnchorBlueDiamondTaskDetail());
            exportDTO.setNewAnchorBlueDiamondCompletion(guildRankTaskDistributionDTO.getAllTaskResult().getNewAnchorBlueDiamondCompletion());
            exportDTO.setNewAnchorBlueDiamondScore(guildRankTaskDistributionDTO.getAllTaskResult().getNewAnchorBlueDiamondScore());
            exportDTO.setGuildBlueDiamondTaskDetail(guildRankTaskDistributionDTO.getAllTaskResult().getGuildBlueDiamondTaskDetail());
            exportDTO.setGuildBlueDiamondCompletion(guildRankTaskDistributionDTO.getAllTaskResult().getGuildBlueDiamondCompletion());
            exportDTO.setGuildBlueDiamondScore(guildRankTaskDistributionDTO.getAllTaskResult().getGuildBlueDiamondScore());
            exportDTO.setMonthBlueIncomeTaskDetail(guildRankTaskDistributionDTO.getAllTaskResult().getMonthBlueIncomeTaskDetail());
            exportDTO.setMonthBlueIncomeCompletion(guildRankTaskDistributionDTO.getAllTaskResult().getMonthBlueIncomeCompletion());
            exportDTO.setMonthBlueIncomeScore(guildRankTaskDistributionDTO.getAllTaskResult().getMonthBlueIncomeScore());
            exportDTO.setSpringHeadlineTaskDetail(guildRankTaskDistributionDTO.getAllTaskResult().getSpringHeadlineTaskDetail());
            exportDTO.setSpringHeadlineCompletion(guildRankTaskDistributionDTO.getAllTaskResult().getSpringHeadlineCompletion());
            exportDTO.setSpringHeadlineScore(guildRankTaskDistributionDTO.getAllTaskResult().getSpringHeadlineScore());
            exportDTO.setSummerHeadlineTaskDetail(guildRankTaskDistributionDTO.getAllTaskResult().getSummerHeadlineTaskDetail());
            exportDTO.setSummerHeadlineCompletion(guildRankTaskDistributionDTO.getAllTaskResult().getSummerHeadlineCompletion());
            exportDTO.setSummerHeadlineScore(guildRankTaskDistributionDTO.getAllTaskResult().getSummerHeadlineScore());
            exportDTO.setGuildRaceS1TaskDetail(guildRankTaskDistributionDTO.getAllTaskResult().getGuildRaceS1TaskDetail());
            exportDTO.setGuildRaceS1Completion(guildRankTaskDistributionDTO.getAllTaskResult().getGuildRaceS1Completion());
            exportDTO.setGuildRaceS1Score(guildRankTaskDistributionDTO.getAllTaskResult().getGuildRaceS1Score());
            exportDTO.setGuildRaceS2TaskDetail(guildRankTaskDistributionDTO.getAllTaskResult().getGuildRaceS2TaskDetail());
            exportDTO.setGuildRaceS2Completion(guildRankTaskDistributionDTO.getAllTaskResult().getGuildRaceS2Completion());
            exportDTO.setGuildRaceS2Score(guildRankTaskDistributionDTO.getAllTaskResult().getGuildRaceS2Score());
            exportDTO.setGuildRaceMainTaskDetail(guildRankTaskDistributionDTO.getAllTaskResult().getGuildRaceMainTaskDetail());
            exportDTO.setGuildRaceMainCompletion(guildRankTaskDistributionDTO.getAllTaskResult().getGuildRaceMainCompletion());
            exportDTO.setGuildRaceMainScore(guildRankTaskDistributionDTO.getAllTaskResult().getGuildRaceMainScore());
            exportDTO.setPersonalRaceMainTaskDetail(guildRankTaskDistributionDTO.getAllTaskResult().getPersonalRaceMainTaskDetail());
            exportDTO.setPersonalRaceMainCompletion(guildRankTaskDistributionDTO.getAllTaskResult().getPersonalRaceMainCompletion());
            exportDTO.setPersonalRaceMainScore(guildRankTaskDistributionDTO.getAllTaskResult().getPersonalRaceMainScore());
        }
        return exportDTO;
    }

}
