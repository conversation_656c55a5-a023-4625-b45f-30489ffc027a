package com.yy.yyzone.guildrank.yrpc.impl;

import com.google.common.collect.Maps;
import com.yy.yyzone.guildrank.api.GuildRank.QueryGuildRankReq;
import com.yy.yyzone.guildrank.api.GuildRank.QueryGuildRankResp;
import com.yy.yyzone.guildrank.api.GuildRank.updateFlagReq;
import com.yy.yyzone.guildrank.api.GuildRank.updateFlagResp;
import com.yy.yyzone.guildrank.api.GuildrankService;
import com.yy.yyzone.guildrank.api.RankChange;
import com.yy.yyzone.guildrank.db.gen.mapper.DmYyGuildSegmentScoreMonthMapper;
import com.yy.yyzone.guildrank.db.gen.mapper.DmYySidSegmentScoreMonthMapper;
import com.yy.yyzone.guildrank.db.gen.model.DmYyGuildSegmentScoreMonth;
import com.yy.yyzone.guildrank.db.gen.model.DmYyGuildSegmentScoreMonthExample;
import com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreMonth;
import com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreMonthExample;
import com.yy.yyzone.guildrank.repository.GuildRankRepository;
import com.yy.yyzone.guildrank.service.*;
import com.yy.yyzone.guildrank.util.MyDateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Generated;
import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Generated("忽略代码规范检查")
@Service(protocol = "yrpc",registry = "reg-yrpc")
public class GuildrankServiceImpl implements GuildrankService {

	private static Logger logger = LoggerFactory.getLogger(GuildrankServiceImpl.class);

	@Autowired
	private DmYySidSegmentScoreMonthMapper dmYySidSegmentScoreMonthMapper;

	@Autowired
	private GuildRankService guildRankService;

	@Autowired
	private GuildRankV2Service guildRankV2Service;

	@Autowired
	private DmYyGuildSegmentScoreMonthMapper dmYyGuildSegmentScoreMonthMapper;

    @Autowired
    private GuildRankTransService transService;

	@Override
	public QueryGuildRankResp queryGuildRank(QueryGuildRankReq req ){
        if(transService.useV3Data(req.getMonth()) && transService.useV3("GuildrankServiceImpl.queryGuildRank")) {
            return queryGuildRankV3(req, true);
        }

		logger.info(req.toString());
		String month = req.getMonth();
		if (!guildRankV2Service.hasUpdate()){// 还没更新查上个月数据
			try {
				month = DateFormatUtils.format(DateUtils.addMonths(DateUtils.parseDate(month,"yyyy-MM"), -1),"yyyy-MM");
			} catch (ParseException e) {
				logger.error(e.getMessage(),e);
			}
		}
		month = month + "-01";
		List<Long> guildUid = req.getOwUidsList();
		if (guildUid == null || guildUid.size()==0){
			return QueryGuildRankResp.newBuilder().setResult(1).build();
		}
		if (guildUid.size() > 100){
			return QueryGuildRankResp.newBuilder().setResult(2).build();
		}
//		DmYySidSegmentScoreMonthExample example = new DmYySidSegmentScoreMonthExample();
//		example.createCriteria().andSidOwneridIn(guildUid).andDtEqualTo(month);
//		example.setOrderByClause(" id ");
//		List<DmYySidSegmentScoreMonth> list = dmYySidSegmentScoreMonthMapper.selectByExample(example);
		DmYyGuildSegmentScoreMonthExample example = new DmYyGuildSegmentScoreMonthExample();
		example.createCriteria().andSidOwneridIn(guildUid).andDtEqualTo(month);
		example.setOrderByClause(" id ");
		List<DmYyGuildSegmentScoreMonth> list = dmYyGuildSegmentScoreMonthMapper.selectByExample(example);

		//查3月份数据要和2月旧数据对比
		Map<Long, Integer> oldLevel = Maps.newHashMap();
		if ("2020-03-01".equals(month)){
			DmYySidSegmentScoreMonthExample dmYySidSegmentScoreMonthExample = new DmYySidSegmentScoreMonthExample();
			dmYySidSegmentScoreMonthExample.createCriteria().andSidOwneridIn(guildUid).andDtEqualTo("2020-02-01");
			dmYySidSegmentScoreMonthExample.setOrderByClause(" id ");
			List<DmYySidSegmentScoreMonth> dmYySidSegmentScoreMonths = dmYySidSegmentScoreMonthMapper.selectByExample(dmYySidSegmentScoreMonthExample);
			for (DmYySidSegmentScoreMonth dmYySidSegmentScoreMonth : dmYySidSegmentScoreMonths) {
				oldLevel.put(dmYySidSegmentScoreMonth.getSidOwnerid(), dmYySidSegmentScoreMonth.getPsSAllCor().intValue());
			}
		}
		Map<Long, Integer> levelMap = Maps.newHashMap();
		Map<Long, Integer> changeMap = Maps.newHashMap();
		if (CollectionUtils.isNotEmpty(list)){
			for (DmYyGuildSegmentScoreMonth item : list) {
				Integer level = item.getPsSAll();
				Integer change = item.getPsSAllRr();
				if (change == null){
					change = level;
				}
				if ("2020-03-01".equals(month)){
					Integer old = oldLevel.get(item.getSidOwnerid());
					if (old!=null){
						change = level - old;
					}else {
						change = level;
					}
				}
				levelMap.put(item.getSidOwnerid(), level == null? 0:level.intValue());
				changeMap.put(item.getSidOwnerid(), change == null? 0:change.intValue());
			}
		}
		return QueryGuildRankResp.newBuilder().setResult(0).putAllUidLevel(levelMap)
				.putAllUidChange(changeMap).build();
	}

    @Override
    public QueryGuildRankResp queryGuildRankV2(QueryGuildRankReq req ){
        return queryGuildRankV3(req, false);
    }

	@Override
	public updateFlagResp queryHasUpdate(updateFlagReq req){
        if(transService.useV3("GuildrankServiceImpl.queryHasUpdate")) {
            return queryHasUpdateV3(req);
        }

		if (guildRankV2Service.hasUpdate()){
			return updateFlagResp.newBuilder().setResult(1).build();
		}
		return updateFlagResp.newBuilder().setResult(0).build();
	}

	@Override
	public RankChange.QueryRankChangeRes queryRankChange(RankChange.QueryRankChangeReq req) {
        if (transService.useV3Data(req.getMonth()) && transService.useV3("GuildrankServiceImpl.queryRankChange")) {
            return queryRankChangeV3(req);
        }

		logger.info("queryRankChange, req:{}",req);
		String month = req.getMonth();
		if (MyDateUtil.getCurrentMonthStr().equals(month) && !guildRankV2Service.hasUpdate()){
			return RankChange.QueryRankChangeRes.newBuilder().setResult(1).build();
		}

		DmYyGuildSegmentScoreMonthExample example = new DmYyGuildSegmentScoreMonthExample();
		example.createCriteria().andDtEqualTo(month + "-01");
		example.setOrderByClause(" id asc ");
		List<DmYyGuildSegmentScoreMonth> list = dmYyGuildSegmentScoreMonthMapper.selectByExample(example);
		if (CollectionUtils.isEmpty(list)){
			return RankChange.QueryRankChangeRes.newBuilder().setResult(1).build();
		}

		Map<Long, Integer> levelMap = Maps.newHashMapWithExpectedSize(list.size());
		Map<Long, Integer> changeMap = Maps.newHashMapWithExpectedSize(list.size());
		list.forEach(item -> {
			levelMap.put(item.getSidOwnerid(), item.getPsSAll());
			if (req.getType() == 1) {
				changeMap.put(item.getSidOwnerid(), item.getPsSAllRr() == null ? item.getPsSAll() - 1 : item.getPsSAllRr());
			} else {
				changeMap.put(item.getSidOwnerid(), item.getPsSAllRr() == null ? item.getPsSAll() : item.getPsSAllRr());
			}
		});
		return RankChange.QueryRankChangeRes.newBuilder().setResult(0).putAllRankMap(levelMap).putAllChangeMap(changeMap).build();
	}

    @Autowired
    private GuildRankRepository guildRankRepository;
    @Autowired
    private RankCacheV3Service rankCacheV3Service;

    public QueryGuildRankResp queryGuildRankV3(QueryGuildRankReq req, boolean useCache) {
        logger.info("queryGuildRankV3 req:{},useCache:{}", req, useCache);
        Date month = MyDateUtil.parseMonth(req.getMonth());
        // 还没更新查上个月数据（这个逻辑有点怪，但老版本这样写，这里也这样写）
        if (!rankCacheV3Service.hasUpdate()) {
            month = DateUtils.addMonths(month, -1);
        }

        List<Long> guildUid = req.getOwUidsList();
        if (guildUid == null || guildUid.size() == 0) {
            return QueryGuildRankResp.newBuilder().setResult(1).build();
        }
        if (guildUid.size() > 100) {
            return QueryGuildRankResp.newBuilder().setResult(2).build();
        }
        Map<Long, Integer> map = DateUtils.truncatedEquals(rankCacheV3Service.getRankCurrentMonth(), month, Calendar.MONTH)
                ? (useCache
                ? rankCacheV3Service.batchQueryCurrentRank(guildUid)
                : rankCacheV3Service.batchQueryCurrentRankNoCache(guildUid))
                : guildRankRepository.getRankFromHistory(guildUid, month);
        Date lastMonth = DateUtils.addMonths(month, -1);
        Map<Long, Integer> lastMap = guildRankRepository.getRankFromHistory(guildUid, lastMonth);

        Map<Long, Integer> changeMap = Maps.newHashMap();
        for (Map.Entry<Long, Integer> e : map.entrySet()) {
            Integer lastLevel = lastMap.get(e.getKey());
            Integer change = e.getValue() - (lastLevel == null ? 0 : lastLevel);
            changeMap.put(e.getKey(), change);
        }

        logger.info("queryGuildRankV3 lastMonth:{},map:{},changeMap:{}", MyDateUtil.fmtDate(lastMonth), map, changeMap);
        return QueryGuildRankResp.newBuilder().setResult(0).putAllUidLevel(map)
                .putAllUidChange(changeMap).build();
    }

    public updateFlagResp queryHasUpdateV3(updateFlagReq req) {
        if (rankCacheV3Service.hasUpdate()) {
            return updateFlagResp.newBuilder().setResult(1).build();
        }
        return updateFlagResp.newBuilder().setResult(0).build();
    }

    public RankChange.QueryRankChangeRes queryRankChangeV3(RankChange.QueryRankChangeReq req) {
        logger.info("queryRankChangeV3 req:{}", req);
        Date month = MyDateUtil.parseMonth(req.getMonth());
        boolean flag = rankCacheV3Service.hasUpdate();
        if (MyDateUtil.lastMonth().equals(month) && !flag) {
            return RankChange.QueryRankChangeRes.newBuilder().setResult(1).build();
        }
        Map<Long, Integer> levelMap = DateUtils.truncatedEquals(rankCacheV3Service.getRankCurrentMonth(), month, Calendar.MONTH)
                ? rankCacheV3Service.allRank()
                : guildRankRepository.getAllRankFromHistory(month);
        if (MapUtils.isEmpty(levelMap)) {
            return RankChange.QueryRankChangeRes.newBuilder().setResult(1).build();
        }

        Date lastMonth = DateUtils.addMonths(month, -1);
        logger.info("queryRankChangeV3 month:{},lastMonth:{}", month, MyDateUtil.fmtDate(lastMonth));
        Map<Long, Integer> lastMap = guildRankRepository.getAllRankFromHistory(lastMonth);

        Map<Long, Integer> changeMap = Maps.newHashMapWithExpectedSize(levelMap.size());
        boolean isTransMonth = transService.isTransMonth(month);
        for (Map.Entry<Long, Integer> e : levelMap.entrySet()) {
            Integer currentLevel = e.getValue();
            Integer lastLevel = lastMap.get(e.getKey());
            if (isTransMonth) {
                // 改版月默认返回无历史等级
                lastLevel = 0;
            }
            Integer change = lastLevel == null ? null : (currentLevel - lastLevel);

            if (req.getType() == 1) {
                changeMap.put(e.getKey(), change == null ? currentLevel - 1 : change);
            } else {
                changeMap.put(e.getKey(), change == null ? currentLevel: change);
            }
        }

        return RankChange.QueryRankChangeRes.newBuilder().setResult(0).putAllRankMap(levelMap).putAllChangeMap(changeMap).build();
    }
}
