package com.yy.yyzone.guildrank.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yy.yyzone.guildrank.db.gen.model.YyDmEntityGuildCmpHealthAnalysisExptDi;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.util.Date;

@Data
@NoArgsConstructor
public class YyDmEntityGuildCmpHealthAnalysisExptDiDTO {
    /**
     * new_aid_rat_3_4_aid_num_rn_next_score 3&4星主播签约数量排名下一等级分值
     */
    private Integer newAidRat34AidNumRnNextScore;

    /**
     * new_aid_rat_3_4_aid_num_trgt_diff 3&4星主播签约数量目标差值
     */
    private Long newAidRat34AidNumTrgtDiff;

    /**
     * new_aid_rat_3_4_aid_num_diff_next_score 3&4星主播签约数量趋势下一等级分值
     */
    private Integer newAidRat34AidNumDiffNextScore;

    /**
     * new_aid_rat_3_4_aid_num_diff_trgt_diff 3&4星主播签约数量趋势目标差值
     */
    private Long newAidRat34AidNumDiffTrgtDiff;

    /**
     * upgrd_high_aid_num_diff_next_score 升级高优主播数趋势下一等级分值
     */
    private Integer upgrdHighAidNumDiffNextScore;

    /**
     * upgrd_high_aid_num_diff_trgt_diff 升级高优主播数趋势目标差值
     */
    private Integer upgrdHighAidNumDiffTrgtDiff;

    /**
     * upgrd_waist_aid_num_diff_next_score 升级腰部主播数趋势下一等级分值
     */
    private Integer upgrdWaistAidNumDiffNextScore;

    /**
     * upgrd_waist_aid_num_diff_trgt_diff 升级腰部主播数趋势目标差值
     */
    private Integer upgrdWaistAidNumDiffTrgtDiff;

    /**
     * avg_high_valid_live_aid_num_rn_next_score 月日均高优有效开播主播数排名下一等级分值
     */
    private Integer avgHighValidLiveAidNumRnNextScore;

    /**
     * avg_high_valid_live_aid_num_trgt_diff 月日均高优有效开播主播数目标差值
     */
    private Double avgHighValidLiveAidNumTrgtDiff;

    /**
     * avg_high_valid_live_aid_num_diff_next_score 月日均高优有效开播主播数趋势下一等级分值
     */
    private Integer avgHighValidLiveAidNumDiffNextScore;

    /**
     * avg_high_valid_live_aid_num_diff_trgt_diff 月日均高优有效开播主播数趋势目标差值
     */
    private Double avgHighValidLiveAidNumDiffTrgtDiff;

    /**
     * avg_waist_valid_live_aid_num_rn_next_score 月日均腰部有效开播主播数排名下一等级分值
     */
    private Integer avgWaistValidLiveAidNumRnNextScore;

    /**
     * avg_waist_valid_live_aid_num_trgt_diff 月日均腰部有效开播主播数目标差值
     */
    private Double avgWaistValidLiveAidNumTrgtDiff;


    /**
     * avg_waist_valid_live_aid_num_diff_next_score 月日均腰部有效开播主播数趋势下一等级分值
     */
    private Integer avgWaistValidLiveAidNumDiffNextScore;

    /**
     * avg_waist_valid_live_aid_num_diff_trgt_diff 月日均腰部有效开播主播数趋势目标差值
     */
    private Double avgWaistValidLiveAidNumDiffTrgtDiff;

    /**
     * avg_nto_valid_live_aid_rate_rn_next_score 新转存主播开播率排名下一等级分值
     */
    private Integer avgNtoValidLiveAidRateRnNextScore;

    /**
     * avg_nto_valid_live_aid_rate_trgt_diff 新转存主播开播率目标差值
     */
    private Double avgNtoValidLiveAidRateTrgtDiff;

    /**
     * avg_stock_valid_live_aid_rate_rn_next_score存量主播开播率排名下一等级分值
     */
    private Integer avgStockValidLiveAidRateRnNextScore;

    /**
     * avg_stock_valid_live_aid_rate_trgt_diff 存量主播开播率目标差值
     */
    private Double avgStockValidLiveAidRateTrgtDiff;

    /**
     * new_auth_golden_aid_num_rn_next_score 新授权金牌艺人排名下一等级分值
     */
    private Integer newAuthGoldenAidNumRnNextScore;

    /**
     * new_auth_golden_aid_num_trgt_diff 新授权金牌艺人目标差值
     */
    private Long newAuthGoldenAidNumTrgtDiff;

    /**
     * valid_live_new_aid_incm_amt_rn_next_score 新主播蓝钻收入排名下一等级分值
     */
    private Integer validLiveNewAidIncmAmtRnNextScore;

    /**
     * valid_live_new_aid_incm_amt_trgt_diff 新主播蓝钻收入目标差值
     */
    private Double validLiveNewAidIncmAmtTrgtDiff;

    /**
     * valid_live_new_aid_incm_amt_diff_next_score 新主播蓝钻收入趋势下一等级分值
     */
    private Integer validLiveNewAidIncmAmtDiffNextScore;

    /**
     * valid_live_new_aid_incm_amt_diff_trgt_diff 新主播蓝钻收入趋势目标差值@@
     */
    private Double validLiveNewAidIncmAmtDiffTrgtDiff;

    /**
     * unvalid_live_new_aid_incm_amt_rn_next_score 存量主播蓝钻收入排名下一等级分值
     */
    private Integer unvalidLiveNewAidIncmAmtRnNextScore;

    /**
     * unvalid_live_new_aid_incm_amt_trgt_diff 存量主播蓝钻收入目标差值
     */
    private Double unvalidLiveNewAidIncmAmtTrgtDiff;

    /**
     * unvalid_live_new_aid_incm_amt_diff_next_score 存量主播蓝钻收入趋势下一等级分值
     */
    private Integer unvalidLiveNewAidIncmAmtDiffNextScore;

    /**
     * unvalid_live_new_aid_incm_amt_diff_trgt_diff 存量主播蓝钻收入趋势目标差值
     */
    private Double unvalidLiveNewAidIncmAmtDiffTrgtDiff;

    /**
     * unvalid_live_new_aid_incm_amt_rate_diff_next_score 存量主播蓝钻收入占比下一等级分值
     */
    private Integer unvalidLiveNewAidIncmAmtRateDiffNextScore;

    /**
     * unvalid_live_new_aid_incm_amt_rate_diff_trgt_diff 存量主播蓝钻收入占比目标差值
     */
    private Double unvalidLiveNewAidIncmAmtRateDiffTrgtDiff;

    /**
     *  guild_health_point_next_score 公会健康分下一等级分值
     */
    private Integer guildHealthPointNextScore;

    /**
     *  guild_health_point_trgt_diff 公会健康分目标差值
     */
    private Integer guildHealthPointTrgtDiff;

    /**
     * 日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date dt;

    public YyDmEntityGuildCmpHealthAnalysisExptDiDTO(YyDmEntityGuildCmpHealthAnalysisExptDi di) {
        if (di == null) {
            return;
        }
        BeanUtils.copyProperties(di, this);
    }
}