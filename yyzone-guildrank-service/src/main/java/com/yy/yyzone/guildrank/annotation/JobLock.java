package com.yy.yyzone.guildrank.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2021/6/21
 *
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface JobLock {
    /**
     * lock超时（秒）
      * @return
     */
    int timeout() default 3600 * 2;
}