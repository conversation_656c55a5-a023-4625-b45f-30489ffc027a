package com.yy.yyzone.guildrank.util;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2019/7/10
 */
public class PageResp<T> implements Serializable{
    private int code; // 0成功-其他失败
    private String msg;
    private int total; // 总数
    private T data;

    private PageResp(int code){
        this.code = code;
    }

    private PageResp(int code, String msg){
        this.code = code;
        this.msg = msg;
    }

    private PageResp(int code, T data){
        this.code = code;
        this.data = data;
    }

    private PageResp(int code, String msg, T data){
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    private PageResp(T data, int total){
        this.total = total;
        this.data = data;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public int getcode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public T getData() {
        return data;
    }

    /*判断当前状态是否成功.成功返回true.失败返回false
     */
    @JsonIgnore
    //使isSuccess返回值不被序列化,因为该方法不需要被转化成json对象
    public Boolean isSuccess(){
        return this.code == ResponseCode.SUCCESS.getCode();
    }

    //返回成功的Resp对象.
    public static<T> PageResp<T> createBySuccess(){
        return new PageResp<>(ResponseCode.SUCCESS.getCode());
    }
    public static<T> PageResp<T> createBySuccessMassage(String msg){
        return new PageResp<>(ResponseCode.SUCCESS.getCode(),msg);
    }
    public static<T> PageResp<T> createBySuccess(T data){
        return new PageResp<>(ResponseCode.SUCCESS.getCode(),data);
    }
    public static<T> PageResp<T> createBySuccess(String msg, T data){
        return new PageResp<>(ResponseCode.SUCCESS.getCode(),msg,data);
    }

    public static<T> PageResp<T> createBySuccess(T data, int total){
        return new PageResp<>(data, total);
    }

    //返回失败的Resp对象.
    public static<T> PageResp<T> createByError(){
        return new PageResp<>(ResponseCode.ERROR.getCode(),ResponseCode.ERROR.getDesc());
    }
    public static<T> PageResp<T> createByError(String msg){
        return new PageResp<>(ResponseCode.ERROR.getCode(),msg);
    }
    public static<T> PageResp<T> createByError(int code, String msg){
        return new PageResp<>(code,msg);
    }
}