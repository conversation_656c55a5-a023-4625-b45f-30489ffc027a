package com.yy.yyzone.guildrank.service;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.yyzone.guildrank.constant.BigdaSyncConstant;
import com.yy.yyzone.guildrank.db.custom.mapper.BigdaSyncDetailExtMapper;
import com.yy.yyzone.guildrank.db.gen.mapper.BigdaSyncDetailMapper;
import com.yy.yyzone.guildrank.db.gen.model.BigdaSyncDetail;
import com.yy.yyzone.guildrank.db.gen.model.BigdaSyncDetailExample;
import com.yy.yyzone.guildrank.util.MyDateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Array;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BigdaSyncDetailService {
    @Autowired
    private BigdaSyncDetailMapper detailMapper;
    @Autowired
    private BigdaSyncDetailExtMapper detailExtMapper;

    /**
     * 获取最近的同步日期
     *
     * @param tbls
     * @return
     */
    public Map<BigdaSyncConstant.TableName, Date> getLatestDt(Set<BigdaSyncConstant.TableName> tbls, BigdaSyncConstant.Status status) {
        List<String> names = tbls.stream().map(t -> t.getName()).collect(Collectors.toList());
        List<Map<String, Object>> maps = detailExtMapper.selectLatestDt(names, status.getValue());
        Map<BigdaSyncConstant.TableName, Date> result = Maps.newHashMap();
        for (Map<String, Object> map : maps) {
            BigdaSyncConstant.TableName tbl = BigdaSyncConstant.TableName.of(map.get("table_name").toString());
            result.put(tbl, MyDateUtil.parseDate(map.get("dt").toString(), tbl.getDtFmt(), null));
        }
        return result;
    }

    public Date getCheckDoneLatestDt(BigdaSyncConstant.TableName tbl) {
        return getLatestDt(Sets.newHashSet(tbl), BigdaSyncConstant.Status.CHECK_DONE).get(tbl);
    }

    public Date getDataTime(BigdaSyncConstant.TableName tbl, Date dt) {
        BigdaSyncDetailExample example = new BigdaSyncDetailExample();
        example.createCriteria().andTableNameEqualTo(tbl.getName()).andDtEqualTo(DateFormatUtils.format(dt, tbl.getDtFmt()));
        example.setOrderByClause("data_time desc");
        example.setLimit(1);
        List<BigdaSyncDetail> details = detailMapper.selectByExample(example);
        return details.isEmpty() ? null : details.get(0).getDataTime();
    }

    public boolean hasSyncDone(BigdaSyncConstant.TableName tbl, Date dt) {
        BigdaSyncDetailExample example = new BigdaSyncDetailExample();
        example.createCriteria().andDtEqualTo(DateFormatUtils.format(dt, tbl.getDtFmt()))
                .andStatusEqualTo(BigdaSyncConstant.Status.SYNC_DONE.getValue())
                .andTableNameEqualTo(tbl.getName());
        int count = detailMapper.countByExample(example);
        return count > 0;
    }

    public boolean hasCheckDone(BigdaSyncConstant.TableName tbl, Date dt) {
        BigdaSyncDetailExample example = new BigdaSyncDetailExample();
        example.createCriteria().andDtEqualTo(DateFormatUtils.format(dt, tbl.getDtFmt()))
                .andStatusEqualTo(BigdaSyncConstant.Status.CHECK_DONE.getValue())
                .andTableNameEqualTo(tbl.getName());
        int count = detailMapper.countByExample(example);
        return count > 0;
    }

    public int setCheckDone(BigdaSyncConstant.TableName tbl, Date dt) {
        BigdaSyncDetailExample example = new BigdaSyncDetailExample();
        example.createCriteria().andDtEqualTo(DateFormatUtils.format(dt, tbl.getDtFmt()))
                .andStatusEqualTo(BigdaSyncConstant.Status.SYNC_DONE.getValue())
                .andTableNameEqualTo(tbl.getName());

        BigdaSyncDetail record = new BigdaSyncDetail();
        record.setStatus(BigdaSyncConstant.Status.CHECK_DONE.getValue());
        record.setDataTime(new Date());
        return detailMapper.updateByExampleSelective(record, example);
    }

    public int setCheckDone(long id) {
        BigdaSyncDetail record = new BigdaSyncDetail();
        record.setId(id);
        record.setStatus(BigdaSyncConstant.Status.CHECK_DONE.getValue());
        record.setDataTime(new Date());
        return detailMapper.updateByPrimaryKeySelective(record);
    }

    public List<BigdaSyncDetail> getSyncDoneDetail(BigdaSyncConstant.TableName tbl) {
        BigdaSyncDetailExample example = new BigdaSyncDetailExample();
        example.createCriteria().andStatusEqualTo(BigdaSyncConstant.Status.SYNC_DONE.getValue()).andTableNameEqualTo(tbl.getName());
        List<BigdaSyncDetail> details = detailMapper.selectByExample(example);
        return details;
    }
}