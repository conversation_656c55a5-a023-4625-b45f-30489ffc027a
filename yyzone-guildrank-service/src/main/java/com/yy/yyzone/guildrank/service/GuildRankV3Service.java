package com.yy.yyzone.guildrank.service;

import com.yy.yyzone.guildrank.constant.BigdaSyncConstant;
import com.yy.yyzone.guildrank.constant.RankConstant;
import com.yy.yyzone.guildrank.db.custom.mapper.BiExtMapper;
import com.yy.yyzone.guildrank.db.custom.model.GuildCmpAvgScore;
import com.yy.yyzone.guildrank.db.gen.mapper.GuildrankWhilelistMapper;
import com.yy.yyzone.guildrank.db.gen.mapper.YyDmEntityGuildCmpHealthAnalysisDiMapper;
import com.yy.yyzone.guildrank.db.gen.mapper.YyDmEntityGuildCmpHealthAnalysisExptDiMapper;
import com.yy.yyzone.guildrank.db.gen.model.*;
import com.yy.yyzone.guildrank.dto.*;
import com.yy.yyzone.guildrank.util.MyDateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GuildRankV3Service {
    private static final BigDecimal BD_100 = new BigDecimal("100");

    @Autowired
    private YyDmEntityGuildCmpHealthAnalysisDiMapper diMapper;

    @Autowired
    private BiExtMapper extMapper;

    @Autowired
    private YyDmEntityGuildCmpHealthAnalysisExptDiMapper exptDiMapper;

    @Autowired
    private GuildrankWhilelistMapper whilelistMapper;

    @Autowired
    private RankCacheV3Service rankCacheV3Service;

    @Autowired
    private BigdaSyncDetailService bigdaSyncDetailService;

    @Autowired
    private GuildRankConfigService configService;

    @Autowired
    private GuildRankTaskDistributionService guildRankTaskDistributionService;

    @Autowired
    private SwitchService switchService;

    public GuildRankInfoDTO guildRankInfo(long companyUid, Date month) {
        GuildRankInfoDTO.GuildRankInfoDTOBuilder builder = GuildRankInfoDTO.builder();
        month = MyDateUtil.monthEnd(month);
        int delta = rankCacheV3Service.hasUpdate() ? RankConstant.LAST : RankConstant.PRE_LAST;
        Date max = MyDateUtil.monthEnd(delta);
        if (month.after(max)) {
            return builder.build();
        }

        Date lastMonth = MyDateUtil.lastMonthEnd(month);
        Date nextDt = null;
        if (month.equals(max)) {
            Date latestDt = bigdaSyncDetailService.getCheckDoneLatestDt(BigdaSyncConstant.TableName.CMP_HEALTH_DI);
            if (latestDt != null) {
                // 是month的下月日期
                if (DateUtils.truncatedEquals(MyDateUtil.lastMonthEnd(latestDt), month, Calendar.MONTH)) {
                    nextDt = latestDt;
                }

                Date dataTime = bigdaSyncDetailService.getDataTime(BigdaSyncConstant.TableName.CMP_HEALTH_DI, latestDt);
                builder.updateTime(dataTime);
            }
        }

        List<Date> dts = new ArrayList<>(10);
        dts.add(month);
        dts.add(lastMonth);
        if (nextDt != null) {
            dts.add(nextDt);
        }
        Map<Date, Integer> whitelistMap = getWhitelist(companyUid, dts);
        log.info("guildRankInfo companyUid:{},reqDt:{},hasUpdate:{},dts:{},whitelistMap:{}", companyUid, MyDateUtil.fmt(month), rankCacheV3Service.hasUpdate(), MyDateUtil.fmtDate(dts), whitelistMap);

        YyDmEntityGuildCmpHealthAnalysisDiExample example = new YyDmEntityGuildCmpHealthAnalysisDiExample();
        example.createCriteria().andGuildCmpOwnrIdEqualTo(companyUid).andDtIn(dts);
        Map<Date, YyDmEntityGuildCmpHealthAnalysisDi> diMap = diMapper.selectByExample(example).stream()
                .collect(Collectors.toMap(l -> l.getDt(), Function.identity()));

        YyDmEntityGuildCmpHealthAnalysisDi thisDi = diMap.get(month);
        YyDmEntityGuildCmpHealthAnalysisDi lastDi = diMap.get(lastMonth);
        if (thisDi != null) {
            Integer rank = whitelistMap.get(month);
            if (rank != null) {
                thisDi.setDataCmpStarLvl(rank);
                thisDi.setCmpStarLvl(rank);
            }
            builder.thisMonthDi(new YyDmEntityGuildCmpHealthAnalysisDiDTO(thisDi,switchService.isSwitchToCmpStarLvl()));
            builder.thisMonthGuildRankTask(guildRankTaskDistributionService.getGuildTasks(companyUid,month));
        } else {
            //兼容没BI数据的情况
            int rank = rankCacheV3Service.queryCurrentRank(companyUid);
            if (rank > 0) {
                log.info("guildRankInfo no bi data companyUid:{},rank:{}", companyUid, rank);
                YyDmEntityGuildCmpHealthAnalysisDiDTO dto = new YyDmEntityGuildCmpHealthAnalysisDiDTO();
                dto.setDataCmpStarLvl(rank);
                dto.setCmpStarLvl(rank);
                builder.thisMonthDi(dto);
            }
        }
        Integer rank = whitelistMap.get(lastMonth);
        if (rank != null) {
            if (lastDi == null) {
                lastDi = new YyDmEntityGuildCmpHealthAnalysisDi();
            }
            lastDi.setDataCmpStarLvl(rank);
            lastDi.setCmpStarLvl(rank);
        }
        if (lastDi != null) {
            builder.lastMonthDi(new YyDmEntityGuildCmpHealthAnalysisDiDTO(lastDi,switchService.isSwitchToCmpStarLvl()));
        }
        if (nextDt != null) {
            YyDmEntityGuildCmpHealthAnalysisDi nextDi = diMap.get(nextDt);
            if (nextDi != null) {
                YyDmEntityGuildCmpHealthAnalysisDiDTO next = new YyDmEntityGuildCmpHealthAnalysisDiDTO(nextDi,switchService.isSwitchToCmpStarLvl());
                // 隐藏任务指标
                // next.calcMoreScoreTips(configService.getTaskIndexConfig(true));
                builder.nextMonthDi(next);

                YyDmEntityGuildCmpHealthAnalysisExptDi exptDi = getExptDi(companyUid, nextDt);
                if (exptDi != null) {
                    builder.exptDi(new YyDmEntityGuildCmpHealthAnalysisExptDiDTO(exptDi));
                }
            }
            builder.nextMonthGuildRankTask(guildRankTaskDistributionService.getGuildTasks(companyUid,nextDt));
        }
        return builder.build();
    }

    public YyDmEntityGuildCmpHealthAnalysisExptDi getExptDi(long uid, Date dt) {
        YyDmEntityGuildCmpHealthAnalysisExptDiExample example = new YyDmEntityGuildCmpHealthAnalysisExptDiExample();
        example.createCriteria().andGuildCmpOwnrIdEqualTo(uid).andDtEqualTo(dt);
        example.setLimit(1);
        List<YyDmEntityGuildCmpHealthAnalysisExptDi> list = exptDiMapper.selectByExample(example);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    public List<LastTwelveMonthV3Resp> lastTwelveMonth(long companyUid) {
        int monthCount = 12;
        List<Date> monthEnds = new ArrayList<>(20);
        for (int i = 1; i <= monthCount + 1; i++) {
            monthEnds.add(MyDateUtil.monthEnd(-i));
        }
        log.info("lastTwelveMonth companyUid:{},dts:{}", companyUid, MyDateUtil.fmtDate(monthEnds));
        YyDmEntityGuildCmpHealthAnalysisDiExample example = new YyDmEntityGuildCmpHealthAnalysisDiExample();
        example.createCriteria().andGuildCmpOwnrIdEqualTo(companyUid).andDtIn(monthEnds);
        example.setOrderByClause(" dt desc ");
        example.setLimit(monthCount);
        List<YyDmEntityGuildCmpHealthAnalysisDi> list = diMapper.selectByExample(example);
        return list.stream().map(l -> {
            LastTwelveMonthV3Resp r = new LastTwelveMonthV3Resp();
            r.setMonth(l.getDt());
            r.setStarLevel(l.getDataCmpStarLvl());
            return r;
        }).collect(Collectors.toList());

    }

    public RadarChartData getRadarChartData(long companyUid) {
        Date dt = bigdaSyncDetailService.getCheckDoneLatestDt(BigdaSyncConstant.TableName.CMP_HEALTH_DI);
        log.info("getRadarChartData dt:{}, uid:{}", MyDateUtil.fmtDate(dt), companyUid);
        if (dt == null) {
            return null;
        }

        GuildCmpAvgScore myScore = extMapper.selectGuildCmpAvgScoreByUid(dt, companyUid);
        if (myScore == null) {
            return null;
        }
        RadarChartData data = new RadarChartData();
        GuildCmpAvgScore myData = new GuildCmpAvgScore();
        myData.setRecruScore(toRadar(myScore.getRecruScore(), RankConstant.TotalScore.RECRU));
        myData.setHatchScore(toRadar(myScore.getHatchScore(), RankConstant.TotalScore.HATCH));
        myData.setRetScore(toRadar(myScore.getRetScore(), RankConstant.TotalScore.RET));
        myData.setRvnuScore(toRadar(myScore.getRvnuScore(), RankConstant.TotalScore.RVNU));
        myData.setComScore(toRadar(myScore.getComScore(), RankConstant.TotalScore.COM));
        myData.setLevel(myScore.getLevel());
        data.setMine(myData);

        if (myScore.getLevel() != null) {
            int nextLevel = myScore.getLevel() + 1;
            GuildCmpAvgScore next = extMapper.selectGuildCmpAvgScoreByLvl(dt, nextLevel);
            if (next != null) {
                GuildCmpAvgScore nextData = new GuildCmpAvgScore();
                nextData.setRecruScore(toRadar(next.getRecruScore(), RankConstant.TotalScore.RECRU));
                nextData.setHatchScore(toRadar(next.getHatchScore(), RankConstant.TotalScore.HATCH));
                nextData.setRetScore(toRadar(next.getRetScore(), RankConstant.TotalScore.RET));
                nextData.setRvnuScore(toRadar(next.getRvnuScore(), RankConstant.TotalScore.RVNU));
                nextData.setComScore(toRadar(next.getComScore(), RankConstant.TotalScore.COM));
                nextData.setLevel(nextLevel);
                data.setNext(nextData);
            }
        }

        return data;
    }

    private BigDecimal toRadar(BigDecimal score, BigDecimal total) {
        if (score == null) {
            return null;
        }
        return score.divide(total, 10, RoundingMode.HALF_UP).multiply(BD_100).setScale(2, RoundingMode.HALF_UP);
    }

    private Map<Date, Integer> getWhitelist(long uid, List<Date> dates) {
        if (CollectionUtils.isEmpty(dates)) {
            return Collections.emptyMap();
        }
        try {
            List<String> monthList = dates.stream().map(d -> MyDateUtil.fmtMonth(DateUtils.addMonths(d, 1))).collect(Collectors.toList());
            GuildrankWhilelistExample example = new GuildrankWhilelistExample();
            example.createCriteria().andGuildUidEqualTo(uid).andMonthstrIn(monthList);
            return whilelistMapper.selectByExample(example).stream()
                    .collect(Collectors.toMap(w -> DateUtils.addMonths(MyDateUtil.monthEnd(MyDateUtil.parseMonth(w.getMonthstr())), -1),
                            w -> w.getRank(), (n, o) -> n));
        } catch (Exception e) {
            log.error("getWhitelist exception uid:{},dts:{}", uid, MyDateUtil.fmtDate(dates), e);
        }
        return Collections.emptyMap();
    }
}
