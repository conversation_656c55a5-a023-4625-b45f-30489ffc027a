package com.yy.yyzone.guildrank.service;

import com.google.common.collect.Maps;
import com.yy.yyzone.guildrank.dto.ApiResponse;
import com.yy.yyzone.guildrank.dto.udb.AuthVO;
import com.yy.yyzone.guildrank.dto.udb.BaseReqVO;
import com.yy.yyzone.guildrank.dto.udb.NCertMaskInfoRes;
import com.yy.yyzone.guildrank.dto.udb.NEntreCertMaskInfoRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class UdbUserInfoService {

    @Resource(name = "httpRestTemplate2")
    private RestTemplate restTemplate;

    @Value("#{'${yy.env}'.equalsIgnoreCase('dev')||'${yy.env}'.equalsIgnoreCase('test')}")
    private Boolean isTest;

    private static final String APP_ID = "1238732223";

    private static final String GET_CERT_MASK_INFO_BY_UID_API = "/userinfo/get-cert-mask-info-by-uid";

    private static final String GET_ENTRE_CERT_MASK_INFO_BY_UID_API = "/userinfo/get-entre-cert-mask-info-by-uid";

    private static final ParameterizedTypeReference<ApiResponse<NCertMaskInfoRes>> GET_CERT_MASK_INFO_BY_UID_REF = new ParameterizedTypeReference<ApiResponse<NCertMaskInfoRes>>() {
    };
    private static final ParameterizedTypeReference<ApiResponse<NEntreCertMaskInfoRes>> GET_ENTRE_CERT_MASK_INFO_BY_UID_REF = new ParameterizedTypeReference<ApiResponse<NEntreCertMaskInfoRes>>() {
    };

    public Map<Long, NCertMaskInfoRes> getCertMaskInfoByUid(List<Long> uidList) {
        Map<Long, NCertMaskInfoRes> dataMap = Maps.newHashMap();
        uidList.forEach(uid -> dataMap.put(uid, getCertMaskInfoByUid(uid)));
        return dataMap;
    }

    /**
     * 获取用户实名信息，证件号为掩码 (lg_userinfo_sa_getCertMaskInfoByUid)
     */
    public NCertMaskInfoRes getCertMaskInfoByUid(long uid) {
        NCertMaskInfoRes ret = null;
        try {
            BaseReqVO req = new BaseReqVO();
            req.setAuth(genAuth());
            req.setAppid("1238732223");
            req.setRequest(String.valueOf(uid));
            // request的类型。1001:通行证，1002:yyuid 1003:yyid
            req.setReqtype(1002);
            log.info(req.toString());
            ResponseEntity<ApiResponse<NCertMaskInfoRes>> responseEntity = restTemplate.exchange(getDomain() + GET_CERT_MASK_INFO_BY_UID_API,
                    HttpMethod.POST, new HttpEntity<>(req), GET_CERT_MASK_INFO_BY_UID_REF);
            log.info("getCertMaskInfoByUid, uid:{},  responseEntity:{}", uid, responseEntity);
            if (responseEntity.getStatusCodeValue() == HttpStatus.OK.value()) {
                ApiResponse<NCertMaskInfoRes> apiResponse = responseEntity.getBody();
                if (apiResponse != null && apiResponse.isSuccess()) {
                    return apiResponse.getData();
                }
            }
            log.info("getEntreCertMaskInfoByUid  uid:{}, ret:{}", uid, ret.toString());
        } catch (Exception e) {
            log.info("getEntreCertMaskInfoByUid error, uid:{}", uid, e);
        }
        return null;
    }

    /**
     * 获取企业认证信息掩码 (lg_userinfo_sa_getEntreCertMaskInfoByUid)
     */
    public NEntreCertMaskInfoRes getEntreCertMaskInfoByUid(long uid) {
        BaseReqVO req = new BaseReqVO();
        req.setAuth(genAuth());
        req.setAppid("1238732223");
        req.setRequest(String.valueOf(uid));
        // request的类型。1001:通行证，1002:yyuid 1003:yyid
        req.setReqtype(1002);
        log.info("getEntreCertMaskInfoByUid uid:{}",uid);
        ResponseEntity<ApiResponse<NEntreCertMaskInfoRes>> responseEntity = restTemplate.exchange(getDomain() +GET_ENTRE_CERT_MASK_INFO_BY_UID_API,
                    HttpMethod.POST, new HttpEntity<>(req), GET_ENTRE_CERT_MASK_INFO_BY_UID_REF);
        log.info("getEntreCertMaskInfoByUid, uid:{},  responseEntity:{}", uid, responseEntity);
        if (responseEntity.getStatusCodeValue() == HttpStatus.OK.value()) {
            ApiResponse<NEntreCertMaskInfoRes> apiResponse = responseEntity.getBody();
            if (apiResponse.isSuccess()) {
                return apiResponse.getData();
            }
        }
        throw new RuntimeException("getEntreCertMaskInfoByUid error, uid:" + uid +" fail!");
    }

    private String getDomain() {
        return isTest ? "https://riskadaptor-test.yy.com" : "https://riskadaptor.yy.com";
    }

    private static AuthVO genAuth() {
        Map<String, String> keyValue = new HashMap<>();
        keyValue.put("auth-type", "4");
        return new AuthVO(APP_ID, "8dd0d5502ccc497ba62e0b29f84117a3", keyValue);
    }
}
