package com.yy.yyzone.guildrank.dto.guildranktaskconfig;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
public class Indicator {
    @NotNull
    private IndicatorEnum indicator;

    /**
     * 指标单位数量
     */
    @Min(1)
    private Integer unitsPerCount;

    /**
     * 一个单位多少分
     */
    @Min(1)
    private Integer scorePerUnit;

    /**
     * 固定分
     */
    @Min(1)
    private Integer score;      // 固定分值时用
}
