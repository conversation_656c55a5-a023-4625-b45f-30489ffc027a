package com.yy.yyzone.guildrank.service;

import com.google.common.collect.Lists;
import com.yy.yyzone.guildrank.constant.BigdaSyncConstant;
import com.yy.yyzone.guildrank.db.custom.mapper.BiExtMapper;
import com.yy.yyzone.guildrank.db.gen.mapper.YyDmEntityGuildCmpHealthAnalysisDiTmpMapper;
import com.yy.yyzone.guildrank.db.gen.model.BigdaSyncDetail;
import com.yy.yyzone.guildrank.db.gen.model.YyDmEntityGuildCmpHealthAnalysisDiTmp;
import com.yy.yyzone.guildrank.db.gen.model.YyDmEntityGuildCmpHealthAnalysisDiTmpExample;
import com.yy.yyzone.guildrank.util.MyDateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RankCheckV3Service {
    @Autowired
    private YyDmEntityGuildCmpHealthAnalysisDiTmpMapper tmpMapper;

    @Autowired
    private BiExtMapper extMapper;

    @Autowired
    private MsgService msgService;

    @Autowired
    private BigdaSyncDetailService bigdaSyncDetailService;

    public static final int BATCH_SIZE = 200;

    /**
     * 校验 公会段位数据存在不在1~7段位之间的情况或公会段位数据存在1/2/3/4/5星级公会数量为0的情况
     * 将临时数据写入正式表
     * 加积分
     */
    @Transactional(rollbackFor = {RuntimeException.class, Exception.class})
    public void rankCheck(Date currentDate, boolean ignoreDataErr) {
        log.info("rankCheck currentDate:{},ignoreDataErr:{}", MyDateUtil.fmt(currentDate), ignoreDataErr);
        boolean flag = bigdaSyncDetailService.hasSyncDone(BigdaSyncConstant.TableName.CMP_HEALTH_DI, currentDate);
        if (flag) {
            log.info("rankCheck sync done");
            YyDmEntityGuildCmpHealthAnalysisDiTmpExample tmpExample = new YyDmEntityGuildCmpHealthAnalysisDiTmpExample();
            tmpExample.createCriteria().andDtEqualTo(currentDate);
            List<YyDmEntityGuildCmpHealthAnalysisDiTmp> list = tmpMapper.selectByExample(tmpExample);

            boolean dataError = list.stream().map(YyDmEntityGuildCmpHealthAnalysisDiTmp::getDataCmpStarLvl).anyMatch(v -> v > 7 || v < 1);

            Map<Integer, Long> cntMap = list.stream().collect(Collectors.groupingBy(YyDmEntityGuildCmpHealthAnalysisDiTmp::getDataCmpStarLvl, Collectors.counting()));
            boolean numError = !cntMap.containsKey(1) || !cntMap.containsKey(2) || !cntMap.containsKey(3)
                    || !cntMap.containsKey(4) || !cntMap.containsKey(5);
            boolean err = dataError || numError;
            if (!ignoreDataErr && err) {
                String dateStr = MyDateUtil.fmtDate(currentDate);
                log.error("rankCheck data error cntMap:{},dateStr:{}", cntMap, dateStr);
                msgService.sendAlarmMsg(dateStr);
            } else {
                List<List<YyDmEntityGuildCmpHealthAnalysisDiTmp>> lists = Lists.partition(list, BATCH_SIZE);
                for (List<YyDmEntityGuildCmpHealthAnalysisDiTmp> l : lists) {
                    extMapper.insertBatch(l);
                }

                // 暂不启用积分
                // Date now = new Date();
                // String typeSerial = DateFormatUtils.format(currentDate, "yyyyMM");
                // Date dueDt = DateUtils.addDays(DateUtils.addMonths(MyDateUtil.currentMonth(), PointConstant.POINT_VALID_MONTH), -1);
                // List<GuildrankPointDetail> points = list.stream().filter(l -> l.getTaskTotalItg() != null && l.getTaskTotalItg() > 0).map(l -> {
                //     GuildrankPointDetail d = new GuildrankPointDetail();
                //     d.setUid(l.getGuildCmpOwnrId());
                //     d.setPoint(l.getTaskTotalItg());
                //     d.setType(PointConstant.Type.MONTHLY_RANK.getValue());
                //     d.setTypeSerial(typeSerial);
                //     d.setStatus(PointConstant.Status.TO_BE_GRANT.getValue());
                //     d.setDueDt(dueDt);
                //     d.setRemark(PointConstant.POINT_REMARK);
                //     d.setDt(l.getDt());
                //     d.setCreateTime(now);
                //     d.setUpdateTime(now);
                //     return d;
                // }).collect(Collectors.toList());
                // List<List<GuildrankPointDetail>> pointsList = Lists.partition(points, BATCH_SIZE);
                // for (List<GuildrankPointDetail> p : pointsList) {
                //     extMapper.insertPoints(p);
                // }

                bigdaSyncDetailService.setCheckDone(BigdaSyncConstant.TableName.CMP_HEALTH_DI, currentDate);
            }
        }
    }

    public boolean rankCheckDone(Date dt) {
        return bigdaSyncDetailService.hasCheckDone(BigdaSyncConstant.TableName.CMP_HEALTH_DI, dt);
    }

    /**
     * 非月末数据写入正式表
     */
    public void syncNotMonthEndData() {
        List<BigdaSyncDetail> details = bigdaSyncDetailService.getSyncDoneDetail(BigdaSyncConstant.TableName.CMP_HEALTH_DI);
        log.info("syncNotMonthEndData size:{}", details.size());
        if (details.isEmpty()) {
            return;
        }

        for (BigdaSyncDetail d : details) {
            Date dt = MyDateUtil.parseDate(d.getDt());
            if (MyDateUtil.isMonthEnd(dt)) {
                log.info("syncNotMonthEndData isMonthEnd dt:{}", d.getDt());
                continue;
            }

            YyDmEntityGuildCmpHealthAnalysisDiTmpExample tmpExample = new YyDmEntityGuildCmpHealthAnalysisDiTmpExample();
            tmpExample.createCriteria().andDtEqualTo(dt);
            List<YyDmEntityGuildCmpHealthAnalysisDiTmp> list = tmpMapper.selectByExample(tmpExample);
            log.info("syncNotMonthEndData tmp dt:{},size:{}", d.getDt(), list.size());
            if (CollectionUtils.isNotEmpty(list)) {
                List<List<YyDmEntityGuildCmpHealthAnalysisDiTmp>> lists = Lists.partition(list, BATCH_SIZE);
                for (List<YyDmEntityGuildCmpHealthAnalysisDiTmp> l : lists) {
                    extMapper.insertBatch(l);
                }
            }

            bigdaSyncDetailService.setCheckDone(d.getId());
        }
    }
}
