package com.yy.yyzone.guildrank.yrpc.dto.goldchannel;

import com.yy.ent.commons.protopack.util.Uint;
import com.yy.yyzone.guildrank.yrpc.dto.MapUintStringBean;
import lombok.Data;

import java.util.List;

@Data
public class PGetAssociatedCompanyResp {
    private Uint result;
    /**
     * 若无金牌授权公会（即非关联公会），则列表无该值
     * 1-UID 关联公会uid
     * 2-COMPANY_ID 授权uid
     * 3-TIME
     * 4-OPERATOR
     */
    private List<MapUintStringBean> info;
}