package com.yy.yyzone.guildrank.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yy.yyzone.guildrank.dto.guildranktaskconfig.GuildRankTaskDTO;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

@Data
@Builder
public class GuildRankInfoDTO {
    /**
     * 更新时间 yyyy-MM-dd HH:mm:ss
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 本月数据
     */
    private YyDmEntityGuildCmpHealthAnalysisDiDTO thisMonthDi;

    /**
     * 上月数据
     */
    private YyDmEntityGuildCmpHealthAnalysisDiDTO lastMonthDi;

    /**
     * 下月数据（预计）
     */
    private YyDmEntityGuildCmpHealthAnalysisDiDTO nextMonthDi;

    /**
     * 距离下一目标分值差值
     */
    private YyDmEntityGuildCmpHealthAnalysisExptDiDTO exptDi;

    /**
     * 本月公会等级任务
     */
    private GuildRankTaskDTO thisMonthGuildRankTask;
    /**
     * 下个月公会等级任务
     */
    private GuildRankTaskDTO nextMonthGuildRankTask;
}