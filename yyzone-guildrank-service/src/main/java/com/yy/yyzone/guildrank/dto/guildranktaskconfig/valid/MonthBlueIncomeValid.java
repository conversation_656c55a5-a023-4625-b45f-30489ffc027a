package com.yy.yyzone.guildrank.dto.guildranktaskconfig.valid;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = MonthBlueIncomeValidator.class)
public @interface MonthBlueIncomeValid {
    String message() default "monthBlueIncome 配置不合法";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}