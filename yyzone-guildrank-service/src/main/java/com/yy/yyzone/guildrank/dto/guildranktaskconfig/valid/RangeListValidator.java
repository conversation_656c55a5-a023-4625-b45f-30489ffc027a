package com.yy.yyzone.guildrank.dto.guildranktaskconfig.valid;

import com.yy.yyzone.guildrank.dto.guildranktaskconfig.Range;

import javax.validation.*;
import java.util.*;

public class RangeListValidator implements ConstraintValidator<ValidRanges, List<Range>> {

    @Override
    public boolean isValid(List<Range> ranges, ConstraintValidatorContext ctx) {
        if (ranges == null || ranges.isEmpty()) {
            return false;
        }
        // 1. 每条 max ≥ min
        for (Range r : ranges) {
            if (r.getMax() != null && r.getMax() < r.getMin()) {
                ctx.disableDefaultConstraintViolation();
                ctx.buildConstraintViolationWithTemplate("max 必须 ≥ min").addConstraintViolation();
                return false;
            }
        }
        // 2. 按 min 升序
        ranges.sort(Comparator.comparing(Range::getMin));
        // 3. 不重叠 & 连续
        int coverStart = 0;
        for (Range r : ranges) {
            if (r.getMin() > coverStart) {
                ctx.disableDefaultConstraintViolation();
                ctx.buildConstraintViolationWithTemplate("区间不连续").addConstraintViolation();
                return false;
            }
            if (r.getMax() == null) {
                return true; // 已到无穷大，覆盖完成
            }
            coverStart = r.getMax(); // 当前 max 为开区间，所以下一个 min 必须等于它
        }
        // 4. 未覆盖到无穷大
        ctx.disableDefaultConstraintViolation();
        ctx.buildConstraintViolationWithTemplate("区间未覆盖到无穷大").addConstraintViolation();
        return false;
    }
}