package com.yy.yyzone.guildrank.dto.guildranktaskconfig;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
public class Range {
    @NotNull
    @Min(0)
    private Integer min;
    @Min(1)
    private Integer max;
    @NotNull
    @Min(0)
    private Integer required;
    @Valid
    private RangeScore score;
}
