package com.yy.yyzone.guildrank.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssociateCompanyNotify {
    /**
     * 新增关联
     */
    public static final int TYPE_ADD = 1;

    /**
     * 解除关联
     */
    public static final int TYPE_DEL = 2;

    /**
     * 1新增关联 2解除关联
     */
    private Integer type;

    /**
     * 公会uid
     */
    private Long guildUid;

    /**
     * 公司uid
     */
    private Long companyUid;

    /**
     * 操作者uid
     */
    private Long opUid;
}