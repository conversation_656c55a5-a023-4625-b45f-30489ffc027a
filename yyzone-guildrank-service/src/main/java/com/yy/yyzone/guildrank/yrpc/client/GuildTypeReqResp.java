// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: guildType.proto

package com.yy.yyzone.guildrank.yrpc.client;

public final class GuildTypeReqResp {
  private GuildTypeReqResp() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface batchGetGuildTypeReqOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.yrpc.client.batchGetGuildTypeReq)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated uint32 owUids = 1;</code>
     */
    java.util.List<Integer> getOwUidsList();
    /**
     * <code>repeated uint32 owUids = 1;</code>
     */
    int getOwUidsCount();
    /**
     * <code>repeated uint32 owUids = 1;</code>
     */
    int getOwUids(int index);

    /**
     * <code>map&lt;uint32, string&gt; extendData = 2;</code>
     */
    int getExtendDataCount();
    /**
     * <code>map&lt;uint32, string&gt; extendData = 2;</code>
     */
    boolean containsExtendData(
            int key);
    /**
     * Use {@link #getExtendDataMap()} instead.
     */
    @Deprecated
    java.util.Map<Integer, String>
    getExtendData();
    /**
     * <code>map&lt;uint32, string&gt; extendData = 2;</code>
     */
    java.util.Map<Integer, String>
    getExtendDataMap();
    /**
     * <code>map&lt;uint32, string&gt; extendData = 2;</code>
     */

    String getExtendDataOrDefault(
            int key,
            String defaultValue);
    /**
     * <code>map&lt;uint32, string&gt; extendData = 2;</code>
     */

    String getExtendDataOrThrow(
            int key);
  }
  /**
   * Protobuf type {@code com.yy.yyzone.guildrank.yrpc.client.batchGetGuildTypeReq}
   */
  public  static final class batchGetGuildTypeReq extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.yrpc.client.batchGetGuildTypeReq)
      batchGetGuildTypeReqOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use batchGetGuildTypeReq.newBuilder() to construct.
    private batchGetGuildTypeReq(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private batchGetGuildTypeReq() {
      owUids_ = java.util.Collections.emptyList();
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private batchGetGuildTypeReq(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                owUids_ = new java.util.ArrayList<Integer>();
                mutable_bitField0_ |= 0x00000001;
              }
              owUids_.add(input.readUInt32());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001) && input.getBytesUntilLimit() > 0) {
                owUids_ = new java.util.ArrayList<Integer>();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                owUids_.add(input.readUInt32());
              }
              input.popLimit(limit);
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                extendData_ = com.google.protobuf.MapField.newMapField(
                    ExtendDataDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000002;
              }
              com.google.protobuf.MapEntry<Integer, String>
              extendData__ = input.readMessage(
                  ExtendDataDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              extendData_.getMutableMap().put(
                  extendData__.getKey(), extendData__.getValue());
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          owUids_ = java.util.Collections.unmodifiableList(owUids_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return GuildTypeReqResp.internal_static_com_yy_yyzone_guildrank_yrpc_client_batchGetGuildTypeReq_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 2:
          return internalGetExtendData();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return GuildTypeReqResp.internal_static_com_yy_yyzone_guildrank_yrpc_client_batchGetGuildTypeReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              batchGetGuildTypeReq.class, Builder.class);
    }

    public static final int OWUIDS_FIELD_NUMBER = 1;
    private java.util.List<Integer> owUids_;
    /**
     * <code>repeated uint32 owUids = 1;</code>
     */
    public java.util.List<Integer>
        getOwUidsList() {
      return owUids_;
    }
    /**
     * <code>repeated uint32 owUids = 1;</code>
     */
    public int getOwUidsCount() {
      return owUids_.size();
    }
    /**
     * <code>repeated uint32 owUids = 1;</code>
     */
    public int getOwUids(int index) {
      return owUids_.get(index);
    }
    private int owUidsMemoizedSerializedSize = -1;

    public static final int EXTENDDATA_FIELD_NUMBER = 2;
    private static final class ExtendDataDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          Integer, String> defaultEntry =
              com.google.protobuf.MapEntry
              .<Integer, String>newDefaultInstance(
                  GuildTypeReqResp.internal_static_com_yy_yyzone_guildrank_yrpc_client_batchGetGuildTypeReq_ExtendDataEntry_descriptor,
                  com.google.protobuf.WireFormat.FieldType.UINT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "");
    }
    private com.google.protobuf.MapField<
        Integer, String> extendData_;
    private com.google.protobuf.MapField<Integer, String>
    internalGetExtendData() {
      if (extendData_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ExtendDataDefaultEntryHolder.defaultEntry);
      }
      return extendData_;
    }

    public int getExtendDataCount() {
      return internalGetExtendData().getMap().size();
    }
    /**
     * <code>map&lt;uint32, string&gt; extendData = 2;</code>
     */

    public boolean containsExtendData(
        int key) {
      
      return internalGetExtendData().getMap().containsKey(key);
    }
    /**
     * Use {@link #getExtendDataMap()} instead.
     */
    @Deprecated
    public java.util.Map<Integer, String> getExtendData() {
      return getExtendDataMap();
    }
    /**
     * <code>map&lt;uint32, string&gt; extendData = 2;</code>
     */

    public java.util.Map<Integer, String> getExtendDataMap() {
      return internalGetExtendData().getMap();
    }
    /**
     * <code>map&lt;uint32, string&gt; extendData = 2;</code>
     */

    public String getExtendDataOrDefault(
        int key,
        String defaultValue) {
      
      java.util.Map<Integer, String> map =
          internalGetExtendData().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;uint32, string&gt; extendData = 2;</code>
     */

    public String getExtendDataOrThrow(
        int key) {
      
      java.util.Map<Integer, String> map =
          internalGetExtendData().getMap();
      if (!map.containsKey(key)) {
        throw new IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (getOwUidsList().size() > 0) {
        output.writeUInt32NoTag(10);
        output.writeUInt32NoTag(owUidsMemoizedSerializedSize);
      }
      for (int i = 0; i < owUids_.size(); i++) {
        output.writeUInt32NoTag(owUids_.get(i));
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetExtendData(),
          ExtendDataDefaultEntryHolder.defaultEntry,
          2);
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < owUids_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(owUids_.get(i));
        }
        size += dataSize;
        if (!getOwUidsList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        owUidsMemoizedSerializedSize = dataSize;
      }
      for (java.util.Map.Entry<Integer, String> entry
           : internalGetExtendData().getMap().entrySet()) {
        com.google.protobuf.MapEntry<Integer, String>
        extendData__ = ExtendDataDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(2, extendData__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof batchGetGuildTypeReq)) {
        return super.equals(obj);
      }
      batchGetGuildTypeReq other = (batchGetGuildTypeReq) obj;

      boolean result = true;
      result = result && getOwUidsList()
          .equals(other.getOwUidsList());
      result = result && internalGetExtendData().equals(
          other.internalGetExtendData());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getOwUidsCount() > 0) {
        hash = (37 * hash) + OWUIDS_FIELD_NUMBER;
        hash = (53 * hash) + getOwUidsList().hashCode();
      }
      if (!internalGetExtendData().getMap().isEmpty()) {
        hash = (37 * hash) + EXTENDDATA_FIELD_NUMBER;
        hash = (53 * hash) + internalGetExtendData().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static batchGetGuildTypeReq parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static batchGetGuildTypeReq parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static batchGetGuildTypeReq parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static batchGetGuildTypeReq parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static batchGetGuildTypeReq parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static batchGetGuildTypeReq parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static batchGetGuildTypeReq parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static batchGetGuildTypeReq parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static batchGetGuildTypeReq parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static batchGetGuildTypeReq parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static batchGetGuildTypeReq parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static batchGetGuildTypeReq parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(batchGetGuildTypeReq prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.yyzone.guildrank.yrpc.client.batchGetGuildTypeReq}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.yrpc.client.batchGetGuildTypeReq)
        batchGetGuildTypeReqOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return GuildTypeReqResp.internal_static_com_yy_yyzone_guildrank_yrpc_client_batchGetGuildTypeReq_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 2:
            return internalGetExtendData();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 2:
            return internalGetMutableExtendData();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return GuildTypeReqResp.internal_static_com_yy_yyzone_guildrank_yrpc_client_batchGetGuildTypeReq_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                batchGetGuildTypeReq.class, Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.yrpc.client.GuildTypeReqResp.batchGetGuildTypeReq.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        owUids_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        internalGetMutableExtendData().clear();
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return GuildTypeReqResp.internal_static_com_yy_yyzone_guildrank_yrpc_client_batchGetGuildTypeReq_descriptor;
      }

      @Override
      public batchGetGuildTypeReq getDefaultInstanceForType() {
        return batchGetGuildTypeReq.getDefaultInstance();
      }

      @Override
      public batchGetGuildTypeReq build() {
        batchGetGuildTypeReq result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public batchGetGuildTypeReq buildPartial() {
        batchGetGuildTypeReq result = new batchGetGuildTypeReq(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) == 0x00000001)) {
          owUids_ = java.util.Collections.unmodifiableList(owUids_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.owUids_ = owUids_;
        result.extendData_ = internalGetExtendData();
        result.extendData_.makeImmutable();
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof batchGetGuildTypeReq) {
          return mergeFrom((batchGetGuildTypeReq)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(batchGetGuildTypeReq other) {
        if (other == batchGetGuildTypeReq.getDefaultInstance()) return this;
        if (!other.owUids_.isEmpty()) {
          if (owUids_.isEmpty()) {
            owUids_ = other.owUids_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureOwUidsIsMutable();
            owUids_.addAll(other.owUids_);
          }
          onChanged();
        }
        internalGetMutableExtendData().mergeFrom(
            other.internalGetExtendData());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        batchGetGuildTypeReq parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (batchGetGuildTypeReq) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<Integer> owUids_ = java.util.Collections.emptyList();
      private void ensureOwUidsIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          owUids_ = new java.util.ArrayList<Integer>(owUids_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated uint32 owUids = 1;</code>
       */
      public java.util.List<Integer>
          getOwUidsList() {
        return java.util.Collections.unmodifiableList(owUids_);
      }
      /**
       * <code>repeated uint32 owUids = 1;</code>
       */
      public int getOwUidsCount() {
        return owUids_.size();
      }
      /**
       * <code>repeated uint32 owUids = 1;</code>
       */
      public int getOwUids(int index) {
        return owUids_.get(index);
      }
      /**
       * <code>repeated uint32 owUids = 1;</code>
       */
      public Builder setOwUids(
          int index, int value) {
        ensureOwUidsIsMutable();
        owUids_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 owUids = 1;</code>
       */
      public Builder addOwUids(int value) {
        ensureOwUidsIsMutable();
        owUids_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 owUids = 1;</code>
       */
      public Builder addAllOwUids(
          Iterable<? extends Integer> values) {
        ensureOwUidsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, owUids_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 owUids = 1;</code>
       */
      public Builder clearOwUids() {
        owUids_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }

      private com.google.protobuf.MapField<
          Integer, String> extendData_;
      private com.google.protobuf.MapField<Integer, String>
      internalGetExtendData() {
        if (extendData_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ExtendDataDefaultEntryHolder.defaultEntry);
        }
        return extendData_;
      }
      private com.google.protobuf.MapField<Integer, String>
      internalGetMutableExtendData() {
        onChanged();;
        if (extendData_ == null) {
          extendData_ = com.google.protobuf.MapField.newMapField(
              ExtendDataDefaultEntryHolder.defaultEntry);
        }
        if (!extendData_.isMutable()) {
          extendData_ = extendData_.copy();
        }
        return extendData_;
      }

      public int getExtendDataCount() {
        return internalGetExtendData().getMap().size();
      }
      /**
       * <code>map&lt;uint32, string&gt; extendData = 2;</code>
       */

      public boolean containsExtendData(
          int key) {
        
        return internalGetExtendData().getMap().containsKey(key);
      }
      /**
       * Use {@link #getExtendDataMap()} instead.
       */
      @Deprecated
      public java.util.Map<Integer, String> getExtendData() {
        return getExtendDataMap();
      }
      /**
       * <code>map&lt;uint32, string&gt; extendData = 2;</code>
       */

      public java.util.Map<Integer, String> getExtendDataMap() {
        return internalGetExtendData().getMap();
      }
      /**
       * <code>map&lt;uint32, string&gt; extendData = 2;</code>
       */

      public String getExtendDataOrDefault(
          int key,
          String defaultValue) {
        
        java.util.Map<Integer, String> map =
            internalGetExtendData().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;uint32, string&gt; extendData = 2;</code>
       */

      public String getExtendDataOrThrow(
          int key) {
        
        java.util.Map<Integer, String> map =
            internalGetExtendData().getMap();
        if (!map.containsKey(key)) {
          throw new IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearExtendData() {
        internalGetMutableExtendData().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;uint32, string&gt; extendData = 2;</code>
       */

      public Builder removeExtendData(
          int key) {
        
        internalGetMutableExtendData().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @Deprecated
      public java.util.Map<Integer, String>
      getMutableExtendData() {
        return internalGetMutableExtendData().getMutableMap();
      }
      /**
       * <code>map&lt;uint32, string&gt; extendData = 2;</code>
       */
      public Builder putExtendData(
          int key,
          String value) {
        
        if (value == null) { throw new NullPointerException(); }
        internalGetMutableExtendData().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;uint32, string&gt; extendData = 2;</code>
       */

      public Builder putAllExtendData(
          java.util.Map<Integer, String> values) {
        internalGetMutableExtendData().getMutableMap()
            .putAll(values);
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.yrpc.client.batchGetGuildTypeReq)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.yrpc.client.batchGetGuildTypeReq)
    private static final batchGetGuildTypeReq DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new batchGetGuildTypeReq();
    }

    public static batchGetGuildTypeReq getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<batchGetGuildTypeReq>
        PARSER = new com.google.protobuf.AbstractParser<batchGetGuildTypeReq>() {
      @Override
      public batchGetGuildTypeReq parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new batchGetGuildTypeReq(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<batchGetGuildTypeReq> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<batchGetGuildTypeReq> getParserForType() {
      return PARSER;
    }

    @Override
    public batchGetGuildTypeReq getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface batchGetGuildTypeRespOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.yrpc.client.batchGetGuildTypeResp)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 result = 1;</code>
     */
    int getResult();

    /**
     * <pre>
     * 0-普通 1-星级 2-金牌
     * </pre>
     *
     * <code>map&lt;uint32, uint32&gt; types = 2;</code>
     */
    int getTypesCount();
    /**
     * <pre>
     * 0-普通 1-星级 2-金牌
     * </pre>
     *
     * <code>map&lt;uint32, uint32&gt; types = 2;</code>
     */
    boolean containsTypes(
            int key);
    /**
     * Use {@link #getTypesMap()} instead.
     */
    @Deprecated
    java.util.Map<Integer, Integer>
    getTypes();
    /**
     * <pre>
     * 0-普通 1-星级 2-金牌
     * </pre>
     *
     * <code>map&lt;uint32, uint32&gt; types = 2;</code>
     */
    java.util.Map<Integer, Integer>
    getTypesMap();
    /**
     * <pre>
     * 0-普通 1-星级 2-金牌
     * </pre>
     *
     * <code>map&lt;uint32, uint32&gt; types = 2;</code>
     */

    int getTypesOrDefault(
            int key,
            int defaultValue);
    /**
     * <pre>
     * 0-普通 1-星级 2-金牌
     * </pre>
     *
     * <code>map&lt;uint32, uint32&gt; types = 2;</code>
     */

    int getTypesOrThrow(
            int key);
  }
  /**
   * Protobuf type {@code com.yy.yyzone.guildrank.yrpc.client.batchGetGuildTypeResp}
   */
  public  static final class batchGetGuildTypeResp extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.yrpc.client.batchGetGuildTypeResp)
      batchGetGuildTypeRespOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use batchGetGuildTypeResp.newBuilder() to construct.
    private batchGetGuildTypeResp(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private batchGetGuildTypeResp() {
      result_ = 0;
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private batchGetGuildTypeResp(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              result_ = input.readUInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                types_ = com.google.protobuf.MapField.newMapField(
                    TypesDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000002;
              }
              com.google.protobuf.MapEntry<Integer, Integer>
              types__ = input.readMessage(
                  TypesDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              types_.getMutableMap().put(
                  types__.getKey(), types__.getValue());
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return GuildTypeReqResp.internal_static_com_yy_yyzone_guildrank_yrpc_client_batchGetGuildTypeResp_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 2:
          return internalGetTypes();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return GuildTypeReqResp.internal_static_com_yy_yyzone_guildrank_yrpc_client_batchGetGuildTypeResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              batchGetGuildTypeResp.class, Builder.class);
    }

    private int bitField0_;
    public static final int RESULT_FIELD_NUMBER = 1;
    private int result_;
    /**
     * <code>uint32 result = 1;</code>
     */
    public int getResult() {
      return result_;
    }

    public static final int TYPES_FIELD_NUMBER = 2;
    private static final class TypesDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          Integer, Integer> defaultEntry =
              com.google.protobuf.MapEntry
              .<Integer, Integer>newDefaultInstance(
                  GuildTypeReqResp.internal_static_com_yy_yyzone_guildrank_yrpc_client_batchGetGuildTypeResp_TypesEntry_descriptor,
                  com.google.protobuf.WireFormat.FieldType.UINT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.UINT32,
                  0);
    }
    private com.google.protobuf.MapField<
        Integer, Integer> types_;
    private com.google.protobuf.MapField<Integer, Integer>
    internalGetTypes() {
      if (types_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            TypesDefaultEntryHolder.defaultEntry);
      }
      return types_;
    }

    public int getTypesCount() {
      return internalGetTypes().getMap().size();
    }
    /**
     * <pre>
     * 0-普通 1-星级 2-金牌
     * </pre>
     *
     * <code>map&lt;uint32, uint32&gt; types = 2;</code>
     */

    public boolean containsTypes(
        int key) {
      
      return internalGetTypes().getMap().containsKey(key);
    }
    /**
     * Use {@link #getTypesMap()} instead.
     */
    @Deprecated
    public java.util.Map<Integer, Integer> getTypes() {
      return getTypesMap();
    }
    /**
     * <pre>
     * 0-普通 1-星级 2-金牌
     * </pre>
     *
     * <code>map&lt;uint32, uint32&gt; types = 2;</code>
     */

    public java.util.Map<Integer, Integer> getTypesMap() {
      return internalGetTypes().getMap();
    }
    /**
     * <pre>
     * 0-普通 1-星级 2-金牌
     * </pre>
     *
     * <code>map&lt;uint32, uint32&gt; types = 2;</code>
     */

    public int getTypesOrDefault(
        int key,
        int defaultValue) {
      
      java.util.Map<Integer, Integer> map =
          internalGetTypes().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     * 0-普通 1-星级 2-金牌
     * </pre>
     *
     * <code>map&lt;uint32, uint32&gt; types = 2;</code>
     */

    public int getTypesOrThrow(
        int key) {
      
      java.util.Map<Integer, Integer> map =
          internalGetTypes().getMap();
      if (!map.containsKey(key)) {
        throw new IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (result_ != 0) {
        output.writeUInt32(1, result_);
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetTypes(),
          TypesDefaultEntryHolder.defaultEntry,
          2);
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (result_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, result_);
      }
      for (java.util.Map.Entry<Integer, Integer> entry
           : internalGetTypes().getMap().entrySet()) {
        com.google.protobuf.MapEntry<Integer, Integer>
        types__ = TypesDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(2, types__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof batchGetGuildTypeResp)) {
        return super.equals(obj);
      }
      batchGetGuildTypeResp other = (batchGetGuildTypeResp) obj;

      boolean result = true;
      result = result && (getResult()
          == other.getResult());
      result = result && internalGetTypes().equals(
          other.internalGetTypes());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + getResult();
      if (!internalGetTypes().getMap().isEmpty()) {
        hash = (37 * hash) + TYPES_FIELD_NUMBER;
        hash = (53 * hash) + internalGetTypes().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static batchGetGuildTypeResp parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static batchGetGuildTypeResp parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static batchGetGuildTypeResp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static batchGetGuildTypeResp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static batchGetGuildTypeResp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static batchGetGuildTypeResp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static batchGetGuildTypeResp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static batchGetGuildTypeResp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static batchGetGuildTypeResp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static batchGetGuildTypeResp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static batchGetGuildTypeResp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static batchGetGuildTypeResp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(batchGetGuildTypeResp prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.yyzone.guildrank.yrpc.client.batchGetGuildTypeResp}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.yrpc.client.batchGetGuildTypeResp)
        batchGetGuildTypeRespOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return GuildTypeReqResp.internal_static_com_yy_yyzone_guildrank_yrpc_client_batchGetGuildTypeResp_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 2:
            return internalGetTypes();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 2:
            return internalGetMutableTypes();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return GuildTypeReqResp.internal_static_com_yy_yyzone_guildrank_yrpc_client_batchGetGuildTypeResp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                batchGetGuildTypeResp.class, Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.yrpc.client.GuildTypeReqResp.batchGetGuildTypeResp.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        result_ = 0;

        internalGetMutableTypes().clear();
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return GuildTypeReqResp.internal_static_com_yy_yyzone_guildrank_yrpc_client_batchGetGuildTypeResp_descriptor;
      }

      @Override
      public batchGetGuildTypeResp getDefaultInstanceForType() {
        return batchGetGuildTypeResp.getDefaultInstance();
      }

      @Override
      public batchGetGuildTypeResp build() {
        batchGetGuildTypeResp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public batchGetGuildTypeResp buildPartial() {
        batchGetGuildTypeResp result = new batchGetGuildTypeResp(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.result_ = result_;
        result.types_ = internalGetTypes();
        result.types_.makeImmutable();
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof batchGetGuildTypeResp) {
          return mergeFrom((batchGetGuildTypeResp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(batchGetGuildTypeResp other) {
        if (other == batchGetGuildTypeResp.getDefaultInstance()) return this;
        if (other.getResult() != 0) {
          setResult(other.getResult());
        }
        internalGetMutableTypes().mergeFrom(
            other.internalGetTypes());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        batchGetGuildTypeResp parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (batchGetGuildTypeResp) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int result_ ;
      /**
       * <code>uint32 result = 1;</code>
       */
      public int getResult() {
        return result_;
      }
      /**
       * <code>uint32 result = 1;</code>
       */
      public Builder setResult(int value) {
        
        result_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 result = 1;</code>
       */
      public Builder clearResult() {
        
        result_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.MapField<
          Integer, Integer> types_;
      private com.google.protobuf.MapField<Integer, Integer>
      internalGetTypes() {
        if (types_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              TypesDefaultEntryHolder.defaultEntry);
        }
        return types_;
      }
      private com.google.protobuf.MapField<Integer, Integer>
      internalGetMutableTypes() {
        onChanged();;
        if (types_ == null) {
          types_ = com.google.protobuf.MapField.newMapField(
              TypesDefaultEntryHolder.defaultEntry);
        }
        if (!types_.isMutable()) {
          types_ = types_.copy();
        }
        return types_;
      }

      public int getTypesCount() {
        return internalGetTypes().getMap().size();
      }
      /**
       * <pre>
       * 0-普通 1-星级 2-金牌
       * </pre>
       *
       * <code>map&lt;uint32, uint32&gt; types = 2;</code>
       */

      public boolean containsTypes(
          int key) {
        
        return internalGetTypes().getMap().containsKey(key);
      }
      /**
       * Use {@link #getTypesMap()} instead.
       */
      @Deprecated
      public java.util.Map<Integer, Integer> getTypes() {
        return getTypesMap();
      }
      /**
       * <pre>
       * 0-普通 1-星级 2-金牌
       * </pre>
       *
       * <code>map&lt;uint32, uint32&gt; types = 2;</code>
       */

      public java.util.Map<Integer, Integer> getTypesMap() {
        return internalGetTypes().getMap();
      }
      /**
       * <pre>
       * 0-普通 1-星级 2-金牌
       * </pre>
       *
       * <code>map&lt;uint32, uint32&gt; types = 2;</code>
       */

      public int getTypesOrDefault(
          int key,
          int defaultValue) {
        
        java.util.Map<Integer, Integer> map =
            internalGetTypes().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       * 0-普通 1-星级 2-金牌
       * </pre>
       *
       * <code>map&lt;uint32, uint32&gt; types = 2;</code>
       */

      public int getTypesOrThrow(
          int key) {
        
        java.util.Map<Integer, Integer> map =
            internalGetTypes().getMap();
        if (!map.containsKey(key)) {
          throw new IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearTypes() {
        internalGetMutableTypes().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       * 0-普通 1-星级 2-金牌
       * </pre>
       *
       * <code>map&lt;uint32, uint32&gt; types = 2;</code>
       */

      public Builder removeTypes(
          int key) {
        
        internalGetMutableTypes().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @Deprecated
      public java.util.Map<Integer, Integer>
      getMutableTypes() {
        return internalGetMutableTypes().getMutableMap();
      }
      /**
       * <pre>
       * 0-普通 1-星级 2-金牌
       * </pre>
       *
       * <code>map&lt;uint32, uint32&gt; types = 2;</code>
       */
      public Builder putTypes(
          int key,
          int value) {
        
        
        internalGetMutableTypes().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       * 0-普通 1-星级 2-金牌
       * </pre>
       *
       * <code>map&lt;uint32, uint32&gt; types = 2;</code>
       */

      public Builder putAllTypes(
          java.util.Map<Integer, Integer> values) {
        internalGetMutableTypes().getMutableMap()
            .putAll(values);
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.yrpc.client.batchGetGuildTypeResp)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.yrpc.client.batchGetGuildTypeResp)
    private static final batchGetGuildTypeResp DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new batchGetGuildTypeResp();
    }

    public static batchGetGuildTypeResp getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<batchGetGuildTypeResp>
        PARSER = new com.google.protobuf.AbstractParser<batchGetGuildTypeResp>() {
      @Override
      public batchGetGuildTypeResp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new batchGetGuildTypeResp(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<batchGetGuildTypeResp> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<batchGetGuildTypeResp> getParserForType() {
      return PARSER;
    }

    @Override
    public batchGetGuildTypeResp getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_yrpc_client_batchGetGuildTypeReq_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_yrpc_client_batchGetGuildTypeReq_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_yrpc_client_batchGetGuildTypeReq_ExtendDataEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_yrpc_client_batchGetGuildTypeReq_ExtendDataEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_yrpc_client_batchGetGuildTypeResp_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_yrpc_client_batchGetGuildTypeResp_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_yrpc_client_batchGetGuildTypeResp_TypesEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_yrpc_client_batchGetGuildTypeResp_TypesEntry_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    String[] descriptorData = {
      "\n\017guildType.proto\022#com.yy.yyzone.guildra" +
      "nk.yrpc.client\032\turi.proto\"\275\001\n\024batchGetGu" +
      "ildTypeReq\022\016\n\006owUids\030\001 \003(\r\022]\n\nextendData" +
      "\030\002 \003(\0132I.com.yy.yyzone.guildrank.yrpc.cl" +
      "ient.batchGetGuildTypeReq.ExtendDataEntr" +
      "y\0321\n\017ExtendDataEntry\022\013\n\003key\030\001 \001(\r\022\r\n\005val" +
      "ue\030\002 \001(\t:\0028\001:\003\310>\001\"\260\001\n\025batchGetGuildTypeR" +
      "esp\022\016\n\006result\030\001 \001(\r\022T\n\005types\030\002 \003(\0132E.com" +
      ".yy.yyzone.guildrank.yrpc.client.batchGe" +
      "tGuildTypeResp.TypesEntry\032,\n\nTypesEntry\022" +
      "\013\n\003key\030\001 \001(\r\022\r\n\005value\030\002 \001(\r:\0028\001:\003\310>\0022\237\001\n" +
      "\020GuildTypeService\022\212\001\n\021batchGetGuildType\022" +
      "9.com.yy.yyzone.guildrank.yrpc.client.ba" +
      "tchGetGuildTypeReq\032:.com.yy.yyzone.guild" +
      "rank.yrpc.client.batchGetGuildTypeRespB\022" +
      "B\020GuildTypeReqRespb\006proto3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          yyp.Uri.getDescriptor(),
        }, assigner);
    internal_static_com_yy_yyzone_guildrank_yrpc_client_batchGetGuildTypeReq_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yy_yyzone_guildrank_yrpc_client_batchGetGuildTypeReq_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_yrpc_client_batchGetGuildTypeReq_descriptor,
        new String[] { "OwUids", "ExtendData", });
    internal_static_com_yy_yyzone_guildrank_yrpc_client_batchGetGuildTypeReq_ExtendDataEntry_descriptor =
      internal_static_com_yy_yyzone_guildrank_yrpc_client_batchGetGuildTypeReq_descriptor.getNestedTypes().get(0);
    internal_static_com_yy_yyzone_guildrank_yrpc_client_batchGetGuildTypeReq_ExtendDataEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_yrpc_client_batchGetGuildTypeReq_ExtendDataEntry_descriptor,
        new String[] { "Key", "Value", });
    internal_static_com_yy_yyzone_guildrank_yrpc_client_batchGetGuildTypeResp_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yy_yyzone_guildrank_yrpc_client_batchGetGuildTypeResp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_yrpc_client_batchGetGuildTypeResp_descriptor,
        new String[] { "Result", "Types", });
    internal_static_com_yy_yyzone_guildrank_yrpc_client_batchGetGuildTypeResp_TypesEntry_descriptor =
      internal_static_com_yy_yyzone_guildrank_yrpc_client_batchGetGuildTypeResp_descriptor.getNestedTypes().get(0);
    internal_static_com_yy_yyzone_guildrank_yrpc_client_batchGetGuildTypeResp_TypesEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_yrpc_client_batchGetGuildTypeResp_TypesEntry_descriptor,
        new String[] { "Key", "Value", });
    com.google.protobuf.ExtensionRegistry registry =
        com.google.protobuf.ExtensionRegistry.newInstance();
    registry.add(yyp.Uri.uri);
    com.google.protobuf.Descriptors.FileDescriptor
        .internalUpdateFileDescriptor(descriptor, registry);
    yyp.Uri.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
