package com.yy.yyzone.guildrank.service;

import com.yy.yyzone.guildrank.db.custom.mapper.DmYyGuildSegmentScoreMonthExtMapper;
import com.yy.yyzone.guildrank.db.gen.mapper.BigdaSyncDetailMapper;
import com.yy.yyzone.guildrank.db.gen.mapper.DmYyGuildSegmentScoreMonthTmpMapper;
import com.yy.yyzone.guildrank.db.gen.model.BigdaSyncDetail;
import com.yy.yyzone.guildrank.db.gen.model.BigdaSyncDetailExample;
import com.yy.yyzone.guildrank.db.gen.model.DmYyGuildSegmentScoreMonthTmp;
import com.yy.yyzone.guildrank.db.gen.model.DmYyGuildSegmentScoreMonthTmpExample;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * https://doc.yy.com/pages/viewpage.action?pageId=169610513
 * <AUTHOR>
 * @date 2022/01/07
 **/
@Service
@Slf4j
public class RankCheckService {

    @Autowired
    private BigdaSyncDetailMapper bigdaSyncDetailMapper;

    @Autowired
    private DmYyGuildSegmentScoreMonthTmpMapper tmpMapper;

    @Autowired
    private DmYyGuildSegmentScoreMonthExtMapper extMapper;

    @Autowired
    private MsgService msgService;

    public static final int SYNC_DONE = 0;
    public static final int CHECK_DONE = 1;
    public static final String TABLE_NAME = "data_out.dm_yy_guild_segment_score_month";

    /**
     * 校验 公会段位数据存在不在1~7段位之间的情况或公会段位数据存在1/2/3/4/5星级公会数量为0的情况
     */
    @Transactional(rollbackFor = {RuntimeException.class, Exception.class})
    public void rankCheck() {
        String currentDate = DateFormatUtils.format(DateUtils.addMonths(new Date(), -1), "yyyy-MM");
        BigdaSyncDetailExample example = new BigdaSyncDetailExample();
        example.createCriteria().andDtEqualTo(currentDate).andStatusEqualTo(SYNC_DONE).andTableNameEqualTo(TABLE_NAME);
        int count = bigdaSyncDetailMapper.countByExample(example);
        if (count > 0) {
            log.info("rankCheck sync done");
            DmYyGuildSegmentScoreMonthTmpExample tmpExample = new DmYyGuildSegmentScoreMonthTmpExample();
            tmpExample.createCriteria().andDtEqualTo(currentDate + "-01");
            List<DmYyGuildSegmentScoreMonthTmp> list = tmpMapper.selectByExample(tmpExample);

            boolean dataError = list.stream().map(DmYyGuildSegmentScoreMonthTmp::getPsSAll).anyMatch(v -> v > 7 || v < 1);

            Map<Integer, Long> cntMap = list.stream().collect(Collectors.groupingBy(DmYyGuildSegmentScoreMonthTmp::getPsSAll, Collectors.counting()));
            boolean numError = !cntMap.containsKey(1)|| !cntMap.containsKey(2) || !cntMap.containsKey(3)
                    || !cntMap.containsKey(4) || !cntMap.containsKey(5);
            if (dataError || numError) {
                log.error("rankCheck data Error, cntMap:{}, list:{}", cntMap, list);
                msgService.sendAlarmMsg(currentDate);
            } else {
                // copy to dm_yy_guild_segment_score_month
                extMapper.insertBatch(list);

                BigdaSyncDetail record = new BigdaSyncDetail();
                record.setStatus(CHECK_DONE);
                record.setDataTime(new Date());
                bigdaSyncDetailMapper.updateByExampleSelective(record, example);
            }
        }
    }

    public boolean rankCheckDone(String currentDate) {
        BigdaSyncDetailExample example = new BigdaSyncDetailExample();
        example.createCriteria().andDtEqualTo(currentDate).andStatusEqualTo(CHECK_DONE).andTableNameEqualTo(TABLE_NAME);
        return bigdaSyncDetailMapper.countByExample(example) > 0;
    }

    public boolean rankSyncDone(String currentDate) {
        BigdaSyncDetailExample example = new BigdaSyncDetailExample();
        example.createCriteria().andDtEqualTo(currentDate).andTableNameEqualTo(TABLE_NAME);
        return bigdaSyncDetailMapper.countByExample(example) > 0;
    }
}
