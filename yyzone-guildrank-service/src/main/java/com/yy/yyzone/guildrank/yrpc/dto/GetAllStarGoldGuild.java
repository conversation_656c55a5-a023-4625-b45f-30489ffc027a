package com.yy.yyzone.guildrank.yrpc.dto;

import com.yy.ent.commons.protopack.util.Uint;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/1/13
 */
public class GetAllStarGoldGuild {

    private Uint offset;
    private Uint limit;
    Map<Uint, String> extend;

    public Uint getOffset() {
        return offset;
    }

    public void setOffset(Uint offset) {
        this.offset = offset;
    }

    public Uint getLimit() {
        return limit;
    }

    public void setLimit(Uint limit) {
        this.limit = limit;
    }

    public Map<Uint, String> getExtend() {
        return extend;
    }

    public void setExtend(Map<Uint, String> extend) {
        this.extend = extend;
    }

    @Override
    public String toString() {
        return "GetAllStarGoldGuild{" +
                "offset=" + offset +
                ", limit=" + limit +
                ", extend=" + extend +
                '}';
    }
}
