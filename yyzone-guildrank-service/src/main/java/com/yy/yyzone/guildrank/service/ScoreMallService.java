package com.yy.yyzone.guildrank.service;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Stopwatch;
import com.google.common.collect.ImmutableMap;
import com.yy.ent.commons.protopack.util.Uint;
import com.yy.ent.mobile.metrics.MetricsStopWatch;
import com.yy.yyzone.guildrank.constant.PointConstant;
import com.yy.yyzone.guildrank.db.gen.mapper.GuildrankPointDetailMapper;
import com.yy.yyzone.guildrank.db.gen.mapper.GuildrankPointGrantLogMapper;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankPointDetail;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankPointDetailExample;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankPointGrantLog;
import com.yy.yyzone.guildrank.util.MyDateUtil;
import com.yy.yyzone.guildrank.yrpc.client.ScoreMallClient;
import com.yy.yyzone.guildrank.yrpc.dto.scoremall.AddScoreReq;
import com.yy.yyzone.guildrank.yrpc.dto.scoremall.AddScoreResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class ScoreMallService {
    /**
     * 货币类型
     */
    private static final Uint SCORE_TYPE = new Uint(245);
    private static final String APP_NAME = "yyzone_guildrank";

    @Reference(protocol = "yyp", owner = "scoreMall")
    private ScoreMallClient scoreMallClient;

    @Autowired
    private GuildrankPointDetailMapper pointDetailMapper;

    @Autowired
    private GuildrankPointGrantLogMapper grantLogMapper;

    /**
     * 发放积分
     */
    public Integer grantPoint() {
        GuildrankPointDetailExample example = new GuildrankPointDetailExample();
        example.createCriteria().andStatusEqualTo(PointConstant.Status.TO_BE_GRANT.getValue());
        List<GuildrankPointDetail> list = pointDetailMapper.selectByExample(example);
        log.info("grantPoint size:{}", list.size());
        if (list.isEmpty()) {
            return 0;
        }

        int count = 0;
        Stopwatch sw = Stopwatch.createStarted();
        for (GuildrankPointDetail p : list) {
            sw.reset().start();
            Integer result = null;
            log.info("grantPoint begin id:{}", p.getId());
            try {
                int updateCount = updateGrantStatus(p, PointConstant.Status.TO_BE_GRANT, PointConstant.Status.GRANTING, "发放中");
                if (updateCount < 1) {
                    log.info("grantPoint updateGrantStatus failed id:{}", p.getId());
                    result = -100;
                    continue;
                }

                AddScoreReq req = new AddScoreReq();
                req.setUid(p.getUid());
                req.setBizName(APP_NAME);
                req.setOrderNo(genOrderNo(p));
                req.setRemark(PointConstant.POINT_REMARK);
                req.setScoreType(SCORE_TYPE);
                req.setAddAmount(new Uint(p.getPoint()));
                req.setExtendInfo(ImmutableMap.of("dueDate", MyDateUtil.fmtDate(p.getDueDt()), "subType", "0"));
                log.info("grantPoint req:{}", req);
                AddScoreResp resp = scoreMallClient.addScore(req);
                result = resp.getResult().intValue();
                log.info("grantPoint resp:{}", resp);
                GuildrankPointGrantLog grantLog = new GuildrankPointGrantLog();
                grantLog.setDetailId(p.getId());
                grantLog.setUid(p.getUid());
                grantLog.setPoint(p.getPoint());
                grantLog.setReqOrderNo(req.getOrderNo());
                grantLog.setRspResult(result);
                grantLog.setRspErrMsg(resp.getErrMsg());
                grantLog.setRspExt(JSON.toJSONString(resp.getExtendInfo()));
                grantLog.setCreateTime(new Date());
                grantLogMapper.insertSelective(grantLog);
                if (result == 0) {
                    updateGrantStatus(p, PointConstant.Status.GRANTING, PointConstant.Status.GRANTED, "发放成功");
                    count++;
                } else {
                    updateGrantStatus(p, PointConstant.Status.GRANTING, PointConstant.Status.GRANT_FAIL, "发放失败，code:" + result);
                }

            } catch (Exception e) {
                log.error("grantPoint exception id:{}", p.getId(), e);
            } finally {
                log.info("grantPoint end id:{},result:{},elapsed:{}", p.getId(), result, sw.elapsed(TimeUnit.MILLISECONDS));
            }
        }

        return count;
    }

    private int updateGrantStatus(GuildrankPointDetail p, PointConstant.Status expectedStatus,
                                  PointConstant.Status resultStatus, String remark) {
        Date now = new Date();
        GuildrankPointDetail update = new GuildrankPointDetail();
        update.setUpdateTime(now);
        update.setGrantTime(now);
        update.setStatus(resultStatus.getValue());
        update.setRemark(remark);
        GuildrankPointDetailExample example = new GuildrankPointDetailExample();
        example.createCriteria().andIdEqualTo(p.getId()).andStatusEqualTo(expectedStatus.getValue());
        int count = pointDetailMapper.updateByExampleSelective(update, example);
        return count;
    }

    private String genOrderNo(GuildrankPointDetail p) {
        long delta = 0L;

        if (p.getGrantTime() != null && p.getCreateTime() != null) {
            delta = (System.currentTimeMillis() - p.getCreateTime().getTime()) / TimeUnit.MINUTES.toMillis(10);
        }
        return String.format("grp_%s_%s_%s_%s", p.getUid(), p.getType(), p.getTypeSerial(), delta);
    }

    /**
     * 有超时（5min）发放中的记录告警
     *
     * @return
     */
    public Integer hasGrantingAlarm() {
        MetricsStopWatch metricsStopWatch = MetricsStopWatch.startServerWatch().uri("Alarm/pointHasGrantingAlarm");
        Date max = DateUtils.addMinutes(new Date(), -5);
        GuildrankPointDetailExample example = new GuildrankPointDetailExample();
        example.createCriteria().andStatusEqualTo(PointConstant.Status.GRANTING.getValue()).andGrantTimeLessThanOrEqualTo(max);
        int count = pointDetailMapper.countByExample(example);
        if (count > 0) {
            metricsStopWatch.failCode().markDurationAndCode();
        } else {
            metricsStopWatch.successCode().markDurationAndCode();
        }
        return count;
    }

    /**
     * 有发放失败的记录告警
     *
     * @return
     */
    public Integer hasGrantFailAlarm() {
        MetricsStopWatch metricsStopWatch = MetricsStopWatch.startServerWatch().uri("Alarm/pointHasGrantFailAlarm");
        GuildrankPointDetailExample example = new GuildrankPointDetailExample();
        example.createCriteria().andStatusEqualTo(PointConstant.Status.GRANT_FAIL.getValue());
        int count = pointDetailMapper.countByExample(example);
        if (count > 0) {
            metricsStopWatch.failCode().markDurationAndCode();
        } else {
            metricsStopWatch.successCode().markDurationAndCode();
        }
        return count;
    }
}