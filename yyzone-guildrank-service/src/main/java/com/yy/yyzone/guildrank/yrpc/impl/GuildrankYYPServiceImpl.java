package com.yy.yyzone.guildrank.yrpc.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.yyzone.guildrank.api.*;
import com.yy.yyzone.guildrank.constant.RankConstant;
import com.yy.yyzone.guildrank.db.gen.mapper.DmYyGuildSegmentScoreMonthMapper;
import com.yy.yyzone.guildrank.db.gen.mapper.DmYySidSegmentScoreMonthMapper;
import com.yy.yyzone.guildrank.db.gen.model.DmYyGuildSegmentScoreMonth;
import com.yy.yyzone.guildrank.db.gen.model.DmYyGuildSegmentScoreMonthExample;
import com.yy.yyzone.guildrank.service.*;
import com.yy.yyzone.guildrank.util.MyDateUtil;
import com.yy.yyzone.guildrank.util.YrpcUtil;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Generated;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service(protocol = "yyp",registry = "reg-yyp")
@Generated("忽略代码规范检查")
public class GuildrankYYPServiceImpl implements GuildrankYYPService {

	private static Logger logger = LoggerFactory.getLogger(GuildrankYYPServiceImpl.class);

	@Autowired
	private DmYySidSegmentScoreMonthMapper dmYySidSegmentScoreMonthMapper;

	@Autowired
	private GuildRankService guildRankService;

	@Autowired
	private GuildRankV2Service guildRankV2Service;

	@Autowired
	private RankCacheService rankCacheService;

    @Autowired
    private RankCheckService rankCheckService;

    @Autowired
    private GuildRankTransService transService;

    @Autowired
    private GuildrankYYPV3Service yypv3Service;

	@Override
	public GuildRank.CurrentRankResp queryCurrentRank(GuildRank.CurrentRankReq req) {
        if(transService.useV3("GuildrankYYPServiceImpl.queryCurrentRank")) {
            return yypv3Service.queryCurrentRank(req);
        }

		int rank = rankCacheService.queryCurrentRank(req.getUid());
		return GuildRank.CurrentRankResp.newBuilder().setLevel(rank).setResult(0).build();
	}

    @Override
    public GuildRank.CurrentRankResp queryCurrentRankTest(GuildRank.CurrentRankReq req) {
	    logger.info(req.getUid()+"=======================");
        return GuildRank.CurrentRankResp.newBuilder().build();
    }


    private static Map<String, String> appidMap = new HashMap<>();
	static {
		appidMap.put("1001","adpnrfhav"); // 营收活动
	}

	@Override
	public GuildRank.QueryCurrentRankResp queryCurrentRankByFrom(GuildRank.QueryCurrentRankReq req) {
		String appid = req.getAppid();
		String sign = req.getSign();
		Long uid = req.getUid();
		logger.info("appid:{}, sign:{} , uid:{}", appid, sign, uid);

		if (StringUtils.isBlank(appid) || StringUtils.isBlank(sign)){
			return GuildRank.QueryCurrentRankResp.newBuilder().setResult(1).build();
		}
		String password = appidMap.get(appid);
		if (StringUtils.isBlank(password)){
			return GuildRank.QueryCurrentRankResp.newBuilder().setResult(2).build();
		}
		String mySign = DigestUtils.md5Hex(uid+"_"+password);
		if (!sign.equals(mySign)){
			return GuildRank.QueryCurrentRankResp.newBuilder().setResult(3).build();
		}

        if(transService.useV3("GuildrankYYPServiceImpl.queryCurrentRankByFrom")) {
            return yypv3Service.queryCurrentRankByFrom(req);
        }

		int rank = rankCacheService.queryCurrentRank(req.getUid());
		return GuildRank.QueryCurrentRankResp.newBuilder().setResult(0).setLevel(rank).build();
	}

	public static void main(String[] args) {
		long s=System.currentTimeMillis();
		System.out.println(DigestUtils.md5Hex("1000"+"_"+"adpnrfhav"));
		System.out.println(System.currentTimeMillis() -s);
		System.out.println(105 << 8 | 110);
		System.out.println(106 << 8 | 110);
	}


	@Override
	public YYPResp testPojoYYP(YYPReq req) {
		logger.info(req.getUid().intValue()+"");
		try {
			Thread.sleep(50L);
		} catch (InterruptedException e) {

		}
		YYPResp resp = new YYPResp();
		resp.setCode("22222");
		return resp;
	}

    @Override
    public GuildRank.BatchQueryCurrentRankResp batchQueryCurrentRank(GuildRank.BatchQueryCurrentRankReq req) {
        if(transService.useV3("GuildrankYYPServiceImpl.batchQueryCurrentRank")) {
            return yypv3Service.batchQueryCurrentRank(req);
        }

        List<Integer> uids = req.getUidList();
        List<Long> uidsLong = Lists.newArrayList();
        for (Integer uid : uids) {
            uidsLong.add(YrpcUtil.cppInt32ToLong(uid));
        }
		Map<Long, Integer> result = rankCacheService.batchQueryCurrentRank(uidsLong);
        Map<Integer, Integer> resp = Maps.newHashMap();
        for (Map.Entry<Long, Integer> entry : result.entrySet()) {
            resp.put(entry.getKey().intValue(),entry.getValue());
        }
        return GuildRank.BatchQueryCurrentRankResp.newBuilder().setResult(0).putAllUidLevel(resp).build();
    }

    @Override
    public GuildRank.BatchQueryGuildRankResp batchQueryGuildRank(GuildRank.BatchQueryGuildRankReq req) {
        if(transService.useV3("GuildrankYYPServiceImpl.batchQueryGuildRank")) {
            return yypv3Service.batchQueryGuildRank(req);
        }

        List<Integer> uids = req.getUidList();
        List<Long> uidsLong = Lists.newArrayList();
        for (Integer uid : uids) {
            uidsLong.add(YrpcUtil.cppInt32ToLong(uid));
        }
		int isNew = guildRankV2Service.hasUpdate() ? RankConstant.UPDATE : RankConstant.NOT_UPDATE;
        Map<Long, Integer> result = rankCacheService.batchQueryCurrentRank(uidsLong);
        Map<Integer, Integer> resp = Maps.newHashMap();
        for (Map.Entry<Long, Integer> entry : result.entrySet()) {
            resp.put(entry.getKey().intValue(),entry.getValue());
        }
        return GuildRank.BatchQueryGuildRankResp.newBuilder().setResult(0)
                .setIsNew(isNew).putAllUidLevel(resp).build();
    }


    @Autowired
    private DmYyGuildSegmentScoreMonthMapper dmYyGuildSegmentScoreMonthMapper;

    @Override
    public AllGuildRank.QueryAllGuildRankByOrderResp queryAllGuildRankByOrder(AllGuildRank.QueryAllGuildRankByOrderReq req) {
        if (transService.useV3Data(req.getMonth()) && transService.useV3("GuildrankYYPServiceImpl.queryAllGuildRankByOrder")) {
            return yypv3Service.queryAllGuildRankByOrder(req);
        }

        String month = req.getMonth();
		month = month + "-01";
        int isNew = 0;
        if (guildRankV2Service.hasUpdate()){
            isNew = 1;
			DmYyGuildSegmentScoreMonthExample example = new DmYyGuildSegmentScoreMonthExample();
			example.createCriteria().andDtEqualTo(month);
			if (req.getOrderType() == 1){
				example.setOrderByClause("month_diamond desc");
			}
			List<DmYyGuildSegmentScoreMonth> list = dmYyGuildSegmentScoreMonthMapper.selectByExample(example);
			Map<Long, Long> uidId = new HashMap<>();
			List<Long> result = Lists.newArrayList();
			for (DmYyGuildSegmentScoreMonth scoreMonth : list) {
				Long uid = scoreMonth.getSidOwnerid();
				if (uidId.containsKey(uid)){
					if (scoreMonth.getId() > uidId.get(uid)){
						result.remove(uid);
						result.add(uid);
					}
				}else {
					result.add(uid);
					uidId.put(uid, scoreMonth.getId());
				}
			}
			List<Integer> resultInt = Lists.newArrayList();
			for (Long aLong : result) {
				resultInt.add(aLong.intValue());
			}
			return AllGuildRank.QueryAllGuildRankByOrderResp.newBuilder().setIsNew(isNew).addAllOwuids(resultInt).build();
        }
		return AllGuildRank.QueryAllGuildRankByOrderResp.newBuilder().setIsNew(isNew).build();
    }

	@Override
	public AllGuildRank.QueryAllGuildRankResp queryAllGuildRank(AllGuildRank.QueryAllGuildRankReq req) {
        if (transService.useV3Data(req.getMonth()) && transService.useV3("GuildrankYYPServiceImpl.queryAllGuildRank")) {
            return yypv3Service.queryAllGuildRank(req);
        }

        String month = req.getMonth();
		month = month + "-01";
		int isNew = 0;
		if (guildRankV2Service.hasUpdate()){
			isNew = 1;
			DmYyGuildSegmentScoreMonthExample example = new DmYyGuildSegmentScoreMonthExample();
			example.createCriteria().andDtEqualTo(month);
			List<DmYyGuildSegmentScoreMonth> list = dmYyGuildSegmentScoreMonthMapper.selectByExample(example);
			Map<Long, Long> uidId = Maps.newHashMap();
			Map<Long, AllGuildRank.GuildRankVO> result = Maps.newHashMap();
			for (DmYyGuildSegmentScoreMonth scoreMonth : list) {
				Long uid = scoreMonth.getSidOwnerid();
				if (uidId.containsKey(uid)){
					if (scoreMonth.getId() > uidId.get(uid)){
						result.remove(uid);
						result.put(uid, guildRankDAO2PB(scoreMonth));
					}
				}else {
					result.put(uid, guildRankDAO2PB(scoreMonth));
					uidId.put(uid, scoreMonth.getId());
				}
			}

			return AllGuildRank.QueryAllGuildRankResp.newBuilder().setIsNew(isNew).addAllDataList(result.values()).build();
		}
		return AllGuildRank.QueryAllGuildRankResp.newBuilder().setIsNew(isNew).build();
	}

	@Override
	public GuildRankI64.BatchQueryCurrentRankResp64 batchQueryCurrentRankNew(GuildRankI64.BatchQueryCurrentRankReq64 req) {
        if(transService.useV3("GuildrankYYPServiceImpl.batchQueryCurrentRankNew")) {
            return yypv3Service.batchQueryCurrentRankNew(req);
        }

        List<Long> uidsLong = req.getUidList();
		Map<Long, Integer> result = rankCacheService.batchQueryCurrentRank(uidsLong);
		return GuildRankI64.BatchQueryCurrentRankResp64.newBuilder().setResult(0).putAllUidLevel(result).build();
	}

	@Override
	public GuildRankI64.BatchQueryGuildRankResp64 batchQueryGuildRankNew(GuildRankI64.BatchQueryGuildRankReq64 req) {
        if(transService.useV3("GuildrankYYPServiceImpl.batchQueryGuildRankNew")) {
            return yypv3Service.batchQueryGuildRankNew(req);
        }

		List<Long> uidsLong = req.getUidList();
		int isNew = guildRankV2Service.hasUpdate() ? RankConstant.UPDATE : RankConstant.NOT_UPDATE;
		Map<Long, Integer> result = rankCacheService.batchQueryCurrentRank(uidsLong);
		return GuildRankI64.BatchQueryGuildRankResp64.newBuilder().setResult(0)
				.setIsNew(isNew).putAllUidLevel(result).build();
	}

	@Override
	public AllGuildRankI64.QueryAllGuildRankByOrderResp queryAllGuildRankByOrderNew(AllGuildRankI64.QueryAllGuildRankByOrderReq req) {
        /**
         * 目前找到的调用方是manage的com.yy.zone.manage.service.job.GuildBoardJob#blueIncome（公会蓝钻排行）
         * 只是拿蓝钻排序用，但新的bi表yy_dm_entity_guild_cmp_health_analysis_di并没有相关字段且是以主体为维度的
         * 因此v3逻辑暂时仅修改是否更新数据的逻辑
         */

        String month = req.getMonth();
		month = month + "-01";
		int isNew = 0;
        boolean hasUpdate = rankCheckService.rankSyncDone(DateFormatUtils.format(MyDateUtil.lastMonth(), "yyyy-MM"));
		if (hasUpdate){
			isNew = 1;
			DmYyGuildSegmentScoreMonthExample example = new DmYyGuildSegmentScoreMonthExample();
			example.createCriteria().andDtEqualTo(month);
			if (req.getOrderType() == 1){
				example.setOrderByClause("month_diamond desc");
			}
			List<DmYyGuildSegmentScoreMonth> list = dmYyGuildSegmentScoreMonthMapper.selectByExample(example);
			Map<Long, Long> uidId = new HashMap<>();
			List<Long> result = Lists.newArrayList();
			for (DmYyGuildSegmentScoreMonth scoreMonth : list) {
				Long uid = scoreMonth.getSidOwnerid();
				if (uidId.containsKey(uid)){
					if (scoreMonth.getId() > uidId.get(uid)){
						result.remove(uid);
						result.add(uid);
					}
				}else {
					result.add(uid);
					uidId.put(uid, scoreMonth.getId());
				}
			}
			return AllGuildRankI64.QueryAllGuildRankByOrderResp.newBuilder().setIsNew(isNew).addAllOwuids(result).build();
		}
		return AllGuildRankI64.QueryAllGuildRankByOrderResp.newBuilder().setIsNew(isNew).build();
	}

	private AllGuildRank.GuildRankVO guildRankDAO2PB(DmYyGuildSegmentScoreMonth dao){
		Map<String, String> map = Maps.newHashMap();
		// setValue
		map.put("id", stringValue(dao.getId()));
		map.put("sidOwnerid", stringValue(dao.getSidOwnerid()));
		map.put("sidOwyyid", stringValue(dao.getSidOwyyid()));
		map.put("monthDiamond", stringValue(dao.getMonthDiamond()));
		map.put("monthDiamondRr", stringValue(dao.getMonthDiamondRr()));
		map.put("validLiveUv", stringValue(dao.getValidLiveUv()));
		map.put("validLiveUvRr", stringValue(dao.getValidLiveUvRr()));
		map.put("acu300Uv", stringValue(dao.getAcu300Uv()));
		map.put("acu300UvRr", stringValue(dao.getAcu300UvRr()));
		map.put("acu50300Uv", stringValue(dao.getAcu50300Uv()));
		map.put("acu50300UvRr", stringValue(dao.getAcu50300UvRr()));
		map.put("acu1050Uv", stringValue(dao.getAcu1050Uv()));
		map.put("acu1050UvRr", stringValue(dao.getAcu1050UvRr()));
		map.put("acu10Uv", stringValue(dao.getAcu10Uv()));
		map.put("acu10UvRr", stringValue(dao.getAcu10UvRr()));
		map.put("breakAUv", stringValue(dao.getBreakAUv()));
		map.put("breakBUv", stringValue(dao.getBreakBUv()));
		map.put("breakCUv", stringValue(dao.getBreakCUv()));
		map.put("breakEUv", stringValue(dao.getBreakEUv()));
		map.put("liveUv", stringValue(dao.getLiveUv()));
		map.put("oldBreakUv", stringValue(dao.getOldBreakUv()));
		map.put("breakUvPp", stringValue(dao.getBreakUvPp()));
		map.put("breakUvPpRr", stringValue(dao.getBreakUvPpRr()));
		map.put("psSAll", stringValue(dao.getPsSAll()));
		map.put("psSAllRr", stringValue(dao.getPsSAllRr()));
		map.put("psSMonthDiamond", stringValue(dao.getPsSMonthDiamond()));
		map.put("sprMonthDiamond", stringValue(dao.getSprMonthDiamond()));
		map.put("psSValidLiveUv", stringValue(dao.getPsSValidLiveUv()));
		map.put("sprValidLiveUv", stringValue(dao.getSprValidLiveUv()));
		map.put("psAcu", stringValue(dao.getPsAcu()));
		map.put("psSAcu", stringValue(dao.getPsSAcu()));
		map.put("sprAcu", stringValue(dao.getSprAcu()));
		map.put("psSLiveUv", stringValue(dao.getPsSLiveUv()));
		map.put("psSBreakUvPp", stringValue(dao.getPsSBreakUvPp()));
		map.put("sprBreakUvPp", stringValue(dao.getSprBreakUvPp()));
		map.put("dt", stringValue(dao.getDt()));
		return AllGuildRank.GuildRankVO.newBuilder().putAllData(map).build();
	}

	private <T> String stringValue(T t) {
		return t + "";
	}
}
