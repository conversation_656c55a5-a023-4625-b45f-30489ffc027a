package com.yy.yyzone.guildrank.yrpc.client;

import com.yy.yyzone.guildrank.yrpc.dto.scoremall.AddScoreReq;
import com.yy.yyzone.guildrank.yrpc.dto.scoremall.AddScoreResp;
import org.apache.dubbo.common.annotation.Yrpc;

public interface ScoreMallClient {
    /**
     * 给用户添加货币数量 https://doc.yy.com/pages/viewpage.action?pageId=180854015
     *
     * @param req
     * @return
     */
    @Yrpc(reqUri = 10101 << 8 | 63, resUri = 10102 << 8 | 63)
    AddScoreResp addScore(AddScoreReq req);
}
