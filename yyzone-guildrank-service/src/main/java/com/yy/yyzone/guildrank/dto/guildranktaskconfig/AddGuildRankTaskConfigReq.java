package com.yy.yyzone.guildrank.dto.guildranktaskconfig;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
public class AddGuildRankTaskConfigReq {
    @Valid
    @NotNull
    private GuildRankAllTaskConfig allTaskConfig;
    @Min(0)
    @NotNull
    /**
     * 传入上一次返回的 version
     */
    private Long version;
}
