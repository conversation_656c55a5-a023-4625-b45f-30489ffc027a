package com.yy.yyzone.guildrank.enums;

public enum GuildPlatformActivityTask {
    SPRING_HEADLINE("春季头条任务", "spring_headline_task_detail"),
    SUMMER_HEADLINE("夏季头条任务", "summer_headline_task_detail"),
    GUILD_RACE_S1("公会赛S1任务", "guild_race_s1_task_detail"),
    GUILD_RACE_S2("公会赛S2任务", "guild_race_s2_task_detail"),
    GUILD_RACE_MAIN("公会赛正赛任务", "guild_race_main_task_detail"),
    PERSONAL_RACE_MAIN("个人赛正赛任务", "personal_race_main_task_detail");

    private final String displayName;
    private final String columnName;

    GuildPlatformActivityTask(String displayName, String columnName) {
        this.displayName = displayName;
        this.columnName = columnName;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getColumnName() {
        return columnName;
    }

    public static GuildPlatformActivityTask fromDisplayName(String displayName) {
        for (GuildPlatformActivityTask type : values()) {
            if (type.displayName.equals(displayName)) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的任务类型: " + displayName);
    }
}