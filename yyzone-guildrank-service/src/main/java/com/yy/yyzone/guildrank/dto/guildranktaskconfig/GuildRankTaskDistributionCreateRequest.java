package com.yy.yyzone.guildrank.dto.guildranktaskconfig;

import lombok.Data;

import javax.validation.constraints.*;
import java.util.List;
import java.util.Set;

@Data
public class  GuildRankTaskDistributionCreateRequest {
    @NotEmpty(message = "UID列表不能为空")
    @Size(min = 1)
    private Set<@NotNull(message = "UID不能为空") @Positive(message = "UID必须为正数") Long> uids;

    @NotNull(message = "日期不能为空")
    @Min(value = 100000, message = "日期格式需为yyyyMM")
    @Max(value = 999999, message = "日期格式需为yyyyMM")
    /**
     * 格式 yyyyMM 202507
     */
    private Integer dt;

    @NotEmpty(message = "任务列表不能为空")
    @Size(min = 1)
    private List<@NotBlank(message = "任务类型不能为空") String> tasks;

    private String reason;
}