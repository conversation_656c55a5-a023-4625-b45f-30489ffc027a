package com.yy.yyzone.guildrank.util;

import java.util.AbstractMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/21
 */
public class CommonUtil {
    /**
     * @param map
     * @param <K>
     * @param <V>
     * @return
     */
    public static <K, V> Map<V, K> revertKV(Map<K, Set<V>> map) {
        return map.entrySet().stream().flatMap(entry -> entry.getValue().stream()
                        .map(value -> new AbstractMap.SimpleEntry<>(value, entry.getKey())))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }
}