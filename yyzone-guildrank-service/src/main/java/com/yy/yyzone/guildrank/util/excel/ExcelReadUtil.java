package com.yy.yyzone.guildrank.util.excel;

import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Generated;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import static com.yy.yyzone.guildrank.util.excel.Columns.ColumnType.*;

/**
 * Created by chenjinying on 2017/6/26.
 * mail: <EMAIL>
 */
@Generated("忽略代码规范检查")
public class ExcelReadUtil {
    public final static String XLSX_SUFFIX = ".xlsx";
    public final static int SUCCESS_CODE = 200;
    private static final Logger LOGGER = LoggerFactory.getLogger(ExcelReadUtil.class);

    public static String KEY_CODE = "code";
    public static String KEY_MSG = "msg";
    public static String KEY_DATA = "data";

    /**
     * 读取excel数据，只读第一个sheet
     * @param file 后缀为.xlsx
     * @param columns 列说明：对应类型的字段名、数据类型、是否允许空、默认值
     * @param c 返回数据对象类型
     * @param <T>
     * @return Map，KEY_CODE=200表示成功，KEY_MSG:处理描述，KEY_DATA:数据列表
     * @throws IOException
     */
    public static <T> Map<String, Object> getDataList(MultipartFile file, Columns columns, Class<T> c) throws IOException {
        return getDataList(file, columns, c, 0);
    }

    /**
     * 读取excel数据
     * @param file 后缀为.xlsx
     * @param columns 列说明：对应类型的字段名、数据类型、是否允许空、默认值
     * @param c 返回数据对象类型
     * @param sheetIndex 读第几个sheet，第一个为0
     * @param <T>
     * @return Map，KEY_CODE=200表示成功，KEY_MSG:处理描述，KEY_DATA:数据列表
     * @throws IOException
     */
    public static <T> Map<String, Object> getDataList(MultipartFile file, Columns columns, Class<T> c, int sheetIndex) throws IOException {
        List<T> datas = new ArrayList<>();
        Map<String, Object> result = Maps.newHashMap();
        result.put(KEY_CODE, -1);
        result.put(KEY_MSG, "读取数据失败");
        result.put(KEY_DATA, datas);

        if (columns == null || columns.isEmpty()) {
            result.put(KEY_MSG, "调用出错，columns=null");return result;
        }

        InputStream is = null;
        try {
            is = file.getInputStream();
            String s = "获取文件输入流失败";
            if (is == null) {result.put(KEY_MSG, s);return result;}
            Workbook workbook;
            try {
                workbook = getWorkbook(is, file.getOriginalFilename());
            } catch (InvalidFormatException e) {
                LOGGER.error(e.getMessage(), e);
                result.put(KEY_MSG, e.getMessage());return result;
            }
            if (workbook == null) {
                result.put(KEY_MSG, "获取Workbook失败");return result;
            }
            Sheet sheet = workbook.getSheetAt(sheetIndex);
            int max = 10000;
            if (sheet.getLastRowNum() >= max) {
                result.put(KEY_MSG, "导入的文件不能超过10000行");return result;
            }

            boolean firstLine = true;
            for (Row cells : sheet) {
                if (firstLine) {firstLine = false; continue;}
                try {
                    Map<Integer, Cell> rowDataMap = cellsToMap(cells, columns);
                    T info = buildInfo(rowDataMap, columns, c);
                    if (info == null) {
                        result.put(KEY_MSG, "第["+(cells.getRowNum() + 1)+"]行数据读取失败");return result;
                    } else {
                        datas.add(info);
                    }
                } catch (Exception e) {
                    LOGGER.error("getDataList exception", e);
                    result.put(KEY_MSG, "第["+(cells.getRowNum() + 1)+"]行"+e.getMessage());return result;
                }
            }
        } finally {
            if (is != null) {try {is.close();} catch (IOException ignored) {}}
        }
        result.put(KEY_CODE, 200);
        result.put(KEY_MSG, "读取数据成功");
        return result;
    }

    private static Map<Integer, Cell> cellsToMap(Row cells, Columns columns) {
        Map<Integer, Cell> rowData = Maps.newHashMap();
        for (int i=0; i<columns.size(); i++) {
            Cell cell = cells.getCell(i);
            rowData.put(i, cell);
        }
        return rowData;
    }

    private static <T> T buildInfo(Map<Integer, Cell> rowDataMap, Columns columns, Class<T> c) throws Exception {
        T info = c.newInstance();
        for (int i=0; i<columns.size(); i++) {
            Columns.Column column = columns.getColumn(i);
            Cell cell = rowDataMap.get(i);
            Object data = changeDataType(cell, column);
            if (data != null) {
                info = invoke(info, column.name, data);;
            }
        }
        return info;
    }

    private static Object changeDataType(Cell cell, Columns.Column column) throws Exception {
        if (cell == null) {
            return null;
        }
        Object data = null;
        int cellType = cell.getCellType();
        String s1 = "boolean无法转成short";
        String s2 = "boolean无法转成int";
        String s3 = "boolean无法转成long";
        String s4 = "boolean无法转成BigDeciaml";
        if (Cell.CELL_TYPE_BOOLEAN == cellType) {
            if (column.type == CELL_TYPE_SHORT) {throw new Exception(s1);}
            if (column.type == CELL_TYPE_INT) {throw new Exception(s2);}
            if (column.type == CELL_TYPE_LONG) {throw new Exception(s3);}
            if (column.type == CELL_TYPE_BIGDECIMAL) {throw new Exception(s4);}
            if (column.type == CELL_TYPE_BOOLEAN) {
                data = cell.getBooleanCellValue();
            } else if (column.type == CELL_TYPE_STRING) {
                data = ((Boolean) cell.getBooleanCellValue()).toString();
            }
        } else if (Cell.CELL_TYPE_NUMERIC == cellType) {
            String s5 = "数值无法转成boolean";
            if (column.type == CELL_TYPE_BOOLEAN) {throw new Exception(s5);}
            if (column.type == CELL_TYPE_SHORT) {
                data = ((Double) cell.getNumericCellValue()).shortValue();
            } else if (column.type == CELL_TYPE_INT) {
                data = ((Double) cell.getNumericCellValue()).intValue();
            } else if (column.type == CELL_TYPE_LONG) {
                data = ((Double) cell.getNumericCellValue()).longValue();
            } else if (column.type == CELL_TYPE_STRING) {
                DecimalFormat df = new DecimalFormat("0");
                data = df.format(cell.getNumericCellValue());
            } else if (column.type == CELL_TYPE_BIGDECIMAL) {
                data = BigDecimal.valueOf(cell.getNumericCellValue());
            }
        } else if (Cell.CELL_TYPE_STRING == cellType) {
            String cellValue = cell.getStringCellValue();
            if (StringUtils.isBlank(cellValue) && column.allowNumm) {
                data = column.defaultVal;
            } else {
                if (column.type == CELL_TYPE_BOOLEAN) {
                    data = Boolean.parseBoolean(cellValue);
                } else if (column.type == CELL_TYPE_SHORT) {
                    data = Short.parseShort(cellValue);
                } else if (column.type == CELL_TYPE_INT) {
                    data = Integer.parseInt(cellValue);
                } else if (column.type == CELL_TYPE_LONG) {
                    data = Long.parseLong(cellValue);
                } else if (column.type == CELL_TYPE_BIGDECIMAL) {
                    data = new BigDecimal(cellValue);
                } else {
                    data = cellValue;
                }
            }
        } else if (Cell.CELL_TYPE_BLANK == cellType) {
            if (column.allowNumm) {
                data = column.defaultVal;
            } else {
                throw new Exception("数据不允许为空");
            }
        }
        return data;
    }

    private static <T> T invoke(T obj, String fieldName, Object value) throws Exception {
        try {
            Class[] parameterTypes = new Class[1];
            Field field = null;
            try {
                field = obj.getClass().getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                field = obj.getClass().getSuperclass().getDeclaredField(fieldName);
            }
            parameterTypes[0] = field.getType();
            String sb = "set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
            Method method = obj.getClass().getMethod(sb, parameterTypes);
            method.invoke(obj, value);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new Exception("反射set "+fieldName+"值失败");
        }
        return obj;
    }

    public static Workbook getWorkbook(InputStream is, String fileName) throws IOException, InvalidFormatException {
        Workbook workbook;
        String xlsx = "xlsx";
        String xls = "xls";
        if (fileName.endsWith(xlsx)) {
            // 因为Excel版本问题，不使用 workbook = new XSSFWorkbook(is);
            workbook = WorkbookFactory.create(is);
        } else if (fileName.endsWith(xls)) {
            workbook = new HSSFWorkbook(is);
        } else {
            throw new IllegalArgumentException("The specified file is not Excel file");
        }
        return workbook;
    }

    public static Map<Integer, Object> rowToMap(Row cells) {
        Map<Integer, Object> rowData = Maps.newHashMap();

        Iterator<Cell> cellIterator = cells.cellIterator();
        int i = 0;
        while (cellIterator.hasNext()) {
            Cell cell = cellIterator.next();

            switch (cell.getCellType()) {
                case Cell.CELL_TYPE_STRING:
                    rowData.put(i, cell.getStringCellValue());
                    break;
                case Cell.CELL_TYPE_BOOLEAN:
                    rowData.put(i, cell.getBooleanCellValue());
                    break;
                case Cell.CELL_TYPE_NUMERIC:
                    rowData.put(i, cell.getNumericCellValue());
                    break;
                default:
                    break;
            }
            i++;
        }
        return rowData;

    }
}
