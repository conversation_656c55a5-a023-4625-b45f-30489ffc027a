package com.yy.yyzone.guildrank.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/12/27
 *
 */
@Data
@Builder
public class RuliuRobotMsg {
    @JsonProperty("content_prefix")
    private String contentPrefix;

    @JsonProperty("msg_body")
    private MsgBody msgBody;

    private String toid;

    @JsonProperty("webhook_acess_token")
    private String webhookAcessToken;

    private String atall;

    @JsonProperty("AT_type")
    private String atType;

    @JsonProperty("msg_type")
    private String msgType;

    @Data
    @Builder
    public static class MsgBody {
        private String content;
    }
}