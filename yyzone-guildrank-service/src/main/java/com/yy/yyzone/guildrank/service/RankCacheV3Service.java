package com.yy.yyzone.guildrank.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.yy.ent.mobile.metrics.MetricsStopWatch;
import com.yy.yyzone.guildrank.constant.RankConstant;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankHistory;
import com.yy.yyzone.guildrank.dto.AssociateCompanyNotify;
import com.yy.yyzone.guildrank.repository.GuildRankRepository;
import com.yy.yyzone.guildrank.util.CommonUtil;
import com.yy.yyzone.guildrank.util.MyDateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 段位
 **/
@Slf4j
@Service
public class RankCacheV3Service extends MessageListenerAdapter {
    @Autowired
    private StarService starService;

    @Autowired
    private RankCheckV3Service rankCheckV3Service;

    @Autowired
    private GoldChannelService goldChannelService;

    @Autowired
    private GuildService guildService;

    @Autowired
    private GuildRankRepository guildRankRepository;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private SwitchService switchService;

    private final ReentrantLock lock = new ReentrantLock();
    public volatile int HAS_UPDATE = 0;
    private volatile Date currentMonthStart;
    private ConcurrentHashMap<Long, Integer> currentRank = new ConcurrentHashMap<>();

    @Scheduled(cron = "0 2/10 * * * ?")
    public void reload() {
        reload(true);
    }

    public void reload(boolean saveHistory) {
        log.info("reload saveHistory:{}", saveHistory);
        MetricsStopWatch metricsStopWatch = MetricsStopWatch.startInternalWatch().uri("Rank/reloadV3");
        boolean flag;
        lock.lock();
        try {
            boolean hasUpdate = rankCheckV3Service.rankCheckDone(MyDateUtil.lastMonthEnd());
            int delta = hasUpdate ? RankConstant.LAST : RankConstant.PRE_LAST;
            Date date = MyDateUtil.monthEnd(delta);
            Date monthStart = DateUtils.truncate(date, Calendar.MONTH);
            Pair<Map<Long, Integer>, List<GuildrankHistory>> result = calRank(date, monthStart);
            if(MapUtils.isEmpty(result.getLeft())) {
                log.error("no rank data");
            }
            currentRank = new ConcurrentHashMap<>(result.getLeft());
            HAS_UPDATE = hasUpdate ? RankConstant.UPDATE : RankConstant.NOT_UPDATE;
            currentMonthStart = monthStart;
            if (saveHistory) {
                guildRankRepository.saveHistory(monthStart, result.getRight());
            }
            flag = true;
            log.info("reload rank dt:{},hasUpdate:{},monthStart:{},rankMap:{}", MyDateUtil.fmtDate(date), HAS_UPDATE, currentMonthStart, result.getLeft());
        } finally {
            lock.unlock();
        }

        if (flag) {
            metricsStopWatch.successCode().markDurationAndCode();
        } else {
            metricsStopWatch.failCode().markDurationAndCode();

        }
    }

    private Pair<Map<Long, Integer>, List<GuildrankHistory>> calRank(Date date, Date monthStart) {
        Map<Long, Integer> cmpRankMap = new HashMap<>(guildRankRepository.getCmpRankFromDB(date,switchService.isSwitchToCmpStarLvl()));
        Map<Long, Set<Long>> associatedUserMap = goldChannelService.getAllAssociatedUser();
        Map<Long, Long> guildCompanyMap = CommonUtil.revertKV(associatedUserMap);
        String whitelistMonth = MyDateUtil.fmtMonth(DateUtils.addMonths(monthStart, 1));
        Map<Long, Integer> rankWhitelist =  guildRankRepository.getWhitelistRank(whitelistMonth);
        log.info("calRank dt:{},monthStart:{},whitelistMonth:{},rankWhitelist:{}", date, MyDateUtil.fmtDate(monthStart), whitelistMonth, rankWhitelist);

        Set<Long> defaultUids = new HashSet<>();
        List<Long> starGuildList = starService.getAllStarGoldGuild();
        Map<Long, Set<Long>> starCmpMap = guildService.getStarCmp(associatedUserMap, starGuildList);
        // 无段位的星级主体默认为一星
        starCmpMap.entrySet().stream().filter(e -> !cmpRankMap.containsKey(e.getKey())).forEach(e -> {
            cmpRankMap.put(e.getKey(), RankConstant.DEFAULT_RANK);
            defaultUids.add(e.getKey());
            log.info("calRank default rank uid:{}", e.getKey());
        });
        //段位白名单
        rankWhitelist.entrySet().stream().filter(e -> starCmpMap.containsKey(e.getKey())).forEach(e -> {
            log.info("calRank whitelist uid:{},old:{},new:{}", e.getKey(), cmpRankMap.get(e.getKey()), e.getValue());
            cmpRankMap.put(e.getKey(), e.getValue());
        });

        Map<Long, Integer> rankMap = new HashMap<>(2000);
        List<GuildrankHistory> histories = new ArrayList<>(2000);
        for (Map.Entry<Long, Integer> e : cmpRankMap.entrySet()) {
            Integer lvl = e.getValue();
            Long cmpUid = guildCompanyMap.get(e.getKey());
            if (cmpUid != null && cmpRankMap.get(cmpUid) != null) {
                // 以主体星级为准
                lvl = cmpRankMap.get(cmpUid);
                if (!e.getValue().equals(lvl)) {
                    log.info("RankCacheV3Service reload uid:{},cmpUid:{},biLevel:{},finalLevel:{}", e.getKey(), cmpUid, e.getValue(), lvl);
                }
            }

            rankMap.put(e.getKey(), lvl);

            GuildrankHistory h1 = new GuildrankHistory();
            RankConstant.RankType type = defaultUids.contains(e.getKey())
                    ? RankConstant.RankType.NO_RANK_DEFAULT
                    : RankConstant.RankType.BI;
            if (rankWhitelist.containsKey(e.getKey())) {
                type = RankConstant.RankType.WHITELIST;
            }
            h1.setMonth(monthStart);
            h1.setMainGuildUid(e.getKey());
            h1.setGuildUid(e.getKey());
            h1.setLevel(lvl);
            h1.setType(type.getCode());
            histories.add(h1);

            // 关联公会和主体段位一致
            Set<Long> associated = associatedUserMap.get(e.getKey());
            if (CollectionUtils.isNotEmpty(associated)) {
                for (Long aUid : associated) {
                    if (e.getKey().equals(aUid)) {
                        continue;
                    }

                    rankMap.put(aUid, lvl);

                    GuildrankHistory h2 = new GuildrankHistory();
                    h2.setMonth(monthStart);
                    h2.setMainGuildUid(e.getKey());
                    h2.setGuildUid(aUid);
                    h2.setLevel(lvl);
                    h2.setType(type.getCode());
                    histories.add(h2);
                }
            }
        }
        return Pair.of(rankMap, histories);
    }

    @PostConstruct
    public void init() {
        reload(false);
    }

    public Map<Long, Integer> batchQueryCurrentRank(List<Long> guildUidList) {
        MetricsStopWatch metricsStopWatch = MetricsStopWatch.startServerWatch().uri("batchQueryCurrentRankV3");
        if (CollectionUtils.isEmpty(guildUidList)) {
            return Collections.emptyMap();
        }

        Map<Long, Integer> result = Maps.newHashMapWithExpectedSize(guildUidList.size());
        guildUidList.stream().filter(Objects::nonNull).filter(currentRank::containsKey).forEach(uid -> result.put(uid, currentRank.get(uid)));
        metricsStopWatch.code(0).markDurationAndCode();
        return result;
    }

    public Map<Long, Integer> batchQueryCurrentRankNoCache(List<Long> guildUidList) {
        MetricsStopWatch metricsStopWatch = MetricsStopWatch.startServerWatch().uri("batchQueryCurrentRankV3");
        if (CollectionUtils.isEmpty(guildUidList)) {
            return Collections.emptyMap();
        }
        Date dt = MyDateUtil.monthEnd(currentMonthStart);
        log.info("batchQueryCurrentRankNoCache dt:{},month:{}", MyDateUtil.fmtDate(dt), MyDateUtil.fmtDate(currentMonthStart));
        Map<Long, Integer> rank = calRank(dt, currentMonthStart).getLeft();
        Map<Long, Integer> result = Maps.newHashMapWithExpectedSize(guildUidList.size());
        guildUidList.stream().filter(Objects::nonNull).filter(rank::containsKey).forEach(uid -> result.put(uid, rank.get(uid)));
        metricsStopWatch.code(0).markDurationAndCode();
        return result;
    }

    public Map<Long, Integer> allRank() {
        return new HashMap<>(currentRank);
    }

    public int queryCurrentRank(Long guildUid) {
        MetricsStopWatch metricsStopWatch = MetricsStopWatch.startServerWatch().uri("currentRankV3");
        int rank = currentRank.getOrDefault(guildUid, 0);
        metricsStopWatch.code(0).markDurationAndCode();
        return rank;
    }

    /**
     * 当前段位月份 1号日期
     *
     * @return
     */
    public Date getRankCurrentMonth() {
        return currentMonthStart;
    }

    /**
     * 查询当前月份数据是否已经更新
     *
     * @return
     */
    public boolean hasUpdate() {
        return HAS_UPDATE == RankConstant.UPDATE;
    }

    public void refreshRankCache() {
        refreshRankCache(0);
    }

    public void refreshRankCache(int delayMs) {
        CompletableFuture.runAsync(() -> {
            try {
                if (delayMs > 0) {
                    TimeUnit.MILLISECONDS.sleep(delayMs);
                }
                redisTemplate.convertAndSend(RankConstant.RedisConstants.RELOAD_RANK_CHANNEL, String.valueOf(System.currentTimeMillis()));
                log.info("refreshRankCache async delay:{}ms", delayMs);
            } catch (Exception e) {
                log.error("refreshRankCache exception", e);
            }
        });
    }

    @Override
    public void onMessage(final Message message, final byte[] pattern) {
        String channel = new String(message.getChannel(), StandardCharsets.UTF_8);
        log.info("onMessage channel:{}", channel);
        if (RankConstant.RedisConstants.RELOAD_RANK_CHANNEL.equals(channel)) {
            reload(true);
        } else if (RankConstant.RedisConstants.ASSOICATE_COMPANY_CHANNEL.equals(channel)) {
            String s = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("onMessage bodyString:{}", s);
            AssociateCompanyNotify notify = JSON.parseObject(s, AssociateCompanyNotify.class);
            if (notify.getGuildUid() == null) {
                return;
            }
            if (notify.getType() == AssociateCompanyNotify.TYPE_ADD) {
                if (notify.getCompanyUid() == null) {
                    return;
                }
                Integer rank = currentRank.get(notify.getCompanyUid());
                log.info("onMessage AssociateCompanyNotify add guildUid:{},rank:{}", notify.getGuildUid(), rank);
                if (rank != null) {
                    currentRank.put(notify.getGuildUid(), rank);
                }
            } else if (notify.getType() == AssociateCompanyNotify.TYPE_DEL) {
                // Integer remove = currentRank.remove(notify.getGuildUid());
                reload(false);
                log.info("onMessage AssociateCompanyNotify del");
            }
        }
    }
}
