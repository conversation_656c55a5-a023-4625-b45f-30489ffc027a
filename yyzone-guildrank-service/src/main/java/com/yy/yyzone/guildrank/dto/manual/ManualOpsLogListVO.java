package com.yy.yyzone.guildrank.dto.manual;

import lombok.Data;

@Data
public class ManualOpsLogListVO {

    /**
     * 记录id
     */
    private Long id;
    /**
     * 原因
     */
    private String modifyReason;
    /**
     * 月份
     */
    private String month;
    /**
     * 主体uid
     */
    private Long mainGuildUid;
    /**
     * 操作类型，1-加，2-减
     */
    private Byte modifyType;
    /**
     * 修改分值
     */
    private Integer modifyNum;
    /**
     * 操作类型，1-新增，2-删除，3-修改
     */
    private Byte opeType;
    /**
     * 操作人
     */
    private String passport;
    /**
     * 操作时间，yyyy-MM-dd hh:mm:ss 格式
     */
    private String modifyTime;

}
