package com.yy.yyzone.guildrank.dto.manual;

import lombok.Data;

@Data
public class ManualOpsListVO {

    /**
     * 记录id，用于编辑或删除
     */
    private Long id;
    /**
     * 月份
     */
    private String month;
    /**
     * 主体uid
     */
    private Long mainGuildUid;
    /**
     * 公会yy号
     */
    private String owYyNo;
    /**
     * 公会名称
     */
    private String owYyName;
    /**
     * 企业名称
     */
    private String enterpriseName;
    /**
     * 操作类型，1-加，2-减
     */
    private Byte modifyType;
    /**
     * 修改分值
     */
    private Integer modifyNum;
    /**
     * 原因
     */
    private String modifyReason;
    /**
     * 操作人
     */
    private String passport;
    /**
     * 修改时间，yyyy-MM-dd hh:mm:ss 格式
     */
    private String modifyTime;

}
