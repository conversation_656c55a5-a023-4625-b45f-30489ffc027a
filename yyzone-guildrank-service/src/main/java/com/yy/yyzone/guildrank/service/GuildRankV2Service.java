/**
 * Autogenerated by Thrift
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 */
package com.yy.yyzone.guildrank.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.ent.mobile.metrics.MetricsStopWatch;
import com.yy.yyzone.guildrank.constant.RankConstant;
import com.yy.yyzone.guildrank.db.gen.mapper.DmYyGuildSegmentScoreMonthMapper;
import com.yy.yyzone.guildrank.db.gen.mapper.DmYySidSegmentOriginStarDayMapper;
import com.yy.yyzone.guildrank.db.gen.mapper.DmYySidSegmentScoreMonthMapper;
import com.yy.yyzone.guildrank.db.gen.model.*;
import com.yy.yyzone.guildrank.dto.LastTwelveMonthResp;
import com.yy.yyzone.guildrank.util.MyDateUtil;
import com.yy.yyzone.guildrank.yrpc.dto.PGetGuildInfoResp64;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020/3/04
 */
@Service
public class GuildRankV2Service {

    @Autowired
    private DmYyGuildSegmentScoreMonthMapper dmYyGuildSegmentScoreMonthMapper;

    @Autowired
    private DmYySidSegmentOriginStarDayMapper dmYySidSegmentOriginStarDayMapper;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private RankCacheService rankCacheService;

    private static Logger logger = LoggerFactory.getLogger(GuildRankV2Service.class);
    public static final Duration TIME = Duration.ofMinutes(10);
    public static final String DONE = "1";
    public static final String NOT_DONE = "0";

    /**
     * 获取星级公会升星目标差值
     * @param guildUid
     * @return
     */
    public DmYySidSegmentOriginStarDay getStarTargetDiff(long guildUid) {
        DmYySidSegmentOriginStarDayExample example = new DmYySidSegmentOriginStarDayExample();
        example.createCriteria().andSidOwneridEqualTo(String.valueOf(guildUid));
        example.setOrderByClause("dt desc");
        example.setLimit(1);
        List<DmYySidSegmentOriginStarDay> list = dmYySidSegmentOriginStarDayMapper.selectByExample(example);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    public DmYyGuildSegmentScoreMonth guildRankInfo(Long guildUid, String month) {
        DmYyGuildSegmentScoreMonthExample example = new DmYyGuildSegmentScoreMonthExample();
        example.createCriteria().andSidOwneridEqualTo(guildUid).andDtEqualTo(month);
        example.setOrderByClause(" id desc");
        List<DmYyGuildSegmentScoreMonth> list = dmYyGuildSegmentScoreMonthMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(list)){
            return list.get(0);
        }
        return null;
    }

    private static String update_key = "guildrank_update_flag_v2_";

    /**
     * 查询当前月份数据是否已经更新
     * @return
     */
    public boolean hasUpdate() {
        Calendar calendar = Calendar.getInstance();
        // 待BI更新
        if (calendar.get(Calendar.DATE) == 1 && calendar.get(Calendar.HOUR_OF_DAY) < 9) {
            return false;
        }

        String currentDate = DateFormatUtils.format(DateUtils.addMonths(new Date(), -1),"yyyy-MM");
        String redisKey = update_key+currentDate;
        String result = redisTemplate.opsForValue().get(redisKey);
        if (DONE.equals(result)){
            return true;
        } else if (NOT_DONE.equals(result)){
            return false;
        }
        boolean updateFlag = rankCacheService.getUpdateFlag() == RankConstant.UPDATE;
        redisTemplate.opsForValue().set(redisKey, updateFlag ? DONE : NOT_DONE, TIME);
        return updateFlag;
    }

    @Autowired
    private DmYySidSegmentScoreMonthMapper dmYySidSegmentScoreMonthMapper;

    public List<LastTwelveMonthResp> lastTwelveMonth(Long guildUid) throws Exception{

        DmYyGuildSegmentScoreMonthExample example = new DmYyGuildSegmentScoreMonthExample();
        example.createCriteria().andSidOwneridEqualTo(guildUid);
        example.setOrderByClause(" id desc ");
        example.setLimit(20);// 查20条出来去重足够了
        List<DmYyGuildSegmentScoreMonth> list = dmYyGuildSegmentScoreMonthMapper.selectByExample(example);
        Map<String, String> levelMap = Maps.newHashMap();
        Map<String, String> rankMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(list)){
            for (DmYyGuildSegmentScoreMonth item : list) {
                levelMap.put(item.getDt(), String.valueOf(item.getPsSAll().intValue()));
            }
        }
        //如果当前时间是2020-10-01之前  则还需要去查旧数据（最后的旧数据是2020-02的）
        if (new Date().before(DateUtils.parseDate("2020-10-01","yyyy-MM-dd"))){
            DmYySidSegmentScoreMonthExample example2 = new DmYySidSegmentScoreMonthExample();
            List<String> dtList = Lists.newArrayList("2020-02-01","2020-01-01"
                    ,"2019-12-01","2019-11-01","2019-10-01","2019-09-01");
            example2.createCriteria().andSidOwneridEqualTo(guildUid).andDtIn(dtList);
            example2.setOrderByClause(" id ");
            example2.setLimit(10);
            List<DmYySidSegmentScoreMonth> list2 = dmYySidSegmentScoreMonthMapper.selectByExample(example2);
            if (CollectionUtils.isNotEmpty(list2)){
                for (DmYySidSegmentScoreMonth item : list2) {
                    levelMap.put(item.getDt(), String.valueOf(item.getPsSAllCor().intValue()));
                    rankMap.put(item.getDt(), String.valueOf(item.getPsSAllRnCor().intValue()));
                }
            }
        }

        List<LastTwelveMonthResp> resp = Lists.newArrayList();
        for (int i = 1; i<=12; i++){
            String month = MyDateUtil.getFirstDay(-i);
            String key = month.substring(0,7);
            String level = levelMap.get(month);
            String rank = rankMap.get(month);
            LastTwelveMonthResp item = new LastTwelveMonthResp();
            item.setMonth(key);
            item.setRank(StringUtils.isBlank(rank) ? 0 : Integer.parseInt(rank));
            item.setStarLevel(StringUtils.isBlank(level) ? 0 : Integer.parseInt(level));
            resp.add(item);
        }
        return resp;
    }

    @Deprecated
    public DmYyGuildSegmentScoreMonth currentRank(Long uid){
        MetricsStopWatch metricsStopWatch = MetricsStopWatch.startServerWatch();
        String currentDate = DateFormatUtils.format(DateUtils.addMonths(new Date(), -1),"yyyy-MM");
        boolean hasUpdate = hasUpdate();
        if (!hasUpdate){
            currentDate = DateFormatUtils.format(DateUtils.addMonths(new Date(), -2),"yyyy-MM");
        }
        String cacheDataKey = currentDate + "_queryCurrentRank_v2_" + uid;
        String data = redisTemplate.opsForValue().get(cacheDataKey);
        if (StringUtils.isNotBlank(data)){
            logger.info("queryCurrentRankV2 from cache key:{}, data:{}", cacheDataKey, JSON.toJSONString(data));
            metricsStopWatch.code(0).uri("currentRankV2").markDurationAndCode();
            return JSON.parseObject(data,DmYyGuildSegmentScoreMonth.class);
        }

        DmYyGuildSegmentScoreMonth dbData = guildRankInfo(uid, currentDate+"-01");
        if (dbData == null || dbData.getSidOwnerid() <=0){// 没查到的， 去查一下最新身份是否星级
            dbData = buildData(uid);
        }
        redisTemplate.opsForValue().setIfAbsent(cacheDataKey, JSON.toJSONString(dbData), TIME);
        logger.info("queryCurrentRankV2 from db uid:{}, data:{}", uid, JSON.toJSONString(dbData));
        metricsStopWatch.code(0).uri("currentRankV2").markDurationAndCode();
        return dbData;
    }


    public DmYyGuildSegmentScoreMonth buildData(Long uid){
        PGetGuildInfoResp64 resp = userInfoService.getGuildInfoFromCache(uid);

        if (StringUtils.isNotBlank(resp.getGoldenDate()) || StringUtils.isNotBlank( resp.getStarDate())){// 星级金牌公会
            return buildEmptyDmYySidSegmentScoreMonth(uid, 1);
        }else {
            return buildEmptyDmYySidSegmentScoreMonth(uid, 0);
        }
    }


    private DmYyGuildSegmentScoreMonth buildEmptyDmYySidSegmentScoreMonth(Long uid, Integer level){
        DmYyGuildSegmentScoreMonth data = new DmYyGuildSegmentScoreMonth();
        data.setId(0L);
        data.setSidOwnerid(uid);
        data.setSidOwyyid(0L);
        data.setMonthDiamond(0.0D);
        data.setMonthDiamondRr(0.0D);
        data.setValidLiveUv(0.0D);
        data.setValidLiveUvRr(0.0D);
        data.setAcu300Uv(0.0D);
        data.setAcu300UvRr(0.0D);
        data.setAcu50300Uv(0.0D);
        data.setAcu50300UvRr(0.0D);
        data.setAcu1050Uv(0.0D);
        data.setAcu1050UvRr(0.0D);
        data.setAcu10Uv(0.0D);
        data.setAcu10UvRr(0.0D);
        data.setBreakAUv(0L);
        data.setBreakBUv(0L);
        data.setBreakCUv(0L);
        data.setBreakEUv(0L);
        data.setLiveUv(0L);
        data.setOldBreakUv(0L);
        data.setBreakUvPp(0.0D);
        data.setBreakUvPpRr(0.0D);
        data.setPsSAll(level);
        data.setPsSMonthDiamond(0);
        data.setSprMonthDiamond(0.0D);
        data.setPsSValidLiveUv(0);
        data.setSprValidLiveUv(0.0D);
        data.setPsAcu(0.0D);
        data.setPsSAcu(0);
        data.setSprAcu(0.0D);
        data.setPsSLiveUv(0);
        data.setPsSBreakUvPp(0);
        data.setSprBreakUvPp(0.0D);
        data.setDt("");
        data.setAvgHighAidNum(0.0D);
        data.setAvgHighAidNumRr(0.0D);
        data.setAvgWaistAidNum(0.0D);
        data.setAvgWaistAidNumRr(0.0D);
        data.setAvgTailAidNum(0.0D);
        data.setAvgTailAidNumRr(0.0D);
        data.setAidValueScore(0.0D);
        data.setAidValueScoreStarLvl(0);
        data.setAidValueScoreSprintValue(0.0D);
        return data;
    }


    @Deprecated
    public Map<Long, Integer> batchQueryCurrentRank(List<Long> guildUids){
        MetricsStopWatch metricsStopWatch = MetricsStopWatch.startServerWatch();
        logger.info(JSON.toJSONString(guildUids));
        String currentDate = DateFormatUtils.format(DateUtils.addMonths(new Date(), -1),"yyyy-MM");
        boolean hasUpdate = hasUpdate();
        if (!hasUpdate){
            currentDate = DateFormatUtils.format(DateUtils.addMonths(new Date(), -2),"yyyy-MM");
        }

        Map<Long, Integer> result = Maps.newHashMap();
        DmYyGuildSegmentScoreMonthExample example = new DmYyGuildSegmentScoreMonthExample();
        example.createCriteria().andDtEqualTo(currentDate+"-01").andSidOwneridIn(guildUids);
        example.setOrderByClause(" id desc ");
        List<DmYyGuildSegmentScoreMonth> list = dmYyGuildSegmentScoreMonthMapper.selectByExample(example);
        List<DmYyGuildSegmentScoreMonth> quChongList = Lists.newArrayList();
        Set<Long> uidSet = Sets.newHashSet();
        for (DmYyGuildSegmentScoreMonth dmYyGuildSegmentScoreMonth : list) {
            if (!uidSet.contains(dmYyGuildSegmentScoreMonth.getSidOwnerid())){
                quChongList.add(dmYyGuildSegmentScoreMonth);
                uidSet.add(dmYyGuildSegmentScoreMonth.getSidOwnerid());
            }
        }

        Map<Long, Integer> dbData = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(quChongList)){
            dbData = quChongList.stream().collect(Collectors.toMap(DmYyGuildSegmentScoreMonth::getSidOwnerid, DmYyGuildSegmentScoreMonth::getPsSAll));
        }

        List<Long> guildUidInt = Lists.newArrayList();
        for (Long uid : guildUids) {
            Integer level = dbData.get(uid);
            if (level != null && level>0){
                result.put(uid, level.intValue());
            }else {
                // 查不到的uid 去查最新身份
                guildUidInt.add(uid);
            }
        }

        Map<Long, Integer> guildTypeMap = userInfoService.batchGetGuildType(guildUidInt);
        if (MapUtils.isNotEmpty(guildTypeMap)){
            for (Map.Entry<Long, Integer> entry : guildTypeMap.entrySet()) {
                if (entry.getValue()>0){
                    result.put(entry.getKey(), 1);
                }
            }
        }
        logger.info(JSON.toJSONString(result));
        metricsStopWatch.code(0).uri("batchQueryCurrentRank").markDurationAndCode();
        return result;
    }

}
