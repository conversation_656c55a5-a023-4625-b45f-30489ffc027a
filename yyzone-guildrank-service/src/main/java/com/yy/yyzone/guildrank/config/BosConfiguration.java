package com.yy.yyzone.guildrank.config;

import com.yy.yyzone.guildrank.service.BosService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class BosConfiguration {

    @Bean
    @ConditionalOnProperty(name = "bos.bucket")
    @ConfigurationProperties(prefix = "bos")
    public BosService bosService() {
        return new BosService();
    }
}
