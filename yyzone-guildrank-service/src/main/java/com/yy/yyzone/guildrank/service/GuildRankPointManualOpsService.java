package com.yy.yyzone.guildrank.service;

import com.google.common.collect.Lists;
import com.yy.java.webdb.WebdbUserInfo;
import com.yy.yyzone.guildrank.db.gen.mapper.GuildRankPointOpsLogMapper;
import com.yy.yyzone.guildrank.db.gen.mapper.GuildRankPointOpsManualMapper;
import com.yy.yyzone.guildrank.db.gen.model.GuildRankPointOpsLog;
import com.yy.yyzone.guildrank.db.gen.model.GuildRankPointOpsLogExample;
import com.yy.yyzone.guildrank.db.gen.model.GuildRankPointOpsManual;
import com.yy.yyzone.guildrank.db.gen.model.GuildRankPointOpsManualExample;
import com.yy.yyzone.guildrank.dto.manual.ManualOpsListVO;
import com.yy.yyzone.guildrank.dto.manual.ManualOpsLogListVO;
import com.yy.yyzone.guildrank.dto.manual.ScoreManualDelDTO;
import com.yy.yyzone.guildrank.dto.manual.ScoreManualModifyDTO;
import com.yy.yyzone.guildrank.dto.udb.NCertMaskInfoRes;
import com.yy.yyzone.guildrank.util.PageResp;
import com.yy.yyzone.guildrank.util.Resp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GuildRankPointManualOpsService {

    @Autowired
    private GuildRankPointOpsManualMapper opsManualMapper;

    @Autowired
    private GuildRankPointOpsLogMapper opsLogMapper;

    @Autowired
    private UserInfoService webdbUserInfoService;

    @Autowired
    private UdbUserInfoService udbUserInfoService;

    private static final byte
            OPE_TYPE_ADD = 1,
            OPE_TYPE_DEL = 2,
            OPE_TYPE_MODIFY = 3;


    public PageResp<List<ManualOpsListVO>> pageList(List<Long> uids, String yearMonth, int pageNum, int pageSize) throws Exception {
        Date month = StringUtils.isBlank(yearMonth) ? null : DateUtils.parseDate(yearMonth, "yyyy-MM");

        GuildRankPointOpsManualExample example = new GuildRankPointOpsManualExample();
        GuildRankPointOpsManualExample.Criteria criteria = example.createCriteria();
        if (uids != null) {
            criteria.andEntityUidIn(uids);
        }
        if (month != null) {
            criteria.andTargetMonthEqualTo(month);
        }

        int count = opsManualMapper.countByExample(example);
        if (count == 0) {
            return PageResp.createBySuccess(Lists.newArrayList(), count);
        }

        example.setOffset((pageNum - 1) * pageSize);
        example.setLimit(pageSize);
        example.setOrderByClause("id desc");
        List<GuildRankPointOpsManual> opsManuals = opsManualMapper.selectByExample(example);

        List<Long> entityUidList = opsManuals.stream().map(GuildRankPointOpsManual::getEntityUid).distinct().collect(Collectors.toList());

        Map<String, WebdbUserInfo> userInfoMap = webdbUserInfoService.getUserInfo(entityUidList);
        Map<Long, NCertMaskInfoRes> certMaskInfoMap = udbUserInfoService.getCertMaskInfoByUid(entityUidList);

        List<ManualOpsListVO> list = opsManuals.stream().map(l -> toManualOpsListVO(l, userInfoMap, certMaskInfoMap)).collect(Collectors.toList());
        return PageResp.createBySuccess(list, count);

    }

    private ManualOpsListVO toManualOpsListVO(GuildRankPointOpsManual manual,
                                              Map<String, WebdbUserInfo> userInfoMap,
                                              Map<Long, NCertMaskInfoRes> certMaskInfoMap) {

        WebdbUserInfo userInfo = userInfoMap.get(String.valueOf(manual.getEntityUid()));

        NCertMaskInfoRes nCertMaskInfoRes = certMaskInfoMap.get(manual.getEntityUid());

        ManualOpsListVO vo = new ManualOpsListVO();
        vo.setId(manual.getId());
        vo.setMonth(DateFormatUtils.format(manual.getTargetMonth(), "yyyy-MM"));
        vo.setMainGuildUid(manual.getEntityUid());
        vo.setOwYyNo(userInfo != null ? userInfo.getYyno() : "");
        vo.setOwYyName(userInfo != null ? userInfo.getNick() : "");
        vo.setEnterpriseName(nCertMaskInfoRes != null ? nCertMaskInfoRes.maskName : "");
        vo.setModifyType(manual.getModifyType());
        vo.setModifyNum(manual.getModifyNum());
        vo.setModifyReason(manual.getModifyReason());
        vo.setPassport(manual.getPassport());
        vo.setModifyTime(DateFormatUtils.format(manual.getUpdateTime(), "yyyy-MM-dd HH:mm:ss"));
        return vo;
    }

    public Resp<?> addOrModify(ScoreManualModifyDTO modifyDTO, String operator) throws Exception {
        long id = Optional.ofNullable(modifyDTO.getId()).orElse(0L);

        Date month = DateUtils.parseDate(modifyDTO.getMonth(), "yyyy-MM");
        // 判断月份是否在本月之后
        Date monthStart = DateUtils.truncate(new Date(), Calendar.MONTH);
        if (month.before(monthStart)) {
            return Resp.createByError("不可选已过月份");
        }

        if (id > 0) {
            if (!StringUtils.isNumeric(modifyDTO.getMainGuildUid())) {
                return Resp.createByError("请输入有效的uid");
            }
            long mainGuildUid = Long.parseLong(modifyDTO.getMainGuildUid());

            GuildRankPointOpsManual opsManual = toGuildRankPointOpsManual(mainGuildUid, modifyDTO, month, operator);

            GuildRankPointOpsManualExample example = new GuildRankPointOpsManualExample();
            example.createCriteria().andIdEqualTo(modifyDTO.getId());
            if (opsManualMapper.countByExample(example) == 0) {
                return Resp.createByError("原记录不存在");
            }
            opsManual.setEntityUid(null);
            opsManualMapper.updateByPrimaryKeySelective(opsManual);

            // 记录流水
            opsLogMapper.insert(toGuildRankPointOpsLog(OPE_TYPE_MODIFY, mainGuildUid, opsManual.getModifyType(), opsManual.getTargetMonth(), opsManual.getModifyNum(), opsManual.getModifyReason(), operator));

        } else {
            String[] uidSplit = modifyDTO.getMainGuildUid().split("\n");
            for (String uidStr : uidSplit) {
                if (!StringUtils.isNumeric(uidStr)) {
                    return Resp.createByError("存在非法的uid，请检查后提交");
                }
            }
            for (String uidStr : uidSplit) {
                addAndLog(Long.parseLong(uidStr), modifyDTO, month, operator);
            }
        }

        return Resp.createBySuccess();
    }

    private void addAndLog(long mainGuildUid, ScoreManualModifyDTO modifyDTO, Date month, String operator) {
        GuildRankPointOpsManual opsManual = toGuildRankPointOpsManual(mainGuildUid, modifyDTO, month, operator);
        opsManualMapper.insert(opsManual);
        opsLogMapper.insert(toGuildRankPointOpsLog(OPE_TYPE_ADD, mainGuildUid, opsManual.getModifyType(), opsManual.getTargetMonth(), opsManual.getModifyNum(), opsManual.getModifyReason(), operator));
    }

    private GuildRankPointOpsManual toGuildRankPointOpsManual(long entityUid,
                                                              ScoreManualModifyDTO modifyDTO,
                                                              Date month,
                                                              String operator) {
        GuildRankPointOpsManual opsManual = new GuildRankPointOpsManual();
        opsManual.setId(modifyDTO.getId());
        opsManual.setTargetMonth(month);
        opsManual.setEntityUid(entityUid);
        opsManual.setModifyType(modifyDTO.getModifyType());
        opsManual.setModifyNum(modifyDTO.getModifyNum());
        opsManual.setModifyReason(modifyDTO.getModifyReason());
        opsManual.setPassport(operator);
        opsManual.setCreateTime(new Date());
        opsManual.setUpdateTime(new Date());
        return opsManual;
    }

    private GuildRankPointOpsLog toGuildRankPointOpsLog(byte opeType,
                                                        long entityUid,
                                                        byte modifyType,
                                                        Date targetMonth,
                                                        int modifyNum,
                                                        String modifyReason,
                                                        String operator) {
        GuildRankPointOpsLog log = new GuildRankPointOpsLog();
        log.setOpeType(opeType);
        log.setEntityUid(entityUid);
        log.setModifyType(modifyType);
        log.setTargetMonth(targetMonth);
        log.setModifyNum(modifyNum);
        log.setModifyReason(modifyReason);
        log.setPassport(operator);
        log.setCreateTime(new Date());
        log.setUpdateTime(new Date());
        return log;
    }

    public Resp<?> del(ScoreManualDelDTO modifyDTO, String operator) {
        // 把原本这条查出来
        GuildRankPointOpsManual opsManual = opsManualMapper.selectByPrimaryKey(modifyDTO.getId());
        if (opsManual == null) {
            return Resp.createByError("原记录不存在");
        }
        opsManualMapper.deleteByPrimaryKey(modifyDTO.getId());
        // 记录流水
        opsLogMapper.insert(toGuildRankPointOpsLog(OPE_TYPE_DEL, opsManual.getEntityUid(), opsManual.getModifyType(), opsManual.getTargetMonth(), opsManual.getModifyNum(), modifyDTO.getDelReason(), operator));
        return Resp.createBySuccess();
    }

    public PageResp<List<ManualOpsLogListVO>> changeLogPageList(byte modifyType, List<Long> uids, int pageNum, int pageSize) {
        GuildRankPointOpsLogExample example = new GuildRankPointOpsLogExample();
        GuildRankPointOpsLogExample.Criteria criteria = example.createCriteria();
        if (uids != null) {
            criteria.andEntityUidIn(uids);
        }
        if (modifyType > 0) {
            criteria.andOpeTypeEqualTo(modifyType);
        }

        int count = opsLogMapper.countByExample(example);
        if (count == 0) {
            return PageResp.createBySuccess(Lists.newArrayList(), count);
        }

        example.setOffset((pageNum - 1) * pageSize);
        example.setLimit(pageSize);
        example.setOrderByClause("id desc");
        List<GuildRankPointOpsLog> opsManuals = opsLogMapper.selectByExample(example);

        List<ManualOpsLogListVO> list = opsManuals.stream().map(this::toManualOpsLogListVO).collect(Collectors.toList());
        return PageResp.createBySuccess(list, count);
    }

    private ManualOpsLogListVO toManualOpsLogListVO(GuildRankPointOpsLog log) {
        ManualOpsLogListVO vo = new ManualOpsLogListVO();
        vo.setId(log.getId());
        vo.setModifyReason(log.getModifyReason());
        vo.setMonth(DateFormatUtils.format(log.getTargetMonth(), "yyyy-MM"));
        vo.setMainGuildUid(log.getEntityUid());
        vo.setModifyType(log.getModifyType());
        vo.setOpeType(log.getOpeType());
        vo.setModifyNum(log.getModifyNum());
        vo.setPassport(log.getPassport());
        vo.setModifyTime(DateFormatUtils.format(log.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
        return vo;
    }
}
