package com.yy.yyzone.guildrank.dto.guildranktaskconfig;

import com.yy.yyzone.guildrank.db.gen.model.GuildRankPlatformActivityTaskResult;
import com.yy.yyzone.guildrank.db.gen.model.GuildRankTaskRequirementsAndResult;
import lombok.Data;

import java.text.DecimalFormat;

@Data
public class GuildRankAllTaskResultDTO {
    private static final DecimalFormat NUMBER_FORMATTER = new DecimalFormat("#,###");
    private String guildCmpName;
    private String onlineBind3And4StarTaskDetail;
    private String onlineBind3And4StarCompletionForAdmin;
    private String onlineBind3And4StarCompletion;
    private String onlineBind3StarCompletion;
    private String onlineBind4StarCompletion;
    private Integer onlineBind3And4StarScore;

    private String newHighWaistAnchorTaskDetail;
    private String newHighWaistAnchorCompletion;
    private String newHighWaistAnchorCompletionForAdmin;
    private String newHighAnchorCompletion;
    private String newWaistAnchorCompletion;
    private Integer newHighWaistAnchorScore;

    private String newAnchorBlueDiamondTaskDetail;
    private String newAnchorBlueDiamondCompletion;
    private Integer newAnchorBlueDiamondScore;

    private String guildBlueDiamondTaskDetail;
    private String guildBlueDiamondCompletion;
    private Integer guildBlueDiamondScore;


    private String monthBlueIncomeTaskDetail;
    private String monthBlueIncomeCompletion;
    private Integer monthBlueIncomeScore;

    /**
     * 春季头条任务详情
     */
    private String springHeadlineTaskDetail;

    /**
     * 春季头条完成情况
     */
    private String springHeadlineCompletion;

    /**
     * 春季头条得分
     */
    private Integer springHeadlineScore;

    /**
     * 夏季头条任务详情
     */
    private String summerHeadlineTaskDetail;

    /**
     * 夏季头条完成情况
     */
    private String summerHeadlineCompletion;

    /**
     * 夏季头条得分
     */
    private Integer summerHeadlineScore;

    /**
     * 公会赛S1任务详情
     */
    private String guildRaceS1TaskDetail;

    /**
     * 公会赛S1完成情况
     */
    private String guildRaceS1Completion;

    /**
     * 公会赛S1得分
     */
    private Integer guildRaceS1Score;

    /**
     * 公会赛S2任务详情
     */
    private String guildRaceS2TaskDetail;

    /**
     * 公会赛S2完成情况
     */
    private String guildRaceS2Completion;

    /**
     * 公会赛S2得分
     */
    private Integer guildRaceS2Score;

    /**
     * 公会赛正赛任务详情
     */
    private String guildRaceMainTaskDetail;

    /**
     * 公会赛正赛完成情况
     */
    private String guildRaceMainCompletion;

    /**
     * 公会赛正赛得分
     */
    private Integer guildRaceMainScore;

    /**
     * 个人赛正赛任务详情
     */
    private String personalRaceMainTaskDetail;

    /**
     * 个人赛正赛完成情况
     */
    private String personalRaceMainCompletion;

    /**
     * 个人赛正赛得分
     */
    private Integer personalRaceMainScore;


    public static GuildRankAllTaskResultDTO build(GuildRankTaskRequirementsAndResult guildRankTaskRequirementsAndResult , GuildRankPlatformActivityTaskResult guildRankPlatformActivityTaskResult  ){
        GuildRankAllTaskResultDTO guildRankAllTaskResultDTO = new GuildRankAllTaskResultDTO();
        if(guildRankPlatformActivityTaskResult != null){
            guildRankAllTaskResultDTO.setSpringHeadlineTaskDetail(guildRankPlatformActivityTaskResult.getSpringHeadlineTaskDetail());
            guildRankAllTaskResultDTO.setSpringHeadlineCompletion(guildRankPlatformActivityTaskResult.getSpringHeadlineCompletion());
            guildRankAllTaskResultDTO.setSpringHeadlineScore(guildRankPlatformActivityTaskResult.getSpringHeadlineScore());
            guildRankAllTaskResultDTO.setSummerHeadlineTaskDetail(guildRankPlatformActivityTaskResult.getSummerHeadlineTaskDetail());
            guildRankAllTaskResultDTO.setSummerHeadlineCompletion(guildRankPlatformActivityTaskResult.getSummerHeadlineCompletion());
            guildRankAllTaskResultDTO.setSummerHeadlineScore(guildRankPlatformActivityTaskResult.getSummerHeadlineScore());
            guildRankAllTaskResultDTO.setGuildRaceS1TaskDetail(guildRankPlatformActivityTaskResult.getGuildRaceS1TaskDetail());
            guildRankAllTaskResultDTO.setGuildRaceS1Completion(guildRankPlatformActivityTaskResult.getGuildRaceS1Completion());
            guildRankAllTaskResultDTO.setGuildRaceS1Score(guildRankPlatformActivityTaskResult.getGuildRaceS1Score());
            guildRankAllTaskResultDTO.setGuildRaceS2TaskDetail(guildRankPlatformActivityTaskResult.getGuildRaceS2TaskDetail());
            guildRankAllTaskResultDTO.setGuildRaceS2Completion(guildRankPlatformActivityTaskResult.getGuildRaceS2Completion());
            guildRankAllTaskResultDTO.setGuildRaceS2Score(guildRankPlatformActivityTaskResult.getGuildRaceS2Score());
            guildRankAllTaskResultDTO.setGuildRaceMainTaskDetail(guildRankPlatformActivityTaskResult.getGuildRaceMainTaskDetail());
            guildRankAllTaskResultDTO.setGuildRaceMainCompletion(guildRankPlatformActivityTaskResult.getGuildRaceMainCompletion());
            guildRankAllTaskResultDTO.setGuildRaceMainScore(guildRankPlatformActivityTaskResult.getGuildRaceMainScore());
            guildRankAllTaskResultDTO.setPersonalRaceMainTaskDetail(guildRankPlatformActivityTaskResult.getPersonalRaceMainTaskDetail());
            guildRankAllTaskResultDTO.setPersonalRaceMainCompletion(guildRankPlatformActivityTaskResult.getPersonalRaceMainCompletion());
            guildRankAllTaskResultDTO.setPersonalRaceMainScore(guildRankPlatformActivityTaskResult.getPersonalRaceMainScore());
        }
        if(guildRankTaskRequirementsAndResult != null){
            guildRankAllTaskResultDTO.setGuildCmpName(guildRankTaskRequirementsAndResult.getGuildCmpName());
            guildRankAllTaskResultDTO.setOnlineBind3And4StarTaskDetail(String.format("本月线上绑定3&4星新主播数≥%s个",guildRankTaskRequirementsAndResult.getAvgNewAidRat3Or4AidNum3mAim()));
            guildRankAllTaskResultDTO.setOnlineBind3And4StarScore(guildRankTaskRequirementsAndResult.getAvgNewAidRat3Or4AidNum3mScore());
            guildRankAllTaskResultDTO.setOnlineBind3And4StarCompletion(String.format("%s",guildRankTaskRequirementsAndResult.getNewAidRat3AidNum()+ guildRankTaskRequirementsAndResult.getNewAidRat4AidNum()));
            guildRankAllTaskResultDTO.setOnlineBind3And4StarCompletionForAdmin(String.format("%s（3星+4星总数）",guildRankTaskRequirementsAndResult.getNewAidRat3AidNum()+ guildRankTaskRequirementsAndResult.getNewAidRat4AidNum()));
            guildRankAllTaskResultDTO.setOnlineBind3StarCompletion(String.format("%s",guildRankTaskRequirementsAndResult.getNewAidRat3AidNum()));
            guildRankAllTaskResultDTO.setOnlineBind4StarCompletion(String.format("%s",guildRankTaskRequirementsAndResult.getNewAidRat4AidNum()));

            guildRankAllTaskResultDTO.setNewHighWaistAnchorTaskDetail(String.format("本月新增头&腰部主播%s个",guildRankTaskRequirementsAndResult.getAvgNewAidRat3Or4AidNum3mAim()));
            guildRankAllTaskResultDTO.setNewHighWaistAnchorCompletionForAdmin(String.format("%s （高优+腰部总数）",guildRankTaskRequirementsAndResult.getUpgrdHighAidNum()+ guildRankTaskRequirementsAndResult.getUpgrdWaistAidNum()));
            guildRankAllTaskResultDTO.setNewHighWaistAnchorCompletion(String.format("%s",guildRankTaskRequirementsAndResult.getUpgrdHighAidNum()+ guildRankTaskRequirementsAndResult.getUpgrdWaistAidNum()));
            guildRankAllTaskResultDTO.setNewHighWaistAnchorScore(guildRankTaskRequirementsAndResult.getAvgUpgrdHighWaistAidNum3mScore());
            guildRankAllTaskResultDTO.setNewHighAnchorCompletion(String.format("%s",guildRankTaskRequirementsAndResult.getUpgrdHighAidNum()));
            guildRankAllTaskResultDTO.setNewWaistAnchorCompletion(String.format("%s",guildRankTaskRequirementsAndResult.getUpgrdWaistAidNum()));

            guildRankAllTaskResultDTO.setNewAnchorBlueDiamondTaskDetail(String.format("新主播蓝钻任务完成率>%s",guildRankTaskRequirementsAndResult.getNewAidIncmComRateAim())+"%");
            guildRankAllTaskResultDTO.setNewAnchorBlueDiamondCompletion(String.format("%s",guildRankTaskRequirementsAndResult.getNewAidIncmComRate())+"%");
            guildRankAllTaskResultDTO.setNewAnchorBlueDiamondScore(guildRankTaskRequirementsAndResult.getNewAidIncmComRateScore());

            guildRankAllTaskResultDTO.setGuildBlueDiamondTaskDetail(String.format("完成公会蓝钻任务目标%s",guildRankTaskRequirementsAndResult.getGuildTaskIncmLvlAim()));
            guildRankAllTaskResultDTO.setGuildBlueDiamondCompletion(String.format("%s",guildRankTaskRequirementsAndResult.getGuildTaskIncmLvl()));
            guildRankAllTaskResultDTO.setGuildBlueDiamondScore(guildRankTaskRequirementsAndResult.getGuildTaskIncmLvlScore());

            if(guildRankTaskRequirementsAndResult.getAccumIncmAmtAim() != null){
                guildRankAllTaskResultDTO.setMonthBlueIncomeTaskDetail(String.format("本月月累计蓝钻>=%skw",guildRankTaskRequirementsAndResult.getAccumIncmAmtAimForBlueDiamondToKw()));
            }
            if(guildRankTaskRequirementsAndResult.getIncmAmtForBlueDiamond() != null){
                guildRankAllTaskResultDTO.setMonthBlueIncomeCompletion(String.format("%s",NUMBER_FORMATTER.format(guildRankTaskRequirementsAndResult.getIncmAmtForBlueDiamond())));
            }
            guildRankAllTaskResultDTO.setMonthBlueIncomeScore(guildRankTaskRequirementsAndResult.getAccumIncmAmtSocre());
        }
        return guildRankAllTaskResultDTO;
    }
}
