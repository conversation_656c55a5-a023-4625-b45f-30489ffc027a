package com.yy.yyzone.guildrank.dto.guildrankconfig;

import com.yy.yyzone.guildrank.constant.RankConstant;
import com.yy.yyzone.guildrank.util.Resp;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Data
public class RankScoreConfig {
    @NotNull(message = "【主体星级】分值设置不能为空")
    @NotEmpty(message = "【主体星级】分值设置不能为空")
    private List<RankScoreConfigItem> items;

    public Resp validate() {
        if (CollectionUtils.isEmpty(items) || items.size() != RankConstant.RANK_COUNT) {
            return Resp.createByError("【主体星级】应有" + RankConstant.RANK_COUNT + "个设置");
        }
        Map<Integer, RankScoreConfigItem> map = items.stream().collect(Collectors.toMap(l -> l.getRank().intValue(), Function.identity()));
        for (int i = 1; i <= RankConstant.RANK_COUNT; i++) {
            RankScoreConfigItem item = map.get(i);
            if (item == null) {
                return Resp.createByError("【主体星级】设置" + i + "星不存在");
            }
            RankScoreConfigItem nextItem = map.get(i + 1);
            if (i != RankConstant.RANK_COUNT && nextItem == null) {
                return Resp.createByError("【主体星级】设置异常(1001)");
            }
            if (nextItem != null) {
                if (item.getUpper() != nextItem.getLower()) {
                    return Resp.createByError("【主体星级】设置异常：分值不连续");
                }
            }
        }
        // if (map.get(1).getLower() != 0) {
        //     return Resp.createByError("【主体星级】设置异常：1星开始分值应为0");
        // }
        if (map.get(RankConstant.RANK_COUNT).getUpper() != null) {
            return Resp.createByError("【主体星级】设置异常：7星结束分值应为正无穷");
        }

        return Resp.createBySuccess();
    }
}