package com.yy.yyzone.guildrank.service;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

@Service
public class SwitchService {

    @Value("${switch.to.cmpStarLvl.date}")
    private String switchToCmpStarLvlDateStr;

    /**
     * 是否切换段位到cmp_start_lvl字段
     * @return
     */
    public boolean isSwitchToCmpStarLvl() {
        LocalDate switchDate = LocalDate.parse(switchToCmpStarLvlDateStr);
        return !switchDate.isAfter(LocalDate.now());
    }
}
