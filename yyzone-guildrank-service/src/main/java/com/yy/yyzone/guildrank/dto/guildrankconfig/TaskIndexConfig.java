package com.yy.yyzone.guildrank.dto.guildrankconfig;

import com.yy.yyzone.guildrank.constant.RankConstant;
import com.yy.yyzone.guildrank.util.Resp;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 任务指标分值设置
 */
@Data
public class TaskIndexConfig {
    /**
     * 任务指标分值设置 key为指标
     * 新签3&4星主播数:NEW_SIGN_STAR32_ANCHOR_COUNT
     * 新增头部主播数:NEW_HEAD_ANCHOR_COUNT
     * 新增腰部主播数:NEW_WAIST_ANCHOR_COUNT
     * 续约1-6级金牌艺人数:RENEW_16_GOLD_ARIST_COUNT
     * 礼物流水占比总流水≥70%:GIFT_FLOW_RATIO_GE70
     */
    private Map<String, TaskIndexConfigItem> indexMap;

    /**
     * 健康度加分总上限
     */
    @Valid
    @NotNull(message = "【任务指标】健康度加分总上限不能为空")
    private Long healthScoreTotalLimit;

    /**
     * 积分加分总上限
     */
    @Valid
    @NotNull(message = "【任务指标】积分加分总上限不能为空")
    private Long pointScoreTotalLimit;

    public Resp validate() {
        if (MapUtils.isEmpty(indexMap)) {
            return Resp.createByError("【任务指标】分值设置不能为空");
        }
        Set<String> names = Arrays.stream(RankConstant.TaskIndex.values()).map(l -> l.getCode()).collect(Collectors.toSet());
        String s = indexMap.keySet().stream().filter(i -> !names.contains(i)).collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(s)) {
            return Resp.createByError("【任务指标】指标不存在：" + s);
        }
        return Resp.createBySuccess();
    }
}