package com.yy.yyzone.guildrank.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.ent.commons.protopack.util.Ulong;
import com.yy.ent.mobile.metrics.MetricsStopWatch;
import com.yy.java.webdb.WebdbQueryColumnEnum;
import com.yy.java.webdb.WebdbUserInfo;
import com.yy.java.webdb.sa.SaWebdbResult;
import com.yy.java.webdb.sa.SaWebdbUserInfoService;
import com.yy.yyzone.guildrank.api.GuildNameClient;
import com.yy.yyzone.guildrank.api.dto.PBatchGetGuildNameReq64;
import com.yy.yyzone.guildrank.api.dto.PBatchGetGuildNameResp64;
import com.yy.yyzone.guildrank.util.ResponseCode;
import com.yy.yyzone.guildrank.yrpc.client.GuildTypeService;
import com.yy.yyzone.guildrank.yrpc.dto.PBatchGetGuildTypeReq64;
import com.yy.yyzone.guildrank.yrpc.dto.PBatchGetGuildTypeResp64;
import com.yy.yyzone.guildrank.yrpc.dto.PGetGuildInfoReq64;
import com.yy.yyzone.guildrank.yrpc.dto.PGetGuildInfoResp64;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/12/5
 */
@Service
public class UserInfoService {

    private static Logger logger = LoggerFactory.getLogger(UserInfoService.class);

    @Autowired
    private SaWebdbUserInfoService webdbUserInfoService;

    @Reference(protocol = "yyp", owner = "guild_name")
    private GuildNameClient guildNameClient;

    @Reference(protocol = "yyp", owner = "yyzone_ent_star")
    private GuildTypeService guildTypeService;

    /**
     * 批量获取userInfo
     *
     * @param uidList uid List
     * @return
     */
    public Map<String, WebdbUserInfo> getUserInfo(List<Long> uidList) {
        if(CollectionUtils.isEmpty(uidList)) {
            return Collections.emptyMap();
        }
        int listSize = uidList.size();
        logger.info("batchGetUserInfo listSize:{}", listSize);

        int dataSize = 500;
        Map<String, WebdbUserInfo> result = Maps.newHashMap();
        if (uidList.size() < dataSize) {
            SaWebdbResult<Map<String, WebdbUserInfo>> webdbResult = webdbUserInfoService.getUserInfo(uidList);
            result = webdbResult.getCode() == 0 ? webdbResult.getData() : Maps.newHashMap();
        } else {
            int maxRound = listSize / dataSize + 1;
            for (int i = 1; i <= maxRound; i++) {
                int start = (i - 1) * dataSize;
                int end = start + dataSize;

                if (end > listSize) {
                    end = listSize;
                }
                List<Long> subList = uidList.subList(start, end);
                SaWebdbResult<Map<String, WebdbUserInfo>> webdbResult = webdbUserInfoService.getUserInfo(subList);
                if (webdbResult.getCode() == 0 && webdbResult.getData() != null) {
                    result.putAll(Maps.newHashMap(webdbResult.getData()));
                }
            }
        }

        return result;
    }

    public Map<Long, String> batchGetGuildName(List<Long> uidList) {
        Map<Long, String> map = Maps.newHashMap();
        if (CollectionUtils.isEmpty(uidList)) {
            return map;
        }
        List<List<Long>> lists = Lists.partition(uidList, 500);
        for (List<Long> l : lists) {
            map.putAll(getGuildName(l));
        }
        return map;
    }

    /**
     * 批量获取公会名称
     *
     * @param uidList
     * @return
     */
    private Map<Long, String> getGuildName(List<Long> uidList) {
        Map<Long, String> map = Maps.newHashMap();
        if (CollectionUtils.isEmpty(uidList)) {
            return map;
        }

        List<Ulong> uidLongList = uidList.stream().map(Ulong::new).collect(Collectors.toList());

        try {
            PBatchGetGuildNameReq64 req = new PBatchGetGuildNameReq64();
            req.setOwuids(uidLongList);
            logger.info("uri:103 << 8 | 252 req:{}", req);
            PBatchGetGuildNameResp64 resp = guildNameClient.batchGetGuildName(req);
            logger.info("uri:104 << 8 | 252 resp:{}", resp);

            if (resp.getResult().intValue() == 0) {
                return resp.getNameInfo().entrySet().stream().collect(Collectors.toMap(k -> k.getKey().longValue(), Map.Entry::getValue));
            }
        } catch (Exception e) {
            logger.error("batchGetGuildName fail, uidList:{}", uidList, e);
        }
        return map;
    }

    public String getGuildName(Long uid){
        return uid == null || uid <= 0 ? "" : batchGetGuildName(Lists.newArrayList(uid)).get(uid);
    }


    /**
     * 批量获取公会类型
     *
     * @param uidList 公会uid
     * @return 0-普通 1-星级 2-金牌
     */
    public Map<Long, Integer> batchGetGuildType(List<Long> uidList) {
        if (CollectionUtils.isEmpty(uidList)) {
            return Collections.emptyMap();
        }

        MetricsStopWatch metricsStopWatch = MetricsStopWatch.startInternalWatch();
        int code = ResponseCode.SUCCESS.getCode();
        try {
            PBatchGetGuildTypeReq64 req = new PBatchGetGuildTypeReq64();
            req.setOwUids(uidList);
            logger.info("batchGetGuildTypeNew req:{}", req);
            PBatchGetGuildTypeResp64 resp = guildTypeService.batchGetGuildTypeNew(req);
            logger.info("batchGetGuildTypeNew resp:{}", resp);
            return resp.getTypes();
        } catch (Exception ex) {
            code = ResponseCode.ERROR.getCode();
            logger.error("batchGetGuildType exception uidList:{}", uidList, ex);
        } finally {
            metricsStopWatch.code(code).uri("GuildService_batchGetGuildType").markDurationAndCode();
        }

        return Maps.newHashMap();
    }

    /**
     * 获取公会类型
     *
     * @param uid 公会uid
     * @return 0-普通 1-星级 2-金牌
     */
    public int getGuildType(Long uid) {
        return batchGetGuildType(Lists.newArrayList(uid)).getOrDefault(uid, 0);
    }

    public PGetGuildInfoResp64 getGuildInfoFromCache(Long guildUid) {
        try {
            PGetGuildInfoReq64 req = new PGetGuildInfoReq64();
            req.setOwUid(guildUid);
            logger.info("getGuildInfoFromCache req:{}", req);
            PGetGuildInfoResp64 resp = guildTypeService.getGuildInfoFromCacheNew(req);
            logger.info("getGuildInfoFromCache resp:{}", resp);
            return resp;
        } catch (Exception ex) {
            logger.error("getGuildInfoFromCacheNew exception guildUid:{}", guildUid, ex);
            return new PGetGuildInfoResp64();
        }
    }

    /**
     * uid vs yy
     * @param uidList
     * @return
     */
    public Map<Long,Long> batchGetYyByUid(List<Long> uidList) {
        if (CollectionUtils.isEmpty(uidList)) {
            return Collections.emptyMap();
        }
        Map<String, WebdbUserInfo> userInfoMap = getUserInfo(uidList);
        if(MapUtils.isNotEmpty(userInfoMap)){
            return userInfoMap.entrySet().stream().collect(Collectors.toMap(e -> Long.parseLong(e.getKey()), e -> Long.parseLong(e.getValue().getYyno())));
        }
        return Collections.emptyMap();
    }

    /**
     * uid获取yy号
     *
     * @param uid uid
     * @return yy
     */
    public String getYyByUid(Long uid) {
        if (uid == null || uid == 0) {
            return StringUtils.EMPTY;
        }

        SaWebdbResult<WebdbUserInfo> result = webdbUserInfoService.getUserInfo(uid, Sets.newHashSet(WebdbQueryColumnEnum.YYNO));
        return result.getCode() == 0 ? result.getData().getYyno() : StringUtils.EMPTY;
    }

    public Long getUidByYy(String yy) {
        SaWebdbResult<Long> result = webdbUserInfoService.getUidByYyno(yy);
        return result.getCode() == 0 ? result.getData() : 0L;
    }

    /**
     * 批量根据yy号查uid
     * @param yys
     * @return
     */
    public List<Long> batchGetUidByYys(List<Long> yys) {
        if (CollectionUtils.isEmpty(yys)) {
            return Collections.emptyList();
        }
        SaWebdbResult<Map<String, Long>> result = webdbUserInfoService.getUidByYyno(yys.stream().map(String::valueOf).collect(Collectors.toList()));
        return result.getCode() != 0 ? Collections.emptyList()
                : result.getData().values().stream().collect(Collectors.toList());
    }
    /**
     * 批量根据yy号查uid
     *
     * @param yyNos
     * @return
     */
    public Map<String, String> batchGetUidByYy(List<String> yyNos) {
        if (CollectionUtils.isEmpty(yyNos)) {
            return Maps.newHashMap();
        }
        SaWebdbResult<Map<String, Long>> result = webdbUserInfoService.getUidByYyno(yyNos);
        return result.getCode() != 0 ? Collections.emptyMap()
                : result.getData().entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, v -> v.getValue().toString()));
    }

    public WebdbUserInfo getUserInfo(Long uid) {
        SaWebdbResult<WebdbUserInfo> result = webdbUserInfoService.getUserInfo(uid);
        return result.getCode() == 0 ? result.getData() : null;
    }
}
