package com.yy.yyzone.guildrank.dto.export;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExportData {
    /**
     * 标题
     */
    private List<String> titles;

    /**
     * 行数据
     */
    private List<List<String>> rows;

    /**
     *
     */
    private String sheetName;

    /**
     * 合并单元格
     */
    private List<CellRangeAddress> merges;
}