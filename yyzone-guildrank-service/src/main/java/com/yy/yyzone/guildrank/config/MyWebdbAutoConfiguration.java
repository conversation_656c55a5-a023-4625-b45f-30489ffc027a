package com.yy.yyzone.guildrank.config;

import com.yy.java.webdb.sa.SaWebdbChannelInfoService;
import com.yy.java.webdb.sa.SaWebdbConfig;
import com.yy.java.webdb.sa.SaWebdbUserInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * webdb自动装配 by longtuxing
 */
@Configuration
@ConditionalOnProperty(prefix = "yy.webdb", name = "enable", havingValue = "true")
public class MyWebdbAutoConfiguration {

    @Value("${yy.s2s.access-account:}")
    private String appName;

    @Bean
    public SaWebdbConfig saWebdbConfig() {
        SaWebdbConfig webdbConfig = new SaWebdbConfig();
        //appKey：业务英文标识
        webdbConfig.setAppKey(appName);
        //authUser：sa申请的appid
        webdbConfig.setAuthUser("**********");
        //authKey：sa申请的appSecret
        webdbConfig.setAuthKey("8dd0d5502ccc497ba62e0b29f84117a3");
        return webdbConfig;
    }

    @Bean("saUserInfoService")
    @ConditionalOnExpression("!'${yy.env:}'.equalsIgnoreCase('dev')")
    public SaWebdbUserInfoService saUserInfoService(@Autowired SaWebdbConfig saWebdbConfig) {
        return new SaWebdbUserInfoService(saWebdbConfig, System.getenv("MY_HOST_IP") + ":12500");
    }

    @Bean("saWebdbChannelInfoService")
    @ConditionalOnExpression("!'${yy.env:}'.equalsIgnoreCase('dev')")
    public SaWebdbChannelInfoService saWebdbChannelInfoService(@Autowired SaWebdbConfig saWebdbConfig) {
        return new SaWebdbChannelInfoService(saWebdbConfig, System.getenv("MY_HOST_IP") + ":12500");
    }

    /** 本地启动需指定server **/
    @Bean("saUserInfoService")
    @ConditionalOnExpression("'${yy.env:}'.equalsIgnoreCase('dev')")
    public SaWebdbUserInfoService userInfoService(@Autowired SaWebdbConfig saWebdbConfig) {
        return new SaWebdbUserInfoService(saWebdbConfig, "webdb-dbdproxy-sz.yy.com:8090");
    }

    @Bean("saWebdbChannelInfoService")
    @ConditionalOnExpression("'${yy.env:}'.equalsIgnoreCase('dev')")
    public SaWebdbChannelInfoService webdbChannelInfoService(@Autowired SaWebdbConfig saWebdbConfig) {
        return new SaWebdbChannelInfoService(saWebdbConfig, "webdb-dbdproxy-sz.yy.com:8090");
    }
}
