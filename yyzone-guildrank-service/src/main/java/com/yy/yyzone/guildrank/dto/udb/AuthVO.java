package com.yy.yyzone.guildrank.dto.udb;

import org.apache.commons.lang3.StringUtils;

import java.util.Map;

public class AuthVO {
    private String authUser;
    private String authKey;

    private Map<String, String> keyValue;

    public AuthVO(String authUser, String authKey, Map<String, String> keyValue) {
        this.authUser = authUser;
        this.authKey = authKey;
        this.keyValue = keyValue;
    }

    public String getAuthUser() {
        return authUser;
    }

    public void setAuthUser(String authUser) {
        this.authUser = authUser;
    }

    public String getAuthKey() {
        return authKey;
    }

    public void setAuthKey(String authKey) {
        this.authKey = authKey;
    }

    public Map<String, String> getKeyValue() {
        return keyValue;
    }

    public void setKeyValue(Map<String, String> keyValue) {
        this.keyValue = keyValue;
    }

    @Override
    public String toString() {
        return "AuthorizeMsg{" +
                "authUser='" + authUser + '\'' +
                ", authKey='" + (StringUtils.isNotEmpty(authKey) ? authKey.replaceAll(".{2,2}$", "**") : authKey) + '\'' +
                ", keyValue=" + keyValue +
                '}';
    }
}
