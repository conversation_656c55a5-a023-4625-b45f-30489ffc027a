package com.yy.yyzone.guildrank.util;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

/**
 * <AUTHOR>
 * @since 2019/7/30
 */
public class YyTicketUtil {


    public static String  getAirplaneTicket(String fromUid, String toUid) throws Exception {
        String airplaneTicket = "yy://imchat-[#{encode}]";
        String source = new StringBuilder()
                .append("uid").append("=").append(toUid)
                .append("&")
                .append("from").append("=").append(fromUid)
                .append("&")
                .append("ex").append("=").append(System.currentTimeMillis() + (1000 * 30))
                .toString();
        String base64 = encrypt(source, "YWlydGlja2V0tick");
        String result = airplaneTicket.replace("#{encode}", base64);
        return result;
    }

    private static String encrypt(String content, String passwd) throws Exception{

        String padChar = "\0";
        int padCount = content.getBytes().length % 16;
        if (padCount > 0) {
            for (int i = 0; i < 16 - padCount; i++) {
                content = content + padChar;
            }
        }
        Cipher aesECB = Cipher.getInstance("AES/ECB/NoPadding");
        SecretKeySpec key = new SecretKeySpec(passwd.getBytes(), "AES");
        aesECB.init(Cipher.ENCRYPT_MODE, key);
        byte[] result = aesECB.doFinal(content.getBytes("utf8"));
        return new String(Base64.encodeBase64(result));

    }
}
