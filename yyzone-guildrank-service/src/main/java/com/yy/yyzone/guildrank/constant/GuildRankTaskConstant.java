package com.yy.yyzone.guildrank.constant;

import java.util.*;

public interface GuildRankTaskConstant {
    public static final String ONLINE_BIND_3_AND_4_STAR = "onlineBind3And4Star";
    public static final String NEW_HIGH_WAIST_ANCHOR = "newHighWaistAnchor";
    public static final String NEW_ANCHOR_BLUE_DIAMOND = "newAnchorBlueDiamond";
    public static final String GUILD_BLUE_DIAMOND = "guildBlueDiamond";
    public static final String MONTH_BLUE_INCOME = "monthBlueIncome";
    /**
     * 春季头条任务
     */
    public static final String SPRING_HEADLINE = "springHeadline";

    /**
     * 夏季头条任务
     */
    public static final String SUMMER_HEADLINE = "summerHeadline";

    /**
     * 公会赛S1任务
     */
    public static final String GUILD_RACE_S1 = "guildRaceS1";

    /**
     * 公会赛S2任务
     */
    public static final String GUILD_RACE_S2 = "guildRaceS2";

    /**
     * 公会赛正赛任务
     */
    public static final String GUILD_RACE_MAIN = "guildRaceMain";
    /**
     * 个人赛正赛任务
     */
    public static final String PERSONAL_RACE_MAIN = "personalRaceMain";


    public static String getTagName(String task){
        if(ONLINE_BIND_3_AND_4_STAR.equals(task)){
            return "线上绑定3&4星主播";
        }else if(NEW_HIGH_WAIST_ANCHOR.equals(task)){
            return "新增高优&腰部主播";
        }else if(NEW_ANCHOR_BLUE_DIAMOND.equals(task)){
            return "新主播蓝钻";
        }else if(GUILD_BLUE_DIAMOND.equals(task)){
            return "公会蓝钻";
        }else if(MONTH_BLUE_INCOME.equals(task)){
            return "自然月累计蓝钻收入";
        }else if(SPRING_HEADLINE.equals(task)){
            return "春季头条";
        }else if(SUMMER_HEADLINE.equals(task)){
            return "夏季头条";
        }else if (GUILD_RACE_S1.equals(task)){
            return "公会赛S1";
        }else if (GUILD_RACE_S2.equals(task)){
            return "公会赛S2";
        }else if (GUILD_RACE_MAIN.equals(task)){
            return "公会赛正赛";
        }else if (PERSONAL_RACE_MAIN.equals(task)){
            return "个人赛正赛";
        }
        throw new IllegalArgumentException("任务类型不存在 "+ task);
    }


    public static final List<String> ALL_TASK_TYPES = Collections.unmodifiableList(Arrays.asList(
            ONLINE_BIND_3_AND_4_STAR,
            NEW_HIGH_WAIST_ANCHOR,
            NEW_ANCHOR_BLUE_DIAMOND,
            GUILD_BLUE_DIAMOND,
            MONTH_BLUE_INCOME,
            SPRING_HEADLINE,
            SUMMER_HEADLINE,
            GUILD_RACE_S1,
            GUILD_RACE_S2,
            GUILD_RACE_MAIN,
            PERSONAL_RACE_MAIN));
}
