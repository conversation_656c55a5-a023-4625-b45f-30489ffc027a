package com.yy.yyzone.guildrank.service;

import com.baidubce.BceClientConfiguration;
import com.baidubce.Protocol;
import com.baidubce.auth.BceCredentials;
import com.baidubce.auth.DefaultBceCredentials;
import com.baidubce.auth.DefaultBceSessionCredentials;
import com.baidubce.services.bos.BosClient;
import com.baidubce.services.bos.BosClientConfiguration;
import com.baidubce.services.bos.model.ObjectMetadata;
import com.baidubce.services.bos.model.PutObjectRequest;
import com.baidubce.services.bos.model.PutObjectResponse;
import com.baidubce.services.sts.StsClient;
import com.baidubce.services.sts.model.GetSessionTokenRequest;
import com.baidubce.services.sts.model.GetSessionTokenResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.UUID;

@Slf4j
public class BosService {
    private String bucket;
    private String ak;
    private String sk;
    private BosClient bosClient;
    private String endpoint;
    private String stsEndpoint;
    private StsClient stsClient;

    private static final String PREFIX = "/guildrank/";

    @PostConstruct
    public void init() {
        BosClientConfiguration config = new BosClientConfiguration();
        config.setCredentials(new DefaultBceCredentials(getAk(), getSk()));
        config.setEndpoint(getEndpoint());
        config.setProtocol(Protocol.HTTPS);
        bosClient = new BosClient(config);
        log.info("bos init bucket:{}", getBucket());
        // 获取STS
        BceCredentials credentials = new DefaultBceCredentials(getAk(), getSk());
        this.stsClient = new StsClient(
                new BceClientConfiguration().withEndpoint(stsEndpoint).withCredentials(credentials)
        );
    }

    /**
     * 上传文件
     *
     * @param file
     * @return /bucket/key
     */
    public String putFile(MultipartFile file) {
        String suffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
        String key = PREFIX +UUID.randomUUID().toString() + suffix;
        try(InputStream inStream = file.getInputStream()) {
            ObjectMetadata metadata = new ObjectMetadata();
            PutObjectRequest req = new PutObjectRequest(getBucket(), key, inStream, metadata);
            req.setStorageClass(BosClient.STORAGE_CLASS_STANDARD);
            PutObjectResponse resp = bosClient.putObject(req);
            log.info("upload stream:{},key:{},etag:{},url:{}", inStream, key, resp.getETag());
            return key;
        } catch (RuntimeException e) {
            log.error("upload exception file:{},key:{}", key, e);
            throw e;
        } catch (IOException e) {
            log.error("upload exception file:{},key:{}", key, e);
            throw new RuntimeException(e);
        }
    }

    public String generatePresignedUrl(String objectKey){
        return this.generatePresignedUrl(objectKey,15* 60);
    }

    public String generatePresignedUrl(String objectKey, int expirationInSeconds) {
        GetSessionTokenResponse response = stsClient.getSessionToken(new GetSessionTokenRequest());
        // 生成bosClient
        BceCredentials bosstsCredentials = new DefaultBceSessionCredentials( response.getAccessKeyId(), response.getSecretAccessKey(),response.getSessionToken());
        BosClientConfiguration config = new BosClientConfiguration();
        config.setCredentials(bosstsCredentials);
        config.setEndpoint(getEndpoint());
        BosClient bosClient = new BosClient(config);
        // 指定用户需要获取的Object所在的Bucket名称、该Object名称、URL的有效时长
        URL url = bosClient.generatePresignedUrl(getBucket(), objectKey, expirationInSeconds);
        return url.toString();
    }

    private String getBucket() {
        return bucket;
    }

    public void setBucket(String bucket) {
        this.bucket = bucket;
    }

    private String getAk() {
        return ak;
    }

    public void setAk(String ak) {
        this.ak = ak;
    }

    private String getSk() {
        return sk;
    }

    public void setSk(String sk) {
        this.sk = sk;
    }

    private String getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }

    public void setStsEndpoint(String stsEndpoint) {
        this.stsEndpoint = stsEndpoint;
    }
}
