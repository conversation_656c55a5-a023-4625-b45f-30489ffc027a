package com.yy.yyzone.guildrank.service;

import com.alibaba.fastjson.JSON;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.yy.yyzone.guildrank.constant.RankConstant;
import com.yy.yyzone.guildrank.db.gen.mapper.GuildrankConfigLogMapper;
import com.yy.yyzone.guildrank.db.gen.mapper.GuildrankConfigMapper;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankConfig;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankConfigLog;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankConfigLogExample;
import com.yy.yyzone.guildrank.dto.guildrankconfig.ConfigLogDto;
import com.yy.yyzone.guildrank.dto.guildrankconfig.ConfigLogReq;
import com.yy.yyzone.guildrank.dto.guildrankconfig.GuildRankConfigDto;
import com.yy.yyzone.guildrank.dto.guildrankconfig.TaskIndexConfig;
import com.yy.yyzone.guildrank.util.PageResp;
import com.yy.yyzone.guildrank.util.Resp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GuildRankConfigService {
    private static final long CONFIG_ID = 1L;
    @Autowired
    private GuildrankConfigMapper configMapper;

    @Autowired
    private GuildrankConfigLogMapper configLogMapper;

    private static final Cache<String, GuildRankConfigDto> CACHE = CacheBuilder.newBuilder()
            .concurrencyLevel(8)
            .initialCapacity(2)
            .maximumSize(1)
            .expireAfterWrite(10, TimeUnit.MINUTES).build();

    public GuildRankConfigDto getConfig(boolean useCache) {
        Function<Void, GuildRankConfigDto> func = (v) -> {
            GuildrankConfig c = configMapper.selectByPrimaryKey(CONFIG_ID);
            if (c == null) {
                return null;
            }

            return JSON.parseObject(c.getConfigJson(), GuildRankConfigDto.class);
        };

        if (useCache) {
            try {
                return CACHE.get("CONFIG", () -> func.apply(null));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return func.apply(null);
    }

    public TaskIndexConfig getTaskIndexConfig(boolean useCache) {
        return null;
    }

    @Transactional(rollbackFor = {RuntimeException.class, Exception.class})
    public Resp save(GuildRankConfigDto configDto, String passport) {
        GuildrankConfig dbConfig = configMapper.selectByPrimaryKey(CONFIG_ID);
        if (dbConfig == null) {
            return Resp.createByError("设置不存在");
        }

        String configJson = JSON.toJSONString(configDto);
        if (configJson.equals(dbConfig.getConfigJson())) {
            return Resp.createByError("未修改任何配置");
        }

        Date now = new Date();
        GuildrankConfig update = new GuildrankConfig();
        update.setId(CONFIG_ID);
        update.setConfigJson(configJson);
        update.setUpdateTime(now);
        update.setUpdatePassport(passport);
        int i = configMapper.updateByPrimaryKeySelective(update);
        if (i == 0) {
            return Resp.createByError("保存失败");
        }
        GuildrankConfigLog configLog = new GuildrankConfigLog();
        configLog.setType(RankConstant.LogTypeConstants.GUILDRANK_CONFIG);
        configLog.setConfigId(CONFIG_ID);
        configLog.setOldData(dbConfig.getConfigJson());
        configLog.setNewData(configJson);
        configLog.setOptPassport(passport);
        configLog.setOptTime(now);
        configLogMapper.insertSelective(configLog);

        return Resp.createBySuccess();
    }

    public PageResp<List<ConfigLogDto>> logList(ConfigLogReq req) {
        GuildrankConfigLogExample example = new GuildrankConfigLogExample();
        GuildrankConfigLogExample.Criteria criteria = example.createCriteria().andConfigIdEqualTo(CONFIG_ID)
                .andTypeEqualTo(RankConstant.LogTypeConstants.GUILDRANK_CONFIG);
        if (req.getId() != null && req.getId() > 0) {
            criteria.andIdEqualTo(req.getId());
        }
        if (req.getOptTimeStart() != null) {
            criteria.andOptTimeGreaterThanOrEqualTo(req.getOptTimeStart());
        }
        if (req.getOptTimeEnd() != null) {
            criteria.andOptTimeLessThanOrEqualTo(req.getOptTimeEnd());
        }
        if (StringUtils.isNotBlank(req.getOptPassport())) {
            criteria.andOptPassportLike("%" + req.getOptPassport().trim() + "%");
        }

        int count = configLogMapper.countByExample(example);
        List<ConfigLogDto> list = Lists.newArrayList();
        if (count > 0) {
            example.setOffset((req.getPageNum() - 1) * req.getPageSize());
            example.setLimit(req.getPageSize());
            example.setOrderByClause("id desc");
            list = configLogMapper.selectByExampleWithBLOBs(example).stream().map(l -> {
                ConfigLogDto dto = new ConfigLogDto();
                BeanUtils.copyProperties(l, dto);
                return dto;
            }).collect(Collectors.toList());
        }
        return PageResp.createBySuccess(list, count);
    }
}
