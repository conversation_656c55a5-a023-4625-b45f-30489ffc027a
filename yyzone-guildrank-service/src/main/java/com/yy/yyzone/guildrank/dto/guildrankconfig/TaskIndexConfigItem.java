package com.yy.yyzone.guildrank.dto.guildrankconfig;

import lombok.Data;

/**
 * 任务指标分值设置项
 */
@Data
public class TaskIndexConfigItem {
    /**
     * 健康度加分（人）可能为null比如指标“礼物流水占比总流水≥70%”
     */
    private Long healthScoreCount;

    /**
     * 健康度加分（分）
     */
    private Long healthScoreUnit;

    /**
     * 健康度加分上限
     */
    private Long healthScoreLimit;


    /**
     * 积分加分（人）可能为null比如指标“礼物流水占比总流水≥70%”
     */
    private Long pointScoreCount;

    /**
     * 积分加分（分）
     */
    private Long pointScoreUnit;

    /**
     * 积分加分上限
     */
    private Long pointScoreLimit;
}