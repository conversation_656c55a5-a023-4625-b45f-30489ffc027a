package com.yy.yyzone.guildrank.service;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.ent.commons.protopack.util.Uint;
import com.yy.yyzone.guildrank.yrpc.client.MoneySettleService;
import com.yy.yyzone.guildrank.yrpc.dto.AccountType2;
import com.yy.yyzone.guildrank.yrpc.dto.IncomeEx;
import com.yy.yyzone.guildrank.yrpc.dto.QueryGuildIncomeReq;
import com.yy.yyzone.guildrank.yrpc.dto.QueryGuildIncomeResp;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Generated;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/8/13
 *
 */
@Service
@Generated("忽略代码规范检查")
public class MoneyInfoService {
    private static Logger logger = LoggerFactory.getLogger(MoneyInfoService.class);

    @Reference(protocol = "yyp", owner = "to_music_gateway", timeout = 10000)
    private MoneySettleService moneySettleService;

    /**
     * 按区间查询合约期内主播收入（147|15）
     * https://doc.yy.com/pages/viewpage.action?pageId=*********
     *
     * @param owUid     公会uid
     * @param uidList   主播uid list
     * @param pageNum   页码
     * @param pageSize  页大小
     * @param queryType 操作类型 1-按天查询 2-按月查询
     * @param moneyType 货币类型 待结算佣金-11 蓝钻-12
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return
     */
    public QueryGuildIncomeResp queryGuildIncome(Long owUid, List<Long> uidList, int pageNum, int pageSize, int queryType,
                                                 Set<Uint> moneyType, String beginTime, String endTime) {
        QueryGuildIncomeReq req = new QueryGuildIncomeReq();
        req.setOptname(new Uint(queryType));
        req.setMoneytype(moneyType);
        req.setBegintime(beginTime);
        req.setEndtime(endTime);
        AccountType2 account = new AccountType2(owUid, (byte) 1);
        req.setAccountType2(account);
        req.setOffset(new Uint((pageNum - 1) * pageSize));
        req.setLimit(new Uint(pageSize));
        Map<String, String> filter = Maps.newHashMap();
        // 收支类型，多个类型以逗号分隔(6-贵族佣金 7-守护佣金)
        filter.put("paytype", "6,7");
        req.setFilter(filter);
        req.setUids(CollectionUtils.isEmpty(uidList) ? Collections.emptySet() : Sets.newHashSet(uidList));

        logger.info("queryGuildIncome, uri:147 << 8 | 15 req:{}", req.toString());
        QueryGuildIncomeResp resp = moneySettleService.queryGuildIncome(req);
        logger.info("queryGuildIncome, uri:148 << 8 | 15 resp:{}", resp.toString());

        return resp;
    }

    /**
     *
     * @param owUid
     * @param queryType 操作类型 1-按天查询 2-按月查询
     * @param beginTime
     * @param endTime
     * @return
     */
    public Map<String, Long> queryGuildBlueDiamond(Long owUid, int queryType, String beginTime, String endTime) {
        QueryGuildIncomeResp resp = queryGuildIncome(owUid, null, 1, 100, queryType,
                Sets.newHashSet(new Uint(12)), beginTime, endTime);
        Map<String, Long> result = Maps.newHashMap();
        if (resp.getResult().intValue() == 0 && CollectionUtils.isNotEmpty(resp.getData())) {
            for (IncomeEx incomeEx : resp.getData()) {
                Map<String, String> details = incomeEx.getMap();
                result.put(details.get("paytime"), Long.parseLong(details.get("diamond")));
            }
        }
        return result;
    }
}