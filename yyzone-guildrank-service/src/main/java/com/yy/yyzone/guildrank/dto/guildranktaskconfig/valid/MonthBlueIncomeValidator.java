package com.yy.yyzone.guildrank.dto.guildranktaskconfig.valid;

import com.yy.yyzone.guildrank.dto.guildranktaskconfig.*;
import org.apache.commons.collections.CollectionUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.List;

/**
 * 自然月月累积蓝钻收入
 */
public class MonthBlueIncomeValidator
        implements ConstraintValidator<MonthBlueIncomeValid, GuildRankTask> {

    @Override
    public boolean isValid(GuildRankTask task, ConstraintValidatorContext ctx) {
        if (task == null) {
            return false;
        }
        List<Range> ranges = task.getCondition().getRanges();
        for(Range range : ranges) {
            if(range.getScore() == null) {
                return fail(ctx, "range.score 不能为空");
            }
            if(CollectionUtils.isEmpty(range.getScore().getIndicators()) && CollectionUtils.isEmpty(range.getScore().getRangeIndicators())){
                return fail(ctx, "range.score.indicators 和 range.score.rangeIndicators 不能同时为空");
            }
            if(CollectionUtils.isNotEmpty(range.getScore().getIndicators()) && CollectionUtils.isNotEmpty(range.getScore().getRangeIndicators())){
                return fail(ctx, "range.score.indicators 和 range.score.rangeIndicators 不能同时配置");
            }
            if(CollectionUtils.isNotEmpty(range.getScore().getIndicators())){
                for(Indicator indicator : range.getScore().getIndicators()){
                    if(!IndicatorEnum.monthBlueIncomeCurrentMonthIncrease.equals(indicator.getIndicator())){
                        return fail(ctx, "range.score.indicators 只能配置 monthBlueIncomeCurrentMonthBlueDiamondIncrease 指标");
                    }
                    if(indicator.getScorePerUnit() == null || indicator.getScorePerUnit() <= 0){
                        return fail(ctx, "range.score.indicators.scorePerUnit 必须大于 0");
                    }
                    if(indicator.getUnitsPerCount() == null || indicator.getUnitsPerCount() <= 0){
                        return fail(ctx, "range.score.indicators.unitsPerCount 必须大于 0");
                    }
                }
            }
            if(CollectionUtils.isNotEmpty(range.getScore().getRangeIndicators())){
                for(RangeIndicator rangeIndicator : range.getScore().getRangeIndicators()){
                    if(rangeIndicator.getScore() == null || rangeIndicator.getScore() <= 0){
                        return fail(ctx, "range.score.rangeIndicators.score 必须大于 0");
                    }
                    if(!IndicatorEnum.monthBlueIncomeCurrentMonthTotal.equals(rangeIndicator.getIndicator())){
                        return fail(ctx, "range.score.rangeIndicators.indicator 只能配置 monthBlueIncomeCurrentMonthBlueDiamondTotal 指标");
                    }
                }
            }
        }
        // score.indicators 必须为空
        if (task.getScore().getIndicators() != null && !task.getScore().getIndicators().isEmpty()) {
            return fail(ctx, "不允许配置 indicators");
        }
        return true;
    }

    private boolean fail(ConstraintValidatorContext ctx, String msg) {
        ctx.disableDefaultConstraintViolation();
        ctx.buildConstraintViolationWithTemplate("自然月月累积蓝钻收入 "+msg).addConstraintViolation();
        return false;
    }
}