package com.yy.yyzone.guildrank.dto;

/**
 * Created by Administrator on 2017/10/17.
 */
public class GuildRankInfo {
    private long owuid;
    private int rank;
    private String pre_1_rank;
    private String pre_2_rank;
    private String pre_3_rank;
    private String pre_4_rank;
    private String pre_5_rank;

    private int hight_quality_anchor_level;
    private int new_anchor_level;
    private int guild_size_leve;
    private int income_level;
    private int out_line_rate_level;
    private int escape_rate_level;

    private String hight_quality_anchor_level_sug;
    private String new_anchor_level_sug;
    private String guild_size_leve_sug;
    private String income_level_sug;
    private String hight_quality_anchor_score_sug;
    private String new_anchor_score_sug;
    private String guild_size_sug;
    private String income_score_sug;

    private String out_line_rate;
    private String escape_rate;
    private String new_anchor_kb_rate;
    private String valid_anchor_remain_rate;
    private String new_anchor_income_rate;

    public long getOwuid() {
		return owuid;
	}

	public void setOwuid(long owuid) {
		this.owuid = owuid;
	}

	public int getRank() {
        return rank;
    }

    public void setRank(int rank) {
        this.rank = rank;
    }

    public String getPre_1_rank() {
        return pre_1_rank;
    }

    public void setPre_1_rank(String pre_1_rank) {
        this.pre_1_rank = pre_1_rank;
    }

    public String getPre_2_rank() {
        return pre_2_rank;
    }

    public void setPre_2_rank(String pre_2_rank) {
        this.pre_2_rank = pre_2_rank;
    }

    public String getPre_3_rank() {
        return pre_3_rank;
    }

    public void setPre_3_rank(String pre_3_rank) {
        this.pre_3_rank = pre_3_rank;
    }

    public String getPre_4_rank() {
        return pre_4_rank;
    }

    public void setPre_4_rank(String pre_4_rank) {
        this.pre_4_rank = pre_4_rank;
    }

    public String getPre_5_rank() {
        return pre_5_rank;
    }

    public void setPre_5_rank(String pre_5_rank) {
        this.pre_5_rank = pre_5_rank;
    }

    public int getHight_quality_anchor_level() {
        return hight_quality_anchor_level;
    }

    public void setHight_quality_anchor_level(int hight_quality_anchor_level) {
        this.hight_quality_anchor_level = hight_quality_anchor_level;
    }

    public int getNew_anchor_level() {
        return new_anchor_level;
    }

    public void setNew_anchor_level(int new_anchor_level) {
        this.new_anchor_level = new_anchor_level;
    }


    public int getGuild_size_leve() {
		return guild_size_leve;
	}

	public void setGuild_size_leve(int guild_size_leve) {
		this.guild_size_leve = guild_size_leve;
	}

	public int getIncome_level() {
        return income_level;
    }

    public void setIncome_level(int income_level) {
        this.income_level = income_level;
    }

    public int getOut_line_rate_level() {
        return out_line_rate_level;
    }

    public void setOut_line_rate_level(int out_line_rate_level) {
        this.out_line_rate_level = out_line_rate_level;
    }

    public int getEscape_rate_level() {
        return escape_rate_level;
    }

    public void setEscape_rate_level(int escape_rate_level) {
        this.escape_rate_level = escape_rate_level;
    }

    public String getHight_quality_anchor_level_sug() {
        return hight_quality_anchor_level_sug;
    }

    public void setHight_quality_anchor_level_sug(String hight_quality_anchor_level_sug) {
        this.hight_quality_anchor_level_sug = hight_quality_anchor_level_sug;
    }

    public String getNew_anchor_level_sug() {
        return new_anchor_level_sug;
    }

    public void setNew_anchor_level_sug(String new_anchor_level_sug) {
        this.new_anchor_level_sug = new_anchor_level_sug;
    }

    public String getGuild_size_leve_sug() {
		return guild_size_leve_sug;
	}

	public void setGuild_size_leve_sug(String guild_size_leve_sug) {
		this.guild_size_leve_sug = guild_size_leve_sug;
	}

	public String getIncome_level_sug() {
        return income_level_sug;
    }

    public void setIncome_level_sug(String income_level_sug) {
        this.income_level_sug = income_level_sug;
    }

    public String getHight_quality_anchor_score_sug() {
        return hight_quality_anchor_score_sug;
    }

    public void setHight_quality_anchor_score_sug(String hight_quality_anchor_score_sug) {
        this.hight_quality_anchor_score_sug = hight_quality_anchor_score_sug;
    }

    public String getNew_anchor_score_sug() {
        return new_anchor_score_sug;
    }

    public void setNew_anchor_score_sug(String new_anchor_score_sug) {
        this.new_anchor_score_sug = new_anchor_score_sug;
    }

    public String getGuild_size_sug() {
		return guild_size_sug;
	}

	public void setGuild_size_sug(String guild_size_sug) {
		this.guild_size_sug = guild_size_sug;
	}

	public String getIncome_score_sug() {
        return income_score_sug;
    }

    public void setIncome_score_sug(String income_score_sug) {
        this.income_score_sug = income_score_sug;
    }

    public String getOut_line_rate() {
        return out_line_rate;
    }

    public void setOut_line_rate(String out_line_rate) {
        this.out_line_rate = out_line_rate;
    }

    public String getEscape_rate() {
        return escape_rate;
    }

    public void setEscape_rate(String escape_rate) {
        this.escape_rate = escape_rate;
    }

    public String getNew_anchor_kb_rate() {
        return new_anchor_kb_rate;
    }

    public void setNew_anchor_kb_rate(String new_anchor_kb_rate) {
        this.new_anchor_kb_rate = new_anchor_kb_rate;
    }

    public String getValid_anchor_remain_rate() {
        return valid_anchor_remain_rate;
    }

    public void setValid_anchor_remain_rate(String valid_anchor_remain_rate) {
        this.valid_anchor_remain_rate = valid_anchor_remain_rate;
    }

    public String getNew_anchor_income_rate() {
        return new_anchor_income_rate;
    }

    public void setNew_anchor_income_rate(String new_anchor_income_rate) {
        this.new_anchor_income_rate = new_anchor_income_rate;
    }
}
