package com.yy.yyzone.guildrank.constant;

import com.google.common.collect.Sets;
import com.yy.yyzone.guildrank.dto.YyDmEntityGuildCmpHealthAnalysisExptDiDTO;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Set;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2023/03/15
 **/
public class RankConstant {

    public static final int DEFAULT_RANK = 1;
    public static final int LAST = -1;
    public static final int PRE_LAST = -2;
    public static final int UPDATE = 1;
    public static final int NOT_UPDATE = 0;

    public interface RedisConstants {
        String ASSOICATE_COMPANY_CHANNEL = "AssoicateCompanyNotifty";
        String RELOAD_RANK_CHANNEL = "ReloadRankNotifty";
        String RANK_WHITELIST = "yyzone:guildrank:whitelist";
    }


    public static final double GIFT_FLOW_RATIO_GE = 0.7D;

    public static final int RANK_COUNT = 7;

    /**
     * 数据指标
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum DataIndex {
        STAR34_ANCHOR_SIGN_COUNT("STAR34_ANCHOR_SIGN_COUNT", "公会3&4星主播签约数", "new_aid_rat_3_4_aid_num", "new_aid_rat_3_4_aid_num_rn_score", d -> tip("差%s人可得%s分", d.getNewAidRat34AidNumTrgtDiff(), d.getNewAidRat34AidNumRnNextScore())),
        STAR34_ANCHOR_SIGN_COUNT_DIFF("STAR34_ANCHOR_SIGN_COUNT_DIFF", "公会3&4星主播签约数趋势", "new_aid_rat_3_4_aid_num_diff", "new_aid_rat_3_4_aid_num_diff_score", d -> tip("差%s人可得%s分", d.getNewAidRat34AidNumDiffTrgtDiff(), d.getNewAidRat34AidNumDiffNextScore())),
        M_DA_HQ_VL_ANCHOR_COUNT("M_DA_HQ_VL_ANCHOR_COUNT", "公会月日均高优有效开播主播数", "avg_high_valid_live_aid_num", "avg_high_valid_live_aid_num_rn_score", d -> tip("差%s人可得%s分", d.getAvgHighValidLiveAidNumTrgtDiff(), d.getAvgHighValidLiveAidNumRnNextScore())),
        M_DA_WAIST_VL_ANCHOR_COUNT("M_DA_WAIST_VL_ANCHOR_COUNT", "公会月日均腰部有效开播主播数", "avg_waist_valid_live_aid_num", "avg_waist_valid_live_aid_num_rn_score", d -> tip("差%s人可得%s分", d.getAvgWaistValidLiveAidNumTrgtDiff(), d.getAvgWaistValidLiveAidNumRnNextScore())),
        HEAD_ANCHOR_COUNT_DIFF("HEAD_ANCHOR_COUNT_DIFF", "头部主播数趋势", "upgrd_high_aid_num_diff", "upgrd_high_aid_num_diff_score", d -> tip("差%s人可得%s分", d.getUpgrdHighAidNumDiffTrgtDiff(), d.getUpgrdHighAidNumDiffNextScore())),
        WAIST_ANCHOR_COUNT_DIFF("WAIST_ANCHOR_COUNT_DIFF", "腰部主播数趋势", "upgrd_waist_aid_num_diff", "upgrd_waist_aid_num_diff_score", d -> tip("差%s人可得%s分", d.getUpgrdWaistAidNumDiffTrgtDiff(), d.getUpgrdWaistAidNumDiffNextScore())),
        M_DA_HQ_VL_ANCHOR_COUNT_DIFF("M_DA_HQ_VL_ANCHOR_COUNT_DIFF", "公会月日均高优有效开播主播数趋势", "avg_high_valid_live_aid_num_diff", "avg_high_valid_live_aid_num_diff_score", d -> tip("差%s人可得%s分", d.getAvgHighValidLiveAidNumDiffTrgtDiff(), d.getAvgHighValidLiveAidNumDiffNextScore())),
        M_DA_WAIST_VL_ANCHOR_COUNT_DIFF("M_DA_WAIST_VL_ANCHOR_COUNT_DIFF", "公会月日均腰部有效开播主播数趋势", "avg_waist_valid_live_aid_num_diff", "avg_waist_valid_live_aid_num_diff_score", d -> tip("差%s人可得%s分", d.getAvgWaistValidLiveAidNumDiffTrgtDiff(), d.getAvgWaistValidLiveAidNumDiffNextScore())),
        N2S_LIVE_RATIO("N2S_LIVE_RATIO", "公会新转存开播率", "avg_nto_valid_live_aid_rate", "avg_nto_valid_live_aid_rate_rn_score", d -> tip("差%s人开播可得%s分", d.getAvgNtoValidLiveAidRateTrgtDiff(), d.getAvgNtoValidLiveAidRateRnNextScore())),
        STOCK_ANCHOR_LIVE_RATIO("STOCK_ANCHOR_LIVE_RATIO", "公会存量主播开播率", "avg_stock_valid_live_aid_rate", "avg_stock_valid_live_aid_rate_rn_score", d -> tip("差%s人开播可得%s分", d.getAvgStockValidLiveAidRateTrgtDiff(), d.getAvgStockValidLiveAidRateRnNextScore())),
        NEW_SIGN_GOLD_ARTIST_COUNT("NEW_SIGN_GOLD_ARTIST_COUNT", "公会新签金牌艺人数", "new_auth_golden_aid_num", "new_auth_golden_aid_num_rn_score", d -> tip("差%s人可得%s分", d.getNewAuthGoldenAidNumTrgtDiff(), d.getNewAuthGoldenAidNumRnNextScore())),

        NEW_ANCHOR_FLOW("NEW_ANCHOR_FLOW", "公会新主播流水金额", "valid_live_new_aid_prod_pay_amt", "valid_live_new_aid_prod_pay_amt_rn_score", null),
        STOCK_ANCHOR_FLOW("STOCK_ANCHOR_FLOW", "公会存量主播流水金额", "unvalid_live_new_aid_prod_pay_amt", "unvalid_live_new_aid_prod_pay_amt_rn_score", null),
        NEW_ANCHOR_FLOW_DIFF("NEW_ANCHOR_FLOW_DIFF", "新主播流水金额趋势", "valid_live_new_aid_prod_pay_amt_diff", "valid_live_new_aid_prod_pay_amt_diff_score", null),
        STOCK_ANCHOR_FLOW_DIFF("STOCK_ANCHOR_FLOW_DIFF", "存量主播流水金额趋势", "unvalid_live_new_aid_prod_pay_amt_diff", "unvalid_live_new_aid_prod_pay_amt_diff_score", null),
        STOCK_ANCHOR_FLOW_RATIO("STOCK_ANCHOR_FLOW_RATIO", "存量主播流水金额占比", "unvalid_live_new_aid_prod_pay_amt_rate", "unvalid_live_new_aid_prod_pay_amt_rate_diff_score", null),

        NEW_ANCHOR_BLUEDIAMOND("NEW_ANCHOR_BLUEDIAMOND", "新主播蓝钻金额", "valid_live_new_aid_incm_amt", "valid_live_new_aid_incm_amt_rn_score", d -> tip("差%s蓝钻可得%s分", d.getValidLiveNewAidIncmAmtTrgtDiff(), d.getValidLiveNewAidIncmAmtRnNextScore())),
        STOCK_ANCHOR_BLUEDIAMOND("STOCK_ANCHOR_BLUEDIAMOND", "存量主播蓝钻金额", "unvalid_live_new_aid_incm_amt", "unvalid_live_new_aid_incm_amt_rn_score", d -> tip("差%s蓝钻可得%s分", d.getUnvalidLiveNewAidIncmAmtTrgtDiff(), d.getUnvalidLiveNewAidIncmAmtRnNextScore())),
        NEW_ANCHOR_BLUEDIAMOND_DIFF("NEW_ANCHOR_BLUEDIAMOND_DIFF", "新主播蓝钻金额趋势", "valid_live_new_aid_incm_amt_diff", "valid_live_new_aid_incm_amt_diff_score", d -> tip("差%s蓝钻可得%s分", d.getValidLiveNewAidIncmAmtDiffTrgtDiff(), d.getValidLiveNewAidIncmAmtDiffNextScore())),
        STOCK_ANCHOR_BLUEDIAMOND_DIFF("STOCK_ANCHOR_BLUEDIAMOND_DIFF", "存量主播蓝钻金额趋势", "unvalid_live_new_aid_incm_amt_diff", "unvalid_live_new_aid_incm_amt_diff_score", d -> tip("差%s蓝钻可得%s分", d.getUnvalidLiveNewAidIncmAmtDiffTrgtDiff(), d.getUnvalidLiveNewAidIncmAmtDiffNextScore())),
        STOCK_ANCHOR_BLUEDIAMOND_RATIO("STOCK_ANCHOR_BLUEDIAMOND_RATIO", "存量主播蓝钻收入占比", "unvalid_live_new_aid_incm_amt_rate", "unvalid_live_new_aid_incm_amt_rate_diff_score", d -> tip("差%s蓝钻可得%s分", d.getUnvalidLiveNewAidIncmAmtRateDiffTrgtDiff(), d.getUnvalidLiveNewAidIncmAmtRateDiffNextScore())),

        GUILD_HEALTH_POINT("GUILD_HEALTH_POINT", "公会健康分", "guild_health_point", "guild_health_point_score", d -> tip("差%s健康分可得%s分", d.getGuildHealthPointTrgtDiff(), d.getGuildHealthPointNextScore())),
        ;
        String code;
        String desc;
        String dataCol;
        String scoreCol;

        /**
         * 距离下一目标分差值方法
         */
        Function<YyDmEntityGuildCmpHealthAnalysisExptDiDTO, String> nextDiffTipFunc;

        public static DataIndex of(String name) {
            if (StringUtils.isEmpty(name)) {
                return null;
            }
            for (DataIndex i : DataIndex.values()) {
                if (i.getCode().equals(name)) {
                    return i;
                }
            }
            return null;
        }

        private static String tip(String fmt, Number diff, Number score) {
            if (fmt == null || diff == null || score == null) {
                return "-";
            }
            double e = 0.000001D;
            if (Math.abs(diff.doubleValue()) < e || Math.abs(score.doubleValue()) < e) {
                return "-";
            }
            String diffStr = diff.toString();
            if (diff instanceof Double || diff instanceof Float) {
                diffStr = new DecimalFormat("#.##").format(diff);
            }
            return String.format(fmt, diffStr, score);
        }
    }

    /**
     * 任务指标
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum TaskIndex {
        NEW_SIGN_STAR32_ANCHOR_COUNT("NEW_SIGN_STAR32_ANCHOR_COUNT", "新签3&4星主播数", "new_aid_rat_3_4_aid_num", "new_aid_rat_3_4_aid_num_score", "new_aid_rat_3_4_aid_num_itg"),
        NEW_HEAD_ANCHOR_COUNT("NEW_HEAD_ANCHOR_COUNT", "新增头部主播数", "upgrd_high_aid_num", "upgrd_high_aid_num_score", "upgrd_high_aid_num_itg"),
        NEW_WAIST_ANCHOR_COUNT("NEW_WAIST_ANCHOR_COUNT", "新增腰部主播数", "upgrd_waist_aid_num", "upgrd_waist_aid_num_score", "upgrd_waist_aid_num_itg"),
        RENEW_16_GOLD_ARIST_COUNT("RENEW_16_GOLD_ARIST_COUNT", "续约1-6级金牌艺人数", "ext_golden_aid_num", "ext_golden_aid_num_score", "ext_golden_aid_num_itg"),
        GIFT_FLOW_RATIO_GE70("GIFT_FLOW_RATIO_GE70", "礼物流水占比总流水≥70%", "actv_prod_pay_amt_rate", "actv_prod_pay_amt_rate_score", "actv_prod_pay_amt_rate_itg"),

        ;
        String code;
        String desc;
        String dataCol;
        String scoreCol;
        String itgCol;

        public static TaskIndex of(String name) {
            if (StringUtils.isEmpty(name)) {
                return null;
            }
            for (TaskIndex i : TaskIndex.values()) {
                if (i.getCode().equals(name)) {
                    return i;
                }
            }
            return null;
        }
    }

    public static final Set<DataIndex> DATA_PERCENT_INDEX = Sets.newHashSet(DataIndex.N2S_LIVE_RATIO, DataIndex.STOCK_ANCHOR_LIVE_RATIO, DataIndex.STOCK_ANCHOR_FLOW_RATIO, DataIndex.STOCK_ANCHOR_BLUEDIAMOND_RATIO);

    /**
     * 考核维度总分
     */
    public interface TotalScore {
        /**
         * 招募总分
         */
        BigDecimal RECRU = new BigDecimal(10);
        /**
         * 孵化总分
         */
        BigDecimal HATCH = new BigDecimal(30);
        /**
         * 留存总分
         */
        BigDecimal RET = new BigDecimal(25);
        /**
         * 营收总分
         */
        BigDecimal RVNU = new BigDecimal(30);
        /**
         * 综合总分
         */
        BigDecimal COM = new BigDecimal(5);
    }

    /**
     * 段位类型
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum RankType {
        BI(0, "BI数据"),
        NO_RANK_DEFAULT(1, "星级主体无段位默认为一星"),
        WHITELIST(2, "段位白名单"),

        ;
        Integer code;
        String desc;
    }

    public interface LogTypeConstants {
        String GUILDRANK_CONFIG = "guildrank_config";
        String GUILDRANK_WHILELIST = "guildrank_whilelist";
    }
}
