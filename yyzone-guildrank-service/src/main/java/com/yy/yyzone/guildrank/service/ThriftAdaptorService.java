package com.yy.yyzone.guildrank.service;

import com.yy.ent.mobile.metrics.MetricsStopWatch;
import com.yy.yyzone.guildrank.dto.QueryContractChannelInfoPageRes;
import com.yy.yyzone.guildrank.dto.QueryContractChannelInfoReq;
import com.yy.yyzone.guildrank.dto.QueryContractChannelInfoRes;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * sdk公会主播及签约信息
 *
 * <AUTHOR>
 * @date 2022/01/24
 **/
@Service
public class ThriftAdaptorService {

    private static final Logger logger = LoggerFactory.getLogger(ThriftAdaptorService.class);
    @Resource(name = "httpRestTemplate")
    private RestTemplate restTemplate;

    @Value("${trevenue.channel-info.url:}")
    private String channelInfoUrl;
    public static final String GAME_LIVE_APPID = "GameLive";

    /**
     * 批量查百战公会信息
     *
     * @param uidList 主播uid
     * @return resp
     */
    public Map<Long, String> batchGetChannelInfo(List<Long> uidList) {
        MetricsStopWatch metricsStopWatch = MetricsStopWatch.startClientWatch().uri("Trevenue_batchGetChannelInfo");
        try {
            HttpHeaders requestHeaders = new HttpHeaders();
            requestHeaders.setContentType(MediaType.APPLICATION_JSON);
            QueryContractChannelInfoReq req = new QueryContractChannelInfoReq(GAME_LIVE_APPID, uidList);
            logger.info("batchGetChannelInfo req:{}", req);
            QueryContractChannelInfoPageRes res = restTemplate.postForObject(channelInfoUrl, new HttpEntity<>(req, requestHeaders), QueryContractChannelInfoPageRes.class);
            logger.info("batchGetChannelInfo resp res:{}", res);
            if (res != null && res.isSuccess()) {
                metricsStopWatch.successCode().markDurationAndCode();
                return res.getData().stream().collect(Collectors.toMap(QueryContractChannelInfoRes::getSid, QueryContractChannelInfoRes::getChannelName, (o, n) -> n));
            } else {
                metricsStopWatch.failCode().markDurationAndCode();
            }
        } catch (Exception e) {
            logger.info("batchGetChannelInfo fail, uid:{}", uidList, e);
            metricsStopWatch.failCode().markDurationAndCode();
        }
        return Collections.emptyMap();
    }

    public String getChannelName(long uid) {
        return batchGetChannelInfo(Collections.singletonList(uid)).getOrDefault(uid, StringUtils.EMPTY);
    }

}
