package com.yy.yyzone.guildrank.yrpc.client;

import com.yy.yyzone.guildrank.yrpc.client.GuildTypeReqResp.batchGetGuildTypeReq;
import com.yy.yyzone.guildrank.yrpc.client.GuildTypeReqResp.batchGetGuildTypeResp;
import com.yy.yyzone.guildrank.yrpc.dto.*;
import org.apache.dubbo.common.annotation.Yrpc;

public interface GuildTypeService {

	@Deprecated
	@Yrpc(functionName="batchGetGuildType",reqUri=199 << 8 | 158 ,resUri=200 << 8 | 158 )
	batchGetGuildTypeResp batchGetGuildType(batchGetGuildTypeReq req);

	@Deprecated
	@Yrpc(functionName="getGuildInfoFromCache", reqUri=293 << 8 |158 ,resUri=294 << 8 |158 )
	GuildInfoFromCache.PGetGuildInfoFromCacheRsp getGuildInfoFromCache(GuildInfoFromCache.PGetGuildInfoFromCacheReq req);

	@Yrpc(functionName = "batchGetGuildTypeNew", reqUri = 199 << 8 | 168, resUri = 200 << 8 | 168)
	PBatchGetGuildTypeResp64 batchGetGuildTypeNew(PBatchGetGuildTypeReq64 req);

	@Yrpc(functionName = "batchGetGuildTypeNew", reqUri = 293  << 8 | 168, resUri = 294 << 8 | 168)
	PGetGuildInfoResp64 getGuildInfoFromCacheNew(PGetGuildInfoReq64 req);

	/**
	 * 查询所有星级和/或金牌公会频道信息
	 *
	 * @param req
	 * @return
	 */
	@Yrpc(functionName = "queryProxyGuildUid", reqUri = 197 << 8 | 158, resUri = 198 << 8 | 158)
	GetAllStarGoldGuildRsp getAllStarGoldGuild(GetAllStarGoldGuild req);
}
