package com.yy.yyzone.guildrank.util;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2019/7/10
 */
public class Resp<T> implements Serializable{
    /**
     * 0-成功 其他-失败
     */
    private int code;
    private String msg;
    private T data;

    private Resp(int code){
        this.code = code;
    }

    private Resp(int code,String msg){
        this.code = code;
        this.msg = msg;
    }

    private Resp(int code,T data){
        this.code = code;
        this.data = data;
    }

    private Resp(int code,String msg,T data){
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public T getData() {
        return data;
    }

    /**
     * 判断当前状态是否成功.成功返回true.失败返回false
     * 使isSuccess返回值不被序列化,因为该方法不需要被转化成json对象
     */
    @JsonIgnore
    public Boolean checkSuccess(){
        return this.code == ResponseCode.SUCCESS.getCode();
    }

    /**
     * 返回成功的Resp对象.
     * @param <T>
     * @return
     */
    public static<T> Resp<T> createBySuccess(){
        return new Resp<>(ResponseCode.SUCCESS.getCode());
    }
    public static<T> Resp<T> createBySuccessMassage(String msg){
        return new Resp<>(ResponseCode.SUCCESS.getCode(),msg);
    }
    public static<T> Resp<T> createBySuccess(T data){
        return new Resp<>(ResponseCode.SUCCESS.getCode(),data);
    }
    public static<T> Resp<T> createBySuccess(String msg,T data){
        return new Resp<>(ResponseCode.SUCCESS.getCode(),msg,data);
    }

    public static<T> Resp<T> createBySuccess(int code, String msg,T data){
        return new Resp<>(code,msg,data);
    }


    /**
     * 返回失败的Resp对象.
     * @param <T>
     * @return
     */
    public static<T> Resp<T> createByError(){
        return new Resp<>(ResponseCode.ERROR.getCode(),ResponseCode.ERROR.getDesc());
    }
    public static<T> Resp<T> createByError(String msg){
        return new Resp<>(ResponseCode.ERROR.getCode(),msg);
    }
    public static<T> Resp<T> createByError(int code,String msg){
        return new Resp<>(code,msg);
    }
}