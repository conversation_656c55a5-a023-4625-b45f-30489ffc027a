package com.yy.yyzone.guildrank.dto.guildranktaskconfig;

import com.yy.yyzone.guildrank.constant.GuildRankTaskConstant;
import lombok.Data;

@Data
public class GuildRankTaskDetailsDTO {
    /**
     * 任务
     *onlineBind3And4Star 线上绑定3&4星主播数量
     *newHighWaistAnchor 新增高优&腰部主播
     *newAnchorBlueDiamond 新主播蓝钻任务
     *guildBlueDiamond 公会蓝钻任务
     *monthBlueIncome 自然月月累积蓝钻收入
     *springHeadline  春季头条任务
     *summerHeadline 夏季头条任务
     *guildRaceS1 公会赛S1任务
     *guildRaceS2 公会赛S2任务
     *guildRaceMain 公会赛正赛任务
     *personalRaceMain 个人赛正赛任务
     *
     */
    private String task;

    private String taskName;
    /**
     * 目标值
     */
    private Long targetValue;
    /**
     * 任务详情
     */
    private String detail;
    /**
     * 任务完成情况
     */
    private String completion;
    /**
     * 得分
     */
    private Integer score;

    public GuildRankTaskDetailsDTO(String task, Long targetValue, GuildRankAllTaskResultDTO resultDTO) {
        this.task = task;
        this.targetValue = targetValue;
        if(!GuildRankTaskConstant.ALL_TASK_TYPES.contains(task)){
           throw new IllegalArgumentException("无效的任务类型: " + task);
        }
        this.taskName = GuildRankTaskConstant.getTagName(task);
        if(resultDTO != null){
            if(task.equals(GuildRankTaskConstant.PERSONAL_RACE_MAIN)){
                this.detail = resultDTO.getOnlineBind3And4StarTaskDetail();
                this.completion = resultDTO.getOnlineBind3And4StarCompletion();
                this.score = resultDTO.getOnlineBind3And4StarScore();
            }else if(task.equals(GuildRankTaskConstant.NEW_HIGH_WAIST_ANCHOR)){
                this.detail = resultDTO.getNewHighWaistAnchorTaskDetail();
                this.completion = resultDTO.getNewHighWaistAnchorCompletion();
                this.score = resultDTO.getNewHighWaistAnchorScore();
            }else if(task.equals(GuildRankTaskConstant.NEW_ANCHOR_BLUE_DIAMOND)){
                this.detail = resultDTO.getNewAnchorBlueDiamondTaskDetail();
                this.completion = resultDTO.getNewAnchorBlueDiamondCompletion();
                this.score = resultDTO.getNewAnchorBlueDiamondScore();
            }else if(task.equals(GuildRankTaskConstant.GUILD_BLUE_DIAMOND)){
                this.detail = resultDTO.getGuildBlueDiamondTaskDetail();
                this.completion = resultDTO.getGuildBlueDiamondCompletion();
                this.score = resultDTO.getGuildBlueDiamondScore();
            }else if(task.equals(GuildRankTaskConstant.MONTH_BLUE_INCOME)){
                this.detail = resultDTO.getMonthBlueIncomeTaskDetail();
                this.completion = resultDTO.getMonthBlueIncomeCompletion();
                this.score = resultDTO.getMonthBlueIncomeScore();
            }else if(task.equals(GuildRankTaskConstant.SPRING_HEADLINE)){
                this.detail = resultDTO.getSpringHeadlineTaskDetail();
                this.completion = resultDTO.getSpringHeadlineCompletion();
                this.score = resultDTO.getSpringHeadlineScore();
            }else if(task.equals(GuildRankTaskConstant.SUMMER_HEADLINE)){
                this.detail = resultDTO.getSummerHeadlineTaskDetail();
                this.completion = resultDTO.getSummerHeadlineCompletion();
                this.score = resultDTO.getSummerHeadlineScore();
            }else if(task.equals(GuildRankTaskConstant.GUILD_RACE_S1)){
                this.detail = resultDTO.getGuildRaceS1TaskDetail();
                this.completion = resultDTO.getGuildRaceS1Completion();
                this.score = resultDTO.getGuildRaceS1Score();
            }else if(task.equals(GuildRankTaskConstant.GUILD_RACE_S2)){
                this.detail = resultDTO.getGuildRaceS2TaskDetail();
                this.completion = resultDTO.getGuildRaceS2Completion();
                this.score = resultDTO.getGuildRaceS2Score();
            }else if(task.equals(GuildRankTaskConstant.GUILD_RACE_MAIN)){
                this.detail = resultDTO.getGuildRaceMainTaskDetail();
                this.completion = resultDTO.getGuildRaceMainCompletion();
                this.score = resultDTO.getGuildRaceMainScore();
            }else if(task.equals(GuildRankTaskConstant.ONLINE_BIND_3_AND_4_STAR)){
                this.detail = resultDTO.getOnlineBind3And4StarTaskDetail();
                this.completion = resultDTO.getOnlineBind3And4StarCompletion();
                this.score = resultDTO.getOnlineBind3And4StarScore();
            }else{
                throw  new IllegalArgumentException("无效的任务类型: " + task);
            }
        }
    }
}
