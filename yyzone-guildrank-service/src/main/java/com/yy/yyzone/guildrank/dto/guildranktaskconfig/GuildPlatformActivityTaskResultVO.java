package com.yy.yyzone.guildrank.dto.guildranktaskconfig;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class GuildPlatformActivityTaskResultVO {
    /**
     * 月份
     */
    @ExcelProperty("月份")
    private String month;

    /**
     * 主体UID
     */
    @ExcelProperty("主体UID")
    private Long uid;

    /**
     * 主体YY号
     */
    @ExcelProperty("主体YY号")
    private Long yy;

    /**
     * 公会名称(上传时候的，非实时)
     */
    @ExcelProperty("公会名称")
    private String guildName;

    /**
     * 春季头条任务详情
     */
    @ExcelProperty("春季头条任务详情")
    private String springHeadlineTaskDetail;

    /**
     * 春季头条完成情况
     */
    @ExcelProperty("春季头条完成情况")
    private String springHeadlineCompletion;

    /**
     * 春季头条得分
     */
    @ExcelProperty("春季头条得分")
    private Integer springHeadlineScore;

    /**
     * 夏季头条任务详情
     */
    @ExcelProperty("夏季头条任务详情")
    private String summerHeadlineTaskDetail;

    /**
     * 夏季头条完成情况
     */
    @ExcelProperty("夏季头条完成情况")
    private String summerHeadlineCompletion;

    /**
     * 夏季头条得分
     */
    @ExcelProperty("夏季头条得分")
    private Integer summerHeadlineScore;

    /**
     * 公会赛S1任务详情
     */
    @ExcelProperty("公会赛S1任务详情")
    private String guildRaceS1TaskDetail;

    /**
     * 公会赛S1完成情况
     */
    @ExcelProperty("公会赛S1完成情况")
    private String guildRaceS1Completion;

    /**
     * 公会赛S1得分
     */
    @ExcelProperty("公会赛S1得分")
    private Integer guildRaceS1Score;

    /**
     * 公会赛S2任务详情
     */
    @ExcelProperty("公会赛S2任务详情")
    private String guildRaceS2TaskDetail;

    /**
     * 公会赛S2完成情况
     */
    @ExcelProperty("公会赛S2完成情况")
    private String guildRaceS2Completion;

    /**
     * 公会赛S2得分
     */
    @ExcelProperty("公会赛S2得分")
    private Integer guildRaceS2Score;

    /**
     * 公会赛正赛任务详情
     */
    @ExcelProperty("公会赛正赛任务详情")
    private String guildRaceMainTaskDetail;

    /**
     * 公会赛正赛完成情况
     */
    @ExcelProperty("公会赛正赛完成情况")
    private String guildRaceMainCompletion;

    /**
     * 公会赛正赛得分
     */
    @ExcelProperty("公会赛正赛得分")
    private Integer guildRaceMainScore;

    /**
     * 个人赛正赛任务详情
     */
    @ExcelProperty("个人赛正赛任务详情")
    private String personalRaceMainTaskDetail;

    /**
     * 个人赛正赛完成情况
     */
    @ExcelProperty("个人赛正赛完成情况")
    private String personalRaceMainCompletion;

    /**
     * 个人赛正赛得分
     */
    @ExcelProperty("个人赛正赛得分")
    private Integer personalRaceMainScore;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("上传人")
    private String uploader;
}