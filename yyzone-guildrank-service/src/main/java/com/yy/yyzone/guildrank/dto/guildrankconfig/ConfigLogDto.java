package com.yy.yyzone.guildrank.dto.guildrankconfig;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class ConfigLogDto {
    /**
     * id
     */
    private Long id;

    /**
     * 操作人
     */
    private String optPassport;

    /**
     * 操作时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date optTime;

    /**
     * 变更前数据
     */
    private String oldData;

    /**
     * 变更后数据
     */
    private String newData;
}