package com.yy.yyzone.guildrank.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.ent.mobile.metrics.MetricsStopWatch;
import com.yy.yyzone.guildrank.db.gen.mapper.DmYySidSegmentScoreMonthMapper;
import com.yy.yyzone.guildrank.db.gen.mapper.GuildrankTemporaryChatMapper;
import com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreMonth;
import com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreMonthExample;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankTemporaryChat;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankTemporaryChatExample;
import com.yy.yyzone.guildrank.dto.GuildRankInfo;
import com.yy.yyzone.guildrank.dto.LastTwelveMonthResp;
import com.yy.yyzone.guildrank.util.MyDateUtil;
import com.yy.yyzone.guildrank.yrpc.dto.PGetGuildInfoResp64;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Generated;
import java.text.ParseException;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2019/7/29
 */
@Service
@Generated("忽略代码规范检查")
public class GuildRankService {


    @Autowired
    private DmYySidSegmentScoreMonthMapper dmYySidSegmentScoreMonthMapper;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private GuildrankTemporaryChatMapper guildrankTemporaryChatMapper;

    @Autowired
    private UserInfoService userInfoService;

    private static Logger logger = LoggerFactory.getLogger(GuildRankService.class);


    public DmYySidSegmentScoreMonth guildRankInfo(Long guildUid, String month) {
        DmYySidSegmentScoreMonthExample example = new DmYySidSegmentScoreMonthExample();
        example.createCriteria().andSidOwneridEqualTo(guildUid).andDtEqualTo(month);
        example.setOrderByClause(" id desc");
        List<DmYySidSegmentScoreMonth> list = dmYySidSegmentScoreMonthMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    private static String update_key = "guildrank_update_flag_";
    public static final Duration TIME = Duration.ofMinutes(2);
    public static final String FINISH = "1";

    /**
     * 查询当前月份数据是否已经更新
     * @return
     */
    public boolean hasUpdate() {
        String date = DateFormatUtils.format(new Date(), "yyyy-MM-dd");
        if ("2020-04-01".equals(date)){
            return false;
        }
        String currentDate = DateFormatUtils.format(DateUtils.addMonths(new Date(), -1), "yyyy-MM");
        String redisKey = update_key + currentDate;
        String result = redisTemplate.opsForValue().get(redisKey);
        if (FINISH.equals(result)) {
            return true;
        }
        DmYySidSegmentScoreMonthExample example = new DmYySidSegmentScoreMonthExample();
        example.createCriteria().andDtEqualTo(currentDate + "-01");
        example.setOrderByClause(" id desc ");
        example.setLimit(1);
        List<DmYySidSegmentScoreMonth> list = dmYySidSegmentScoreMonthMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(list)) {
            redisTemplate.opsForValue().set(redisKey, FINISH, TIME);
            return true;
        }
        return false;
    }


    public Long getTemporaryChat(Long guildUid) {
        GuildrankTemporaryChatExample example = new GuildrankTemporaryChatExample();
        example.createCriteria().andGuilduidEqualTo(guildUid).andStatusEqualTo(0);
        List<GuildrankTemporaryChat> list = guildrankTemporaryChatMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0).getServiceyy();
        }
        return 0L;
    }

    public String getTemporaryChatNick(Long guildUid) {
        GuildrankTemporaryChatExample example = new GuildrankTemporaryChatExample();
        example.createCriteria().andGuilduidEqualTo(guildUid).andStatusEqualTo(0);
        List<GuildrankTemporaryChat> list = guildrankTemporaryChatMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0).getServicenick();
        }
        return "";
    }

    public static void main(String[] args) {
        int hour = Calendar.getInstance().get(Calendar.HOUR_OF_DAY);//获取到当前的小时
        System.out.println(Calendar.getInstance().get(Calendar.MONTH));
        int day = Calendar.getInstance().get(Calendar.DAY_OF_MONTH);//获取到当前的小时
        System.out.println(hour);

        try {
            System.out.println(DateFormatUtils.format(DateUtils.addMonths(DateUtils.parseDate("2019-08", "yyyy-MM"), -1), "yyyy-MM"));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        System.out.println(DateFormatUtils.format(DateUtils.addMonths(new Date(), -1), "yyyy-MM"));
    }


    public List<LastTwelveMonthResp> lastTwelveMonth(Long guildUid) throws Exception {

        DmYySidSegmentScoreMonthExample example = new DmYySidSegmentScoreMonthExample();
        example.createCriteria().andSidOwneridEqualTo(guildUid).andDtNotEqualTo("2020-03-01");
        example.setOrderByClause(" id desc ");
        example.setLimit(20);// 查20条出来去重足够了
        List<DmYySidSegmentScoreMonth> list = dmYySidSegmentScoreMonthMapper.selectByExample(example);
        Map<String, String> levelMap = Maps.newHashMap();
        Map<String, String> rankMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(list)) {
            for (DmYySidSegmentScoreMonth item : list) {
                levelMap.put(item.getDt(), String.valueOf(item.getPsSAllCor().intValue()));
                rankMap.put(item.getDt(), String.valueOf(item.getPsSAllRnCor().intValue()));
            }
        }
        //如果当前时间是2020-02-01之前  则还需要去查旧数据（最后的旧数据是2019-07的）
        if (new Date().before(DateUtils.parseDate("2020-02-01", "yyyy-MM-dd"))) {
            Map<String, String> oldData = getOldData(guildUid);
            logger.info(JSON.toJSONString(oldData));
            levelMap.put("2019-02-01", oldData.get("2019-02-01"));
            levelMap.put("2019-03-01", oldData.get("2019-03-01"));
            levelMap.put("2019-04-01", oldData.get("2019-04-01"));
            levelMap.put("2019-05-01", oldData.get("2019-05-01"));
            levelMap.put("2019-06-01", oldData.get("2019-06-01"));
            levelMap.put("2019-07-01", oldData.get("2019-07-01"));
        }

        List<LastTwelveMonthResp> resp = Lists.newArrayList();
        for (int i = 1; i <= 12; i++) {
            String month = MyDateUtil.getFirstDay(-i);
            String key = month.substring(0, 7);
            String level = levelMap.get(month);
            String rank = rankMap.get(month);
            LastTwelveMonthResp item = new LastTwelveMonthResp();
            item.setMonth(key);
            item.setRank(StringUtils.isBlank(rank) ? 0 : Integer.parseInt(rank));
            item.setStarLevel(StringUtils.isBlank(level) ? 0 : Integer.parseInt(level));
            resp.add(item);
        }
        return resp;
    }


    private Map<String, String> getOldData(Long guildUid) {
        JSONObject rankInfo = doGetGuildRankInfo(String.valueOf(guildUid));
        Map<String, String> history = Maps.newHashMap();
        try {
            if (rankInfo.getIntValue("ret") == 0) {
                JSONArray objects = rankInfo.getJSONArray("data");
                if (objects.size() > 0) {
                    GuildRankInfo guildRankInfo = JSONObject.parseObject(objects.getJSONObject(0).toJSONString(), GuildRankInfo.class);
                    history.put(MyDateUtil.getFirstDay(-1), String.valueOf(guildRankInfo.getRank()));
                    history.put(MyDateUtil.getFirstDay(-2), guildRankInfo.getPre_1_rank());
                    history.put(MyDateUtil.getFirstDay(-3), guildRankInfo.getPre_2_rank());
                    history.put(MyDateUtil.getFirstDay(-4), guildRankInfo.getPre_3_rank());
                    history.put(MyDateUtil.getFirstDay(-5), guildRankInfo.getPre_4_rank());
                    history.put(MyDateUtil.getFirstDay(-6), guildRankInfo.getPre_5_rank());
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return history;
    }


    public JSONObject doGetGuildRankInfo(String uid) {
        JSONObject dataResult = new JSONObject();
        HttpClient client = new DefaultHttpClient();
        HttpPost httpPost = new HttpPost("http://10.25.65.210:8518/service/read");
        try {
            //添加请求头
            httpPost.setHeader("User-Agent", "Mozilla/5.0");
            httpPost.setHeader("Content-Type", "text/plain");

            JSONObject requestEntity = new JSONObject();
            requestEntity.put("serviceTypeKey", "guild_rank_info_inc_month");
            JSONObject params = new JSONObject(1);
            params.put("owuid", uid);
            requestEntity.put("params", params);

            httpPost.setEntity(new StringEntity(requestEntity.toJSONString()));

            HttpResponse response = client.execute(httpPost);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != HttpStatus.SC_OK) {
                dataResult.put("code", statusCode);
                return dataResult;
            }
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                String result = EntityUtils.toString(entity, "utf-8");
                logger.info("doGetGuildRankInfo uid:{} | result:{}", uid, result);
                dataResult = JSONObject.parseObject(result);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            client.getConnectionManager().shutdown();
        }
        return dataResult;
    }




    public DmYySidSegmentScoreMonth currentRank(Long uid) {
        MetricsStopWatch metricsStopWatch = MetricsStopWatch.startServerWatch();
        String currentDate = DateFormatUtils.format(DateUtils.addMonths(new Date(), -1), "yyyy-MM");
        boolean hasUpdate = hasUpdate();
        if (!hasUpdate) {
            currentDate = DateFormatUtils.format(DateUtils.addMonths(new Date(), -2), "yyyy-MM");
        }
        String cacheDataKey = currentDate + "_queryCurrentRank_" + uid;
        String data = redisTemplate.opsForValue().get(cacheDataKey);
        if (StringUtils.isNotBlank(data)) {
            logger.info("queryCurrentRank from cache key:{}, data:{}", cacheDataKey, JSON.toJSONString(data));
            metricsStopWatch.code(0).uri("currentRank").markDurationAndCode();
            return JSON.parseObject(data, DmYySidSegmentScoreMonth.class);
        }

        DmYySidSegmentScoreMonth dbData = guildRankInfo(uid, currentDate + "-01");
        if (dbData == null || dbData.getSidOwnerid() <= 0) {// 没查到的， 去查一下最新身份是否星级
            dbData = buildData(uid);
        }
        redisTemplate.opsForValue().setIfAbsent(cacheDataKey, JSON.toJSONString(dbData), Duration.ofMinutes(15));
        logger.info("queryCurrentRank from db uid:{}, data:{}", uid, JSON.toJSONString(dbData));
        metricsStopWatch.code(0).uri("currentRank").markDurationAndCode();
        return dbData;
    }


    public DmYySidSegmentScoreMonth buildData(Long uid) {
        PGetGuildInfoResp64 resp = userInfoService.getGuildInfoFromCache(uid);

        if (StringUtils.isNotBlank(resp.getGoldenDate()) || StringUtils.isNotBlank(resp.getStarDate())) {// 星级金牌公会
            return buildEmptyDmYySidSegmentScoreMonth(uid, 1L);
        } else {
            return buildEmptyDmYySidSegmentScoreMonth(uid, 0L);
        }
    }


    private DmYySidSegmentScoreMonth buildEmptyDmYySidSegmentScoreMonth(Long uid, Long level) {
        DmYySidSegmentScoreMonth data = new DmYySidSegmentScoreMonth();
        data.setId(0L);
        data.setSidOwnerid(uid);
        data.setSidOwyyid(0L);
        data.setNewUv(0L);
        data.setNewUvRr(0L);
        data.setValidLiveNewUv(0L);
        data.setValidLiveNewUvRr(0L);
        data.setPerNewUv(0.0D);
        data.setPerValidLiveNewUv(0.0D);
        data.setNewUvRt(0.0D);
        data.setAcu10Uv(0L);
        data.setAcu10UvRr(0L);
        data.setPerAcu10Uv(0.0D);
        data.setAcu1050Uv(0L);
        data.setAcu50300Uv(0L);
        data.setAcu300Uv(0L);
        data.sethActUv(0L);
        data.sethActUvRr(0L);
        data.setPerHActUv(0.0D);
        data.setMonthDiamond(0.0D);
        data.setMonthDiamondRr(0.0D);
        data.setPerMonthDiamond(0.0D);
        data.setBreakANum(0L);
        data.setBreakBNum(0L);
        data.setBreakCNum(0L);
        data.setBreakENum(0L);
        data.setLiveUv(0L);
        data.setBreakRt(0.0D);
        data.setBreakRr(0.0D);
        data.setPerBreak(0.0D);
        data.setP1(0.0D);
        data.setPs1(0.0D);
        data.setPsS1(0L);
        data.setPsS1Sh("");
        data.setPsS1Rn(0L);
        data.setP2(0.0D);
        data.setPs2(0.0D);
        data.setPsS2(0L);
        data.setPsS2Sh("");
        data.setPsS2Rn(0L);
        data.setP3(0.0D);
        data.setPs3(0.0D);
        data.setPsS3(0L);
        data.setPsS3Sh("");
        data.setPsS3Rn(0L);
        data.setP4(0.0D);
        data.setPs4(0.0D);
        data.setPsS4(0L);
        data.setPsS4Sh("");
        data.setPsS4Rn(0L);
        data.setP5(0.0D);
        data.setPs5(0.0D);
        data.setPsS5(0L);
        data.setPsS5Sh("");
        data.setPsS5Rn(0L);
        data.setPsAll(0.0D);
        data.setPsSAllCor(level);
        data.setPsSAllCorRr("");
        data.setPsSAllRnCor(0L);
        data.setPsSAllRnCorRr("");
        data.setSprNewUv(0.0D);
        data.setSprValidLiveNewUv(0.0D);
        data.setSprAcu10Uv(0.0D);
        data.setSprHActUv(0.0D);
        data.setSprMonthDiamond(0.0D);
        data.setSprP5(0.0D);
        data.setDt("");
        return data;
    }


    public Map<Long, Integer> batchQueryCurrentRank(List<Long> guildUids) {
        MetricsStopWatch metricsStopWatch = MetricsStopWatch.startServerWatch();
        logger.info(JSON.toJSONString(guildUids));
        String currentDate = DateFormatUtils.format(DateUtils.addMonths(new Date(), -1), "yyyy-MM");
        boolean hasUpdate = hasUpdate();
        if (!hasUpdate) {
            currentDate = DateFormatUtils.format(DateUtils.addMonths(new Date(), -2), "yyyy-MM");
        }

        Map<Long, Integer> result = Maps.newHashMap();
        DmYySidSegmentScoreMonthExample example = new DmYySidSegmentScoreMonthExample();
        example.createCriteria().andDtEqualTo(currentDate + "-01").andSidOwneridIn(guildUids);
        example.setOrderByClause(" id desc ");
        List<DmYySidSegmentScoreMonth> list = dmYySidSegmentScoreMonthMapper.selectByExample(example);
        List<DmYySidSegmentScoreMonth> quChongList = Lists.newArrayList();
        Set<Long> uidSet = Sets.newHashSet();
        for (DmYySidSegmentScoreMonth dmYySidSegmentScoreMonth : list) {
            if (!uidSet.contains(dmYySidSegmentScoreMonth.getSidOwnerid())) {
                quChongList.add(dmYySidSegmentScoreMonth);
                uidSet.add(dmYySidSegmentScoreMonth.getSidOwnerid());
            }
        }

        Map<Long, Long> dbData = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(quChongList)) {
            dbData = quChongList.stream().collect(Collectors.toMap(DmYySidSegmentScoreMonth::getSidOwnerid, DmYySidSegmentScoreMonth::getPsSAllCor));
        }

        List<Long> guildUidInt = Lists.newArrayList();
        for (Long uid : guildUids) {
            Long level = dbData.get(uid);
            if (level != null && level > 0) {
                result.put(uid, level.intValue());
            } else {
                // 查不到的uid 去查最新身份
                guildUidInt.add(uid);
            }
        }

        Map<Long, Integer> guildTypeMap = userInfoService.batchGetGuildType(guildUidInt);
        if (MapUtils.isNotEmpty(guildTypeMap)) {
            for (Map.Entry<Long, Integer> entry : guildTypeMap.entrySet()) {
                if (entry.getValue() > 0) {
                    result.put(entry.getKey(), 1);
                }
            }
        }
        logger.info(JSON.toJSONString(result));
        metricsStopWatch.code(0).uri("batchQueryCurrentRank").markDurationAndCode();
        return result;
    }
}
