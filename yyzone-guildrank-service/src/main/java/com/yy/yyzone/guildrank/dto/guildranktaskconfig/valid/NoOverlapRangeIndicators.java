package com.yy.yyzone.guildrank.dto.guildranktaskconfig.valid;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = NoOverlapRangeIndicatorsValidator.class)
public @interface NoOverlapRangeIndicators {
    String message() default "rangeIndicators 区间不能重叠";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}