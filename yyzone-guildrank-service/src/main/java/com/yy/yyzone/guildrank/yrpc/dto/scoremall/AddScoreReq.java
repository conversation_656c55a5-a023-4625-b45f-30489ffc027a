package com.yy.yyzone.guildrank.yrpc.dto.scoremall;

import com.yy.ent.commons.protopack.util.Uint;
import lombok.Data;

import java.util.Map;

/**
 * 给用户添加货币数量
 */
@Data
public class AddScoreReq {
    /**
     * 用户uid
     */
    private Long uid;

    /**
     * 自己的业务s2s名称，必填。
     */
    private String bizName;

    /**
     * 订单号，必填，不要超过64个字节（支持重试，3个小时的缓存）
     */
    private String orderNo;

    /**
     * 交易备注，必填
     */
    private String remark;

    /**
     * 货币类型，大类，必填
     */
    private Uint scoreType;

    /**
     * 货币数量，必填
     */
    private Uint addAmount;

    /**
     * 必填
     * dueDate 货币有效期，格式： 2020-01-01， （ps：不要带时分秒，默认是截止到日期当天的23:59:59）【注意，注意，注意：】货币有效期不能超过对应商品的兑换截止时间，一定要跟产品确认好商品的兑换截止时间
     * account 扣费账号，如果货币是有价值的，则需要传字段
     * subType 货币子类型，如果没有子类型则填0，和产品确认好
     */
    private Map<String, String> extendInfo;
}