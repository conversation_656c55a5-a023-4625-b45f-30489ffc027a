package com.yy.yyzone.guildrank.service;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.yy.yyzone.guildrank.db.gen.mapper.GuildRankPlatformActivityTaskResultMapper;
import com.yy.yyzone.guildrank.db.gen.mapper.GuildRankPlatformActivityTaskUploadHistoryMapper;
import com.yy.yyzone.guildrank.db.gen.model.GuildRankPlatformActivityTaskResult;
import com.yy.yyzone.guildrank.db.gen.model.GuildRankPlatformActivityTaskUploadHistory;
import com.yy.yyzone.guildrank.dto.guildranktaskconfig.GuildPlatformActivityTaskResultVO;
import com.yy.yyzone.guildrank.enums.GuildPlatformActivityTask;
import com.yy.yyzone.guildrank.util.PageResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Pattern;

@Service
@Slf4j
public class GuildPlatformActivityResultService {

    @Autowired
    private GuildRankPlatformActivityTaskResultMapper resultMapper;

    @Autowired
    private GuildRankPlatformActivityTaskUploadHistoryMapper historyMapper;

    @Autowired
    private ObjectProvider<BosService> bosServiceObjectProvider;

    private static final Pattern MONTH_PATTERN = Pattern.compile("^\\d{4}/\\d{1,2}$");
    private static final DateTimeFormatter MONTH_FORMATTER = DateTimeFormatter.ofPattern("yyyy/MM");

    private static final DateTimeFormatter MONTH_FORMATTER_INT = DateTimeFormatter.ofPattern("yyyyMM");

    private static final List<String> EXPECTED_HEADERS = Arrays.asList("月份",	"主体UID",	"主体YY号"	,"公会名称",	"春季头条任务详情",
            "春季头条完成情况",	"春季头条得分",	"夏季头条任务详情",	"夏季头条完成情况",
            "夏季头条得分"	,"公会赛S1任务详情",	"公会赛S1完成情况",	"公会赛S1得分",	"公会赛S2任务详情",	"公会赛S2完成情况",	"公会赛S2得分",	"公会赛正赛任务详情",
            "公会赛正赛完成情况",	"公会赛正赛得分",	"个人赛正赛任务详情",	"个人赛正赛完成情况",	"个人赛正赛得分",	"备注"
    );

    public Pair<Integer,List<GuildPlatformActivityTaskResultVO>> validateAndReadExcel(MultipartFile file) throws IOException {
        String currentMonth = currentMonth();
        List<GuildPlatformActivityTaskResultVO> results = new ArrayList<>();
        ExcelReaderBuilder readerBuilder = EasyExcelFactory.read(file.getInputStream())
                .head(GuildPlatformActivityTaskResultVO.class)
                .registerReadListener(new AnalysisEventListener<GuildPlatformActivityTaskResultVO>() {
                    @Override
                    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                        if(!headMap.values().containsAll(EXPECTED_HEADERS)){
                            StringBuilder builder = new StringBuilder();
                            builder.append("excel文件表头有问题,表头不存在[");
                            for(String header : EXPECTED_HEADERS){
                                if(!headMap.values().contains(header)){
                                    builder.append(header).append(" ");
                                }
                            }
                            builder.append("]");
                            log.warn("validateAndReadExcel file msg:{} headMap:{}",builder.toString(),headMap);
                            throw new IllegalArgumentException(builder.toString());
                        }
                    }
                    @Override
                    public void invoke(GuildPlatformActivityTaskResultVO vo, AnalysisContext context) {
                        if(vo.getUid() != null && vo.getMonth() != null){
                            try{
                                validateVO(vo,currentMonth);
                                results.add(vo);
                            }catch (IllegalArgumentException e){
                                log.info("validateAndReadExcel 校验失败,vo:{}",vo);
                                throw  e;
                            }
                        }
                    }
                    @Override
                    public void doAfterAllAnalysed(AnalysisContext context) {
                    }
                });
        ExcelReader excelReader = null;
        try {
            excelReader = readerBuilder.build();
            excelReader.read(new ReadSheet(0));
        } finally {
            if (excelReader != null) {
                excelReader.finish();
            }
        }
        if(CollectionUtils.isEmpty(results)){
            throw new IllegalArgumentException("Excel file is empty");
        }
        return Pair.of(convertMonthToInt(currentMonth),results);
    }


    public String getDownloadPath(int dt) {
        GuildRankPlatformActivityTaskUploadHistory guildRankPlatformActivityTaskUploadHistory =  historyMapper.selectByDt(dt);
        if(guildRankPlatformActivityTaskUploadHistory != null){
            return bosServiceObjectProvider.getIfAvailable().generatePresignedUrl(guildRankPlatformActivityTaskUploadHistory.getFilePath());
        }
        return null;
    }

    private void validateVO(GuildPlatformActivityTaskResultVO vo,String currentMonth) {
        // Validate month format and current month
        if (!MONTH_PATTERN.matcher(vo.getMonth()).matches()) {
            throw new IllegalArgumentException("Invalid month format, expected yyyy/MM");
        }
        if (!currentMonth.equals(vo.getMonth())) {
            throw new IllegalArgumentException("日期必须是本月: " + currentMonth+ " ,上传日期: '" + vo.getMonth()+"'");
        }

        // Validate numeric fields
        if (vo.getUid() == null || vo.getUid() <= 0) {
            throw new IllegalArgumentException("UID must be a positive number");
        }
        if (vo.getYy() == null || vo.getYy() <= 0) {
            throw new IllegalArgumentException("YY number must be a positive number");
        }

        // Validate score fields (can be null or non-negative)
        validateScore(vo.getSpringHeadlineScore(), "春季头条得分");
        validateScore(vo.getSummerHeadlineScore(), "夏季头条得分");
        validateScore(vo.getGuildRaceS1Score(), "公会赛S1得分");
        validateScore(vo.getGuildRaceS2Score(), "公会赛S2得分");
        validateScore(vo.getGuildRaceMainScore(), "公会赛正赛得分");
        validateScore(vo.getPersonalRaceMainScore(), "个人赛正赛得分");

        // Validate string fields length
        validateStringLength(vo.getGuildName(), "公会名称", 255);
        validateStringLength(vo.getSpringHeadlineTaskDetail(), "春季头条任务详情", 255);
        validateStringLength(vo.getSpringHeadlineCompletion(), "春季头条完成情况", 255);
        validateStringLength(vo.getSummerHeadlineTaskDetail(), "夏季头条任务详情", 255);
        validateStringLength(vo.getSummerHeadlineCompletion(), "夏季头条完成情况", 255);
        validateStringLength(vo.getGuildRaceS1TaskDetail(), "公会赛S1任务详情", 255);
        validateStringLength(vo.getGuildRaceS1Completion(), "公会赛S1完成情况", 255);
        validateStringLength(vo.getGuildRaceS2TaskDetail(), "公会赛S2任务详情", 255);
        validateStringLength(vo.getGuildRaceS2Completion(), "公会赛S2完成情况", 255);
        validateStringLength(vo.getGuildRaceMainTaskDetail(), "公会赛正赛任务详情", 255);
        validateStringLength(vo.getGuildRaceMainCompletion(), "公会赛正赛完成情况", 255);
        validateStringLength(vo.getPersonalRaceMainTaskDetail(), "个人赛正赛任务详情", 255);
        validateStringLength(vo.getPersonalRaceMainCompletion(), "个人赛正赛完成情况", 255);
        validateStringLength(vo.getRemark(), "备注", 255);
    }

    private void validateScore(Integer score, String fieldName) {
        if (score != null && score < 0) {
            throw new IllegalArgumentException(fieldName + "必须为非负数");
        }
    }

    private void validateStringLength(String value, String fieldName, int maxLength) {
        if (value != null && value.length() > maxLength) {
            throw new IllegalArgumentException(fieldName + "长度不得超过" + maxLength + "字符");
        }
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void saveResults(List<GuildPlatformActivityTaskResultVO> results,int dt, MultipartFile file, String operator) {
        resultMapper.deleteByDt(dt);
        // Convert VO to entity and save
        for (GuildPlatformActivityTaskResultVO vo : results) {
            GuildRankPlatformActivityTaskResult entity = convertToEntity(vo,operator);
            resultMapper.insertOrUpdate(entity);
        }
        // Save upload history
        String filePath = uploadToCloudStorage(file); // Pseudo-code for cloud storage
        GuildRankPlatformActivityTaskUploadHistory history = new GuildRankPlatformActivityTaskUploadHistory();
        history.setFilename(file.getOriginalFilename());
        history.setUploadDate(new Date());
        history.setFilePath(filePath);
        history.setOperator(operator);
        history.setDt(currentMonthInt());
        historyMapper.insertOrUpdate(history);
    }



    public PageResp<List<GuildPlatformActivityTaskResultVO>> getResults(Integer dt, int page, int size, String operator, List<Long> uids, GuildPlatformActivityTask taskType) {
        int offset = (page - 1) * size;
        String notNullDetailColumnName = taskType != null ? taskType.getColumnName() : null;
        List<GuildRankPlatformActivityTaskResult> entities = resultMapper.selectByMonthWithPagination(dt, offset, size, operator, uids, notNullDetailColumnName);
        int totalCount = (int)resultMapper.countByMonth(dt, operator, uids, notNullDetailColumnName);;
        List<GuildPlatformActivityTaskResultVO> results = new ArrayList<>();
        for (GuildRankPlatformActivityTaskResult entity : entities) {
            results.add(convertToVO(entity));
        }
        return  PageResp.createBySuccess(results,totalCount);
    }

    private String uploadToCloudStorage(MultipartFile file) {
        BosService bosService = bosServiceObjectProvider.getIfAvailable();
        return bosService.putFile(file);
    }

    private GuildRankPlatformActivityTaskResult convertToEntity(GuildPlatformActivityTaskResultVO vo,String operator) {
        GuildRankPlatformActivityTaskResult entity = new GuildRankPlatformActivityTaskResult();
        entity.setDt(convertMonthToInt(vo.getMonth()));
        entity.setUid(vo.getUid());
        entity.setYy(vo.getYy());
        entity.setGuildName(vo.getGuildName());
        entity.setSpringHeadlineTaskDetail(vo.getSpringHeadlineTaskDetail());
        entity.setSpringHeadlineCompletion(vo.getSpringHeadlineCompletion());
        entity.setSpringHeadlineScore(vo.getSpringHeadlineScore());
        entity.setSummerHeadlineTaskDetail(vo.getSummerHeadlineTaskDetail());
        entity.setSummerHeadlineCompletion(vo.getSummerHeadlineCompletion());
        entity.setSummerHeadlineScore(vo.getSummerHeadlineScore());
        entity.setGuildRaceS1TaskDetail(vo.getGuildRaceS1TaskDetail());
        entity.setGuildRaceS1Completion(vo.getGuildRaceS1Completion());
        entity.setGuildRaceS1Score(vo.getGuildRaceS1Score());
        entity.setGuildRaceS2TaskDetail(vo.getGuildRaceS2TaskDetail());
        entity.setGuildRaceS2Completion(vo.getGuildRaceS2Completion());
        entity.setGuildRaceS2Score(vo.getGuildRaceS2Score());
        entity.setGuildRaceMainTaskDetail(vo.getGuildRaceMainTaskDetail());
        entity.setGuildRaceMainCompletion(vo.getGuildRaceMainCompletion());
        entity.setGuildRaceMainScore(vo.getGuildRaceMainScore());
        entity.setPersonalRaceMainTaskDetail(vo.getPersonalRaceMainTaskDetail());
        entity.setPersonalRaceMainCompletion(vo.getPersonalRaceMainCompletion());
        entity.setPersonalRaceMainScore(vo.getPersonalRaceMainScore());
        entity.setRemark(vo.getRemark());
        entity.setOperator(operator);
        return entity;
    }

    public GuildRankPlatformActivityTaskResult getResultByUidAndDt(Long uid,Integer dt) {
        GuildRankPlatformActivityTaskResult entity = resultMapper.selectByUidAndDt(uid,dt);
        return entity;
    }

    private GuildPlatformActivityTaskResultVO convertToVO(GuildRankPlatformActivityTaskResult entity) {
        GuildPlatformActivityTaskResultVO vo = new GuildPlatformActivityTaskResultVO();
        vo.setMonth(String.format("%04d/%02d", entity.getDt() / 100, entity.getDt() % 100));
        vo.setUid(entity.getUid());
        vo.setYy(entity.getYy());
        vo.setGuildName(entity.getGuildName());
        vo.setSpringHeadlineTaskDetail(entity.getSpringHeadlineTaskDetail());
        vo.setSpringHeadlineCompletion(entity.getSpringHeadlineCompletion());
        vo.setSpringHeadlineScore(entity.getSpringHeadlineScore());
        vo.setSummerHeadlineTaskDetail(entity.getSummerHeadlineTaskDetail());
        vo.setSummerHeadlineCompletion(entity.getSummerHeadlineCompletion());
        vo.setSummerHeadlineScore(entity.getSummerHeadlineScore());
        vo.setGuildRaceS1TaskDetail(entity.getGuildRaceS1TaskDetail());
        vo.setGuildRaceS1Completion(entity.getGuildRaceS1Completion());
        vo.setGuildRaceS1Score(entity.getGuildRaceS1Score());
        vo.setGuildRaceS2TaskDetail(entity.getGuildRaceS2TaskDetail());
        vo.setGuildRaceS2Completion(entity.getGuildRaceS2Completion());
        vo.setGuildRaceS2Score(entity.getGuildRaceS2Score());
        vo.setGuildRaceMainTaskDetail(entity.getGuildRaceMainTaskDetail());
        vo.setGuildRaceMainCompletion(entity.getGuildRaceMainCompletion());
        vo.setGuildRaceMainScore(entity.getGuildRaceMainScore());
        vo.setPersonalRaceMainTaskDetail(entity.getPersonalRaceMainTaskDetail());
        vo.setPersonalRaceMainCompletion(entity.getPersonalRaceMainCompletion());
        vo.setPersonalRaceMainScore(entity.getPersonalRaceMainScore());
        vo.setRemark(entity.getRemark());
        vo.setUploader(entity.getOperator());
        return vo;
    }

    private int currentMonthInt() {
        return Integer.parseInt(LocalDate.now().format(MONTH_FORMATTER_INT));
    }

    private String currentMonth(){
        return LocalDate.now().format(MONTH_FORMATTER);
    }

    private int convertMonthToInt(String month) {
        return Integer.parseInt(month.replace("/", ""));
    }

    public List<GuildRankPlatformActivityTaskUploadHistory> getUploadHistory(Integer month,String operator) {
        return historyMapper.selectByDtOperator(month,operator);
    }

}