package com.yy.yyzone.guildrank.service;

import com.google.common.collect.Sets;
import com.yy.yyzone.guildrank.constant.GoldCompanyConstant;
import com.yy.yyzone.guildrank.yrpc.client.GoldChannelClient;
import com.yy.yyzone.guildrank.yrpc.dto.goldchannel.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GoldChannelService {
    @Reference(protocol = "yyp", owner = "yyzone_ent_goldchannel", timeout = 10000)
    private GoldChannelClient goldChannelClient;

    /**
     * 查YY号关联的经纪公司
     *
     * @param uids
     * @return
     */
    public Map<Long, Long> getAssociatedCompany(Set<Long> uids) {
        if (CollectionUtils.isEmpty(uids)) {
            return Collections.emptyMap();
        }

        PGetAssociatedCompany req = new PGetAssociatedCompany();
        req.setUids(uids);
        log.info("getAssociatedCompany req:{}", req);
        PGetAssociatedCompanyResp resp = goldChannelClient.getAssociatedCompany(req);
        log.info("getAssociatedCompany resp:{}", resp);
        if (resp.getResult().intValue() != 0 || CollectionUtils.isEmpty(resp.getInfo())) {
            return Collections.emptyMap();
        }
        Map<Long, Long> map = resp.getInfo().stream()
                .collect(Collectors.toMap(s -> Long.valueOf(s.getMap().get(GoldCompanyConstant.AssociateKey.UID)),
                        s -> Long.valueOf(s.getMap().get(GoldCompanyConstant.AssociateKey.COMPANY_ID))));
        return map;
    }

    /**
     * 查YY号关联的经纪公司
     *
     * @param uid
     * @return
     */
    public Long getAssociatedCompany(long uid) {
        return getAssociatedCompany(Sets.newHashSet(uid)).get(uid);
    }

    public Map<Long, Set<Long>> getAllAssociatedUser() {
        PGetAllAssociatedUser req = new PGetAllAssociatedUser();
        log.info("getAllAssociatedUser req:{}", req);
        PGetAllAssociatedUserRsp rsp = goldChannelClient.getAllAssociatedUser(req);
        if (rsp.getResult() != 0) {
            log.warn("getAllAssociatedUser warn rsp:{}", rsp);
            return null;
        }
        log.info("getAllAssociatedUser size:{}", rsp.getInfo().size());
        return rsp.getInfo().entrySet().stream().collect(Collectors.toMap(e -> e.getKey(), e -> e.getValue().getData()));
    }

    public Set<Long> getAllCompanyUid() {
        PGetAllCompanyUid req = new PGetAllCompanyUid();
        PGetAllCompanyUidResp rsp = goldChannelClient.getAllCompanyUid(req);
        if (rsp.getResult() != 0) {
            log.warn("getAllAssociatedUser warn rsp:{}", rsp);
            return null;
        }
        log.info("getAllCompanyUid size:{}", rsp.getUids().size());
        return rsp.getUids();
    }
}