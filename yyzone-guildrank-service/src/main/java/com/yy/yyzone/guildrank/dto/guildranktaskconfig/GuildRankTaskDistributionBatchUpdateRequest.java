package com.yy.yyzone.guildrank.dto.guildranktaskconfig;

import lombok.Data;
import javax.validation.constraints.*;
import java.util.Set;

@Data
public class GuildRankTaskDistributionBatchUpdateRequest {
    @NotEmpty(message = "uids不能为空")
    @Size(min = 1)
    private Set<@Positive(message = "UID必须为正数") Long> uids;

    @NotNull(message = "日期不能为空")
    @Min(value = 100000, message = "日期格式需为yyyyMM")
    @Max(value = 999999, message = "日期格式需为yyyyMM")
    /**
     * 格式 yyyyMM 202507
     */
    private Integer dt;

    @NotNull
    @NotEmpty
    private String task;
    @NotNull
    @Positive(message = "targetValue必须为正数")
    private Long targetValue;
}