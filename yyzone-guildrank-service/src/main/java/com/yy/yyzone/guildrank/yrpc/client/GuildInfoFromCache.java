// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: GuildInfoFromCache.proto

package com.yy.yyzone.guildrank.yrpc.client;

public final class GuildInfoFromCache {
  private GuildInfoFromCache() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface PGetGuildInfoFromCacheReqOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.yrpc.client.PGetGuildInfoFromCacheReq)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 owUid = 1;</code>
     */
    int getOwUid();

    /**
     * <code>map&lt;string, string&gt; extends = 2;</code>
     */
    int getExtendsCount();
    /**
     * <code>map&lt;string, string&gt; extends = 2;</code>
     */
    boolean containsExtends(
            String key);
    /**
     * Use {@link #getExtendsMap()} instead.
     */
    @Deprecated
    java.util.Map<String, String>
    getExtends();
    /**
     * <code>map&lt;string, string&gt; extends = 2;</code>
     */
    java.util.Map<String, String>
    getExtendsMap();
    /**
     * <code>map&lt;string, string&gt; extends = 2;</code>
     */

    String getExtendsOrDefault(
            String key,
            String defaultValue);
    /**
     * <code>map&lt;string, string&gt; extends = 2;</code>
     */

    String getExtendsOrThrow(
            String key);
  }
  /**
   * Protobuf type {@code com.yy.yyzone.guildrank.yrpc.client.PGetGuildInfoFromCacheReq}
   */
  public  static final class PGetGuildInfoFromCacheReq extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.yrpc.client.PGetGuildInfoFromCacheReq)
      PGetGuildInfoFromCacheReqOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PGetGuildInfoFromCacheReq.newBuilder() to construct.
    private PGetGuildInfoFromCacheReq(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PGetGuildInfoFromCacheReq() {
      owUid_ = 0;
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PGetGuildInfoFromCacheReq(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              owUid_ = input.readUInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                extends_ = com.google.protobuf.MapField.newMapField(
                    ExtendsDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000002;
              }
              com.google.protobuf.MapEntry<String, String>
              extends__ = input.readMessage(
                  ExtendsDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              extends_.getMutableMap().put(
                  extends__.getKey(), extends__.getValue());
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return GuildInfoFromCache.internal_static_com_yy_yyzone_guildrank_yrpc_client_PGetGuildInfoFromCacheReq_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 2:
          return internalGetExtends();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return GuildInfoFromCache.internal_static_com_yy_yyzone_guildrank_yrpc_client_PGetGuildInfoFromCacheReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              PGetGuildInfoFromCacheReq.class, Builder.class);
    }

    private int bitField0_;
    public static final int OWUID_FIELD_NUMBER = 1;
    private int owUid_;
    /**
     * <code>uint32 owUid = 1;</code>
     */
    public int getOwUid() {
      return owUid_;
    }

    public static final int EXTENDS_FIELD_NUMBER = 2;
    private static final class ExtendsDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          String, String> defaultEntry =
              com.google.protobuf.MapEntry
              .<String, String>newDefaultInstance(
                  GuildInfoFromCache.internal_static_com_yy_yyzone_guildrank_yrpc_client_PGetGuildInfoFromCacheReq_ExtendsEntry_descriptor,
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "",
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "");
    }
    private com.google.protobuf.MapField<
        String, String> extends_;
    private com.google.protobuf.MapField<String, String>
    internalGetExtends() {
      if (extends_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ExtendsDefaultEntryHolder.defaultEntry);
      }
      return extends_;
    }

    public int getExtendsCount() {
      return internalGetExtends().getMap().size();
    }
    /**
     * <code>map&lt;string, string&gt; extends = 2;</code>
     */

    public boolean containsExtends(
        String key) {
      if (key == null) { throw new NullPointerException(); }
      return internalGetExtends().getMap().containsKey(key);
    }
    /**
     * Use {@link #getExtendsMap()} instead.
     */
    @Deprecated
    public java.util.Map<String, String> getExtends() {
      return getExtendsMap();
    }
    /**
     * <code>map&lt;string, string&gt; extends = 2;</code>
     */

    public java.util.Map<String, String> getExtendsMap() {
      return internalGetExtends().getMap();
    }
    /**
     * <code>map&lt;string, string&gt; extends = 2;</code>
     */

    public String getExtendsOrDefault(
        String key,
        String defaultValue) {
      if (key == null) { throw new NullPointerException(); }
      java.util.Map<String, String> map =
          internalGetExtends().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;string, string&gt; extends = 2;</code>
     */

    public String getExtendsOrThrow(
        String key) {
      if (key == null) { throw new NullPointerException(); }
      java.util.Map<String, String> map =
          internalGetExtends().getMap();
      if (!map.containsKey(key)) {
        throw new IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (owUid_ != 0) {
        output.writeUInt32(1, owUid_);
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeStringMapTo(
          output,
          internalGetExtends(),
          ExtendsDefaultEntryHolder.defaultEntry,
          2);
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (owUid_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, owUid_);
      }
      for (java.util.Map.Entry<String, String> entry
           : internalGetExtends().getMap().entrySet()) {
        com.google.protobuf.MapEntry<String, String>
        extends__ = ExtendsDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(2, extends__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof PGetGuildInfoFromCacheReq)) {
        return super.equals(obj);
      }
      PGetGuildInfoFromCacheReq other = (PGetGuildInfoFromCacheReq) obj;

      boolean result = true;
      result = result && (getOwUid()
          == other.getOwUid());
      result = result && internalGetExtends().equals(
          other.internalGetExtends());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + OWUID_FIELD_NUMBER;
      hash = (53 * hash) + getOwUid();
      if (!internalGetExtends().getMap().isEmpty()) {
        hash = (37 * hash) + EXTENDS_FIELD_NUMBER;
        hash = (53 * hash) + internalGetExtends().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static PGetGuildInfoFromCacheReq parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static PGetGuildInfoFromCacheReq parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static PGetGuildInfoFromCacheReq parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static PGetGuildInfoFromCacheReq parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static PGetGuildInfoFromCacheReq parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static PGetGuildInfoFromCacheReq parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static PGetGuildInfoFromCacheReq parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static PGetGuildInfoFromCacheReq parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static PGetGuildInfoFromCacheReq parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static PGetGuildInfoFromCacheReq parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static PGetGuildInfoFromCacheReq parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static PGetGuildInfoFromCacheReq parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(PGetGuildInfoFromCacheReq prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.yyzone.guildrank.yrpc.client.PGetGuildInfoFromCacheReq}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.yrpc.client.PGetGuildInfoFromCacheReq)
        PGetGuildInfoFromCacheReqOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return GuildInfoFromCache.internal_static_com_yy_yyzone_guildrank_yrpc_client_PGetGuildInfoFromCacheReq_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 2:
            return internalGetExtends();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 2:
            return internalGetMutableExtends();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return GuildInfoFromCache.internal_static_com_yy_yyzone_guildrank_yrpc_client_PGetGuildInfoFromCacheReq_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                PGetGuildInfoFromCacheReq.class, Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.yrpc.client.GuildInfoFromCache.PGetGuildInfoFromCacheReq.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        owUid_ = 0;

        internalGetMutableExtends().clear();
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return GuildInfoFromCache.internal_static_com_yy_yyzone_guildrank_yrpc_client_PGetGuildInfoFromCacheReq_descriptor;
      }

      @Override
      public PGetGuildInfoFromCacheReq getDefaultInstanceForType() {
        return PGetGuildInfoFromCacheReq.getDefaultInstance();
      }

      @Override
      public PGetGuildInfoFromCacheReq build() {
        PGetGuildInfoFromCacheReq result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public PGetGuildInfoFromCacheReq buildPartial() {
        PGetGuildInfoFromCacheReq result = new PGetGuildInfoFromCacheReq(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.owUid_ = owUid_;
        result.extends_ = internalGetExtends();
        result.extends_.makeImmutable();
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof PGetGuildInfoFromCacheReq) {
          return mergeFrom((PGetGuildInfoFromCacheReq)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(PGetGuildInfoFromCacheReq other) {
        if (other == PGetGuildInfoFromCacheReq.getDefaultInstance()) return this;
        if (other.getOwUid() != 0) {
          setOwUid(other.getOwUid());
        }
        internalGetMutableExtends().mergeFrom(
            other.internalGetExtends());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        PGetGuildInfoFromCacheReq parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (PGetGuildInfoFromCacheReq) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int owUid_ ;
      /**
       * <code>uint32 owUid = 1;</code>
       */
      public int getOwUid() {
        return owUid_;
      }
      /**
       * <code>uint32 owUid = 1;</code>
       */
      public Builder setOwUid(int value) {
        
        owUid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 owUid = 1;</code>
       */
      public Builder clearOwUid() {
        
        owUid_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.MapField<
          String, String> extends_;
      private com.google.protobuf.MapField<String, String>
      internalGetExtends() {
        if (extends_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ExtendsDefaultEntryHolder.defaultEntry);
        }
        return extends_;
      }
      private com.google.protobuf.MapField<String, String>
      internalGetMutableExtends() {
        onChanged();;
        if (extends_ == null) {
          extends_ = com.google.protobuf.MapField.newMapField(
              ExtendsDefaultEntryHolder.defaultEntry);
        }
        if (!extends_.isMutable()) {
          extends_ = extends_.copy();
        }
        return extends_;
      }

      public int getExtendsCount() {
        return internalGetExtends().getMap().size();
      }
      /**
       * <code>map&lt;string, string&gt; extends = 2;</code>
       */

      public boolean containsExtends(
          String key) {
        if (key == null) { throw new NullPointerException(); }
        return internalGetExtends().getMap().containsKey(key);
      }
      /**
       * Use {@link #getExtendsMap()} instead.
       */
      @Deprecated
      public java.util.Map<String, String> getExtends() {
        return getExtendsMap();
      }
      /**
       * <code>map&lt;string, string&gt; extends = 2;</code>
       */

      public java.util.Map<String, String> getExtendsMap() {
        return internalGetExtends().getMap();
      }
      /**
       * <code>map&lt;string, string&gt; extends = 2;</code>
       */

      public String getExtendsOrDefault(
          String key,
          String defaultValue) {
        if (key == null) { throw new NullPointerException(); }
        java.util.Map<String, String> map =
            internalGetExtends().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;string, string&gt; extends = 2;</code>
       */

      public String getExtendsOrThrow(
          String key) {
        if (key == null) { throw new NullPointerException(); }
        java.util.Map<String, String> map =
            internalGetExtends().getMap();
        if (!map.containsKey(key)) {
          throw new IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearExtends() {
        internalGetMutableExtends().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;string, string&gt; extends = 2;</code>
       */

      public Builder removeExtends(
          String key) {
        if (key == null) { throw new NullPointerException(); }
        internalGetMutableExtends().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @Deprecated
      public java.util.Map<String, String>
      getMutableExtends() {
        return internalGetMutableExtends().getMutableMap();
      }
      /**
       * <code>map&lt;string, string&gt; extends = 2;</code>
       */
      public Builder putExtends(
          String key,
          String value) {
        if (key == null) { throw new NullPointerException(); }
        if (value == null) { throw new NullPointerException(); }
        internalGetMutableExtends().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;string, string&gt; extends = 2;</code>
       */

      public Builder putAllExtends(
          java.util.Map<String, String> values) {
        internalGetMutableExtends().getMutableMap()
            .putAll(values);
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.yrpc.client.PGetGuildInfoFromCacheReq)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.yrpc.client.PGetGuildInfoFromCacheReq)
    private static final PGetGuildInfoFromCacheReq DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new PGetGuildInfoFromCacheReq();
    }

    public static PGetGuildInfoFromCacheReq getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<PGetGuildInfoFromCacheReq>
        PARSER = new com.google.protobuf.AbstractParser<PGetGuildInfoFromCacheReq>() {
      @Override
      public PGetGuildInfoFromCacheReq parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PGetGuildInfoFromCacheReq(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PGetGuildInfoFromCacheReq> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<PGetGuildInfoFromCacheReq> getParserForType() {
      return PARSER;
    }

    @Override
    public PGetGuildInfoFromCacheReq getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface PGetGuildInfoFromCacheRspOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.yrpc.client.PGetGuildInfoFromCacheRsp)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 result = 1;</code>
     */
    int getResult();

    /**
     * <code>uint32 owUid = 2;</code>
     */
    int getOwUid();

    /**
     * <pre>
     * 星级公会成立日期(if any)
     * </pre>
     *
     * <code>string starDate = 3;</code>
     */
    String getStarDate();
    /**
     * <pre>
     * 星级公会成立日期(if any)
     * </pre>
     *
     * <code>string starDate = 3;</code>
     */
    com.google.protobuf.ByteString
        getStarDateBytes();

    /**
     * <pre>
     * 金牌公会成立日期(if any)
     * </pre>
     *
     * <code>string goldenDate = 4;</code>
     */
    String getGoldenDate();
    /**
     * <pre>
     * 金牌公会成立日期(if any)
     * </pre>
     *
     * <code>string goldenDate = 4;</code>
     */
    com.google.protobuf.ByteString
        getGoldenDateBytes();
  }
  /**
   * Protobuf type {@code com.yy.yyzone.guildrank.yrpc.client.PGetGuildInfoFromCacheRsp}
   */
  public  static final class PGetGuildInfoFromCacheRsp extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.yrpc.client.PGetGuildInfoFromCacheRsp)
      PGetGuildInfoFromCacheRspOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PGetGuildInfoFromCacheRsp.newBuilder() to construct.
    private PGetGuildInfoFromCacheRsp(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PGetGuildInfoFromCacheRsp() {
      result_ = 0;
      owUid_ = 0;
      starDate_ = "";
      goldenDate_ = "";
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PGetGuildInfoFromCacheRsp(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              result_ = input.readUInt32();
              break;
            }
            case 16: {

              owUid_ = input.readUInt32();
              break;
            }
            case 26: {
              String s = input.readStringRequireUtf8();

              starDate_ = s;
              break;
            }
            case 34: {
              String s = input.readStringRequireUtf8();

              goldenDate_ = s;
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return GuildInfoFromCache.internal_static_com_yy_yyzone_guildrank_yrpc_client_PGetGuildInfoFromCacheRsp_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return GuildInfoFromCache.internal_static_com_yy_yyzone_guildrank_yrpc_client_PGetGuildInfoFromCacheRsp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              PGetGuildInfoFromCacheRsp.class, Builder.class);
    }

    public static final int RESULT_FIELD_NUMBER = 1;
    private int result_;
    /**
     * <code>uint32 result = 1;</code>
     */
    public int getResult() {
      return result_;
    }

    public static final int OWUID_FIELD_NUMBER = 2;
    private int owUid_;
    /**
     * <code>uint32 owUid = 2;</code>
     */
    public int getOwUid() {
      return owUid_;
    }

    public static final int STARDATE_FIELD_NUMBER = 3;
    private volatile Object starDate_;
    /**
     * <pre>
     * 星级公会成立日期(if any)
     * </pre>
     *
     * <code>string starDate = 3;</code>
     */
    public String getStarDate() {
      Object ref = starDate_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        starDate_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 星级公会成立日期(if any)
     * </pre>
     *
     * <code>string starDate = 3;</code>
     */
    public com.google.protobuf.ByteString
        getStarDateBytes() {
      Object ref = starDate_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        starDate_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int GOLDENDATE_FIELD_NUMBER = 4;
    private volatile Object goldenDate_;
    /**
     * <pre>
     * 金牌公会成立日期(if any)
     * </pre>
     *
     * <code>string goldenDate = 4;</code>
     */
    public String getGoldenDate() {
      Object ref = goldenDate_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        goldenDate_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 金牌公会成立日期(if any)
     * </pre>
     *
     * <code>string goldenDate = 4;</code>
     */
    public com.google.protobuf.ByteString
        getGoldenDateBytes() {
      Object ref = goldenDate_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        goldenDate_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (result_ != 0) {
        output.writeUInt32(1, result_);
      }
      if (owUid_ != 0) {
        output.writeUInt32(2, owUid_);
      }
      if (!getStarDateBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, starDate_);
      }
      if (!getGoldenDateBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, goldenDate_);
      }
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (result_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, result_);
      }
      if (owUid_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, owUid_);
      }
      if (!getStarDateBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, starDate_);
      }
      if (!getGoldenDateBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, goldenDate_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof PGetGuildInfoFromCacheRsp)) {
        return super.equals(obj);
      }
      PGetGuildInfoFromCacheRsp other = (PGetGuildInfoFromCacheRsp) obj;

      boolean result = true;
      result = result && (getResult()
          == other.getResult());
      result = result && (getOwUid()
          == other.getOwUid());
      result = result && getStarDate()
          .equals(other.getStarDate());
      result = result && getGoldenDate()
          .equals(other.getGoldenDate());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + getResult();
      hash = (37 * hash) + OWUID_FIELD_NUMBER;
      hash = (53 * hash) + getOwUid();
      hash = (37 * hash) + STARDATE_FIELD_NUMBER;
      hash = (53 * hash) + getStarDate().hashCode();
      hash = (37 * hash) + GOLDENDATE_FIELD_NUMBER;
      hash = (53 * hash) + getGoldenDate().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static PGetGuildInfoFromCacheRsp parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static PGetGuildInfoFromCacheRsp parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static PGetGuildInfoFromCacheRsp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static PGetGuildInfoFromCacheRsp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static PGetGuildInfoFromCacheRsp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static PGetGuildInfoFromCacheRsp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static PGetGuildInfoFromCacheRsp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static PGetGuildInfoFromCacheRsp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static PGetGuildInfoFromCacheRsp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static PGetGuildInfoFromCacheRsp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static PGetGuildInfoFromCacheRsp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static PGetGuildInfoFromCacheRsp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(PGetGuildInfoFromCacheRsp prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.yyzone.guildrank.yrpc.client.PGetGuildInfoFromCacheRsp}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.yrpc.client.PGetGuildInfoFromCacheRsp)
        PGetGuildInfoFromCacheRspOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return GuildInfoFromCache.internal_static_com_yy_yyzone_guildrank_yrpc_client_PGetGuildInfoFromCacheRsp_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return GuildInfoFromCache.internal_static_com_yy_yyzone_guildrank_yrpc_client_PGetGuildInfoFromCacheRsp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                PGetGuildInfoFromCacheRsp.class, Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.yrpc.client.GuildInfoFromCache.PGetGuildInfoFromCacheRsp.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        result_ = 0;

        owUid_ = 0;

        starDate_ = "";

        goldenDate_ = "";

        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return GuildInfoFromCache.internal_static_com_yy_yyzone_guildrank_yrpc_client_PGetGuildInfoFromCacheRsp_descriptor;
      }

      @Override
      public PGetGuildInfoFromCacheRsp getDefaultInstanceForType() {
        return PGetGuildInfoFromCacheRsp.getDefaultInstance();
      }

      @Override
      public PGetGuildInfoFromCacheRsp build() {
        PGetGuildInfoFromCacheRsp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public PGetGuildInfoFromCacheRsp buildPartial() {
        PGetGuildInfoFromCacheRsp result = new PGetGuildInfoFromCacheRsp(this);
        result.result_ = result_;
        result.owUid_ = owUid_;
        result.starDate_ = starDate_;
        result.goldenDate_ = goldenDate_;
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof PGetGuildInfoFromCacheRsp) {
          return mergeFrom((PGetGuildInfoFromCacheRsp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(PGetGuildInfoFromCacheRsp other) {
        if (other == PGetGuildInfoFromCacheRsp.getDefaultInstance()) return this;
        if (other.getResult() != 0) {
          setResult(other.getResult());
        }
        if (other.getOwUid() != 0) {
          setOwUid(other.getOwUid());
        }
        if (!other.getStarDate().isEmpty()) {
          starDate_ = other.starDate_;
          onChanged();
        }
        if (!other.getGoldenDate().isEmpty()) {
          goldenDate_ = other.goldenDate_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        PGetGuildInfoFromCacheRsp parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (PGetGuildInfoFromCacheRsp) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int result_ ;
      /**
       * <code>uint32 result = 1;</code>
       */
      public int getResult() {
        return result_;
      }
      /**
       * <code>uint32 result = 1;</code>
       */
      public Builder setResult(int value) {
        
        result_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 result = 1;</code>
       */
      public Builder clearResult() {
        
        result_ = 0;
        onChanged();
        return this;
      }

      private int owUid_ ;
      /**
       * <code>uint32 owUid = 2;</code>
       */
      public int getOwUid() {
        return owUid_;
      }
      /**
       * <code>uint32 owUid = 2;</code>
       */
      public Builder setOwUid(int value) {
        
        owUid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 owUid = 2;</code>
       */
      public Builder clearOwUid() {
        
        owUid_ = 0;
        onChanged();
        return this;
      }

      private Object starDate_ = "";
      /**
       * <pre>
       * 星级公会成立日期(if any)
       * </pre>
       *
       * <code>string starDate = 3;</code>
       */
      public String getStarDate() {
        Object ref = starDate_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          starDate_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       * 星级公会成立日期(if any)
       * </pre>
       *
       * <code>string starDate = 3;</code>
       */
      public com.google.protobuf.ByteString
          getStarDateBytes() {
        Object ref = starDate_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          starDate_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 星级公会成立日期(if any)
       * </pre>
       *
       * <code>string starDate = 3;</code>
       */
      public Builder setStarDate(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        starDate_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 星级公会成立日期(if any)
       * </pre>
       *
       * <code>string starDate = 3;</code>
       */
      public Builder clearStarDate() {
        
        starDate_ = getDefaultInstance().getStarDate();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 星级公会成立日期(if any)
       * </pre>
       *
       * <code>string starDate = 3;</code>
       */
      public Builder setStarDateBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        starDate_ = value;
        onChanged();
        return this;
      }

      private Object goldenDate_ = "";
      /**
       * <pre>
       * 金牌公会成立日期(if any)
       * </pre>
       *
       * <code>string goldenDate = 4;</code>
       */
      public String getGoldenDate() {
        Object ref = goldenDate_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          goldenDate_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       * 金牌公会成立日期(if any)
       * </pre>
       *
       * <code>string goldenDate = 4;</code>
       */
      public com.google.protobuf.ByteString
          getGoldenDateBytes() {
        Object ref = goldenDate_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          goldenDate_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 金牌公会成立日期(if any)
       * </pre>
       *
       * <code>string goldenDate = 4;</code>
       */
      public Builder setGoldenDate(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        goldenDate_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 金牌公会成立日期(if any)
       * </pre>
       *
       * <code>string goldenDate = 4;</code>
       */
      public Builder clearGoldenDate() {
        
        goldenDate_ = getDefaultInstance().getGoldenDate();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 金牌公会成立日期(if any)
       * </pre>
       *
       * <code>string goldenDate = 4;</code>
       */
      public Builder setGoldenDateBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        goldenDate_ = value;
        onChanged();
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.yrpc.client.PGetGuildInfoFromCacheRsp)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.yrpc.client.PGetGuildInfoFromCacheRsp)
    private static final PGetGuildInfoFromCacheRsp DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new PGetGuildInfoFromCacheRsp();
    }

    public static PGetGuildInfoFromCacheRsp getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<PGetGuildInfoFromCacheRsp>
        PARSER = new com.google.protobuf.AbstractParser<PGetGuildInfoFromCacheRsp>() {
      @Override
      public PGetGuildInfoFromCacheRsp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PGetGuildInfoFromCacheRsp(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PGetGuildInfoFromCacheRsp> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<PGetGuildInfoFromCacheRsp> getParserForType() {
      return PARSER;
    }

    @Override
    public PGetGuildInfoFromCacheRsp getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_yrpc_client_PGetGuildInfoFromCacheReq_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_yrpc_client_PGetGuildInfoFromCacheReq_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_yrpc_client_PGetGuildInfoFromCacheReq_ExtendsEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_yrpc_client_PGetGuildInfoFromCacheReq_ExtendsEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_yrpc_client_PGetGuildInfoFromCacheRsp_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_yrpc_client_PGetGuildInfoFromCacheRsp_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    String[] descriptorData = {
      "\n\030GuildInfoFromCache.proto\022#com.yy.yyzon" +
      "e.guildrank.yrpc.client\032\turi.proto\"\277\001\n\031P" +
      "GetGuildInfoFromCacheReq\022\r\n\005owUid\030\001 \001(\r\022" +
      "\\\n\007extends\030\002 \003(\0132K.com.yy.yyzone.guildra" +
      "nk.yrpc.client.PGetGuildInfoFromCacheReq" +
      ".ExtendsEntry\032.\n\014ExtendsEntry\022\013\n\003key\030\001 \001" +
      "(\t\022\r\n\005value\030\002 \001(\t:\0028\001:\005\310>\236\313\004\"g\n\031PGetGuil" +
      "dInfoFromCacheRsp\022\016\n\006result\030\001 \001(\r\022\r\n\005owU" +
      "id\030\002 \001(\r\022\020\n\010starDate\030\003 \001(\t\022\022\n\ngoldenDate" +
      "\030\004 \001(\t:\005\310>\236\315\0042\265\001\n\031GuildInfoFromCacheServ" +
      "ice\022\227\001\n\025getGuildInfoFromCache\022>.com.yy.y" +
      "yzone.guildrank.yrpc.client.PGetGuildInf" +
      "oFromCacheReq\032>.com.yy.yyzone.guildrank." +
      "yrpc.client.PGetGuildInfoFromCacheRspb\006p" +
      "roto3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          yyp.Uri.getDescriptor(),
        }, assigner);
    internal_static_com_yy_yyzone_guildrank_yrpc_client_PGetGuildInfoFromCacheReq_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yy_yyzone_guildrank_yrpc_client_PGetGuildInfoFromCacheReq_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_yrpc_client_PGetGuildInfoFromCacheReq_descriptor,
        new String[] { "OwUid", "Extends", });
    internal_static_com_yy_yyzone_guildrank_yrpc_client_PGetGuildInfoFromCacheReq_ExtendsEntry_descriptor =
      internal_static_com_yy_yyzone_guildrank_yrpc_client_PGetGuildInfoFromCacheReq_descriptor.getNestedTypes().get(0);
    internal_static_com_yy_yyzone_guildrank_yrpc_client_PGetGuildInfoFromCacheReq_ExtendsEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_yrpc_client_PGetGuildInfoFromCacheReq_ExtendsEntry_descriptor,
        new String[] { "Key", "Value", });
    internal_static_com_yy_yyzone_guildrank_yrpc_client_PGetGuildInfoFromCacheRsp_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yy_yyzone_guildrank_yrpc_client_PGetGuildInfoFromCacheRsp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_yrpc_client_PGetGuildInfoFromCacheRsp_descriptor,
        new String[] { "Result", "OwUid", "StarDate", "GoldenDate", });
    com.google.protobuf.ExtensionRegistry registry =
        com.google.protobuf.ExtensionRegistry.newInstance();
    registry.add(yyp.Uri.uri);
    com.google.protobuf.Descriptors.FileDescriptor
        .internalUpdateFileDescriptor(descriptor, registry);
    yyp.Uri.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
