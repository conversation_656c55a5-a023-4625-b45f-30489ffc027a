package com.yy.yyzone.guildrank.service;

import com.yy.yyzone.guildrank.util.MyDateUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Date;

@Slf4j
@Service
public class GuildRankTransService {
    @Value("#{T(com.yy.yyzone.guildrank.util.MyDateUtil).parseDate('${rank.use-v3-date:}')}")
    private Date useV3Date;

    @Autowired
    private RankCacheV3Service rankCacheV3Service;

    @SneakyThrows
    public boolean useV3Data(String reqMonth) {
        // 新功能改版上线后会涉及到新旧公会段位等级查询的问题，目前并不太好去挖掘到底有哪些下游会调用公会段位等级接口查询公会的等级进行业务场景上的处理，
        // 但因为本次公会段位等级调整后与旧逻辑是两个等级统计维度，本身与旧等级就不对等，所以该处默认如果有下游调用接口查询公会历史等级时，默认返回无历史等级，
        // 从上线后开始算公会的新等级，比如10月1日上线，无历史等级，11月1日开始有历史等级；
        return true;

        // if (StringUtils.isBlank(reqMonth)) {
        //     log.info("useV3Data false reqMonth:{}", reqMonth);
        //     return true;
        // }
        // Date m = DateUtils.parseDate(reqMonth, "yyyy-MM");
        // boolean flag = DateUtils.truncatedCompareTo(m, useV3Date, Calendar.MONTH) >= 0;
        // log.info("useV3Data reqMonth:{},flag:{}", reqMonth, flag);
        //
        // return flag;
    }

    /**
     * 过渡阶段是否使用v3版本逻辑（过度阶段后应及时移除旧逻辑） 在指定日期前为false 在指定日期后，若v3版数据已更新则为true，否则false
     *
     * @return
     */
    public boolean useV3(String src) {
        log.info("useV3 src:{}", src);
        return true;

        // if (useV3Date == null) {
        //     log.info("useV3 dt null result:false,src:{}", src);
        //     return false;
        // }
        // Date since = DateUtils.addMonths(useV3Date, 1);
        // if (new Date().before(since)) {
        //     log.info("useV3  before dt:{},result:false,src:{}", MyDateUtil.fmt(since), src);
        //     return false;
        // }
        //
        // boolean updateFlag = rankCacheV3Service.hasUpdate();
        // log.info("useV3 dt:{},result:{},src:{}", MyDateUtil.fmt(since), updateFlag, src);
        // return updateFlag;
    }

    public boolean isTransMonth(Date month) {
        return DateUtils.truncatedEquals(useV3Date,month,Calendar.MONTH);
    }
}