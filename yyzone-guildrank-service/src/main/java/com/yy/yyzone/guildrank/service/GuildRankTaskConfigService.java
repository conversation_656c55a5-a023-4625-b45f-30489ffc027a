package com.yy.yyzone.guildrank.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yy.yyzone.guildrank.db.gen.model.JsonConfig;
import com.yy.yyzone.guildrank.dto.guildranktaskconfig.*;
import com.yy.yyzone.guildrank.enums.JsonConfigNameEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class GuildRankTaskConfigService {

    private static final TypeReference<GuildRankAllTaskConfig> GUILD_RANK_TASK_CONFIG_TYPEREFERENCE =   new TypeReference<GuildRankAllTaskConfig>() {
    };

    private JsonConfigService jsonConfigService;
    public GuildRankTaskConfigService(JsonConfigService jsonConfigService) {
        this.jsonConfigService = jsonConfigService;
    }

    public Pair<Long,GuildRankAllTaskConfig> getGuildRankTaskConfigAndVersion() {
        return jsonConfigService.getLatestConfigAndIdByName(JsonConfigNameEnum.GUILD_RANK_ALL_TASK_CONFIG, GUILD_RANK_TASK_CONFIG_TYPEREFERENCE);
    }
    public List<ConfigHistory<GuildRankAllTaskConfig>> getGuildRankTaskConfigHistory(Date startTime, Date endTime, String operator) {
        return jsonConfigService.getConfigHistoryByNameAndFilter(JsonConfigNameEnum.GUILD_RANK_ALL_TASK_CONFIG, GUILD_RANK_TASK_CONFIG_TYPEREFERENCE,startTime,endTime,operator);
    }

    public GuildRankAllTaskConfig getGuildRankTaskConfigSnapshot(int dt) {
        GuildRankAllTaskConfig guildRankAllTaskConfig = jsonConfigService.getLatestConfigSnapshot(dt,JsonConfigNameEnum.GUILD_RANK_ALL_TASK_CONFIG, GUILD_RANK_TASK_CONFIG_TYPEREFERENCE);
        log.info("getGuildRankTaskConfigSnapshot dt:{} guildRankAllTaskConfig:{}",dt,guildRankAllTaskConfig);
        return guildRankAllTaskConfig;
    }

    public Pair<GuildRankAllTaskConfig,Long> setGuildRankTaskConfig(AddGuildRankTaskConfigReq req, String operator) {
        List<Range> ranges = req.getAllTaskConfig().getTaskConfig().getMonthBlueIncome().getCondition().getRanges();
        for(Range range : ranges) {
            if(CollectionUtils.isNotEmpty(range.getScore().getIndicators())){
                //为了bi方便读取，所以这么干了
                range.getScore().setIndicator(IndicatorEnum.monthBlueIncomeCurrentMonthIncrease);
            }
            if(CollectionUtils.isNotEmpty(range.getScore().getRangeIndicators())){
                //为了bi方便读取，所以这么干了
                range.getScore().setIndicator(IndicatorEnum.monthBlueIncomeCurrentMonthTotal);
            }
        }
        JsonConfig jsonConfig = jsonConfigService.addConfig(req.getVersion() ,req.getAllTaskConfig(),JsonConfigNameEnum.GUILD_RANK_ALL_TASK_CONFIG,operator);
        log.info("setGuildRankTaskConfig success,config:{}",jsonConfig);
        return Pair.of(req.getAllTaskConfig(),jsonConfig.getId());
    }
}
