package com.yy.yyzone.guildrank.config;

import io.lettuce.core.ReadFrom;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.data.redis.LettuceClientConfigurationBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * redis非主库机房时，读从库
 *
 * <AUTHOR>
 * @date 2023/03/15
 **/
@Configuration
public class LettuceClientConfiguration {

    @Value("${yy.idc:}")
    private String idc;
    public static final String SHENZHEN = "shenzhen";

    @Bean
    public LettuceClientConfigurationBuilderCustomizer lettuceClientConfigurationBuilderCustomizer() {
        return builder -> builder.readFrom(SHENZHEN.equalsIgnoreCase(idc) ? ReadFrom.SLAVE_PREFERRED : ReadFrom.MASTER);
    }

}
