package com.yy.yyzone.guildrank.util;

import org.apache.commons.lang3.StringUtils;

public class StrUtils {
    /**
     * 将_分割的字符串转成驼峰格式
     * @param s
     * @return
     */
    public static String toCamelCase(String s) {
        if(StringUtils.isBlank(s)) {
            return s;
        }
        StringBuilder result = new StringBuilder();
        boolean capitalizeNext = false;

        for (int i = 0; i < s.length(); i++) {
            char currentChar = s.charAt(i);

            if (currentChar == '_') {
                capitalizeNext = true;
            } else {
                if (capitalizeNext) {
                    result.append(Character.toUpperCase(currentChar));
                    capitalizeNext = false;
                } else {
                    if (i == 0) {
                        result.append(Character.toUpperCase(currentChar));
                    } else {
                        result.append(currentChar);
                    }
                }
            }
        }

        return result.toString();
    }
}