package com.yy.yyzone.guildrank.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.ent.commons.protopack.util.Uint;
import com.yy.yyzone.guildrank.yrpc.client.GuildTypeService;
import com.yy.yyzone.guildrank.yrpc.dto.GetAllStarGoldGuild;
import com.yy.yyzone.guildrank.yrpc.dto.GetAllStarGoldGuildRsp;
import com.yy.yyzone.guildrank.yrpc.dto.MapStringStringBean;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/03/16
 **/
@Service
public class StarService {
    private static final Logger logger = LoggerFactory.getLogger(StarService.class);

    @Reference(protocol = "yyp", owner = "yyzone_ent_star", timeout = 7000)
    private GuildTypeService guildTypeService;

    private static final String UID = "uid";

    /**
     * 获取所有所有星级和/或金牌公会频道信息
     *
     * @return
     */
    public List<Long> getAllStarGoldGuild() {
        GetAllStarGoldGuild req = new GetAllStarGoldGuild();
        req.setOffset(new Uint(0));
        req.setLimit(new Uint(10000));
        req.setExtend(Maps.newHashMap());

        GetAllStarGoldGuildRsp resp = guildTypeService.getAllStarGoldGuild(req);
        logger.info("getAllStarGoldGuild, resp:{}", resp);

        List<Long> uidList = Lists.newArrayList();
        for (MapStringStringBean map : resp.getData()) {
            uidList.add(new Long(map.getMap().get(UID)));
        }
        return uidList;
    }
}
