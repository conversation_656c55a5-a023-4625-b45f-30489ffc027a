package com.yy.yyzone.guildrank.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class GuildRankTasksExportReq {
    @Size(min = 0,max = 100)
    private List<Long> uids;
    @Size(min = 0,max = 100)
    private List<Long> yys;
    /**
     * 格式 yyyyMM 202507
     */
    private Integer dtStart;
    /**
     * 格式 yyyyMM 202507
     */
    private Integer dtEnd;
    /**
     * onlineBind3And4Star 线上绑定3&4星主播数量
     *  newHighWaistAnchor 新增高优&腰部主播
     * newAnchorBlueDiamond 新主播蓝钻任务
     *  guildBlueDiamond 公会蓝钻任务
     * monthBlueIncome 自然月月累积蓝钻收入
     *  springHeadline  春季头条任务
     * summerHeadline 夏季头条任务
     *   guildRaceS1 公会赛S1任务
     *  guildRaceS2 公会赛S2任务
     *   guildRaceMain 公会赛正赛任务
     *   personalRaceMain 个人赛正赛任务
     */
    private String task;
}
