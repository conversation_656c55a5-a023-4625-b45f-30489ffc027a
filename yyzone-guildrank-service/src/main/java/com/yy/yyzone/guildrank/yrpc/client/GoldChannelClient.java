package com.yy.yyzone.guildrank.yrpc.client;

import com.yy.yyzone.guildrank.yrpc.dto.goldchannel.*;
import org.apache.dubbo.common.annotation.Yrpc;

public interface GoldChannelClient {
    /**
     * 查YY号关联的经纪公司
     *
     * @param req
     * @return
     */
    @Yrpc(reqUri = 71 << 8 | 183, resUri = 72 << 8 | 183)
    PGetAssociatedCompanyResp getAssociatedCompany(PGetAssociatedCompany req);

    /**
     * 查所有经纪YY号关联的用户
     *
     * @param req
     * @return
     */
    @Yrpc(reqUri = 85 << 8 | 183, resUri = 86 << 8 | 183)
    PGetAllAssociatedUserRsp getAllAssociatedUser(PGetAllAssociatedUser req);

    /**
     * 查所有经纪YY号uid
     *
     * @param req
     * @return
     */
    @Yrpc(reqUri = 87 << 8 | 183, resUri = 88 << 8 | 183)
    PGetAllCompanyUidResp getAllCompanyUid(PGetAllCompanyUid req);
}
