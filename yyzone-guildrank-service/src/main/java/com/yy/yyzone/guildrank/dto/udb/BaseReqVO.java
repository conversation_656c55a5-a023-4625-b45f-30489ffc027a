package com.yy.yyzone.guildrank.dto.udb;

public class BaseReqVO {
    protected AuthVO auth;
    protected String appid;
    protected String request;
    //request的类型。1001:通行证，1002:yyuid 1003:yyid//见UserType
    protected Integer reqtype;

    public BaseReqVO(){

    }
    public BaseReqVO(AuthVO auth, String appid, String request, Integer reqtype) {
        this.auth = auth;
        this.appid = appid;
        this.request = request;
        this.reqtype = reqtype;
    }

    public AuthVO getAuth() {
        return auth;
    }

    public void setAuth(AuthVO auth) {
        this.auth = auth;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getRequest() {
        return request;
    }

    public void setRequest(String request) {
        this.request = request;
    }

    public Integer getReqtype() {
        return reqtype;
    }

    public void setReqtype(Integer reqtype) {
        this.reqtype = reqtype;
    }

    @Override
    public String toString() {
        return "BaseReq{" +
                "auth=" + auth +
                ", appid='" + appid + '\'' +
                ", request='" + request + '\'' +
                ", reqtype=" + reqtype +
                '}';
    }
}
