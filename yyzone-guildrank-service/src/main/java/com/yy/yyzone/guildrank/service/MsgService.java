package com.yy.yyzone.guildrank.service;

import com.alibaba.fastjson.JSONObject;
import com.yy.ent.mobile.metrics.MetricsStopWatch;
import com.yy.yyzone.guildrank.dto.MsgTokenResult;
import com.yy.yyzone.guildrank.dto.RuliuRobotMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Generated;
import javax.annotation.Resource;
import java.time.Duration;
import java.util.Map;

/**
 * @author: create by jp
 * @version: v1.0
 * @description: com.yy.yyzone.activity.service.support
 * @date:2021/1/18
 */
@Service
@Generated("忽略代码规范检查")
@Slf4j
public class MsgService {
    private static final String KEY_MSG_TOKEY = "yyzone.guildrank.msgtoken";

    @Resource(name = "httpRestTemplate")
    private RestTemplate restTemplate;

    @Value("${msg.token.url}")
    private String tokenUrl;

    @Value("${msg.token.bizid}")
    private String bizid;

    @Value("${msg.token.bizkey}")
    private String bizkey;

    @Value("${msg.at.url}")
    private String atUrl;

    @Value("${msg.infoflow-webhook-token:}")
    private String infoflowWebhookToken;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Value("${msg.infoflow-toid:0}")
    private Long infoflowToid;

    @Value("${yy.env:}")
    private String env;

    public static final String ALL = "all";
    public static final String AT_TYPE = "TEXT";
    public static final String MSG_TYPE = "AT";

    public Map sendAlarmMsg(String dateStr) {
        if (StringUtils.isBlank(infoflowWebhookToken)) {
            log.warn("sendAlarmMsg webhook token empty uid:{},reason:{},webhook:{},toid:{}",
                    dateStr, infoflowWebhookToken, infoflowToid);
            return null;
        }

        MetricsStopWatch metricsStopWatch = MetricsStopWatch.startClientWatch().uri("sendAlarmMsg");

        try {
            String token = queryMsgToken();
            String title = "PRODUCTION".equalsIgnoreCase(env) ? "【公会段位告警】" : "【测试环境】";
            String content = String.format("%s【公会段位数据获取异常】%s公会段位数据存在不在1~7段位之间的情况或公会段位数据存在" +
                    "1/2/3/4/5星级公会数量为0的情况，请开发侧查明原因，公会数据将在开发处理完异常后同步。", title, dateStr);
            RuliuRobotMsg.MsgBody body = RuliuRobotMsg.MsgBody.builder().content(content).build();
            RuliuRobotMsg.RuliuRobotMsgBuilder builder = RuliuRobotMsg.builder().contentPrefix(title)
                    .msgBody(body).webhookAcessToken(infoflowWebhookToken).atall(ALL).atType(AT_TYPE).msgType(MSG_TYPE);
            if (infoflowToid != null && infoflowToid > 0) {
                builder.toid(String.valueOf(infoflowToid));
            }
            HttpHeaders requestHeaders = new HttpHeaders();
            requestHeaders.setContentType(MediaType.APPLICATION_JSON);
            requestHeaders.add("bizid", bizid);
            requestHeaders.add("token", token);
            RuliuRobotMsg reqVo = builder.build();
            HttpEntity requestEntity = new HttpEntity(reqVo, requestHeaders);
            log.info("sendAlarmMsg url:{},req:{}", atUrl, JSONObject.toJSONString(reqVo));
            Map map = restTemplate.postForObject(atUrl, requestEntity, Map.class);
            log.info("sendAlarmMsg resp map:{}", map);
            Integer status = (Integer) map.get("status");
            if (status != null && status == 201) {
                metricsStopWatch.successCode().markDurationAndCode();
            } else {
                metricsStopWatch.failCode().markDurationAndCode();
            }
            return map;
        } catch (Exception e) {
            log.error("sendAlarmMsg", e);
            metricsStopWatch.failCode().markDurationAndCode();
        }

        return null;
    }

    /**
     * 功能描述:
     *
     * @param
     * @return java.lang.String
     **/
    public String queryMsgToken() {
        try {
            String cacheValue = redisTemplate.opsForValue().get(KEY_MSG_TOKEY);
            if (StringUtils.isNotEmpty(cacheValue)) {
                return cacheValue;
            }
            long time = System.currentTimeMillis() / 1000;
            String getUrl = tokenUrl + "?bizid=" + bizid + "&sign=" + culTokenSign(time) + "&time=" + time;
            MsgTokenResult result = restTemplate.getForObject(getUrl, MsgTokenResult.class);
            log.info("queryMsgToken getUrl:{},httpResult:{}", getUrl, result);
            //表示成功
            if (result.getStatus() == 200) {
                String token = result.getData().getToken();
                cacheMsgToken(token, time, result.getData().getExpiredTime());
                return token;
            }
        } catch (Exception e) {
            log.error("queryMsgToken error:{}", e.getMessage(), e);
        }
        return null;
    }

    private String culTokenSign(long time) {
        return DigestUtils.md5Hex(bizid + "_" + bizkey + "_" + time);
    }

    private void cacheMsgToken(String token, long time, long expireTime) {
        redisTemplate.opsForValue().set(KEY_MSG_TOKEY, token, Duration.ofSeconds(expireTime - time));
    }

}