package com.yy.yyzone.guildrank.config;

import com.yy.yyzone.guildrank.constant.RankConstant;
import com.yy.yyzone.guildrank.service.RankCacheV3Service;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;

@Configuration
public class RedisMessageListenerConfig {
    @Bean
    public RedisMessageListenerContainer container(RedisConnectionFactory connectionFactory,
                                                   RankCacheV3Service rankCacheV3Service) {

        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        container.addMessageListener(rankCacheV3Service, new PatternTopic(RankConstant.RedisConstants.ASSOICATE_COMPANY_CHANNEL));
        container.addMessageListener(rankCacheV3Service, new PatternTopic(RankConstant.RedisConstants.RELOAD_RANK_CHANNEL));
        return container;
    }

}
