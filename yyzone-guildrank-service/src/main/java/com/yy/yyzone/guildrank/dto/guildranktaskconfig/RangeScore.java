package com.yy.yyzone.guildrank.dto.guildranktaskconfig;

import com.yy.yyzone.guildrank.dto.guildranktaskconfig.valid.NoOverlapRangeIndicators;
import lombok.Data;

import javax.validation.Valid;
import java.util.List;

@Data
public class RangeScore {
    @Valid
    @NoOverlapRangeIndicators
    private List<@Valid RangeIndicator> rangeIndicators;
    @Valid
    private List< @Valid Indicator> indicators;
    /**
     * 指标,方便 bi 同学使用
     */
    private IndicatorEnum indicator;
}