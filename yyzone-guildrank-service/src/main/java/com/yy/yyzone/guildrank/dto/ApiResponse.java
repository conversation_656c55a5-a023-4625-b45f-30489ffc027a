package com.yy.yyzone.guildrank.dto;

public class ApiResponse<T> {
    public boolean success;
    private String message;
    private T  data;

    private ApiResponse(){

    }

    public static <T> ApiResponse<T> success(T data){
        ApiResponse<T> result = new ApiResponse<T>();
        result.setSuccess(true);
        result.setMessage("success");
        result.setData(data);
        return result;
    }
    public static <T> ApiResponse<T> fail(String message,T data){
        ApiResponse<T> result = new ApiResponse<T>();
        result.setSuccess(false);
        result.setMessage(message);
        result.setData(data);
        return result;
    }
    public static <Void> ApiResponse<Void> fail(String message){
        ApiResponse<Void> result = new ApiResponse<>();
        result.setSuccess(false);
        result.setMessage(message);
        return result;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "ApiResponse{" +
                "success=" + success +
                ", message='" + message + '\'' +
                ", data=" + data +
                '}';
    }
}
