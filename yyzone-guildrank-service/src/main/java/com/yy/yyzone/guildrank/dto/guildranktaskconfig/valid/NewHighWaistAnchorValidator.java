package com.yy.yyzone.guildrank.dto.guildranktaskconfig.valid;

import com.yy.yyzone.guildrank.dto.guildranktaskconfig.GuildRankTask;
import com.yy.yyzone.guildrank.dto.guildranktaskconfig.Indicator;
import com.yy.yyzone.guildrank.dto.guildranktaskconfig.IndicatorEnum;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.List;

public class NewHighWaistAnchorValidator
        implements ConstraintValidator<NewHighWaistAnchorValid, GuildRankTask> {

    @Override
    public boolean isValid(GuildRankTask task, ConstraintValidatorContext ctx) {
        if (task == null) {
            return false;
        }
        List<Indicator> list = task.getScore().getIndicators();
        if (list == null || list.size() != 2) {
            return fail(ctx, "indicators 必须 2 个");
        }
        if (list.get(0).getIndicator() != IndicatorEnum.lastThreeMonthsNewHighAnchorCount || list.get(1).getIndicator() != IndicatorEnum.lastThreeMonthsNewWaistAnchorCount) {
            return fail(ctx, "indicator 顺序必须是 newHighAnchorCount, newWaistAnchorCount");
        }
        for (Indicator i : list) {
            if (i.getScore() != null) {
                return fail(ctx, "不允许直接配置 score");
            }
            if (i.getUnitsPerCount() == null || i.getScorePerUnit() == null) {
                return fail(ctx, "必须配置 unitsPerCount 和 scorePerUnit");
            }
        }
        if (task.getCondition().getRanges().stream().anyMatch(r -> r.getScore() != null)) {
            return fail(ctx, "不允许配置 rangeIndicators");
        }
        return true;
    }

    private boolean fail(ConstraintValidatorContext ctx, String msg) {
        ctx.disableDefaultConstraintViolation();
        ctx.buildConstraintViolationWithTemplate("新增高优&腰部主播任务 " + msg).addConstraintViolation();
        return false;
    }
}