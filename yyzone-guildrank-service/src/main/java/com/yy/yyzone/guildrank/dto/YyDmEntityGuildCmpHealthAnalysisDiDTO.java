package com.yy.yyzone.guildrank.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yy.yyzone.guildrank.constant.RankConstant;
import com.yy.yyzone.guildrank.db.gen.model.YyDmEntityGuildCmpHealthAnalysisDi;
import com.yy.yyzone.guildrank.dto.guildrankconfig.TaskIndexConfig;
import com.yy.yyzone.guildrank.dto.guildrankconfig.TaskIndexConfigItem;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

import static com.yy.yyzone.guildrank.util.NumUtils.*;
import static com.yy.yyzone.guildrank.constant.RankConstant.TaskIndex.*;

@Data
@NoArgsConstructor
public class YyDmEntityGuildCmpHealthAnalysisDiDTO {
    /**
     * guild_cmp_ownr_id 主体uid
     */
    private Long guildCmpOwnrId;

    /**
     * new_aid_rat_3_4_aid_num 3&4星主播签约数量
     */
    private Long newAidRat34AidNum;

    /**
     * new_aid_rat_3_4_aid_num_diff 3&4星主播签约数量diff
     */
    private Long newAidRat34AidNumDiff;

    /**
     * upgrd_high_aid_num 头部主播数
     */
    private Long upgrdHighAidNum;

    /**
     * upgrd_high_aid_num_diff 头部主播数diff
     */
    private Long upgrdHighAidNumDiff;

    /**
     * upgrd_waist_aid_num 腰部主播数
     */
    private Long upgrdWaistAidNum;

    /**
     * upgrd_waist_aid_num_diff 腰部主播数diff
     */
    private Long upgrdWaistAidNumDiff;

    /**
     * avg_high_valid_live_aid_num 月日均头部有效开播主播数
     */
    private Double avgHighValidLiveAidNum;

    /**
     * avg_high_valid_live_aid_num_diff 月日均头部有效开播主播数diff
     */
    private Double avgHighValidLiveAidNumDiff;

    /**
     * avg_waist_valid_live_aid_num 月日均腰部有效开播主播数
     */
    private Double avgWaistValidLiveAidNum;

    /**
     * avg_waist_valid_live_aid_num_diff 月日均腰部有效开播主播数diff
     */
    private Double avgWaistValidLiveAidNumDiff;

    /**
     * avg_nto_valid_live_aid_rate 新转存开播率
     */
    private Double avgNtoValidLiveAidRate;

    /**
     * avg_stock_valid_live_aid_rate 存量主播开播率
     */
    private Double avgStockValidLiveAidRate;

    /**
     * new_auth_golden_aid_num 新授权金牌艺人
     */
    private Long newAuthGoldenAidNum;

    /**
     * ext_golden_aid_num 续约重点金牌艺人数（续约1-6级金牌艺人）
     */
    private Long extGoldenAidNum;

    /**
     * actv_prod_pay_amt 月累积活动礼物流水
     */
    @JsonIgnore
    private Double actvProdPayAmt;

    /**
     * prod_pay_amt 月累积流水
     */
    @JsonIgnore
    private Double prodPayAmt;

    /**
     * actv_prod_pay_amt_rate 活动礼物流水占比总流水比例
     */
    private Double actvProdPayAmtRate;

    // /**
    //  * valid_live_new_aid_prod_pay_amt 新主播流水
    //  */
    // private Double validLiveNewAidProdPayAmt;

    // /**
    //  * valid_live_new_aid_prod_pay_amt_diff 新主播流水diff
    //  */
    // private Double validLiveNewAidProdPayAmtDiff;

    // /**
    //  * unvalid_live_new_aid_prod_pay_amt 存量主播流水
    //  */
    // private Double unvalidLiveNewAidProdPayAmt;

    // /**
    //  * unvalid_live_new_aid_prod_pay_amt_diff 存量主播流水diff
    //  */
    // private Double unvalidLiveNewAidProdPayAmtDiff;

    // /**
    //  * unvalid_live_new_aid_prod_pay_amt_rate 存量主播流水占比
    //  */
    // private Double unvalidLiveNewAidProdPayAmtRate;

    /**
     * valid_live_new_aid_incm_amt 新主播蓝钻
     */
    private Double validLiveNewAidIncmAmt;

    /**
     * valid_live_new_aid_incm_amt_diff 新主播蓝钻diff
     */
    private Double validLiveNewAidIncmAmtDiff;

    /**
     * unvalid_live_new_aid_incm_amt 存量主播蓝钻
     */
    private Double unvalidLiveNewAidIncmAmt;

    /**
     * unvalid_live_new_aid_incm_amt_diff 存量主播蓝钻diff
     */
    private Double unvalidLiveNewAidIncmAmtDiff;

    /**
     * unvalid_live_new_aid_incm_amt_rate 存量主播蓝钻占比
     */
    private Double unvalidLiveNewAidIncmAmtRate;

    /**
     * guild_health_point 公会健康分
     */
    private Double guildHealthPoint;

    /**
     * new_aid_rat_3_4_aid_num_rn_score 3&4星主播签约数量排名分值
     */
    private Integer newAidRat34AidNumRnScore;

    /**
     * new_aid_rat_3_4_aid_num_diff_score 3&4星主播签约数量diff分值
     */
    private Integer newAidRat34AidNumDiffScore;

    /**
     * upgrd_high_aid_num_diff_score 头部主播数diff分值
     */
    private Integer upgrdHighAidNumDiffScore;

    /**
     * upgrd_waist_aid_num_diff_score 腰部主播数diff分值
     */
    private Integer upgrdWaistAidNumDiffScore;

    /**
     * avg_high_valid_live_aid_num_rn_score 月日均头部有效开播主播数排名分值
     */
    private Integer avgHighValidLiveAidNumRnScore;

    /**
     * avg_waist_valid_live_aid_num_rn_score 月日均腰部有效开播主播数排名分值
     */
    private Integer avgWaistValidLiveAidNumRnScore;

    /**
     * avg_high_valid_live_aid_num_diff_score 月日均头部有效开播主播数diff分值
     */
    private Integer avgHighValidLiveAidNumDiffScore;

    /**
     * avg_waist_valid_live_aid_num_diff_score 月日均腰部有效开播主播数diff分值
     */
    private Integer avgWaistValidLiveAidNumDiffScore;

    /**
     * avg_nto_valid_live_aid_rate_rn_score 新转存开播率排名分值
     */
    private Integer avgNtoValidLiveAidRateRnScore;

    /**
     * avg_stock_valid_live_aid_rate_rn_score 存量主播开播率排名分值
     */
    private Integer avgStockValidLiveAidRateRnScore;

    /**
     * new_auth_golden_aid_num_rn_score 新授权金牌艺人排名分值
     */
    private Integer newAuthGoldenAidNumRnScore;

    // /**
    //  * valid_live_new_aid_prod_pay_amt_rn_score 新主播流水排名分值
    //  */
    // private Integer validLiveNewAidProdPayAmtRnScore;

    // /**
    //  * unvalid_live_new_aid_prod_pay_amt_rn_score 存量主播流水排名分值
    //  */
    // private Integer unvalidLiveNewAidProdPayAmtRnScore;

    // /**
    //  * valid_live_new_aid_prod_pay_amt_diff_score 新主播流水diff分值
    //  */
    // private Integer validLiveNewAidProdPayAmtDiffScore;

    // /**
    //  * unvalid_live_new_aid_prod_pay_amt_diff_score 存量主播流水diff分值
    //  */
    // private Integer unvalidLiveNewAidProdPayAmtDiffScore;

    // /**
    //  * unvalid_live_new_aid_prod_pay_amt_rate_diff_score 存量主播流水占比对比大盘存量流水占比差值分值
    //  */
    // private Integer unvalidLiveNewAidProdPayAmtRateDiffScore;

    /**
     * valid_live_new_aid_incm_amt_rn_score 新主播蓝钻排名分值
     */
    private Integer validLiveNewAidIncmAmtRnScore;

    /**
     * unvalid_live_new_aid_incm_amt_rn_score 存量主播蓝钻排名分值
     */
    private Integer unvalidLiveNewAidIncmAmtRnScore;

    /**
     * valid_live_new_aid_incm_amt_diff_score 新主播蓝钻diff分值
     */
    private Integer validLiveNewAidIncmAmtDiffScore;

    /**
     * unvalid_live_new_aid_incm_amt_diff_score 存量主播蓝钻diff分值
     */
    private Integer unvalidLiveNewAidIncmAmtDiffScore;

    /**
     * unvalid_live_new_aid_incm_amt_rate_diff_score 存量主播蓝钻占比对比大盘存量蓝钻占比差值分值
     */
    private Integer unvalidLiveNewAidIncmAmtRateDiffScore;

    /**
     * guild_health_point_score 公会健康分分值
     */
    private Integer guildHealthPointScore;

    /**
     * data_total_score 数据指标总得分
     */
    private Integer dataTotalScore;

    /**
     * data_cmp_star_lvl 数据指标主体星级
     */
    private Integer dataCmpStarLvl;

    /**
     * total_score 公会任务得分
     */
    private Integer totalScore;

    /**
      * cmp_star_lvl 主体星级
     */
    private Integer cmpStarLvl;

    /**
     * new_aid_rat_3_4_aid_num_score 新签3&4星主播数得分
     */
    private Integer newAidRat34AidNumScore;

    /**
     * upgrd_high_aid_num_score 新增头部主播数得分
     */
    private Integer upgrdHighAidNumScore;

    /**
     * upgrd_waist_aid_num_score 新增腰部主播数得分
     */
    private Integer upgrdWaistAidNumScore;

    /**
     * ext_golden_aid_num_score 续约重点金牌艺人（续约1-6级金牌艺人）得分
     */
    private Integer extGoldenAidNumScore;

    /**
     * actv_prod_pay_amt_rate_score 礼物流水占比大于70%得分
     */
    private Integer actvProdPayAmtRateScore;

    /**
     * task_total_score 任务指标总得分
     */
    private Integer taskTotalScore;

    /**
     * new_aid_rat_3_4_aid_num_itg 新签3&4星主播数积分
     */
    private Integer newAidRat34AidNumItg;

    /**
     * upgrd_high_aid_num_itg 新增头部主播数积分
     */
    private Integer upgrdHighAidNumItg;

    /**
     * upgrd_waist_aid_num_itg 新增腰部主播数积分
     */
    private Integer upgrdWaistAidNumItg;

    /**
     * ext_golden_aid_num_itg 续约重点金牌艺人（续约1-6级金牌艺人）积分
     */
    private Integer extGoldenAidNumItg;

    /**
     * actv_prod_pay_amt_rate_itg 礼物流水占比大于70%积分
     */
    private Integer actvProdPayAmtRateItg;

    /**
     * task_total_itg 任务指标总积分
     */
    private Integer taskTotalItg;

    /**
     * 日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date dt;

    /**
     * 任务指标，还差X人可再得Y分 key为指标 新签3&4星主播数:NEW_SIGN_STAR32_ANCHOR_COUNT 新增头部主播数:NEW_HEAD_ANCHOR_COUNT
     * 新增腰部主播数:NEW_WAIST_ANCHOR_COUNT 续约1-6级金牌艺人数:RENEW_16_GOLD_ARIST_COUNT 礼物流水占比总流水≥70%:GIFT_FLOW_RATIO_GE70
     */
    private Map<String, String> moreScoreTips;

    public YyDmEntityGuildCmpHealthAnalysisDiDTO(YyDmEntityGuildCmpHealthAnalysisDi di,boolean switchToCmpStarLvl) {
        if (di == null) {
            return;
        }
        moreScoreTips = new HashMap<>();
        BeanUtils.copyProperties(di, this);
        if(switchToCmpStarLvl){
            this.dataCmpStarLvl = this.cmpStarLvl;
        }else{
            this.cmpStarLvl = this.dataCmpStarLvl;
        }
    }

    public void calcMoreScoreTips(TaskIndexConfig config) {
        calcMoreScoreTip(NEW_SIGN_STAR32_ANCHOR_COUNT, config, getNewAidRat34AidNum(), getNewAidRat34AidNumScore());
        calcMoreScoreTip(NEW_HEAD_ANCHOR_COUNT, config, getUpgrdHighAidNum(), getUpgrdHighAidNumScore());
        calcMoreScoreTip(NEW_WAIST_ANCHOR_COUNT, config, getUpgrdWaistAidNum(), getUpgrdWaistAidNumScore());
        calcMoreScoreTip(RENEW_16_GOLD_ARIST_COUNT, config, getExtGoldenAidNum(), getExtGoldenAidNumScore());

        TaskIndexConfigItem ci = config.getIndexMap().get(GIFT_FLOW_RATIO_GE70.getCode());
        if (ci != null && ci.getHealthScoreUnit() != null && ci.getHealthScoreUnit() > 0) {
            double diff = nullAs0(getProdPayAmt()) * RankConstant.GIFT_FLOW_RATIO_GE - nullAs0(getActvProdPayAmt());
            if (diff > 0) {
                NumberFormat fmt = NumberFormat.getCurrencyInstance(Locale.CHINA);
                fmt.setRoundingMode(RoundingMode.UP);
                moreScoreTips.put(GIFT_FLOW_RATIO_GE70.getCode(), String.format("还差%s流水可得%s分",
                        fmt.format(diff), ci.getHealthScoreUnit()));
            }
        }
    }

    private void calcMoreScoreTip(RankConstant.TaskIndex index, TaskIndexConfig config, Long biNum, Integer biScore) {
        TaskIndexConfigItem ci = config.getIndexMap().get(index.getCode());
        if (ci == null || ci.getHealthScoreCount() == null || ci.getHealthScoreLimit() == null || ci.getHealthScoreCount() <= 0) {
            return;
        }
        long l = nullAs0(biNum) % ci.getHealthScoreCount();
        long moreCount = ci.getHealthScoreCount() - l;
        long couldMore = ci.getHealthScoreLimit() - nullAs0(biScore);
        long moreScore = couldMore > ci.getHealthScoreUnit() ? ci.getHealthScoreUnit() : couldMore;
        if (moreScore > 0) {
            moreScoreTips.put(index.getCode(), String.format("还差%s人可再得%s分", moreCount, moreScore));
        }
    }
}