package com.yy.yyzone.guildrank.dto.renewartistdata;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class DataListDto {
    /**
     * id
     */
    private Long id;

    /**
     * 数据月
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyyMM")
    private Date dataMonth;

    /**
     * 备注
     */
    private String remark;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 更新人
     */
    private String updatePassport;
}