package com.yy.yyzone.guildrank.yrpc.dto.scoremall;

import com.yy.ent.commons.protopack.util.Uint;
import lombok.Data;

import java.util.Map;

/**
 * 给用户添加货币数量
 */
@Data
public class AddScoreResp {
    /**
     * 结果，0-成功，其他-失败
     */
    private Uint result;

    /**
     * 错误提示
     */
    private String errMsg;

    /**
     * ownNum 用户当前拥有的货币数量
     * orderId 商城订单号
     */
    private Map<String, String> extendInfo;
}