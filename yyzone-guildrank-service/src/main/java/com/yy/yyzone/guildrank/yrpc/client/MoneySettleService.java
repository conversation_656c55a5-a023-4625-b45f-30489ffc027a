package com.yy.yyzone.guildrank.yrpc.client;

import com.yy.yyzone.guildrank.yrpc.dto.QueryGuildIncomeReq;
import com.yy.yyzone.guildrank.yrpc.dto.QueryGuildIncomeResp;
import org.apache.dubbo.common.annotation.Yrpc;

import javax.annotation.Generated;

@Generated("忽略代码规范检查")
public interface MoneySettleService {

    /**
     * javabean
     **/
    @Yrpc(functionName = "queryGuildIncome", reqUri = 10147 << 8 | 15, resUri = 10148 << 8 | 15)
    QueryGuildIncomeResp queryGuildIncome(QueryGuildIncomeReq req);

}
