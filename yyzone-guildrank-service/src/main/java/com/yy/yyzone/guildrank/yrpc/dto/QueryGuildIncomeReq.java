package com.yy.yyzone.guildrank.yrpc.dto;

import com.yy.ent.commons.protopack.util.Uint;
import lombok.Data;

import javax.annotation.Generated;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2019/12/11
 */
@Data
@Generated("忽略代码规范检查")
public class QueryGuildIncomeReq {
    /**
     * 账号信息
     */
    private AccountType2 accountType2;
    /**
     * 操作类型 1-按天查询 2-按月查询
     */
    private Uint optname;
    /**
     * 货币类型 待结算佣金-11 蓝钻-12
     */
    private Set<Uint> moneytype;
    /**
     * 查询开始时间 按天格式：2019-12-09 按月格式：201912
     */
    private String begintime;
    /**
     * 查询结束时间 按天格式：2019-12-09 按月格式：201912
     */
    private String endtime;
    /**
     * 主播uids列表，如果不传则不过滤uid
     */
    private Set<Long> uids;
    /**
     * 过滤条件
     */
    private Map<String, String> filter;
    /**
     * 排序条件
     */
    private Map<String, String> sorter;
    /**
     *
     */
    private Uint offset;
    /**
     *
     */
    private Uint limit;
    /**
     * 扩展字段
     */
    private Map<String, String> extraInfo;

}
