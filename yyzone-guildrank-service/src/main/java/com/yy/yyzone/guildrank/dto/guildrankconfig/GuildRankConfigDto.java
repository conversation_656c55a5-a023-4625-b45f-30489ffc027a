package com.yy.yyzone.guildrank.dto.guildrankconfig;

import com.yy.yyzone.guildrank.util.Resp;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 公会段位门槛设置（健康度）
 */
@Data
public class GuildRankConfigDto {
    /**
     * 【主体星级】分值设置
     */
    @Valid
    @NotNull(message = "【主体星级】分值设置不能为空")
    private RankScoreConfig rankScoreConfig;

    /**
     * 【数据指标】分值设置
     */
    @Valid
    @NotNull(message = "【数据指标】分值设置不能为空")
    private DataIndexConfig dataIndexConfig;

    public Resp validate() {
        Resp r = rankScoreConfig.validate();
        if (!r.checkSuccess()) {
            return r;
        }
        return dataIndexConfig.validate();
    }
}