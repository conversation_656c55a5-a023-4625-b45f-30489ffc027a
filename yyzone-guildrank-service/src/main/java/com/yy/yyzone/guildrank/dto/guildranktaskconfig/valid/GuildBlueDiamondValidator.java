package com.yy.yyzone.guildrank.dto.guildranktaskconfig.valid;

import com.yy.yyzone.guildrank.dto.guildranktaskconfig.GuildRankTask;
import com.yy.yyzone.guildrank.dto.guildranktaskconfig.Indicator;
import com.yy.yyzone.guildrank.dto.guildranktaskconfig.IndicatorEnum;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.ArrayList;
import java.util.List;

public class GuildBlueDiamondValidator implements ConstraintValidator<GuildBlueDiamondValid, GuildRankTask> {

    private static List<IndicatorEnum> EXPECTED_INDICATORS;

    static {
        EXPECTED_INDICATORS = new ArrayList<IndicatorEnum>();
        EXPECTED_INDICATORS.add(IndicatorEnum.guildBlueDiamondMissionAccomplished);
        EXPECTED_INDICATORS.add(IndicatorEnum.guildBlueDiamondMissionStepInc);
        EXPECTED_INDICATORS.add(IndicatorEnum.guildBlueDiamondMaxMissionCompleted);
    }

    @Override
    public boolean isValid(GuildRankTask task, ConstraintValidatorContext ctx) {
        if (task == null) {
            return false;
        }

        List<Indicator> list = task.getScore().getIndicators();
        if (list == null || list.size() != 3) {
            return fail(ctx, "公会蓝钻任务 score.indicators size必须大于等于 3 个");
        }
        for (int i = 0; i < list.size(); i++) {
            if (list.get(i).getIndicator() != EXPECTED_INDICATORS.get(i)) {
                return fail(ctx, "indicator 顺序错误");
            }
        }
        // 只有第 2 个允许有 unitsPerCount / scorePerUnit
        for (int i = 0; i < list.size(); i++) {
            Indicator in = list.get(i);
            if (i == 1) { // missionStepInc
                if (in.getUnitsPerCount() == null || in.getScorePerUnit() == null) {
                    return fail(ctx, "missionStepInc 必须配置 unitsPerCount 和 scorePerUnit");
                }
            } else {
                if (in.getScore() == null) {
                    return fail(ctx, in.getIndicator() + " 必须配置 score");
                }
                if (in.getUnitsPerCount() != null || in.getScorePerUnit() != null) {
                    return fail(ctx, in.getIndicator() + " 不允许配置 unitsPerCount 和 scorePerUnit");
                }
            }
        }
        if (task.getCondition().getRanges().stream().anyMatch(r -> r.getScore() != null)) {
            return fail(ctx, "不允许配置 ranges.score");
        }
        return true;
    }

    private boolean fail(ConstraintValidatorContext ctx, String msg) {
        ctx.disableDefaultConstraintViolation();
        ctx.buildConstraintViolationWithTemplate("公会蓝钻任务 " + msg).addConstraintViolation();
        return false;
    }
}