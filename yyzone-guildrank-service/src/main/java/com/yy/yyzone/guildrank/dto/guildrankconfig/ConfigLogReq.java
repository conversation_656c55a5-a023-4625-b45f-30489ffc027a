package com.yy.yyzone.guildrank.dto.guildrankconfig;

import com.yy.yyzone.guildrank.constant.RankConstant;
import com.yy.yyzone.guildrank.dto.YyDmEntityGuildCmpHealthAnalysisDiDTO;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.lang.reflect.Method;
import java.util.Date;

@Data
public class ConfigLogReq {
    private Long id;

    /**
     * 操作时间开始 yyyy-MM-dd HH:mm:ss
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date optTimeStart;

    /**
     * 操作时间结束 yyyy-MM-dd HH:mm:ss
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date optTimeEnd;

    /**
     * 操作人
     */
    private String optPassport;

    private Integer pageNum;
    private Integer pageSize;


    public static void main(String[] args) throws Exception {
        String underscoreString = "new_aid_rat_3_4_aid_num";
        System.out.println(toCamelCase(underscoreString));
    }

    public static String toCamelCase(String underscoreString) {
        StringBuilder result = new StringBuilder();
        boolean capitalizeNext = false;

        for (int i = 0; i < underscoreString.length(); i++) {
            char currentChar = underscoreString.charAt(i);

            if (currentChar == '_') {
                capitalizeNext = true;
            } else {
                if (capitalizeNext) {
                    result.append(Character.toUpperCase(currentChar));
                    capitalizeNext = false;
                } else {
                    if (i == 0) {
                        result.append(Character.toLowerCase(currentChar));
                    } else {
                        result.append(currentChar);
                    }
                }
            }
        }

        return result.toString();
    }

}