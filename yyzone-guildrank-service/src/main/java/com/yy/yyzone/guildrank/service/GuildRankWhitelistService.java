/**
 * Autogenerated by Thrift
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 */
package com.yy.yyzone.guildrank.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yy.java.webdb.WebdbUserInfo;
import com.yy.yyzone.guildrank.constant.RankConstant;
import com.yy.yyzone.guildrank.db.custom.mapper.GuildrankWhilelistExtMapper;
import com.yy.yyzone.guildrank.db.gen.mapper.GuildrankConfigLogMapper;
import com.yy.yyzone.guildrank.db.gen.mapper.GuildrankWhilelistMapper;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankConfigLog;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankWhilelist;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankWhilelistExample;
import com.yy.yyzone.guildrank.dto.GuildRankWhilelistItem;
import com.yy.yyzone.guildrank.dto.GuildRankWhilelistPageInfo;
import com.yy.yyzone.guildrank.dto.GuildRankWhilelistSaveInfo;
import com.yy.yyzone.guildrank.dto.RankChangeInfo;
import com.yy.yyzone.guildrank.util.MyDateUtil;
import com.yy.yyzone.guildrank.util.Resp;
import com.yy.yyzone.guildrank.yrpc.client.YyzoneResourceClient;
import com.yy.yyzone.guildrank.yrpc.dto.resource.NotifyRankChangeReq;
import com.yy.yyzone.guildrank.yrpc.dto.resource.NotifyRankChangeResp;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/11/5
 *
 */
@Service
public class GuildRankWhitelistService {
    private static final Logger LOGGER = LoggerFactory.getLogger(GuildRankWhitelistService.class);
    private static final int MAX_COUNT = 10;
    @Value("${rank.whitelist.notify-resource.delay:120}")
    private int notifyResourceDelay;

    @Autowired
    private GuildrankWhilelistMapper guildrankWhilelistMapper;

    @Autowired
    private GuildrankWhilelistExtMapper guildrankWhilelistExtMapper;

    @Autowired
    private GuildrankConfigLogMapper configLogMapper;

    @Autowired
    private UserInfoService webdbUserInfoService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private RankCacheV3Service rankCacheV3Service;

    @Autowired
    private GuildService guildService;

    @Reference(protocol = "yyp", owner = "yyzone_resources_yyp", timeout = 10000)
    private YyzoneResourceClient yyzoneResourceClient;

    /**
     * 保存公会段位白名单
     * @param info
     * @param operator
     * @return
     */
    @Transactional(rollbackFor = {RuntimeException.class, Exception.class})
    public Resp<Long> saveWhitelist(GuildRankWhilelistSaveInfo info, String operator) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        try {
            sdf.parse(info.getMonthstr());
        } catch (Exception ex) {
            return Resp.createByError("月份格式错误");
        }

        int maxRank = 7;
        if (info.getRank() < 1 || info.getRank() > maxRank) {
            return Resp.createByError("段位为1-7星");
        }

        Resp<Boolean> resp = canEdit(sdf.format(new Date()));
        if (!resp.checkSuccess()) {
            return Resp.createByError(resp.getMsg());
        }

        resp = canEdit(info.getMonthstr());
        if (!resp.checkSuccess()) {
            return Resp.createByError("只能选择当前月或后续月份");
        }

        Date now = new Date();
        int count;
        GuildrankConfigLog configLog = new GuildrankConfigLog();
        configLog.setType(RankConstant.LogTypeConstants.GUILDRANK_WHILELIST);
        configLog.setConfigId(0L);
        if (info.getId() > 0) {
            // 编辑
            GuildrankWhilelist dbItem = guildrankWhilelistMapper.selectByPrimaryKey(info.getId());
            if (dbItem == null) {
                return Resp.createByError("白名单不存在");
            }
            String dbMonth = dbItem.getMonthstr();

            configLog.setOldData(JSON.toJSONString(dbItem));

            resp = canEdit(dbItem.getMonthstr());
            if (!resp.checkSuccess()) {
                return Resp.createByError(resp.getMsg());
            }

            GuildrankWhilelistExample example = new GuildrankWhilelistExample();
            example.createCriteria().andIdNotEqualTo(dbItem.getId())
                    .andGuildUidEqualTo(dbItem.getGuildUid())
                    .andMonthstrEqualTo(info.getMonthstr());
            int existCount = guildrankWhilelistMapper.countByExample(example);
            if (existCount > 0) {
                return Resp.createByError(MessageFormat.format("{0}以下UID白名单已存在：{1}", info.getMonthstr(),
                        String.valueOf(dbItem.getGuildUid())));
            }

            dbItem.setMonthstr(info.getMonthstr());
            dbItem.setRank(info.getRank());
            dbItem.setRemark(info.getRemark());
            dbItem.setOprator(operator);
            dbItem.setCreateTime(now);
            count = guildrankWhilelistMapper.updateByPrimaryKey(dbItem);
            rankChange(info.getMonthstr(), Arrays.asList(dbItem.getGuildUid()), info.getRank());
            if (!dbMonth.equals(info.getMonthstr())) {
                rankChange(dbMonth, Arrays.asList(dbItem.getGuildUid()), info.getRank());
            }
            configLog.setConfigId(info.getId());
            configLog.setNewData(JSON.toJSONString(dbItem));
        } else {
            if (StringUtils.isBlank(info.getGuildUids())) {
                return Resp.createByError("请指定公会UID");
            }

            List<String> uids = Arrays.asList(StringUtils.split(info.getGuildUids(), ","));
            if (uids.size() > MAX_COUNT) {
                return Resp.createByError(MessageFormat.format("公会UID不能超过{0}个", MAX_COUNT));
            }

            List<String> dupUids = uids.stream()
                    .collect(Collectors.toMap(e -> e, e -> 1, Integer::sum))
                    .entrySet().stream()
                    .filter(entry -> entry.getValue() > 1)
                    .map(entry -> entry.getKey())
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(dupUids)) {
                return Resp.createByError(MessageFormat.format("以下UID重复：{0}", StringUtils.join(dupUids, ",")));
            }

            List<Long> guildUids = Lists.newArrayList();
            for (String uid : uids) {
                try {
                    guildUids.add(Long.parseLong(uid));
                } catch (Exception ex) {
                    LOGGER.error("saveWhitelist Exception uid:{}", uid, ex);
                    return Resp.createByError(MessageFormat.format("UID格式错误：{0}", uid));
                }
            }

            Map<String, WebdbUserInfo> userMap = webdbUserInfoService.getUserInfo(guildUids);
            Collection extras = ListUtils.removeAll(uids, userMap.keySet());
            if (CollectionUtils.isNotEmpty(extras)) {
                return Resp.createByError(MessageFormat.format("以下UID不存在：{0}", StringUtils.join(extras, ",")));
            }

            List<GuildrankWhilelist> list = new ArrayList<>(uids.size());
            List<Long> existUids = new ArrayList<>();
            for (Long uid : guildUids) {
                GuildrankWhilelistExample example = new GuildrankWhilelistExample();
                example.createCriteria().andGuildUidEqualTo(uid)
                        .andMonthstrEqualTo(info.getMonthstr());
                int existCount = guildrankWhilelistMapper.countByExample(example);
                if (existCount > 0) {
                    existUids.add(uid);
                    continue;
                }

                GuildrankWhilelist item = new GuildrankWhilelist();
                item.setGuildUid(uid);
                item.setRank(info.getRank());
                item.setRemark(info.getRemark());
                item.setMonthstr(info.getMonthstr());
                item.setOprator(operator);
                item.setCreateTime(now);
                list.add(item);
            }

            if (CollectionUtils.isNotEmpty(existUids)) {
                return Resp.createByError(MessageFormat.format("{0}以下UID白名单已存在：{1}", info.getMonthstr(),
                        StringUtils.join(existUids, ",")));
            }

            count = guildrankWhilelistExtMapper.insertBatch(list);
            rankChange(info.getMonthstr(), guildUids, info.getRank());
            configLog.setNewData(JSON.toJSONString(list));
        }
        configLog.setOptPassport(operator);
        configLog.setOptTime(now);
        configLogMapper.insertSelective(configLog);

        return Resp.createBySuccess((long) count);
    }

    /**
     * 删除公会段位白名单
     * @param id
     * @return
     */
    @Transactional(rollbackFor = {RuntimeException.class, Exception.class})
    public Resp<Boolean> delWhitelist(Long id, String passport) {
        GuildrankWhilelist item = guildrankWhilelistMapper.selectByPrimaryKey(id);
        if (item == null) {
            return Resp.createByError("白名单不存在");
        }
        Resp<Boolean> resp = canEdit(item.getMonthstr());
        if (!resp.checkSuccess()) {
            return resp;
        }

        GuildrankConfigLog configLog = new GuildrankConfigLog();
        configLog.setType(RankConstant.LogTypeConstants.GUILDRANK_WHILELIST);
        configLog.setConfigId(id);
        configLog.setOldData(JSON.toJSONString(item));
        configLog.setNewData("deleted");
        configLog.setOptPassport(passport);
        configLog.setOptTime(new Date());
        configLogMapper.insertSelective(configLog);

        int count = guildrankWhilelistMapper.deleteByPrimaryKey(id);
        if (count > 0) {
            rankChange(item.getMonthstr(), Arrays.asList(item.getGuildUid()), -1);
            return Resp.createBySuccess(true);
        }
        return Resp.createByError("白名单不存在");
    }

    /**
     * 公会段位白名单列表
     * @param uids
     * @param yynos
     * @param month
     * @param pageIndex
     * @param pageSize
     * @return
     */
    public Resp<GuildRankWhilelistPageInfo> queryWhitelist(String uids, String yynos, String month, int pageIndex, int pageSize) {
        GuildrankWhilelistExample example = new GuildrankWhilelistExample();
        GuildrankWhilelistExample.Criteria criteria = example.createCriteria();

        Set<Long> uidList = Sets.newHashSet();
        if (StringUtils.isNotBlank(uids)) {
            String[] uidArray = StringUtils.split(uids, ",");
            if (uidArray.length > MAX_COUNT) {
                return Resp.createByError(MessageFormat.format("公会UID不能超过{0}个", MAX_COUNT));
            }
            for (String s : uidArray) {
                try {
                    uidList.add(Long.parseLong(s));
                } catch (Exception ex) {
                    LOGGER.error("queryWhitelist Exception uids:{}", uids, ex);
                }
            }
        }

        if (StringUtils.isNotBlank(yynos)) {
            String[] yynoArray = StringUtils.split(yynos, ",");
            if (yynoArray.length > MAX_COUNT) {
                return Resp.createByError(MessageFormat.format("公会YY号不能超过{0}个", MAX_COUNT));
            }
            Map<String, String> ssMap = webdbUserInfoService.batchGetUidByYy(Arrays.asList(yynoArray));
            for (String value : ssMap.values()) {
                try {
                    uidList.add(Long.parseLong(value));
                } catch (Exception ex) {
                    LOGGER.error("queryWhitelist Exception uid:{}", value, ex);
                }
            }
        }

        if (StringUtils.isNotEmpty(month)) {
            criteria.andMonthstrEqualTo(month);
        }

        if (CollectionUtils.isNotEmpty(uidList)) {
            criteria.andGuildUidIn(new ArrayList<>(uidList));
        }

        GuildRankWhilelistPageInfo page = new GuildRankWhilelistPageInfo();
        int count = guildrankWhilelistMapper.countByExample(example);
        page.setTotal(count);
        if (count > 0) {
            int offset = (pageIndex - 1) * pageSize;
            example.setOffset(offset);
            example.setLimit(pageSize);
            example.setOrderByClause("id desc");
            List<GuildrankWhilelist> list = guildrankWhilelistMapper.selectByExample(example);
            List<Long> guildUids = Lists.transform(list, l -> l.getGuildUid());
            Map<String, WebdbUserInfo> userInfoMap = webdbUserInfoService.getUserInfo(guildUids);

            List<GuildRankWhilelistItem> resultList = Lists.transform(list, l -> {
                GuildRankWhilelistItem item = new GuildRankWhilelistItem();
                item.setId(l.getId());
                item.setGuildUid(l.getGuildUid());

                WebdbUserInfo userInfo = userInfoMap.get(String.valueOf(l.getGuildUid()));
                if (userInfo != null) {
                    item.setGuildYyno(userInfo.getYyno());
                }

                item.setRank(l.getRank());
                item.setMonthstr(l.getMonthstr());
                item.setOprator(l.getOprator());
                item.setRemark(l.getRemark());
                item.setOptTime(l.getCreateTime());

                Resp<Boolean> resp = canEdit(l.getMonthstr());
                item.setCanDelete(resp.checkSuccess());
                item.setCanEdit(resp.checkSuccess());
                return item;
            });
            page.setList(resultList);
        }

        return Resp.createBySuccess(page);
    }

    /**
     * 能否编辑删除
     * 每个月最后一天，整个白名单列表不能编辑
     * 只能编辑当前月或者后续月的数据，历史月的不能再编辑
     * @param monthStr yyyy-MM
     * @return
     */
    private Resp<Boolean> canEdit(String monthStr) {
        if (StringUtils.isBlank(monthStr)) {
            return Resp.createBySuccess(true);
        }

        try {

            Calendar instance = Calendar.getInstance();
            int nowYear = instance.get(Calendar.YEAR);
            int nowMonth = instance.get(Calendar.MONTH);

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
            Date date = sdf.parse(monthStr);
            instance.setTime(date);
            int year = instance.get(Calendar.YEAR);
            int month = instance.get(Calendar.MONTH);

            if ((nowYear > year) || (nowYear == year && nowMonth > month)) {
                return Resp.createByError("只能编辑或删除当前月或者后续月的数据");
            }

            return Resp.createBySuccess(true);

        } catch (Exception ex) {
            LOGGER.error("canEdit Exception monthStr:{}", monthStr, ex);
            return Resp.createBySuccess(true);
        }
    }

    /**
     * 是否月最后一天
     * @return
     */
    public boolean isMonthLastday() {
        Calendar instance = Calendar.getInstance();
        boolean b = instance.get(Calendar.DAY_OF_MONTH) == instance.getActualMaximum(Calendar.DAY_OF_MONTH);
        return b;
    }

    /**
     * 段位白名单变更
     * 如果是修改当前月的星级主体的段位则 1、通知刷新段位；2、通知resource修改资源额度
     * @param month yyyy-MM
     * @param uids
     * @param rank
     */
    private void rankChange(String month, List<Long> uids, int rank) {
        LOGGER.info("rankChange month:{},uids:{},rank:{}", month, uids, rank);
        if (CollectionUtils.isEmpty(uids)) {
            return;
        }

        month = MyDateUtil.fmtMonth(DateUtils.addMonths(MyDateUtil.parseMonth(month), -1));
        String curMonth = DateFormatUtils.format(rankCacheV3Service.getRankCurrentMonth(), "yyyy-MM");
        if (!curMonth.equals(month)) {
            LOGGER.info("rankChange rankMonth:{},month:{}", curMonth, month);
            return;
        }
        // 判断是否星级主体
        Map<Long, Set<Long>> starCmp = guildService.getStarCmp();
        Set<Long> cmpUid = uids.stream().filter(uid -> starCmp.containsKey(uid)).collect(Collectors.toSet());
        LOGGER.info("rankChange uids:{},cmpUid:{}", uids, cmpUid);
        if (CollectionUtils.isEmpty(cmpUid)) {
            return;
        }

        // 通知刷新段位
        rankCacheV3Service.refreshRankCache(3000);
        if(notifyResourceDelay < 0) {
            LOGGER.info("rankChange not notify delay:{}",notifyResourceDelay);
            return;
        }

        // 延迟通知resource修改资源额度
        RankChangeInfo info = RankChangeInfo.builder().month(month).uids(cmpUid).rank(rank).build();
        Boolean flag = redisTemplate.opsForZSet().add(RankConstant.RedisConstants.RANK_WHITELIST, JSON.toJSONString(info), System.currentTimeMillis());
        LOGGER.info("rankChange redis month:{},cmpUid:{},rank:{},flag:{}", month, cmpUid, rank, flag);
    }

    /**
     * 操作段位白名单后通知yyzone-resources修改资源额度
     */
    public void notifyResource() {
        // 延迟，等段位缓存刷新
        long max = System.currentTimeMillis() - TimeUnit.SECONDS.toMillis(notifyResourceDelay);
        Set<ZSetOperations.TypedTuple<String>> ts = redisTemplate.opsForZSet().rangeByScoreWithScores(RankConstant.RedisConstants.RANK_WHITELIST, -1, max);
        LOGGER.info("notifyResources delay:{},max:{},maxTime:{},size:{}", notifyResourceDelay, max, MyDateUtil.fmt(new Date(max)), ts.size());
        if (CollectionUtils.isEmpty(ts)) {
            return;
        }

        String curMonth = DateFormatUtils.format(rankCacheV3Service.getRankCurrentMonth(), "yyyy-MM");
        for (ZSetOperations.TypedTuple<String> t : ts) {
            LOGGER.info("notifyResources for:{}", t);
            RankChangeInfo info = JSON.parseObject(t.getValue(), RankChangeInfo.class);

            if (!curMonth.equals(info.getMonth())) {
                LOGGER.info("notifyResources rankMonth:{},month:{}", curMonth, info.getMonth());
                redisTemplate.opsForZSet().remove(RankConstant.RedisConstants.RANK_WHITELIST, t.getValue());
                continue;
            }
            // resource已经判断是否星级主体，这里不再判断

            NotifyRankChangeResp resp = notifyResource(info.getUids());
            if (resp.getResult() == 0) {
                redisTemplate.opsForZSet().remove(RankConstant.RedisConstants.RANK_WHITELIST, t.getValue());
            } else {
                throw new RuntimeException("notifyResource failed value:" + t.getValue());
            }
        }
    }

    public NotifyRankChangeResp notifyResource(Set<Long> uids) {
        NotifyRankChangeReq req = new NotifyRankChangeReq();
        req.setUids(uids);
        NotifyRankChangeResp resp = yyzoneResourceClient.notifyRankChange(req);
        LOGGER.info("notifyResources uids:{},resp:{}", uids, resp);
        return resp;
    }
}