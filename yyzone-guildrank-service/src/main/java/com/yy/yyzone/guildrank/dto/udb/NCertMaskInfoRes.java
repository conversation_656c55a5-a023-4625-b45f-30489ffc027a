package com.yy.yyzone.guildrank.dto.udb;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

import java.util.Map;

@Data
@ToString
public class NCertMaskInfoRes {
    public int rescode; // 0： 成功
    public Map<String, String> keyvalue; //保留字段
    public int type; //0 个人实名; 1 个人独资企业实名; 2 有限公司企业实名
    @JsonProperty("MaskName")
    public String maskName; // 名称掩码
    @JsonProperty("MaskId")
    public String maskId; // 证件号掩码  //个人时为身份证//企业时为许可证
    public String status; // S0I 提交中 S0S 审核中 S0A 审核通过 S0X 审核不通过
    public String time; // 保留字段,不可使用
    @JsonProperty("adult_flag")
    public int adultFlag; // 成年标志
    @JsonProperty("data_flag")
    public int dataFlag; // 监护人标识 （标识资料是否完整）
    public String country; // 156 大陆; 158 台湾;
    public int enhanced; // 0 普通版; 1 加强版 	//加强验证
    @JsonProperty("cert_mode")
    public int certMode; // 0 普通;1 快速;2 人脸;3 银行卡 //认证方式

}
