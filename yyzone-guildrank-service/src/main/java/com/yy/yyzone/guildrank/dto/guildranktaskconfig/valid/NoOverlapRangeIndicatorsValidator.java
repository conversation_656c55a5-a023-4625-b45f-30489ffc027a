package com.yy.yyzone.guildrank.dto.guildranktaskconfig.valid;

import com.yy.yyzone.guildrank.dto.guildranktaskconfig.RangeIndicator;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Comparator;
import java.util.List;

public class NoOverlapRangeIndicatorsValidator
        implements ConstraintValidator<NoOverlapRangeIndicators, List<RangeIndicator>> {

    @Override
    public boolean isValid(List<RangeIndicator> list, ConstraintValidatorContext ctx) {
        if (list == null || list.isEmpty()) {
            return true;
        }

        // 按 min 升序排序
        list.sort(Comparator.comparing(RangeIndicator::getMin));

        // 检查第一个区间的 min 是否合法
        if (list.get(0).getMin() < 0) {
            ctx.disableDefaultConstraintViolation();
            ctx.buildConstraintViolationWithTemplate("第一个区间的 min 值不能小于 0").addConstraintViolation();
            return false;
        }

        // 检查区间是否连续且不重叠
        for (int i = 1; i < list.size(); i++) {
            RangeIndicator prev = list.get(i - 1);
            RangeIndicator curr = list.get(i);

            // 检查是否重叠
            if (curr.getMin() < prev.getMax()) {
                ctx.disableDefaultConstraintViolation();
                ctx.buildConstraintViolationWithTemplate("区间重叠").addConstraintViolation();
                return false;
            }

            // 检查是否连续
            if (curr.getMin() != prev.getMax()) {
                ctx.disableDefaultConstraintViolation();
                ctx.buildConstraintViolationWithTemplate("区间不连续").addConstraintViolation();
                return false;
            }
        }
        // 检查最后一个区间的 max 是否为 null
        if (list.get(list.size() - 1).getMax() != null) {
            ctx.disableDefaultConstraintViolation();
            ctx.buildConstraintViolationWithTemplate("最后一个区间的 max 必须为 null (表示无穷大)").addConstraintViolation();
            return false;
        }

        return true;
    }
}