package com.yy.yyzone.guildrank.dto.guildranktaskconfig;

import lombok.Data;

import javax.validation.constraints.*;
import java.util.Map;

@Data
public class GuildRankTaskDistributionUpdateByOneUidRequest {
    @NotNull(message = "uid不能为空")
    @Positive(message = "UID必须为正数")
    private   Long uid;

    @NotNull(message = "日期不能为空")
    @Min(value = 100000, message = "日期格式需为yyyyMM")
    @Max(value = 999999, message = "日期格式需为yyyyMM")
    /**
     * 格式 yyyyMM 202507
     */
    private Integer dt;

    @NotEmpty
    @Size(min = 1)
    private Map<String,@Positive(message = "targetValue必须为正数") Long> taskAndTargetValues;
}
