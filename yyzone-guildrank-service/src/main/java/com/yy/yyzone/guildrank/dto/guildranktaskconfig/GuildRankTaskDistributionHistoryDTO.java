package com.yy.yyzone.guildrank.dto.guildranktaskconfig;

import com.yy.yyzone.guildrank.db.gen.model.GuildRankTaskDistribution;
import lombok.Data;

import java.util.Date;

@Data
public class GuildRankTaskDistributionHistoryDTO {
    private Long id;
    private Long uid;
    private String guildName;
    private Integer dt;
    private String taskType;
    private Long targetValue;
    private Integer status;
    private String operator;
    private Date createTime;
    private Date updateTime;
    private Boolean history;
    private String opType;
    private String reason;
    /**
     * 前一个操作，如果是最新一条，就是空
     */
    private GuildRankTaskDistributionHistoryDTO prevHistory;

    public static final GuildRankTaskDistributionHistoryDTO build(String guildName,GuildRankTaskDistribution task){
        return build(guildName,task,null);
    }
    public static final GuildRankTaskDistributionHistoryDTO build(String guildName,GuildRankTaskDistribution task,GuildRankTaskDistributionHistoryDTO prevHistory){
        GuildRankTaskDistributionHistoryDTO dto = new GuildRankTaskDistributionHistoryDTO();
        dto.setId(task.getId());
        dto.setGuildName(guildName);
        dto.setUid(task.getUid());
        dto.setDt(task.getDt());
        dto.setTaskType(task.getTaskType());
        dto.setTargetValue(task.getTargetValue());
        dto.setOperator(task.getOperator());
        dto.setCreateTime(task.getCreateTime());
        dto.setUpdateTime(task.getUpdateTime());
        dto.setHistory(task.getHistory());
        dto.setOpType(task.getOpType());
        dto.setPrevHistory(prevHistory);
        dto.setReason(task.getReason());
        return dto;
    };
}