package com.yy.yyzone.guildrank.dto.guildranktaskconfig;

import com.yy.yyzone.guildrank.dto.guildranktaskconfig.valid.ValidRanges;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class Condition {
    @Valid
    @NotNull
    @Size(min = 1, max = 10)
    @ValidRanges
    private List<@Valid Range> ranges;
}