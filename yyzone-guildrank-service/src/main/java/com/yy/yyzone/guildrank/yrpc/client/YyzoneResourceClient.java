package com.yy.yyzone.guildrank.yrpc.client;

import com.yy.yyzone.guildrank.yrpc.dto.resource.NotifyRankChangeReq;
import com.yy.yyzone.guildrank.yrpc.dto.resource.NotifyRankChangeResp;
import org.apache.dubbo.common.annotation.Yrpc;

public interface YyzoneResourceClient {
    /**
     * 通知段位变更
     *
     * @param req
     * @return
     */
    @Yrpc(reqUri = 313 << 8 | 112, resUri = 314 << 8 | 112)
    NotifyRankChangeResp notifyRankChange(NotifyRankChangeReq req);
}
