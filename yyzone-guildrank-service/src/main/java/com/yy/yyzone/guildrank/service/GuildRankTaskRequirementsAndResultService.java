package com.yy.yyzone.guildrank.service;

import com.yy.yyzone.guildrank.constant.BigdaSyncConstant;
import com.yy.yyzone.guildrank.db.gen.mapper.GuildRankTaskRequirementsAndResultMapper;
import com.yy.yyzone.guildrank.db.gen.model.GuildRankTaskRequirementsAndResult;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

@Service
public class GuildRankTaskRequirementsAndResultService {
    @Autowired
    private GuildRankTaskRequirementsAndResultMapper rankTaskRequirementsAndResultMapper;
    @Autowired
    private BigdaSyncDetailService bigdaSyncDetailService;

    public GuildRankTaskRequirementsAndResult getByUidAndDt(long uid,int dt){
        try {
            Date dtDate = getAppropriateDateTime(dt,bigdaSyncDetailService.getCheckDoneLatestDt(BigdaSyncConstant.TableName.CMP_HEALTH_DI));
            return rankTaskRequirementsAndResultMapper.selectByDtAndUid(dtDate,uid);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }
    /**
     * 根据传入的 dt 和最后同步时间，返回适当的日期时间
     * @param dt 格式为 yyyyMM 的数字，例如 202501
     * @param lastSyncTime 最后同步时间
     * @return 适当的 Date
     */
    public static Date getAppropriateDateTime(int dt, Date lastSyncTime) throws ParseException {
        Date dtDate = DateUtils.parseDate(dt+"01","yyyyMMdd");
        if(lastSyncTime == null){
            return dtDate;
        }
        // 获取 dt 的年月
        Calendar dtCalendar = Calendar.getInstance();
        dtCalendar.setTime(dtDate);
        int dtYear = dtCalendar.get(Calendar.YEAR);
        int dtMonth = dtCalendar.get(Calendar.MONTH);
        // 获取最后同步时间的年月
        Calendar syncCalendar = Calendar.getInstance();
        syncCalendar.setTime(lastSyncTime);
        int syncYear = syncCalendar.get(Calendar.YEAR);
        int syncMonth = syncCalendar.get(Calendar.MONTH);
        // 1. 如果 dt 和最后同步时间是同一年同一月，返回最后同步时间
        if (dtYear == syncYear && dtMonth == syncMonth) {
            return lastSyncTime;
        }
        // 2. 如果 dt 比最后同步时间晚，返回该月最后一天的开始时间（00:00:00）
        if (dtDate.before(lastSyncTime)) {
            dtCalendar.set(Calendar.DAY_OF_MONTH, dtCalendar.getActualMaximum(Calendar.DAY_OF_MONTH));
            dtCalendar.set(Calendar.HOUR_OF_DAY, 0);
            dtCalendar.set(Calendar.MINUTE, 0);
            dtCalendar.set(Calendar.SECOND, 0);
            dtCalendar.set(Calendar.MILLISECOND, 0);
            return dtCalendar.getTime();
        }
        // 3. 如果 dt 比最后同步时间还晚
        return dtDate;
    }

    public static void main(String[] args) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            // 示例 1: 同年同月
            Date syncTime1 = sdf.parse("2025-01-15 00:00:00");
            System.out.println(sdf.format(getAppropriateDateTime(202501, syncTime1))); // 应返回 2025-01-15 00:00:00

            // 示例 2: dt 比同步时间晚
            Date syncTime2 = sdf.parse("2024-12-15 00:00:00");
            System.out.println(sdf.format(getAppropriateDateTime(202501, syncTime2))); // 应返回 2025-01-01 00:00:00

            // 示例 3: dt 比同步时间早
            Date syncTime3 = sdf.parse("2025-02-15 00:00:00");
            System.out.println(sdf.format(getAppropriateDateTime(202501, syncTime3))); // 应返回 2025-01-31 00:00:00
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
