package com.yy.yyzone.guildrank.dto.manual;

import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class ScoreManualModifyDTO {

    /**
     * 记录id，新增时传0，修改时大于0
     */
    private Long id;
    /**
     * 主体uid
     */
    @NotNull(message = "主体uid不能为空")
    private String mainGuildUid;
    /**
     * 月份 yyyy-MM 格式
     */
    @NotNull(message = "月份不能为空")
    private String month;
    /**
     * 操作类型，1-加，2-减
     */
    @NotNull(message = "类型不能为空")
    private Byte modifyType;
    /**
     * 修改分值
     */
    @NotNull(message = "分值不能为空")
    private Integer modifyNum;
    /**
     * 原因
     */
    @NotNull(message = "原因不能为空")
    private String modifyReason;

}
