package com.yy.yyzone.guildrank.dto.guildrankconfig;

import com.yy.yyzone.guildrank.constant.RankConstant;
import com.yy.yyzone.guildrank.util.Resp;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 数据指标分值设置
 */
@Data
public class DataIndexConfig {
    /**
     * 数据指标分值设置 key为指标
     * 公会3&4星主播签约数:STAR34_ANCHOR_SIGN_COUNT
     * 公会3&4星主播签约数diff:STAR34_ANCHOR_SIGN_COUNT_DIFF
     * 公会月日均高优有效开播主播数:M_DA_HQ_VL_ANCHOR_COUNT
     * 公会月日均腰部有效开播主播数:M_DA_WAIST_VL_ANCHOR_COUNT
     * 头部主播数diff:HEAD_ANCHOR_COUNT_DIFF
     * 腰部主播数diff:WAIST_ANCHOR_COUNT_DIFF
     * 公会月日均高优有效开播主播数diff:M_DA_HQ_VL_ANCHOR_COUNT_DIFF
     * 公会月日均腰部有效开播主播数diff:M_DA_WAIST_VL_ANCHOR_COUNT_DIFF
     * 公会新转存开播率:N2S_LIVE_RATIO
     * 公会存量主播开播率:STOCK_ANCHOR_LIVE_RATIO
     * 公会新签金牌艺人数:NEW_SIGN_GOLD_ARTIST_COUNT
     * 公会新主播流水金额:NEW_ANCHOR_FLOW
     * 公会存量主播流水金额:STOCK_ANCHOR_FLOW
     * 新主播流水金额diff:NEW_ANCHOR_FLOW_DIFF
     * 存量主播流水金额diff:STOCK_ANCHOR_FLOW_DIFF
     * 存量主播流水金额占比:STOCK_ANCHOR_FLOW_RATIO
     * 公会健康分:GUILD_HEALTH_POINT
     */
    private Map<String, List<DataIndexConfigItem>> indexMap;

    public Resp validate() {
        if (MapUtils.isEmpty(indexMap)) {
            return Resp.createByError("【数据指标】分值设置不能为空");
        }
        Set<String> names = Arrays.stream(RankConstant.DataIndex.values()).map(l -> l.getCode()).collect(Collectors.toSet());
        String s = indexMap.keySet().stream().filter(i -> !names.contains(i)).collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(s)) {
            return Resp.createByError("【数据指标】指标不存在：" + s);
        }
        return Resp.createBySuccess();
    }
}