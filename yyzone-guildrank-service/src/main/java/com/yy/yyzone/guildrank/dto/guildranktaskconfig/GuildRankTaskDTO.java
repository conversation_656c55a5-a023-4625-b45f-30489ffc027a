package com.yy.yyzone.guildrank.dto.guildranktaskconfig;

import lombok.Data;

import java.util.List;

@Data
public class GuildRankTaskDTO {
    /**
     * 数据的月份格式 yyyy-MM ,下载时候记得使用这个日期
     */
    private String month;
    /**
     * 分数总的限制
     */
    private Integer totalScoreLimit;
    private List<GuildRankTaskDetailsDTO> allTaskDetails;

    public GuildRankTaskDTO(int dt,Integer totalScoreLimit , List<GuildRankTaskDetailsDTO> allTaskDetails) {
        this.month = String.valueOf(dt).substring(0, 4) + "-" + String.valueOf(dt).substring(4);
        this.totalScoreLimit = totalScoreLimit;
        this.allTaskDetails = allTaskDetails;
    }
}
