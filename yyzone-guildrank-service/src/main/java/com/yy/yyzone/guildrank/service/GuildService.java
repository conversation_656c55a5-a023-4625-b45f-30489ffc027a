package com.yy.yyzone.guildrank.service;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.yyzone.guildrank.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class GuildService {
    @Autowired
    private StarService starService;

    @Autowired
    private GoldChannelService goldChannelService;

    /**
     * 获取星级主体
     *
     * @return <星级主体uid，Set<关联公会>>
     */
    public Map<Long, Set<Long>> getStarCmp() {
        Map<Long, Set<Long>> associatedUserMap = goldChannelService.getAllAssociatedUser();
        List<Long> starGuildList = starService.getAllStarGoldGuild();
        return getStarCmp(associatedUserMap, starGuildList);
    }

    /**
     * 获取星级主体
     *
     * @param associatedMap
     * @param starGuilds
     * @return <星级主体uid，Set<关联公会>>
     */
    public Map<Long, Set<Long>> getStarCmp(Map<Long, Set<Long>> associatedMap, List<Long> starGuilds) {
        if (MapUtils.isEmpty(associatedMap) || CollectionUtils.isEmpty(starGuilds)) {
            return Collections.emptyMap();
        }

        // <关联公会uid，金牌授权uid>
        Map<Long, Long> revertMap = CommonUtil.revertKV(associatedMap);
        Map<Long, Set<Long>> map = Maps.newHashMapWithExpectedSize(starGuilds.size());
        for (Long s : starGuilds) {
            Long cmpUid = revertMap.get(s);
            if (cmpUid != null) {
                map.put(cmpUid, associatedMap.get(cmpUid));
            } else {
                map.put(s, Sets.newHashSet(s));
            }
        }
        return map;
    }
}