package com.yy.yyzone.guildrank.util.excel;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by chenjinying on 2017/7/6.
 * mail: <EMAIL>
 */
public class Columns {

    private List<Column> columns = new ArrayList<>();

    public void addColumn(String name, ColumnType columnType, boolean allowNumm, Object defaultVal) {
        columns.add(new Column(name, columnType, allowNumm, defaultVal));
    }

    public Column getColumn(int i) {
        return columns.get(i);
    }

    public boolean isEmpty() {
        return columns.size() <= 0;
    }

    public int size() {
        return columns.size();
    }

    public enum ColumnType {
        CELL_TYPE_STRING,
        CELL_TYPE_BOOLEAN,
        CELL_TYPE_LONG,
        CELL_TYPE_INT,
        CELL_TYPE_SHORT,
        CELL_TYPE_BIGDECIMAL
    }

    public static class Column {
        public String name;
        public ColumnType type;
        public boolean allowNumm;
        public Object defaultVal;

        Column(String name, ColumnType type, boolean allowNumm, Object defaultVal) {
            this.name = name;
            this.type = type;
            this.allowNumm = allowNumm;
            this.defaultVal = defaultVal;
        }
    }

}
