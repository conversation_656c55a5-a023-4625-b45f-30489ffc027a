// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: AllGuildRankI64.proto

package com.yy.yyzone.guildrank.api;

public final class AllGuildRankI64 {
  private AllGuildRankI64() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface QueryAllGuildRankByOrderReqOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.api.QueryAllGuildRankByOrderReq)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 2019-07
     * </pre>
     *
     * <code>string month = 1;</code>
     */
    String getMonth();
    /**
     * <pre>
     * 2019-07
     * </pre>
     *
     * <code>string month = 1;</code>
     */
    com.google.protobuf.ByteString
        getMonthBytes();

    /**
     * <pre>
     * 排序标识： 1蓝钻
     * </pre>
     *
     * <code>uint32 orderType = 2;</code>
     */
    int getOrderType();
  }
  /**
   * Protobuf type {@code com.yy.yyzone.guildrank.api.QueryAllGuildRankByOrderReq}
   */
  public  static final class QueryAllGuildRankByOrderReq extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.api.QueryAllGuildRankByOrderReq)
      QueryAllGuildRankByOrderReqOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QueryAllGuildRankByOrderReq.newBuilder() to construct.
    private QueryAllGuildRankByOrderReq(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QueryAllGuildRankByOrderReq() {
      month_ = "";
      orderType_ = 0;
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QueryAllGuildRankByOrderReq(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              String s = input.readStringRequireUtf8();

              month_ = s;
              break;
            }
            case 16: {

              orderType_ = input.readUInt32();
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return AllGuildRankI64.internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderReq_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return AllGuildRankI64.internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              QueryAllGuildRankByOrderReq.class, Builder.class);
    }

    public static final int MONTH_FIELD_NUMBER = 1;
    private volatile Object month_;
    /**
     * <pre>
     * 2019-07
     * </pre>
     *
     * <code>string month = 1;</code>
     */
    public String getMonth() {
      Object ref = month_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        month_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 2019-07
     * </pre>
     *
     * <code>string month = 1;</code>
     */
    public com.google.protobuf.ByteString
        getMonthBytes() {
      Object ref = month_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        month_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ORDERTYPE_FIELD_NUMBER = 2;
    private int orderType_;
    /**
     * <pre>
     * 排序标识： 1蓝钻
     * </pre>
     *
     * <code>uint32 orderType = 2;</code>
     */
    public int getOrderType() {
      return orderType_;
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getMonthBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, month_);
      }
      if (orderType_ != 0) {
        output.writeUInt32(2, orderType_);
      }
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getMonthBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, month_);
      }
      if (orderType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, orderType_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof QueryAllGuildRankByOrderReq)) {
        return super.equals(obj);
      }
      QueryAllGuildRankByOrderReq other = (QueryAllGuildRankByOrderReq) obj;

      boolean result = true;
      result = result && getMonth()
          .equals(other.getMonth());
      result = result && (getOrderType()
          == other.getOrderType());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MONTH_FIELD_NUMBER;
      hash = (53 * hash) + getMonth().hashCode();
      hash = (37 * hash) + ORDERTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getOrderType();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static QueryAllGuildRankByOrderReq parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryAllGuildRankByOrderReq parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryAllGuildRankByOrderReq parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryAllGuildRankByOrderReq parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryAllGuildRankByOrderReq parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryAllGuildRankByOrderReq parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryAllGuildRankByOrderReq parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static QueryAllGuildRankByOrderReq parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static QueryAllGuildRankByOrderReq parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static QueryAllGuildRankByOrderReq parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static QueryAllGuildRankByOrderReq parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static QueryAllGuildRankByOrderReq parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(QueryAllGuildRankByOrderReq prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.yyzone.guildrank.api.QueryAllGuildRankByOrderReq}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.api.QueryAllGuildRankByOrderReq)
        QueryAllGuildRankByOrderReqOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return AllGuildRankI64.internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderReq_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return AllGuildRankI64.internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderReq_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                QueryAllGuildRankByOrderReq.class, Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.api.AllGuildRankI64.QueryAllGuildRankByOrderReq.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        month_ = "";

        orderType_ = 0;

        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return AllGuildRankI64.internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderReq_descriptor;
      }

      @Override
      public QueryAllGuildRankByOrderReq getDefaultInstanceForType() {
        return QueryAllGuildRankByOrderReq.getDefaultInstance();
      }

      @Override
      public QueryAllGuildRankByOrderReq build() {
        QueryAllGuildRankByOrderReq result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public QueryAllGuildRankByOrderReq buildPartial() {
        QueryAllGuildRankByOrderReq result = new QueryAllGuildRankByOrderReq(this);
        result.month_ = month_;
        result.orderType_ = orderType_;
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof QueryAllGuildRankByOrderReq) {
          return mergeFrom((QueryAllGuildRankByOrderReq)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(QueryAllGuildRankByOrderReq other) {
        if (other == QueryAllGuildRankByOrderReq.getDefaultInstance()) return this;
        if (!other.getMonth().isEmpty()) {
          month_ = other.month_;
          onChanged();
        }
        if (other.getOrderType() != 0) {
          setOrderType(other.getOrderType());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        QueryAllGuildRankByOrderReq parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (QueryAllGuildRankByOrderReq) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private Object month_ = "";
      /**
       * <pre>
       * 2019-07
       * </pre>
       *
       * <code>string month = 1;</code>
       */
      public String getMonth() {
        Object ref = month_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          month_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       * 2019-07
       * </pre>
       *
       * <code>string month = 1;</code>
       */
      public com.google.protobuf.ByteString
          getMonthBytes() {
        Object ref = month_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          month_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 2019-07
       * </pre>
       *
       * <code>string month = 1;</code>
       */
      public Builder setMonth(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        month_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 2019-07
       * </pre>
       *
       * <code>string month = 1;</code>
       */
      public Builder clearMonth() {
        
        month_ = getDefaultInstance().getMonth();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 2019-07
       * </pre>
       *
       * <code>string month = 1;</code>
       */
      public Builder setMonthBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        month_ = value;
        onChanged();
        return this;
      }

      private int orderType_ ;
      /**
       * <pre>
       * 排序标识： 1蓝钻
       * </pre>
       *
       * <code>uint32 orderType = 2;</code>
       */
      public int getOrderType() {
        return orderType_;
      }
      /**
       * <pre>
       * 排序标识： 1蓝钻
       * </pre>
       *
       * <code>uint32 orderType = 2;</code>
       */
      public Builder setOrderType(int value) {
        
        orderType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 排序标识： 1蓝钻
       * </pre>
       *
       * <code>uint32 orderType = 2;</code>
       */
      public Builder clearOrderType() {
        
        orderType_ = 0;
        onChanged();
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.api.QueryAllGuildRankByOrderReq)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.api.QueryAllGuildRankByOrderReq)
    private static final QueryAllGuildRankByOrderReq DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new QueryAllGuildRankByOrderReq();
    }

    public static QueryAllGuildRankByOrderReq getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QueryAllGuildRankByOrderReq>
        PARSER = new com.google.protobuf.AbstractParser<QueryAllGuildRankByOrderReq>() {
      @Override
      public QueryAllGuildRankByOrderReq parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QueryAllGuildRankByOrderReq(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QueryAllGuildRankByOrderReq> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<QueryAllGuildRankByOrderReq> getParserForType() {
      return PARSER;
    }

    @Override
    public QueryAllGuildRankByOrderReq getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryAllGuildRankByOrderRespOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.api.QueryAllGuildRankByOrderResp)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 result = 1;</code>
     */
    int getResult();

    /**
     * <pre>
     *是否上个月最新的数据 1:是 0:不是
     * </pre>
     *
     * <code>uint32 isNew = 2;</code>
     */
    int getIsNew();

    /**
     * <code>repeated int64 owuids = 3;</code>
     */
    java.util.List<Long> getOwuidsList();
    /**
     * <code>repeated int64 owuids = 3;</code>
     */
    int getOwuidsCount();
    /**
     * <code>repeated int64 owuids = 3;</code>
     */
    long getOwuids(int index);
  }
  /**
   * Protobuf type {@code com.yy.yyzone.guildrank.api.QueryAllGuildRankByOrderResp}
   */
  public  static final class QueryAllGuildRankByOrderResp extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.api.QueryAllGuildRankByOrderResp)
      QueryAllGuildRankByOrderRespOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QueryAllGuildRankByOrderResp.newBuilder() to construct.
    private QueryAllGuildRankByOrderResp(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QueryAllGuildRankByOrderResp() {
      result_ = 0;
      isNew_ = 0;
      owuids_ = java.util.Collections.emptyList();
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QueryAllGuildRankByOrderResp(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              result_ = input.readUInt32();
              break;
            }
            case 16: {

              isNew_ = input.readUInt32();
              break;
            }
            case 24: {
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
                owuids_ = new java.util.ArrayList<Long>();
                mutable_bitField0_ |= 0x00000004;
              }
              owuids_.add(input.readInt64());
              break;
            }
            case 26: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004) && input.getBytesUntilLimit() > 0) {
                owuids_ = new java.util.ArrayList<Long>();
                mutable_bitField0_ |= 0x00000004;
              }
              while (input.getBytesUntilLimit() > 0) {
                owuids_.add(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
          owuids_ = java.util.Collections.unmodifiableList(owuids_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return AllGuildRankI64.internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderResp_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return AllGuildRankI64.internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              QueryAllGuildRankByOrderResp.class, Builder.class);
    }

    private int bitField0_;
    public static final int RESULT_FIELD_NUMBER = 1;
    private int result_;
    /**
     * <code>uint32 result = 1;</code>
     */
    public int getResult() {
      return result_;
    }

    public static final int ISNEW_FIELD_NUMBER = 2;
    private int isNew_;
    /**
     * <pre>
     *是否上个月最新的数据 1:是 0:不是
     * </pre>
     *
     * <code>uint32 isNew = 2;</code>
     */
    public int getIsNew() {
      return isNew_;
    }

    public static final int OWUIDS_FIELD_NUMBER = 3;
    private java.util.List<Long> owuids_;
    /**
     * <code>repeated int64 owuids = 3;</code>
     */
    public java.util.List<Long>
        getOwuidsList() {
      return owuids_;
    }
    /**
     * <code>repeated int64 owuids = 3;</code>
     */
    public int getOwuidsCount() {
      return owuids_.size();
    }
    /**
     * <code>repeated int64 owuids = 3;</code>
     */
    public long getOwuids(int index) {
      return owuids_.get(index);
    }
    private int owuidsMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (result_ != 0) {
        output.writeUInt32(1, result_);
      }
      if (isNew_ != 0) {
        output.writeUInt32(2, isNew_);
      }
      if (getOwuidsList().size() > 0) {
        output.writeUInt32NoTag(26);
        output.writeUInt32NoTag(owuidsMemoizedSerializedSize);
      }
      for (int i = 0; i < owuids_.size(); i++) {
        output.writeInt64NoTag(owuids_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (result_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, result_);
      }
      if (isNew_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, isNew_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < owuids_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(owuids_.get(i));
        }
        size += dataSize;
        if (!getOwuidsList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        owuidsMemoizedSerializedSize = dataSize;
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof QueryAllGuildRankByOrderResp)) {
        return super.equals(obj);
      }
      QueryAllGuildRankByOrderResp other = (QueryAllGuildRankByOrderResp) obj;

      boolean result = true;
      result = result && (getResult()
          == other.getResult());
      result = result && (getIsNew()
          == other.getIsNew());
      result = result && getOwuidsList()
          .equals(other.getOwuidsList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + getResult();
      hash = (37 * hash) + ISNEW_FIELD_NUMBER;
      hash = (53 * hash) + getIsNew();
      if (getOwuidsCount() > 0) {
        hash = (37 * hash) + OWUIDS_FIELD_NUMBER;
        hash = (53 * hash) + getOwuidsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static QueryAllGuildRankByOrderResp parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryAllGuildRankByOrderResp parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryAllGuildRankByOrderResp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryAllGuildRankByOrderResp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryAllGuildRankByOrderResp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryAllGuildRankByOrderResp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryAllGuildRankByOrderResp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static QueryAllGuildRankByOrderResp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static QueryAllGuildRankByOrderResp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static QueryAllGuildRankByOrderResp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static QueryAllGuildRankByOrderResp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static QueryAllGuildRankByOrderResp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(QueryAllGuildRankByOrderResp prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.yyzone.guildrank.api.QueryAllGuildRankByOrderResp}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.api.QueryAllGuildRankByOrderResp)
        QueryAllGuildRankByOrderRespOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return AllGuildRankI64.internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderResp_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return AllGuildRankI64.internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderResp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                QueryAllGuildRankByOrderResp.class, Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.api.AllGuildRankI64.QueryAllGuildRankByOrderResp.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        result_ = 0;

        isNew_ = 0;

        owuids_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return AllGuildRankI64.internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderResp_descriptor;
      }

      @Override
      public QueryAllGuildRankByOrderResp getDefaultInstanceForType() {
        return QueryAllGuildRankByOrderResp.getDefaultInstance();
      }

      @Override
      public QueryAllGuildRankByOrderResp build() {
        QueryAllGuildRankByOrderResp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public QueryAllGuildRankByOrderResp buildPartial() {
        QueryAllGuildRankByOrderResp result = new QueryAllGuildRankByOrderResp(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.result_ = result_;
        result.isNew_ = isNew_;
        if (((bitField0_ & 0x00000004) == 0x00000004)) {
          owuids_ = java.util.Collections.unmodifiableList(owuids_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.owuids_ = owuids_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof QueryAllGuildRankByOrderResp) {
          return mergeFrom((QueryAllGuildRankByOrderResp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(QueryAllGuildRankByOrderResp other) {
        if (other == QueryAllGuildRankByOrderResp.getDefaultInstance()) return this;
        if (other.getResult() != 0) {
          setResult(other.getResult());
        }
        if (other.getIsNew() != 0) {
          setIsNew(other.getIsNew());
        }
        if (!other.owuids_.isEmpty()) {
          if (owuids_.isEmpty()) {
            owuids_ = other.owuids_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureOwuidsIsMutable();
            owuids_.addAll(other.owuids_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        QueryAllGuildRankByOrderResp parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (QueryAllGuildRankByOrderResp) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int result_ ;
      /**
       * <code>uint32 result = 1;</code>
       */
      public int getResult() {
        return result_;
      }
      /**
       * <code>uint32 result = 1;</code>
       */
      public Builder setResult(int value) {
        
        result_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 result = 1;</code>
       */
      public Builder clearResult() {
        
        result_ = 0;
        onChanged();
        return this;
      }

      private int isNew_ ;
      /**
       * <pre>
       *是否上个月最新的数据 1:是 0:不是
       * </pre>
       *
       * <code>uint32 isNew = 2;</code>
       */
      public int getIsNew() {
        return isNew_;
      }
      /**
       * <pre>
       *是否上个月最新的数据 1:是 0:不是
       * </pre>
       *
       * <code>uint32 isNew = 2;</code>
       */
      public Builder setIsNew(int value) {
        
        isNew_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *是否上个月最新的数据 1:是 0:不是
       * </pre>
       *
       * <code>uint32 isNew = 2;</code>
       */
      public Builder clearIsNew() {
        
        isNew_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<Long> owuids_ = java.util.Collections.emptyList();
      private void ensureOwuidsIsMutable() {
        if (!((bitField0_ & 0x00000004) == 0x00000004)) {
          owuids_ = new java.util.ArrayList<Long>(owuids_);
          bitField0_ |= 0x00000004;
         }
      }
      /**
       * <code>repeated int64 owuids = 3;</code>
       */
      public java.util.List<Long>
          getOwuidsList() {
        return java.util.Collections.unmodifiableList(owuids_);
      }
      /**
       * <code>repeated int64 owuids = 3;</code>
       */
      public int getOwuidsCount() {
        return owuids_.size();
      }
      /**
       * <code>repeated int64 owuids = 3;</code>
       */
      public long getOwuids(int index) {
        return owuids_.get(index);
      }
      /**
       * <code>repeated int64 owuids = 3;</code>
       */
      public Builder setOwuids(
          int index, long value) {
        ensureOwuidsIsMutable();
        owuids_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 owuids = 3;</code>
       */
      public Builder addOwuids(long value) {
        ensureOwuidsIsMutable();
        owuids_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 owuids = 3;</code>
       */
      public Builder addAllOwuids(
          Iterable<? extends Long> values) {
        ensureOwuidsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, owuids_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 owuids = 3;</code>
       */
      public Builder clearOwuids() {
        owuids_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.api.QueryAllGuildRankByOrderResp)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.api.QueryAllGuildRankByOrderResp)
    private static final QueryAllGuildRankByOrderResp DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new QueryAllGuildRankByOrderResp();
    }

    public static QueryAllGuildRankByOrderResp getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QueryAllGuildRankByOrderResp>
        PARSER = new com.google.protobuf.AbstractParser<QueryAllGuildRankByOrderResp>() {
      @Override
      public QueryAllGuildRankByOrderResp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QueryAllGuildRankByOrderResp(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QueryAllGuildRankByOrderResp> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<QueryAllGuildRankByOrderResp> getParserForType() {
      return PARSER;
    }

    @Override
    public QueryAllGuildRankByOrderResp getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderReq_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderReq_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderResp_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderResp_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    String[] descriptorData = {
      "\n\025AllGuildRankI64.proto\022\033com.yy.yyzone.g" +
      "uildrank.api\032\turi.proto\"?\n\033QueryAllGuild" +
      "RankByOrderReq\022\r\n\005month\030\001 \001(\t\022\021\n\torderTy" +
      "pe\030\002 \001(\r\"M\n\034QueryAllGuildRankByOrderResp" +
      "\022\016\n\006result\030\001 \001(\r\022\r\n\005isNew\030\002 \001(\r\022\016\n\006owuid" +
      "s\030\003 \003(\003b\006proto3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          yyp.Uri.getDescriptor(),
        }, assigner);
    internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderReq_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderReq_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderReq_descriptor,
        new String[] { "Month", "OrderType", });
    internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderResp_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderResp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderResp_descriptor,
        new String[] { "Result", "IsNew", "Owuids", });
    yyp.Uri.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
