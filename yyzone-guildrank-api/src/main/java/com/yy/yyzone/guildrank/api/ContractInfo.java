// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: contractInfo.proto

package com.yy.yyzone.guildrank.api;

public final class ContractInfo {
  private ContractInfo() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface QueryContractInfoByAnchorUidsReqOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.api.QueryContractInfoByAnchorUidsReq)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 批量查询的uid集合
     * </pre>
     *
     * <code>repeated uint32 owUids = 1;</code>
     */
    java.util.List<Integer> getOwUidsList();
    /**
     * <pre>
     * 批量查询的uid集合
     * </pre>
     *
     * <code>repeated uint32 owUids = 1;</code>
     */
    int getOwUidsCount();
    /**
     * <pre>
     * 批量查询的uid集合
     * </pre>
     *
     * <code>repeated uint32 owUids = 1;</code>
     */
    int getOwUids(int index);

    /**
     * <pre>
     * 扩展字段，暂未使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extendData = 2;</code>
     */
    int getExtendDataCount();
    /**
     * <pre>
     * 扩展字段，暂未使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extendData = 2;</code>
     */
    boolean containsExtendData(
            String key);
    /**
     * Use {@link #getExtendDataMap()} instead.
     */
    @Deprecated
    java.util.Map<String, String>
    getExtendData();
    /**
     * <pre>
     * 扩展字段，暂未使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extendData = 2;</code>
     */
    java.util.Map<String, String>
    getExtendDataMap();
    /**
     * <pre>
     * 扩展字段，暂未使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extendData = 2;</code>
     */

    String getExtendDataOrDefault(
            String key,
            String defaultValue);
    /**
     * <pre>
     * 扩展字段，暂未使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extendData = 2;</code>
     */

    String getExtendDataOrThrow(
            String key);
  }
  /**
   * Protobuf type {@code com.yy.yyzone.guildrank.api.QueryContractInfoByAnchorUidsReq}
   */
  public  static final class QueryContractInfoByAnchorUidsReq extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.api.QueryContractInfoByAnchorUidsReq)
      QueryContractInfoByAnchorUidsReqOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QueryContractInfoByAnchorUidsReq.newBuilder() to construct.
    private QueryContractInfoByAnchorUidsReq(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QueryContractInfoByAnchorUidsReq() {
      owUids_ = java.util.Collections.emptyList();
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QueryContractInfoByAnchorUidsReq(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                owUids_ = new java.util.ArrayList<Integer>();
                mutable_bitField0_ |= 0x00000001;
              }
              owUids_.add(input.readUInt32());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001) && input.getBytesUntilLimit() > 0) {
                owUids_ = new java.util.ArrayList<Integer>();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                owUids_.add(input.readUInt32());
              }
              input.popLimit(limit);
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                extendData_ = com.google.protobuf.MapField.newMapField(
                    ExtendDataDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000002;
              }
              com.google.protobuf.MapEntry<String, String>
              extendData__ = input.readMessage(
                  ExtendDataDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              extendData_.getMutableMap().put(
                  extendData__.getKey(), extendData__.getValue());
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          owUids_ = java.util.Collections.unmodifiableList(owUids_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ContractInfo.internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsReq_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 2:
          return internalGetExtendData();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ContractInfo.internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              QueryContractInfoByAnchorUidsReq.class, Builder.class);
    }

    public static final int OWUIDS_FIELD_NUMBER = 1;
    private java.util.List<Integer> owUids_;
    /**
     * <pre>
     * 批量查询的uid集合
     * </pre>
     *
     * <code>repeated uint32 owUids = 1;</code>
     */
    public java.util.List<Integer>
        getOwUidsList() {
      return owUids_;
    }
    /**
     * <pre>
     * 批量查询的uid集合
     * </pre>
     *
     * <code>repeated uint32 owUids = 1;</code>
     */
    public int getOwUidsCount() {
      return owUids_.size();
    }
    /**
     * <pre>
     * 批量查询的uid集合
     * </pre>
     *
     * <code>repeated uint32 owUids = 1;</code>
     */
    public int getOwUids(int index) {
      return owUids_.get(index);
    }
    private int owUidsMemoizedSerializedSize = -1;

    public static final int EXTENDDATA_FIELD_NUMBER = 2;
    private static final class ExtendDataDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          String, String> defaultEntry =
              com.google.protobuf.MapEntry
              .<String, String>newDefaultInstance(
                  ContractInfo.internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsReq_ExtendDataEntry_descriptor,
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "",
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "");
    }
    private com.google.protobuf.MapField<
        String, String> extendData_;
    private com.google.protobuf.MapField<String, String>
    internalGetExtendData() {
      if (extendData_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ExtendDataDefaultEntryHolder.defaultEntry);
      }
      return extendData_;
    }

    public int getExtendDataCount() {
      return internalGetExtendData().getMap().size();
    }
    /**
     * <pre>
     * 扩展字段，暂未使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extendData = 2;</code>
     */

    public boolean containsExtendData(
        String key) {
      if (key == null) { throw new NullPointerException(); }
      return internalGetExtendData().getMap().containsKey(key);
    }
    /**
     * Use {@link #getExtendDataMap()} instead.
     */
    @Deprecated
    public java.util.Map<String, String> getExtendData() {
      return getExtendDataMap();
    }
    /**
     * <pre>
     * 扩展字段，暂未使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extendData = 2;</code>
     */

    public java.util.Map<String, String> getExtendDataMap() {
      return internalGetExtendData().getMap();
    }
    /**
     * <pre>
     * 扩展字段，暂未使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extendData = 2;</code>
     */

    public String getExtendDataOrDefault(
        String key,
        String defaultValue) {
      if (key == null) { throw new NullPointerException(); }
      java.util.Map<String, String> map =
          internalGetExtendData().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     * 扩展字段，暂未使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extendData = 2;</code>
     */

    public String getExtendDataOrThrow(
        String key) {
      if (key == null) { throw new NullPointerException(); }
      java.util.Map<String, String> map =
          internalGetExtendData().getMap();
      if (!map.containsKey(key)) {
        throw new IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (getOwUidsList().size() > 0) {
        output.writeUInt32NoTag(10);
        output.writeUInt32NoTag(owUidsMemoizedSerializedSize);
      }
      for (int i = 0; i < owUids_.size(); i++) {
        output.writeUInt32NoTag(owUids_.get(i));
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeStringMapTo(
          output,
          internalGetExtendData(),
          ExtendDataDefaultEntryHolder.defaultEntry,
          2);
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < owUids_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(owUids_.get(i));
        }
        size += dataSize;
        if (!getOwUidsList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        owUidsMemoizedSerializedSize = dataSize;
      }
      for (java.util.Map.Entry<String, String> entry
           : internalGetExtendData().getMap().entrySet()) {
        com.google.protobuf.MapEntry<String, String>
        extendData__ = ExtendDataDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(2, extendData__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof QueryContractInfoByAnchorUidsReq)) {
        return super.equals(obj);
      }
      QueryContractInfoByAnchorUidsReq other = (QueryContractInfoByAnchorUidsReq) obj;

      boolean result = true;
      result = result && getOwUidsList()
          .equals(other.getOwUidsList());
      result = result && internalGetExtendData().equals(
          other.internalGetExtendData());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getOwUidsCount() > 0) {
        hash = (37 * hash) + OWUIDS_FIELD_NUMBER;
        hash = (53 * hash) + getOwUidsList().hashCode();
      }
      if (!internalGetExtendData().getMap().isEmpty()) {
        hash = (37 * hash) + EXTENDDATA_FIELD_NUMBER;
        hash = (53 * hash) + internalGetExtendData().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static QueryContractInfoByAnchorUidsReq parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryContractInfoByAnchorUidsReq parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryContractInfoByAnchorUidsReq parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryContractInfoByAnchorUidsReq parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryContractInfoByAnchorUidsReq parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryContractInfoByAnchorUidsReq parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryContractInfoByAnchorUidsReq parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static QueryContractInfoByAnchorUidsReq parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static QueryContractInfoByAnchorUidsReq parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static QueryContractInfoByAnchorUidsReq parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static QueryContractInfoByAnchorUidsReq parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static QueryContractInfoByAnchorUidsReq parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(QueryContractInfoByAnchorUidsReq prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.yyzone.guildrank.api.QueryContractInfoByAnchorUidsReq}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.api.QueryContractInfoByAnchorUidsReq)
        QueryContractInfoByAnchorUidsReqOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ContractInfo.internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsReq_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 2:
            return internalGetExtendData();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 2:
            return internalGetMutableExtendData();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ContractInfo.internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsReq_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                QueryContractInfoByAnchorUidsReq.class, Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.api.ContractInfo.QueryContractInfoByAnchorUidsReq.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        owUids_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        internalGetMutableExtendData().clear();
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ContractInfo.internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsReq_descriptor;
      }

      @Override
      public QueryContractInfoByAnchorUidsReq getDefaultInstanceForType() {
        return QueryContractInfoByAnchorUidsReq.getDefaultInstance();
      }

      @Override
      public QueryContractInfoByAnchorUidsReq build() {
        QueryContractInfoByAnchorUidsReq result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public QueryContractInfoByAnchorUidsReq buildPartial() {
        QueryContractInfoByAnchorUidsReq result = new QueryContractInfoByAnchorUidsReq(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) == 0x00000001)) {
          owUids_ = java.util.Collections.unmodifiableList(owUids_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.owUids_ = owUids_;
        result.extendData_ = internalGetExtendData();
        result.extendData_.makeImmutable();
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof QueryContractInfoByAnchorUidsReq) {
          return mergeFrom((QueryContractInfoByAnchorUidsReq)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(QueryContractInfoByAnchorUidsReq other) {
        if (other == QueryContractInfoByAnchorUidsReq.getDefaultInstance()) return this;
        if (!other.owUids_.isEmpty()) {
          if (owUids_.isEmpty()) {
            owUids_ = other.owUids_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureOwUidsIsMutable();
            owUids_.addAll(other.owUids_);
          }
          onChanged();
        }
        internalGetMutableExtendData().mergeFrom(
            other.internalGetExtendData());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        QueryContractInfoByAnchorUidsReq parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (QueryContractInfoByAnchorUidsReq) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<Integer> owUids_ = java.util.Collections.emptyList();
      private void ensureOwUidsIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          owUids_ = new java.util.ArrayList<Integer>(owUids_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <pre>
       * 批量查询的uid集合
       * </pre>
       *
       * <code>repeated uint32 owUids = 1;</code>
       */
      public java.util.List<Integer>
          getOwUidsList() {
        return java.util.Collections.unmodifiableList(owUids_);
      }
      /**
       * <pre>
       * 批量查询的uid集合
       * </pre>
       *
       * <code>repeated uint32 owUids = 1;</code>
       */
      public int getOwUidsCount() {
        return owUids_.size();
      }
      /**
       * <pre>
       * 批量查询的uid集合
       * </pre>
       *
       * <code>repeated uint32 owUids = 1;</code>
       */
      public int getOwUids(int index) {
        return owUids_.get(index);
      }
      /**
       * <pre>
       * 批量查询的uid集合
       * </pre>
       *
       * <code>repeated uint32 owUids = 1;</code>
       */
      public Builder setOwUids(
          int index, int value) {
        ensureOwUidsIsMutable();
        owUids_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 批量查询的uid集合
       * </pre>
       *
       * <code>repeated uint32 owUids = 1;</code>
       */
      public Builder addOwUids(int value) {
        ensureOwUidsIsMutable();
        owUids_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 批量查询的uid集合
       * </pre>
       *
       * <code>repeated uint32 owUids = 1;</code>
       */
      public Builder addAllOwUids(
          Iterable<? extends Integer> values) {
        ensureOwUidsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, owUids_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 批量查询的uid集合
       * </pre>
       *
       * <code>repeated uint32 owUids = 1;</code>
       */
      public Builder clearOwUids() {
        owUids_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }

      private com.google.protobuf.MapField<
          String, String> extendData_;
      private com.google.protobuf.MapField<String, String>
      internalGetExtendData() {
        if (extendData_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ExtendDataDefaultEntryHolder.defaultEntry);
        }
        return extendData_;
      }
      private com.google.protobuf.MapField<String, String>
      internalGetMutableExtendData() {
        onChanged();;
        if (extendData_ == null) {
          extendData_ = com.google.protobuf.MapField.newMapField(
              ExtendDataDefaultEntryHolder.defaultEntry);
        }
        if (!extendData_.isMutable()) {
          extendData_ = extendData_.copy();
        }
        return extendData_;
      }

      public int getExtendDataCount() {
        return internalGetExtendData().getMap().size();
      }
      /**
       * <pre>
       * 扩展字段，暂未使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extendData = 2;</code>
       */

      public boolean containsExtendData(
          String key) {
        if (key == null) { throw new NullPointerException(); }
        return internalGetExtendData().getMap().containsKey(key);
      }
      /**
       * Use {@link #getExtendDataMap()} instead.
       */
      @Deprecated
      public java.util.Map<String, String> getExtendData() {
        return getExtendDataMap();
      }
      /**
       * <pre>
       * 扩展字段，暂未使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extendData = 2;</code>
       */

      public java.util.Map<String, String> getExtendDataMap() {
        return internalGetExtendData().getMap();
      }
      /**
       * <pre>
       * 扩展字段，暂未使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extendData = 2;</code>
       */

      public String getExtendDataOrDefault(
          String key,
          String defaultValue) {
        if (key == null) { throw new NullPointerException(); }
        java.util.Map<String, String> map =
            internalGetExtendData().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       * 扩展字段，暂未使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extendData = 2;</code>
       */

      public String getExtendDataOrThrow(
          String key) {
        if (key == null) { throw new NullPointerException(); }
        java.util.Map<String, String> map =
            internalGetExtendData().getMap();
        if (!map.containsKey(key)) {
          throw new IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearExtendData() {
        internalGetMutableExtendData().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       * 扩展字段，暂未使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extendData = 2;</code>
       */

      public Builder removeExtendData(
          String key) {
        if (key == null) { throw new NullPointerException(); }
        internalGetMutableExtendData().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @Deprecated
      public java.util.Map<String, String>
      getMutableExtendData() {
        return internalGetMutableExtendData().getMutableMap();
      }
      /**
       * <pre>
       * 扩展字段，暂未使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extendData = 2;</code>
       */
      public Builder putExtendData(
          String key,
          String value) {
        if (key == null) { throw new NullPointerException(); }
        if (value == null) { throw new NullPointerException(); }
        internalGetMutableExtendData().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       * 扩展字段，暂未使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extendData = 2;</code>
       */

      public Builder putAllExtendData(
          java.util.Map<String, String> values) {
        internalGetMutableExtendData().getMutableMap()
            .putAll(values);
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.api.QueryContractInfoByAnchorUidsReq)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.api.QueryContractInfoByAnchorUidsReq)
    private static final QueryContractInfoByAnchorUidsReq DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new QueryContractInfoByAnchorUidsReq();
    }

    public static QueryContractInfoByAnchorUidsReq getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QueryContractInfoByAnchorUidsReq>
        PARSER = new com.google.protobuf.AbstractParser<QueryContractInfoByAnchorUidsReq>() {
      @Override
      public QueryContractInfoByAnchorUidsReq parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QueryContractInfoByAnchorUidsReq(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QueryContractInfoByAnchorUidsReq> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<QueryContractInfoByAnchorUidsReq> getParserForType() {
      return PARSER;
    }

    @Override
    public QueryContractInfoByAnchorUidsReq getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryContractInfoByAnchorUidsResOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.api.QueryContractInfoByAnchorUidsRes)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 响应码    0：成功    1：失败
     * </pre>
     *
     * <code>uint32 result = 1;</code>
     */
    int getResult();

    /**
     * <pre>
     *返回信息   key：uid value:owUid
     * </pre>
     *
     * <code>map&lt;uint32, uint32&gt; data = 2;</code>
     */
    int getDataCount();
    /**
     * <pre>
     *返回信息   key：uid value:owUid
     * </pre>
     *
     * <code>map&lt;uint32, uint32&gt; data = 2;</code>
     */
    boolean containsData(
            int key);
    /**
     * Use {@link #getDataMap()} instead.
     */
    @Deprecated
    java.util.Map<Integer, Integer>
    getData();
    /**
     * <pre>
     *返回信息   key：uid value:owUid
     * </pre>
     *
     * <code>map&lt;uint32, uint32&gt; data = 2;</code>
     */
    java.util.Map<Integer, Integer>
    getDataMap();
    /**
     * <pre>
     *返回信息   key：uid value:owUid
     * </pre>
     *
     * <code>map&lt;uint32, uint32&gt; data = 2;</code>
     */

    int getDataOrDefault(
            int key,
            int defaultValue);
    /**
     * <pre>
     *返回信息   key：uid value:owUid
     * </pre>
     *
     * <code>map&lt;uint32, uint32&gt; data = 2;</code>
     */

    int getDataOrThrow(
            int key);

    /**
     * <pre>
     * 扩展字段，暂未使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extendData = 3;</code>
     */
    int getExtendDataCount();
    /**
     * <pre>
     * 扩展字段，暂未使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extendData = 3;</code>
     */
    boolean containsExtendData(
            String key);
    /**
     * Use {@link #getExtendDataMap()} instead.
     */
    @Deprecated
    java.util.Map<String, String>
    getExtendData();
    /**
     * <pre>
     * 扩展字段，暂未使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extendData = 3;</code>
     */
    java.util.Map<String, String>
    getExtendDataMap();
    /**
     * <pre>
     * 扩展字段，暂未使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extendData = 3;</code>
     */

    String getExtendDataOrDefault(
            String key,
            String defaultValue);
    /**
     * <pre>
     * 扩展字段，暂未使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extendData = 3;</code>
     */

    String getExtendDataOrThrow(
            String key);
  }
  /**
   * Protobuf type {@code com.yy.yyzone.guildrank.api.QueryContractInfoByAnchorUidsRes}
   */
  public  static final class QueryContractInfoByAnchorUidsRes extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.api.QueryContractInfoByAnchorUidsRes)
      QueryContractInfoByAnchorUidsResOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QueryContractInfoByAnchorUidsRes.newBuilder() to construct.
    private QueryContractInfoByAnchorUidsRes(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QueryContractInfoByAnchorUidsRes() {
      result_ = 0;
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QueryContractInfoByAnchorUidsRes(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              result_ = input.readUInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                data_ = com.google.protobuf.MapField.newMapField(
                    DataDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000002;
              }
              com.google.protobuf.MapEntry<Integer, Integer>
              data__ = input.readMessage(
                  DataDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              data_.getMutableMap().put(
                  data__.getKey(), data__.getValue());
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
                extendData_ = com.google.protobuf.MapField.newMapField(
                    ExtendDataDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000004;
              }
              com.google.protobuf.MapEntry<String, String>
              extendData__ = input.readMessage(
                  ExtendDataDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              extendData_.getMutableMap().put(
                  extendData__.getKey(), extendData__.getValue());
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ContractInfo.internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsRes_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 2:
          return internalGetData();
        case 3:
          return internalGetExtendData();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ContractInfo.internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsRes_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              QueryContractInfoByAnchorUidsRes.class, Builder.class);
    }

    private int bitField0_;
    public static final int RESULT_FIELD_NUMBER = 1;
    private int result_;
    /**
     * <pre>
     * 响应码    0：成功    1：失败
     * </pre>
     *
     * <code>uint32 result = 1;</code>
     */
    public int getResult() {
      return result_;
    }

    public static final int DATA_FIELD_NUMBER = 2;
    private static final class DataDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          Integer, Integer> defaultEntry =
              com.google.protobuf.MapEntry
              .<Integer, Integer>newDefaultInstance(
                  ContractInfo.internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsRes_DataEntry_descriptor,
                  com.google.protobuf.WireFormat.FieldType.UINT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.UINT32,
                  0);
    }
    private com.google.protobuf.MapField<
        Integer, Integer> data_;
    private com.google.protobuf.MapField<Integer, Integer>
    internalGetData() {
      if (data_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            DataDefaultEntryHolder.defaultEntry);
      }
      return data_;
    }

    public int getDataCount() {
      return internalGetData().getMap().size();
    }
    /**
     * <pre>
     *返回信息   key：uid value:owUid
     * </pre>
     *
     * <code>map&lt;uint32, uint32&gt; data = 2;</code>
     */

    public boolean containsData(
        int key) {
      
      return internalGetData().getMap().containsKey(key);
    }
    /**
     * Use {@link #getDataMap()} instead.
     */
    @Deprecated
    public java.util.Map<Integer, Integer> getData() {
      return getDataMap();
    }
    /**
     * <pre>
     *返回信息   key：uid value:owUid
     * </pre>
     *
     * <code>map&lt;uint32, uint32&gt; data = 2;</code>
     */

    public java.util.Map<Integer, Integer> getDataMap() {
      return internalGetData().getMap();
    }
    /**
     * <pre>
     *返回信息   key：uid value:owUid
     * </pre>
     *
     * <code>map&lt;uint32, uint32&gt; data = 2;</code>
     */

    public int getDataOrDefault(
        int key,
        int defaultValue) {
      
      java.util.Map<Integer, Integer> map =
          internalGetData().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     *返回信息   key：uid value:owUid
     * </pre>
     *
     * <code>map&lt;uint32, uint32&gt; data = 2;</code>
     */

    public int getDataOrThrow(
        int key) {
      
      java.util.Map<Integer, Integer> map =
          internalGetData().getMap();
      if (!map.containsKey(key)) {
        throw new IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int EXTENDDATA_FIELD_NUMBER = 3;
    private static final class ExtendDataDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          String, String> defaultEntry =
              com.google.protobuf.MapEntry
              .<String, String>newDefaultInstance(
                  ContractInfo.internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsRes_ExtendDataEntry_descriptor,
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "",
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "");
    }
    private com.google.protobuf.MapField<
        String, String> extendData_;
    private com.google.protobuf.MapField<String, String>
    internalGetExtendData() {
      if (extendData_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ExtendDataDefaultEntryHolder.defaultEntry);
      }
      return extendData_;
    }

    public int getExtendDataCount() {
      return internalGetExtendData().getMap().size();
    }
    /**
     * <pre>
     * 扩展字段，暂未使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extendData = 3;</code>
     */

    public boolean containsExtendData(
        String key) {
      if (key == null) { throw new NullPointerException(); }
      return internalGetExtendData().getMap().containsKey(key);
    }
    /**
     * Use {@link #getExtendDataMap()} instead.
     */
    @Deprecated
    public java.util.Map<String, String> getExtendData() {
      return getExtendDataMap();
    }
    /**
     * <pre>
     * 扩展字段，暂未使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extendData = 3;</code>
     */

    public java.util.Map<String, String> getExtendDataMap() {
      return internalGetExtendData().getMap();
    }
    /**
     * <pre>
     * 扩展字段，暂未使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extendData = 3;</code>
     */

    public String getExtendDataOrDefault(
        String key,
        String defaultValue) {
      if (key == null) { throw new NullPointerException(); }
      java.util.Map<String, String> map =
          internalGetExtendData().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     * 扩展字段，暂未使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extendData = 3;</code>
     */

    public String getExtendDataOrThrow(
        String key) {
      if (key == null) { throw new NullPointerException(); }
      java.util.Map<String, String> map =
          internalGetExtendData().getMap();
      if (!map.containsKey(key)) {
        throw new IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (result_ != 0) {
        output.writeUInt32(1, result_);
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetData(),
          DataDefaultEntryHolder.defaultEntry,
          2);
      com.google.protobuf.GeneratedMessageV3
        .serializeStringMapTo(
          output,
          internalGetExtendData(),
          ExtendDataDefaultEntryHolder.defaultEntry,
          3);
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (result_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, result_);
      }
      for (java.util.Map.Entry<Integer, Integer> entry
           : internalGetData().getMap().entrySet()) {
        com.google.protobuf.MapEntry<Integer, Integer>
        data__ = DataDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(2, data__);
      }
      for (java.util.Map.Entry<String, String> entry
           : internalGetExtendData().getMap().entrySet()) {
        com.google.protobuf.MapEntry<String, String>
        extendData__ = ExtendDataDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(3, extendData__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof QueryContractInfoByAnchorUidsRes)) {
        return super.equals(obj);
      }
      QueryContractInfoByAnchorUidsRes other = (QueryContractInfoByAnchorUidsRes) obj;

      boolean result = true;
      result = result && (getResult()
          == other.getResult());
      result = result && internalGetData().equals(
          other.internalGetData());
      result = result && internalGetExtendData().equals(
          other.internalGetExtendData());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + getResult();
      if (!internalGetData().getMap().isEmpty()) {
        hash = (37 * hash) + DATA_FIELD_NUMBER;
        hash = (53 * hash) + internalGetData().hashCode();
      }
      if (!internalGetExtendData().getMap().isEmpty()) {
        hash = (37 * hash) + EXTENDDATA_FIELD_NUMBER;
        hash = (53 * hash) + internalGetExtendData().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static QueryContractInfoByAnchorUidsRes parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryContractInfoByAnchorUidsRes parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryContractInfoByAnchorUidsRes parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryContractInfoByAnchorUidsRes parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryContractInfoByAnchorUidsRes parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryContractInfoByAnchorUidsRes parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryContractInfoByAnchorUidsRes parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static QueryContractInfoByAnchorUidsRes parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static QueryContractInfoByAnchorUidsRes parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static QueryContractInfoByAnchorUidsRes parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static QueryContractInfoByAnchorUidsRes parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static QueryContractInfoByAnchorUidsRes parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(QueryContractInfoByAnchorUidsRes prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.yyzone.guildrank.api.QueryContractInfoByAnchorUidsRes}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.api.QueryContractInfoByAnchorUidsRes)
        QueryContractInfoByAnchorUidsResOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ContractInfo.internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsRes_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 2:
            return internalGetData();
          case 3:
            return internalGetExtendData();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 2:
            return internalGetMutableData();
          case 3:
            return internalGetMutableExtendData();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ContractInfo.internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsRes_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                QueryContractInfoByAnchorUidsRes.class, Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.api.ContractInfo.QueryContractInfoByAnchorUidsRes.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        result_ = 0;

        internalGetMutableData().clear();
        internalGetMutableExtendData().clear();
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ContractInfo.internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsRes_descriptor;
      }

      @Override
      public QueryContractInfoByAnchorUidsRes getDefaultInstanceForType() {
        return QueryContractInfoByAnchorUidsRes.getDefaultInstance();
      }

      @Override
      public QueryContractInfoByAnchorUidsRes build() {
        QueryContractInfoByAnchorUidsRes result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public QueryContractInfoByAnchorUidsRes buildPartial() {
        QueryContractInfoByAnchorUidsRes result = new QueryContractInfoByAnchorUidsRes(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.result_ = result_;
        result.data_ = internalGetData();
        result.data_.makeImmutable();
        result.extendData_ = internalGetExtendData();
        result.extendData_.makeImmutable();
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof QueryContractInfoByAnchorUidsRes) {
          return mergeFrom((QueryContractInfoByAnchorUidsRes)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(QueryContractInfoByAnchorUidsRes other) {
        if (other == QueryContractInfoByAnchorUidsRes.getDefaultInstance()) return this;
        if (other.getResult() != 0) {
          setResult(other.getResult());
        }
        internalGetMutableData().mergeFrom(
            other.internalGetData());
        internalGetMutableExtendData().mergeFrom(
            other.internalGetExtendData());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        QueryContractInfoByAnchorUidsRes parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (QueryContractInfoByAnchorUidsRes) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int result_ ;
      /**
       * <pre>
       * 响应码    0：成功    1：失败
       * </pre>
       *
       * <code>uint32 result = 1;</code>
       */
      public int getResult() {
        return result_;
      }
      /**
       * <pre>
       * 响应码    0：成功    1：失败
       * </pre>
       *
       * <code>uint32 result = 1;</code>
       */
      public Builder setResult(int value) {
        
        result_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 响应码    0：成功    1：失败
       * </pre>
       *
       * <code>uint32 result = 1;</code>
       */
      public Builder clearResult() {
        
        result_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.MapField<
          Integer, Integer> data_;
      private com.google.protobuf.MapField<Integer, Integer>
      internalGetData() {
        if (data_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              DataDefaultEntryHolder.defaultEntry);
        }
        return data_;
      }
      private com.google.protobuf.MapField<Integer, Integer>
      internalGetMutableData() {
        onChanged();;
        if (data_ == null) {
          data_ = com.google.protobuf.MapField.newMapField(
              DataDefaultEntryHolder.defaultEntry);
        }
        if (!data_.isMutable()) {
          data_ = data_.copy();
        }
        return data_;
      }

      public int getDataCount() {
        return internalGetData().getMap().size();
      }
      /**
       * <pre>
       *返回信息   key：uid value:owUid
       * </pre>
       *
       * <code>map&lt;uint32, uint32&gt; data = 2;</code>
       */

      public boolean containsData(
          int key) {
        
        return internalGetData().getMap().containsKey(key);
      }
      /**
       * Use {@link #getDataMap()} instead.
       */
      @Deprecated
      public java.util.Map<Integer, Integer> getData() {
        return getDataMap();
      }
      /**
       * <pre>
       *返回信息   key：uid value:owUid
       * </pre>
       *
       * <code>map&lt;uint32, uint32&gt; data = 2;</code>
       */

      public java.util.Map<Integer, Integer> getDataMap() {
        return internalGetData().getMap();
      }
      /**
       * <pre>
       *返回信息   key：uid value:owUid
       * </pre>
       *
       * <code>map&lt;uint32, uint32&gt; data = 2;</code>
       */

      public int getDataOrDefault(
          int key,
          int defaultValue) {
        
        java.util.Map<Integer, Integer> map =
            internalGetData().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       *返回信息   key：uid value:owUid
       * </pre>
       *
       * <code>map&lt;uint32, uint32&gt; data = 2;</code>
       */

      public int getDataOrThrow(
          int key) {
        
        java.util.Map<Integer, Integer> map =
            internalGetData().getMap();
        if (!map.containsKey(key)) {
          throw new IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearData() {
        internalGetMutableData().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       *返回信息   key：uid value:owUid
       * </pre>
       *
       * <code>map&lt;uint32, uint32&gt; data = 2;</code>
       */

      public Builder removeData(
          int key) {
        
        internalGetMutableData().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @Deprecated
      public java.util.Map<Integer, Integer>
      getMutableData() {
        return internalGetMutableData().getMutableMap();
      }
      /**
       * <pre>
       *返回信息   key：uid value:owUid
       * </pre>
       *
       * <code>map&lt;uint32, uint32&gt; data = 2;</code>
       */
      public Builder putData(
          int key,
          int value) {
        
        
        internalGetMutableData().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       *返回信息   key：uid value:owUid
       * </pre>
       *
       * <code>map&lt;uint32, uint32&gt; data = 2;</code>
       */

      public Builder putAllData(
          java.util.Map<Integer, Integer> values) {
        internalGetMutableData().getMutableMap()
            .putAll(values);
        return this;
      }

      private com.google.protobuf.MapField<
          String, String> extendData_;
      private com.google.protobuf.MapField<String, String>
      internalGetExtendData() {
        if (extendData_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ExtendDataDefaultEntryHolder.defaultEntry);
        }
        return extendData_;
      }
      private com.google.protobuf.MapField<String, String>
      internalGetMutableExtendData() {
        onChanged();;
        if (extendData_ == null) {
          extendData_ = com.google.protobuf.MapField.newMapField(
              ExtendDataDefaultEntryHolder.defaultEntry);
        }
        if (!extendData_.isMutable()) {
          extendData_ = extendData_.copy();
        }
        return extendData_;
      }

      public int getExtendDataCount() {
        return internalGetExtendData().getMap().size();
      }
      /**
       * <pre>
       * 扩展字段，暂未使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extendData = 3;</code>
       */

      public boolean containsExtendData(
          String key) {
        if (key == null) { throw new NullPointerException(); }
        return internalGetExtendData().getMap().containsKey(key);
      }
      /**
       * Use {@link #getExtendDataMap()} instead.
       */
      @Deprecated
      public java.util.Map<String, String> getExtendData() {
        return getExtendDataMap();
      }
      /**
       * <pre>
       * 扩展字段，暂未使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extendData = 3;</code>
       */

      public java.util.Map<String, String> getExtendDataMap() {
        return internalGetExtendData().getMap();
      }
      /**
       * <pre>
       * 扩展字段，暂未使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extendData = 3;</code>
       */

      public String getExtendDataOrDefault(
          String key,
          String defaultValue) {
        if (key == null) { throw new NullPointerException(); }
        java.util.Map<String, String> map =
            internalGetExtendData().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       * 扩展字段，暂未使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extendData = 3;</code>
       */

      public String getExtendDataOrThrow(
          String key) {
        if (key == null) { throw new NullPointerException(); }
        java.util.Map<String, String> map =
            internalGetExtendData().getMap();
        if (!map.containsKey(key)) {
          throw new IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearExtendData() {
        internalGetMutableExtendData().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       * 扩展字段，暂未使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extendData = 3;</code>
       */

      public Builder removeExtendData(
          String key) {
        if (key == null) { throw new NullPointerException(); }
        internalGetMutableExtendData().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @Deprecated
      public java.util.Map<String, String>
      getMutableExtendData() {
        return internalGetMutableExtendData().getMutableMap();
      }
      /**
       * <pre>
       * 扩展字段，暂未使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extendData = 3;</code>
       */
      public Builder putExtendData(
          String key,
          String value) {
        if (key == null) { throw new NullPointerException(); }
        if (value == null) { throw new NullPointerException(); }
        internalGetMutableExtendData().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       * 扩展字段，暂未使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extendData = 3;</code>
       */

      public Builder putAllExtendData(
          java.util.Map<String, String> values) {
        internalGetMutableExtendData().getMutableMap()
            .putAll(values);
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.api.QueryContractInfoByAnchorUidsRes)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.api.QueryContractInfoByAnchorUidsRes)
    private static final QueryContractInfoByAnchorUidsRes DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new QueryContractInfoByAnchorUidsRes();
    }

    public static QueryContractInfoByAnchorUidsRes getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QueryContractInfoByAnchorUidsRes>
        PARSER = new com.google.protobuf.AbstractParser<QueryContractInfoByAnchorUidsRes>() {
      @Override
      public QueryContractInfoByAnchorUidsRes parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QueryContractInfoByAnchorUidsRes(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QueryContractInfoByAnchorUidsRes> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<QueryContractInfoByAnchorUidsRes> getParserForType() {
      return PARSER;
    }

    @Override
    public QueryContractInfoByAnchorUidsRes getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsReq_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsReq_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsReq_ExtendDataEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsReq_ExtendDataEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsRes_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsRes_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsRes_DataEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsRes_DataEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsRes_ExtendDataEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsRes_ExtendDataEntry_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    String[] descriptorData = {
      "\n\022contractInfo.proto\022\033com.yy.yyzone.guil" +
      "drank.api\032\turi.proto\"\317\001\n QueryContractIn" +
      "foByAnchorUidsReq\022\016\n\006owUids\030\001 \003(\r\022a\n\next" +
      "endData\030\002 \003(\0132M.com.yy.yyzone.guildrank." +
      "api.QueryContractInfoByAnchorUidsReq.Ext" +
      "endDataEntry\0321\n\017ExtendDataEntry\022\013\n\003key\030\001" +
      " \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001:\005\310>\356\306\003\"\323\002\n Query" +
      "ContractInfoByAnchorUidsRes\022\016\n\006result\030\001 " +
      "\001(\r\022U\n\004data\030\002 \003(\0132G.com.yy.yyzone.guildr" +
      "ank.api.QueryContractInfoByAnchorUidsRes" +
      ".DataEntry\022a\n\nextendData\030\003 \003(\0132M.com.yy." +
      "yyzone.guildrank.api.QueryContractInfoBy" +
      "AnchorUidsRes.ExtendDataEntry\032+\n\tDataEnt" +
      "ry\022\013\n\003key\030\001 \001(\r\022\r\n\005value\030\002 \001(\r:\0028\001\0321\n\017Ex" +
      "tendDataEntry\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(" +
      "\t:\0028\001:\005\310>\356\310\0032\265\001\n\023ContractInfoService\022\235\001\n" +
      "\035queryContractInfoByAnchorUids\022=.com.yy." +
      "yyzone.guildrank.api.QueryContractInfoBy" +
      "AnchorUidsReq\032=.com.yy.yyzone.guildrank." +
      "api.QueryContractInfoByAnchorUidsResb\006pr" +
      "oto3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          yyp.Uri.getDescriptor(),
        }, assigner);
    internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsReq_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsReq_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsReq_descriptor,
        new String[] { "OwUids", "ExtendData", });
    internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsReq_ExtendDataEntry_descriptor =
      internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsReq_descriptor.getNestedTypes().get(0);
    internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsReq_ExtendDataEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsReq_ExtendDataEntry_descriptor,
        new String[] { "Key", "Value", });
    internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsRes_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsRes_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsRes_descriptor,
        new String[] { "Result", "Data", "ExtendData", });
    internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsRes_DataEntry_descriptor =
      internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsRes_descriptor.getNestedTypes().get(0);
    internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsRes_DataEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsRes_DataEntry_descriptor,
        new String[] { "Key", "Value", });
    internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsRes_ExtendDataEntry_descriptor =
      internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsRes_descriptor.getNestedTypes().get(1);
    internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsRes_ExtendDataEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_QueryContractInfoByAnchorUidsRes_ExtendDataEntry_descriptor,
        new String[] { "Key", "Value", });
    com.google.protobuf.ExtensionRegistry registry =
        com.google.protobuf.ExtensionRegistry.newInstance();
    registry.add(yyp.Uri.uri);
    com.google.protobuf.Descriptors.FileDescriptor
        .internalUpdateFileDescriptor(descriptor, registry);
    yyp.Uri.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
