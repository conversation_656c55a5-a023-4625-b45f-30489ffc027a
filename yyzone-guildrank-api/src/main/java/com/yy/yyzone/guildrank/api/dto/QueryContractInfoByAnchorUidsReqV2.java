package com.yy.yyzone.guildrank.api.dto;

import lombok.*;

import java.util.List;
import java.util.Map;

/**
 * @author: create by jp
 * @version: v1.0
 * @description: com.yy.yyzone.contractinfo.domain
 * @date:2021/3/4
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
public class QueryContractInfoByAnchorUidsReqV2 {

    /**
     * 主播uidList
     **/
    private List<Long> uids;

    /**
     * 扩展字段
     **/
    private Map<String, String> extendData;
}
