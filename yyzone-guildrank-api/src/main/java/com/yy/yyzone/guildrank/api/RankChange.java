// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: RankChange.proto

package com.yy.yyzone.guildrank.api;

public final class RankChange {
  private RankChange() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface QueryRankChangeReqOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.api.QueryRankChangeReq)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *调用s2sName
     * </pre>
     *
     * <code>string appData = 1;</code>
     */
    String getAppData();
    /**
     * <pre>
     *调用s2sName
     * </pre>
     *
     * <code>string appData = 1;</code>
     */
    com.google.protobuf.ByteString
        getAppDataBytes();

    /**
     * <pre>
     *类型 0-海度数据 1-海度数据+段位变更计算时当月升级星级默认1星
     * </pre>
     *
     * <code>int32 type = 2;</code>
     */
    int getType();

    /**
     * <pre>
     *查询月份 yyyy-MM
     * </pre>
     *
     * <code>string month = 3;</code>
     */
    String getMonth();
    /**
     * <pre>
     *查询月份 yyyy-MM
     * </pre>
     *
     * <code>string month = 3;</code>
     */
    com.google.protobuf.ByteString
        getMonthBytes();
  }
  /**
   * Protobuf type {@code com.yy.yyzone.guildrank.api.QueryRankChangeReq}
   */
  public  static final class QueryRankChangeReq extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.api.QueryRankChangeReq)
      QueryRankChangeReqOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QueryRankChangeReq.newBuilder() to construct.
    private QueryRankChangeReq(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QueryRankChangeReq() {
      appData_ = "";
      type_ = 0;
      month_ = "";
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QueryRankChangeReq(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              String s = input.readStringRequireUtf8();

              appData_ = s;
              break;
            }
            case 16: {

              type_ = input.readInt32();
              break;
            }
            case 26: {
              String s = input.readStringRequireUtf8();

              month_ = s;
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return RankChange.internal_static_com_yy_yyzone_guildrank_api_QueryRankChangeReq_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return RankChange.internal_static_com_yy_yyzone_guildrank_api_QueryRankChangeReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              QueryRankChangeReq.class, Builder.class);
    }

    public static final int APPDATA_FIELD_NUMBER = 1;
    private volatile Object appData_;
    /**
     * <pre>
     *调用s2sName
     * </pre>
     *
     * <code>string appData = 1;</code>
     */
    public String getAppData() {
      Object ref = appData_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        appData_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *调用s2sName
     * </pre>
     *
     * <code>string appData = 1;</code>
     */
    public com.google.protobuf.ByteString
        getAppDataBytes() {
      Object ref = appData_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        appData_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TYPE_FIELD_NUMBER = 2;
    private int type_;
    /**
     * <pre>
     *类型 0-海度数据 1-海度数据+段位变更计算时当月升级星级默认1星
     * </pre>
     *
     * <code>int32 type = 2;</code>
     */
    public int getType() {
      return type_;
    }

    public static final int MONTH_FIELD_NUMBER = 3;
    private volatile Object month_;
    /**
     * <pre>
     *查询月份 yyyy-MM
     * </pre>
     *
     * <code>string month = 3;</code>
     */
    public String getMonth() {
      Object ref = month_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        month_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *查询月份 yyyy-MM
     * </pre>
     *
     * <code>string month = 3;</code>
     */
    public com.google.protobuf.ByteString
        getMonthBytes() {
      Object ref = month_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        month_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getAppDataBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, appData_);
      }
      if (type_ != 0) {
        output.writeInt32(2, type_);
      }
      if (!getMonthBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, month_);
      }
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getAppDataBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, appData_);
      }
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, type_);
      }
      if (!getMonthBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, month_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof QueryRankChangeReq)) {
        return super.equals(obj);
      }
      QueryRankChangeReq other = (QueryRankChangeReq) obj;

      boolean result = true;
      result = result && getAppData()
          .equals(other.getAppData());
      result = result && (getType()
          == other.getType());
      result = result && getMonth()
          .equals(other.getMonth());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + APPDATA_FIELD_NUMBER;
      hash = (53 * hash) + getAppData().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (37 * hash) + MONTH_FIELD_NUMBER;
      hash = (53 * hash) + getMonth().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static QueryRankChangeReq parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryRankChangeReq parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryRankChangeReq parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryRankChangeReq parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryRankChangeReq parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryRankChangeReq parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryRankChangeReq parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static QueryRankChangeReq parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static QueryRankChangeReq parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static QueryRankChangeReq parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static QueryRankChangeReq parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static QueryRankChangeReq parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(QueryRankChangeReq prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.yyzone.guildrank.api.QueryRankChangeReq}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.api.QueryRankChangeReq)
        QueryRankChangeReqOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return RankChange.internal_static_com_yy_yyzone_guildrank_api_QueryRankChangeReq_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return RankChange.internal_static_com_yy_yyzone_guildrank_api_QueryRankChangeReq_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                QueryRankChangeReq.class, Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.api.RankChange.QueryRankChangeReq.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        appData_ = "";

        type_ = 0;

        month_ = "";

        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return RankChange.internal_static_com_yy_yyzone_guildrank_api_QueryRankChangeReq_descriptor;
      }

      @Override
      public QueryRankChangeReq getDefaultInstanceForType() {
        return QueryRankChangeReq.getDefaultInstance();
      }

      @Override
      public QueryRankChangeReq build() {
        QueryRankChangeReq result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public QueryRankChangeReq buildPartial() {
        QueryRankChangeReq result = new QueryRankChangeReq(this);
        result.appData_ = appData_;
        result.type_ = type_;
        result.month_ = month_;
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof QueryRankChangeReq) {
          return mergeFrom((QueryRankChangeReq)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(QueryRankChangeReq other) {
        if (other == QueryRankChangeReq.getDefaultInstance()) return this;
        if (!other.getAppData().isEmpty()) {
          appData_ = other.appData_;
          onChanged();
        }
        if (other.getType() != 0) {
          setType(other.getType());
        }
        if (!other.getMonth().isEmpty()) {
          month_ = other.month_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        QueryRankChangeReq parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (QueryRankChangeReq) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private Object appData_ = "";
      /**
       * <pre>
       *调用s2sName
       * </pre>
       *
       * <code>string appData = 1;</code>
       */
      public String getAppData() {
        Object ref = appData_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          appData_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       *调用s2sName
       * </pre>
       *
       * <code>string appData = 1;</code>
       */
      public com.google.protobuf.ByteString
          getAppDataBytes() {
        Object ref = appData_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          appData_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *调用s2sName
       * </pre>
       *
       * <code>string appData = 1;</code>
       */
      public Builder setAppData(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        appData_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *调用s2sName
       * </pre>
       *
       * <code>string appData = 1;</code>
       */
      public Builder clearAppData() {
        
        appData_ = getDefaultInstance().getAppData();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *调用s2sName
       * </pre>
       *
       * <code>string appData = 1;</code>
       */
      public Builder setAppDataBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        appData_ = value;
        onChanged();
        return this;
      }

      private int type_ ;
      /**
       * <pre>
       *类型 0-海度数据 1-海度数据+段位变更计算时当月升级星级默认1星
       * </pre>
       *
       * <code>int32 type = 2;</code>
       */
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       *类型 0-海度数据 1-海度数据+段位变更计算时当月升级星级默认1星
       * </pre>
       *
       * <code>int32 type = 2;</code>
       */
      public Builder setType(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *类型 0-海度数据 1-海度数据+段位变更计算时当月升级星级默认1星
       * </pre>
       *
       * <code>int32 type = 2;</code>
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }

      private Object month_ = "";
      /**
       * <pre>
       *查询月份 yyyy-MM
       * </pre>
       *
       * <code>string month = 3;</code>
       */
      public String getMonth() {
        Object ref = month_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          month_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       *查询月份 yyyy-MM
       * </pre>
       *
       * <code>string month = 3;</code>
       */
      public com.google.protobuf.ByteString
          getMonthBytes() {
        Object ref = month_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          month_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *查询月份 yyyy-MM
       * </pre>
       *
       * <code>string month = 3;</code>
       */
      public Builder setMonth(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        month_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *查询月份 yyyy-MM
       * </pre>
       *
       * <code>string month = 3;</code>
       */
      public Builder clearMonth() {
        
        month_ = getDefaultInstance().getMonth();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *查询月份 yyyy-MM
       * </pre>
       *
       * <code>string month = 3;</code>
       */
      public Builder setMonthBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        month_ = value;
        onChanged();
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.api.QueryRankChangeReq)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.api.QueryRankChangeReq)
    private static final QueryRankChangeReq DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new QueryRankChangeReq();
    }

    public static QueryRankChangeReq getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QueryRankChangeReq>
        PARSER = new com.google.protobuf.AbstractParser<QueryRankChangeReq>() {
      @Override
      public QueryRankChangeReq parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QueryRankChangeReq(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QueryRankChangeReq> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<QueryRankChangeReq> getParserForType() {
      return PARSER;
    }

    @Override
    public QueryRankChangeReq getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryRankChangeResOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.api.QueryRankChangeRes)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *返回结果 0-成功 1-当月段位未更新
     * </pre>
     *
     * <code>int32 result = 1;</code>
     */
    int getResult();

    /**
     * <pre>
     *段位
     * </pre>
     *
     * <code>map&lt;int64, int32&gt; rankMap = 2;</code>
     */
    int getRankMapCount();
    /**
     * <pre>
     *段位
     * </pre>
     *
     * <code>map&lt;int64, int32&gt; rankMap = 2;</code>
     */
    boolean containsRankMap(
        long key);
    /**
     * Use {@link #getRankMapMap()} instead.
     */
    @Deprecated
    java.util.Map<Long, Integer>
    getRankMap();
    /**
     * <pre>
     *段位
     * </pre>
     *
     * <code>map&lt;int64, int32&gt; rankMap = 2;</code>
     */
    java.util.Map<Long, Integer>
    getRankMapMap();
    /**
     * <pre>
     *段位
     * </pre>
     *
     * <code>map&lt;int64, int32&gt; rankMap = 2;</code>
     */

    int getRankMapOrDefault(
        long key,
        int defaultValue);
    /**
     * <pre>
     *段位
     * </pre>
     *
     * <code>map&lt;int64, int32&gt; rankMap = 2;</code>
     */

    int getRankMapOrThrow(
        long key);

    /**
     * <pre>
     *当月段位比上月变更值
     * </pre>
     *
     * <code>map&lt;int64, int32&gt; changeMap = 3;</code>
     */
    int getChangeMapCount();
    /**
     * <pre>
     *当月段位比上月变更值
     * </pre>
     *
     * <code>map&lt;int64, int32&gt; changeMap = 3;</code>
     */
    boolean containsChangeMap(
        long key);
    /**
     * Use {@link #getChangeMapMap()} instead.
     */
    @Deprecated
    java.util.Map<Long, Integer>
    getChangeMap();
    /**
     * <pre>
     *当月段位比上月变更值
     * </pre>
     *
     * <code>map&lt;int64, int32&gt; changeMap = 3;</code>
     */
    java.util.Map<Long, Integer>
    getChangeMapMap();
    /**
     * <pre>
     *当月段位比上月变更值
     * </pre>
     *
     * <code>map&lt;int64, int32&gt; changeMap = 3;</code>
     */

    int getChangeMapOrDefault(
        long key,
        int defaultValue);
    /**
     * <pre>
     *当月段位比上月变更值
     * </pre>
     *
     * <code>map&lt;int64, int32&gt; changeMap = 3;</code>
     */

    int getChangeMapOrThrow(
        long key);
  }
  /**
   * Protobuf type {@code com.yy.yyzone.guildrank.api.QueryRankChangeRes}
   */
  public  static final class QueryRankChangeRes extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.api.QueryRankChangeRes)
      QueryRankChangeResOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QueryRankChangeRes.newBuilder() to construct.
    private QueryRankChangeRes(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QueryRankChangeRes() {
      result_ = 0;
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QueryRankChangeRes(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              result_ = input.readInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                rankMap_ = com.google.protobuf.MapField.newMapField(
                    RankMapDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000002;
              }
              com.google.protobuf.MapEntry<Long, Integer>
              rankMap__ = input.readMessage(
                  RankMapDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              rankMap_.getMutableMap().put(
                  rankMap__.getKey(), rankMap__.getValue());
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
                changeMap_ = com.google.protobuf.MapField.newMapField(
                    ChangeMapDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000004;
              }
              com.google.protobuf.MapEntry<Long, Integer>
              changeMap__ = input.readMessage(
                  ChangeMapDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              changeMap_.getMutableMap().put(
                  changeMap__.getKey(), changeMap__.getValue());
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return RankChange.internal_static_com_yy_yyzone_guildrank_api_QueryRankChangeRes_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 2:
          return internalGetRankMap();
        case 3:
          return internalGetChangeMap();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return RankChange.internal_static_com_yy_yyzone_guildrank_api_QueryRankChangeRes_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              QueryRankChangeRes.class, Builder.class);
    }

    private int bitField0_;
    public static final int RESULT_FIELD_NUMBER = 1;
    private int result_;
    /**
     * <pre>
     *返回结果 0-成功 1-当月段位未更新
     * </pre>
     *
     * <code>int32 result = 1;</code>
     */
    public int getResult() {
      return result_;
    }

    public static final int RANKMAP_FIELD_NUMBER = 2;
    private static final class RankMapDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          Long, Integer> defaultEntry =
              com.google.protobuf.MapEntry
              .<Long, Integer>newDefaultInstance(
                  RankChange.internal_static_com_yy_yyzone_guildrank_api_QueryRankChangeRes_RankMapEntry_descriptor,
                  com.google.protobuf.WireFormat.FieldType.INT64,
                  0L,
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0);
    }
    private com.google.protobuf.MapField<
        Long, Integer> rankMap_;
    private com.google.protobuf.MapField<Long, Integer>
    internalGetRankMap() {
      if (rankMap_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            RankMapDefaultEntryHolder.defaultEntry);
      }
      return rankMap_;
    }

    public int getRankMapCount() {
      return internalGetRankMap().getMap().size();
    }
    /**
     * <pre>
     *段位
     * </pre>
     *
     * <code>map&lt;int64, int32&gt; rankMap = 2;</code>
     */

    public boolean containsRankMap(
        long key) {
      
      return internalGetRankMap().getMap().containsKey(key);
    }
    /**
     * Use {@link #getRankMapMap()} instead.
     */
    @Deprecated
    public java.util.Map<Long, Integer> getRankMap() {
      return getRankMapMap();
    }
    /**
     * <pre>
     *段位
     * </pre>
     *
     * <code>map&lt;int64, int32&gt; rankMap = 2;</code>
     */

    public java.util.Map<Long, Integer> getRankMapMap() {
      return internalGetRankMap().getMap();
    }
    /**
     * <pre>
     *段位
     * </pre>
     *
     * <code>map&lt;int64, int32&gt; rankMap = 2;</code>
     */

    public int getRankMapOrDefault(
        long key,
        int defaultValue) {
      
      java.util.Map<Long, Integer> map =
          internalGetRankMap().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     *段位
     * </pre>
     *
     * <code>map&lt;int64, int32&gt; rankMap = 2;</code>
     */

    public int getRankMapOrThrow(
        long key) {
      
      java.util.Map<Long, Integer> map =
          internalGetRankMap().getMap();
      if (!map.containsKey(key)) {
        throw new IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int CHANGEMAP_FIELD_NUMBER = 3;
    private static final class ChangeMapDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          Long, Integer> defaultEntry =
              com.google.protobuf.MapEntry
              .<Long, Integer>newDefaultInstance(
                  RankChange.internal_static_com_yy_yyzone_guildrank_api_QueryRankChangeRes_ChangeMapEntry_descriptor,
                  com.google.protobuf.WireFormat.FieldType.INT64,
                  0L,
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0);
    }
    private com.google.protobuf.MapField<
        Long, Integer> changeMap_;
    private com.google.protobuf.MapField<Long, Integer>
    internalGetChangeMap() {
      if (changeMap_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ChangeMapDefaultEntryHolder.defaultEntry);
      }
      return changeMap_;
    }

    public int getChangeMapCount() {
      return internalGetChangeMap().getMap().size();
    }
    /**
     * <pre>
     *当月段位比上月变更值
     * </pre>
     *
     * <code>map&lt;int64, int32&gt; changeMap = 3;</code>
     */

    public boolean containsChangeMap(
        long key) {
      
      return internalGetChangeMap().getMap().containsKey(key);
    }
    /**
     * Use {@link #getChangeMapMap()} instead.
     */
    @Deprecated
    public java.util.Map<Long, Integer> getChangeMap() {
      return getChangeMapMap();
    }
    /**
     * <pre>
     *当月段位比上月变更值
     * </pre>
     *
     * <code>map&lt;int64, int32&gt; changeMap = 3;</code>
     */

    public java.util.Map<Long, Integer> getChangeMapMap() {
      return internalGetChangeMap().getMap();
    }
    /**
     * <pre>
     *当月段位比上月变更值
     * </pre>
     *
     * <code>map&lt;int64, int32&gt; changeMap = 3;</code>
     */

    public int getChangeMapOrDefault(
        long key,
        int defaultValue) {
      
      java.util.Map<Long, Integer> map =
          internalGetChangeMap().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     *当月段位比上月变更值
     * </pre>
     *
     * <code>map&lt;int64, int32&gt; changeMap = 3;</code>
     */

    public int getChangeMapOrThrow(
        long key) {
      
      java.util.Map<Long, Integer> map =
          internalGetChangeMap().getMap();
      if (!map.containsKey(key)) {
        throw new IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (result_ != 0) {
        output.writeInt32(1, result_);
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeLongMapTo(
          output,
          internalGetRankMap(),
          RankMapDefaultEntryHolder.defaultEntry,
          2);
      com.google.protobuf.GeneratedMessageV3
        .serializeLongMapTo(
          output,
          internalGetChangeMap(),
          ChangeMapDefaultEntryHolder.defaultEntry,
          3);
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (result_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, result_);
      }
      for (java.util.Map.Entry<Long, Integer> entry
           : internalGetRankMap().getMap().entrySet()) {
        com.google.protobuf.MapEntry<Long, Integer>
        rankMap__ = RankMapDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(2, rankMap__);
      }
      for (java.util.Map.Entry<Long, Integer> entry
           : internalGetChangeMap().getMap().entrySet()) {
        com.google.protobuf.MapEntry<Long, Integer>
        changeMap__ = ChangeMapDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(3, changeMap__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof QueryRankChangeRes)) {
        return super.equals(obj);
      }
      QueryRankChangeRes other = (QueryRankChangeRes) obj;

      boolean result = true;
      result = result && (getResult()
          == other.getResult());
      result = result && internalGetRankMap().equals(
          other.internalGetRankMap());
      result = result && internalGetChangeMap().equals(
          other.internalGetChangeMap());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + getResult();
      if (!internalGetRankMap().getMap().isEmpty()) {
        hash = (37 * hash) + RANKMAP_FIELD_NUMBER;
        hash = (53 * hash) + internalGetRankMap().hashCode();
      }
      if (!internalGetChangeMap().getMap().isEmpty()) {
        hash = (37 * hash) + CHANGEMAP_FIELD_NUMBER;
        hash = (53 * hash) + internalGetChangeMap().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static QueryRankChangeRes parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryRankChangeRes parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryRankChangeRes parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryRankChangeRes parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryRankChangeRes parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryRankChangeRes parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryRankChangeRes parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static QueryRankChangeRes parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static QueryRankChangeRes parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static QueryRankChangeRes parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static QueryRankChangeRes parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static QueryRankChangeRes parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(QueryRankChangeRes prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.yyzone.guildrank.api.QueryRankChangeRes}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.api.QueryRankChangeRes)
        QueryRankChangeResOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return RankChange.internal_static_com_yy_yyzone_guildrank_api_QueryRankChangeRes_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 2:
            return internalGetRankMap();
          case 3:
            return internalGetChangeMap();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 2:
            return internalGetMutableRankMap();
          case 3:
            return internalGetMutableChangeMap();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return RankChange.internal_static_com_yy_yyzone_guildrank_api_QueryRankChangeRes_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                QueryRankChangeRes.class, Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.api.RankChange.QueryRankChangeRes.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        result_ = 0;

        internalGetMutableRankMap().clear();
        internalGetMutableChangeMap().clear();
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return RankChange.internal_static_com_yy_yyzone_guildrank_api_QueryRankChangeRes_descriptor;
      }

      @Override
      public QueryRankChangeRes getDefaultInstanceForType() {
        return QueryRankChangeRes.getDefaultInstance();
      }

      @Override
      public QueryRankChangeRes build() {
        QueryRankChangeRes result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public QueryRankChangeRes buildPartial() {
        QueryRankChangeRes result = new QueryRankChangeRes(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.result_ = result_;
        result.rankMap_ = internalGetRankMap();
        result.rankMap_.makeImmutable();
        result.changeMap_ = internalGetChangeMap();
        result.changeMap_.makeImmutable();
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof QueryRankChangeRes) {
          return mergeFrom((QueryRankChangeRes)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(QueryRankChangeRes other) {
        if (other == QueryRankChangeRes.getDefaultInstance()) return this;
        if (other.getResult() != 0) {
          setResult(other.getResult());
        }
        internalGetMutableRankMap().mergeFrom(
            other.internalGetRankMap());
        internalGetMutableChangeMap().mergeFrom(
            other.internalGetChangeMap());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        QueryRankChangeRes parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (QueryRankChangeRes) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int result_ ;
      /**
       * <pre>
       *返回结果 0-成功 1-当月段位未更新
       * </pre>
       *
       * <code>int32 result = 1;</code>
       */
      public int getResult() {
        return result_;
      }
      /**
       * <pre>
       *返回结果 0-成功 1-当月段位未更新
       * </pre>
       *
       * <code>int32 result = 1;</code>
       */
      public Builder setResult(int value) {
        
        result_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *返回结果 0-成功 1-当月段位未更新
       * </pre>
       *
       * <code>int32 result = 1;</code>
       */
      public Builder clearResult() {
        
        result_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.MapField<
          Long, Integer> rankMap_;
      private com.google.protobuf.MapField<Long, Integer>
      internalGetRankMap() {
        if (rankMap_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              RankMapDefaultEntryHolder.defaultEntry);
        }
        return rankMap_;
      }
      private com.google.protobuf.MapField<Long, Integer>
      internalGetMutableRankMap() {
        onChanged();;
        if (rankMap_ == null) {
          rankMap_ = com.google.protobuf.MapField.newMapField(
              RankMapDefaultEntryHolder.defaultEntry);
        }
        if (!rankMap_.isMutable()) {
          rankMap_ = rankMap_.copy();
        }
        return rankMap_;
      }

      public int getRankMapCount() {
        return internalGetRankMap().getMap().size();
      }
      /**
       * <pre>
       *段位
       * </pre>
       *
       * <code>map&lt;int64, int32&gt; rankMap = 2;</code>
       */

      public boolean containsRankMap(
          long key) {
        
        return internalGetRankMap().getMap().containsKey(key);
      }
      /**
       * Use {@link #getRankMapMap()} instead.
       */
      @Deprecated
      public java.util.Map<Long, Integer> getRankMap() {
        return getRankMapMap();
      }
      /**
       * <pre>
       *段位
       * </pre>
       *
       * <code>map&lt;int64, int32&gt; rankMap = 2;</code>
       */

      public java.util.Map<Long, Integer> getRankMapMap() {
        return internalGetRankMap().getMap();
      }
      /**
       * <pre>
       *段位
       * </pre>
       *
       * <code>map&lt;int64, int32&gt; rankMap = 2;</code>
       */

      public int getRankMapOrDefault(
          long key,
          int defaultValue) {
        
        java.util.Map<Long, Integer> map =
            internalGetRankMap().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       *段位
       * </pre>
       *
       * <code>map&lt;int64, int32&gt; rankMap = 2;</code>
       */

      public int getRankMapOrThrow(
          long key) {
        
        java.util.Map<Long, Integer> map =
            internalGetRankMap().getMap();
        if (!map.containsKey(key)) {
          throw new IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearRankMap() {
        internalGetMutableRankMap().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       *段位
       * </pre>
       *
       * <code>map&lt;int64, int32&gt; rankMap = 2;</code>
       */

      public Builder removeRankMap(
          long key) {
        
        internalGetMutableRankMap().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @Deprecated
      public java.util.Map<Long, Integer>
      getMutableRankMap() {
        return internalGetMutableRankMap().getMutableMap();
      }
      /**
       * <pre>
       *段位
       * </pre>
       *
       * <code>map&lt;int64, int32&gt; rankMap = 2;</code>
       */
      public Builder putRankMap(
          long key,
          int value) {
        
        
        internalGetMutableRankMap().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       *段位
       * </pre>
       *
       * <code>map&lt;int64, int32&gt; rankMap = 2;</code>
       */

      public Builder putAllRankMap(
          java.util.Map<Long, Integer> values) {
        internalGetMutableRankMap().getMutableMap()
            .putAll(values);
        return this;
      }

      private com.google.protobuf.MapField<
          Long, Integer> changeMap_;
      private com.google.protobuf.MapField<Long, Integer>
      internalGetChangeMap() {
        if (changeMap_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ChangeMapDefaultEntryHolder.defaultEntry);
        }
        return changeMap_;
      }
      private com.google.protobuf.MapField<Long, Integer>
      internalGetMutableChangeMap() {
        onChanged();;
        if (changeMap_ == null) {
          changeMap_ = com.google.protobuf.MapField.newMapField(
              ChangeMapDefaultEntryHolder.defaultEntry);
        }
        if (!changeMap_.isMutable()) {
          changeMap_ = changeMap_.copy();
        }
        return changeMap_;
      }

      public int getChangeMapCount() {
        return internalGetChangeMap().getMap().size();
      }
      /**
       * <pre>
       *当月段位比上月变更值
       * </pre>
       *
       * <code>map&lt;int64, int32&gt; changeMap = 3;</code>
       */

      public boolean containsChangeMap(
          long key) {
        
        return internalGetChangeMap().getMap().containsKey(key);
      }
      /**
       * Use {@link #getChangeMapMap()} instead.
       */
      @Deprecated
      public java.util.Map<Long, Integer> getChangeMap() {
        return getChangeMapMap();
      }
      /**
       * <pre>
       *当月段位比上月变更值
       * </pre>
       *
       * <code>map&lt;int64, int32&gt; changeMap = 3;</code>
       */

      public java.util.Map<Long, Integer> getChangeMapMap() {
        return internalGetChangeMap().getMap();
      }
      /**
       * <pre>
       *当月段位比上月变更值
       * </pre>
       *
       * <code>map&lt;int64, int32&gt; changeMap = 3;</code>
       */

      public int getChangeMapOrDefault(
          long key,
          int defaultValue) {
        
        java.util.Map<Long, Integer> map =
            internalGetChangeMap().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       *当月段位比上月变更值
       * </pre>
       *
       * <code>map&lt;int64, int32&gt; changeMap = 3;</code>
       */

      public int getChangeMapOrThrow(
          long key) {
        
        java.util.Map<Long, Integer> map =
            internalGetChangeMap().getMap();
        if (!map.containsKey(key)) {
          throw new IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearChangeMap() {
        internalGetMutableChangeMap().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       *当月段位比上月变更值
       * </pre>
       *
       * <code>map&lt;int64, int32&gt; changeMap = 3;</code>
       */

      public Builder removeChangeMap(
          long key) {
        
        internalGetMutableChangeMap().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @Deprecated
      public java.util.Map<Long, Integer>
      getMutableChangeMap() {
        return internalGetMutableChangeMap().getMutableMap();
      }
      /**
       * <pre>
       *当月段位比上月变更值
       * </pre>
       *
       * <code>map&lt;int64, int32&gt; changeMap = 3;</code>
       */
      public Builder putChangeMap(
          long key,
          int value) {
        
        
        internalGetMutableChangeMap().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       *当月段位比上月变更值
       * </pre>
       *
       * <code>map&lt;int64, int32&gt; changeMap = 3;</code>
       */

      public Builder putAllChangeMap(
          java.util.Map<Long, Integer> values) {
        internalGetMutableChangeMap().getMutableMap()
            .putAll(values);
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.api.QueryRankChangeRes)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.api.QueryRankChangeRes)
    private static final QueryRankChangeRes DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new QueryRankChangeRes();
    }

    public static QueryRankChangeRes getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QueryRankChangeRes>
        PARSER = new com.google.protobuf.AbstractParser<QueryRankChangeRes>() {
      @Override
      public QueryRankChangeRes parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QueryRankChangeRes(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QueryRankChangeRes> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<QueryRankChangeRes> getParserForType() {
      return PARSER;
    }

    @Override
    public QueryRankChangeRes getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_QueryRankChangeReq_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_QueryRankChangeReq_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_QueryRankChangeRes_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_QueryRankChangeRes_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_QueryRankChangeRes_RankMapEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_QueryRankChangeRes_RankMapEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_QueryRankChangeRes_ChangeMapEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_QueryRankChangeRes_ChangeMapEntry_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    String[] descriptorData = {
      "\n\020RankChange.proto\022\033com.yy.yyzone.guildr" +
      "ank.api\"B\n\022QueryRankChangeReq\022\017\n\007appData" +
      "\030\001 \001(\t\022\014\n\004type\030\002 \001(\005\022\r\n\005month\030\003 \001(\t\"\250\002\n\022" +
      "QueryRankChangeRes\022\016\n\006result\030\001 \001(\005\022M\n\007ra" +
      "nkMap\030\002 \003(\0132<.com.yy.yyzone.guildrank.ap" +
      "i.QueryRankChangeRes.RankMapEntry\022Q\n\tcha" +
      "ngeMap\030\003 \003(\0132>.com.yy.yyzone.guildrank.a" +
      "pi.QueryRankChangeRes.ChangeMapEntry\032.\n\014" +
      "RankMapEntry\022\013\n\003key\030\001 \001(\003\022\r\n\005value\030\002 \001(\005" +
      ":\0028\001\0320\n\016ChangeMapEntry\022\013\n\003key\030\001 \001(\003\022\r\n\005v" +
      "alue\030\002 \001(\005:\0028\0012\210\001\n\021RankChangeService\022s\n\017" +
      "queryRankChange\022/.com.yy.yyzone.guildran" +
      "k.api.QueryRankChangeReq\032/.com.yy.yyzone" +
      ".guildrank.api.QueryRankChangeResb\006proto" +
      "3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
    internal_static_com_yy_yyzone_guildrank_api_QueryRankChangeReq_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yy_yyzone_guildrank_api_QueryRankChangeReq_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_QueryRankChangeReq_descriptor,
        new String[] { "AppData", "Type", "Month", });
    internal_static_com_yy_yyzone_guildrank_api_QueryRankChangeRes_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yy_yyzone_guildrank_api_QueryRankChangeRes_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_QueryRankChangeRes_descriptor,
        new String[] { "Result", "RankMap", "ChangeMap", });
    internal_static_com_yy_yyzone_guildrank_api_QueryRankChangeRes_RankMapEntry_descriptor =
      internal_static_com_yy_yyzone_guildrank_api_QueryRankChangeRes_descriptor.getNestedTypes().get(0);
    internal_static_com_yy_yyzone_guildrank_api_QueryRankChangeRes_RankMapEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_QueryRankChangeRes_RankMapEntry_descriptor,
        new String[] { "Key", "Value", });
    internal_static_com_yy_yyzone_guildrank_api_QueryRankChangeRes_ChangeMapEntry_descriptor =
      internal_static_com_yy_yyzone_guildrank_api_QueryRankChangeRes_descriptor.getNestedTypes().get(1);
    internal_static_com_yy_yyzone_guildrank_api_QueryRankChangeRes_ChangeMapEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_QueryRankChangeRes_ChangeMapEntry_descriptor,
        new String[] { "Key", "Value", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
