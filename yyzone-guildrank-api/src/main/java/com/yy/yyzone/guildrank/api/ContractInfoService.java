package com.yy.yyzone.guildrank.api;

import com.yy.yyzone.guildrank.api.dto.QueryContractInfoByAnchorUidsReqV2;
import com.yy.yyzone.guildrank.api.dto.QueryContractInfoByAnchorUidsResV2;
import org.apache.dubbo.common.annotation.Yrpc;

public interface ContractInfoService {

    @Yrpc(functionName = "queryContractInfoByAnchorUids", reqUri = 227 << 8 | 111, resUri = 228 << 8 | 111)
    QueryContractInfoByAnchorUidsResV2 queryContractInfoByAnchorUids(QueryContractInfoByAnchorUidsReqV2 req);

}
