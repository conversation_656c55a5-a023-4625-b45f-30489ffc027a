package com.yy.yyzone.guildrank.api;

import com.yy.yyzone.guildrank.api.GuildRank.*;
import org.apache.dubbo.common.annotation.Yrpc;

public interface GuildrankYYPService {

	@Yrpc(functionName="queryCurrentRank",reqUri=101 << 8 | 110 ,resUri=102 << 8 | 110 )
	CurrentRankResp queryCurrentRank(CurrentRankReq req);

	@Yrpc(functionName="queryCurrentRankTest",reqUri=103 << 8 | 110 ,resUri=104 << 8 | 110 )
	CurrentRankResp queryCurrentRankTest(CurrentRankReq req);

	@Yrpc(functionName="queryCurrentRankByFrom",reqUri=105  << 8 | 110 ,resUri=106 << 8 | 110 )
	QueryCurrentRankResp queryCurrentRankByFrom(QueryCurrentRankReq req );

	@Yrpc(functionName="testPojoYYP",reqUri=107 << 8 | 110 ,resUri=108 << 8 | 110 )
	YYPResp testPojoYYP(YYPReq req);

	/**
	 * 使用109 << 8 | 111
	 * @param req
	 * @return
	 */
    @Yrpc(functionName="batchQueryCurrentRank",reqUri=109 << 8 | 110 ,resUri=110 << 8 | 110 )
	@Deprecated
    BatchQueryCurrentRankResp batchQueryCurrentRank(BatchQueryCurrentRankReq req);

	/**
	 * 使用111 << 8 | 111
	 * @param req
	 * @return
	 */
	@Yrpc(functionName="batchQueryGuildRank",reqUri=111 << 8 | 110 ,resUri=112 << 8 | 110 )
	@Deprecated
	BatchQueryGuildRankResp batchQueryGuildRank(BatchQueryGuildRankReq req);

	/**
	 * 使用113 << 8 | 111
	 * 按条件排序返回公会uid
	 * @param req
	 * @return
	 */
	@Yrpc(functionName="queryAllGuildRankByOrder",reqUri=113 << 8 | 110 ,resUri=114 << 8 | 110 )
	@Deprecated
	AllGuildRank.QueryAllGuildRankByOrderResp queryAllGuildRankByOrder(AllGuildRank.QueryAllGuildRankByOrderReq req);

	/**
	 * 返回所有公会段位
	 *
	 * @param req
	 * @return
	 */
	@Yrpc(functionName = "queryAllGuildRankByOrder", reqUri = 115 << 8 | 110, resUri = 116 << 8 | 110)
	AllGuildRank.QueryAllGuildRankResp queryAllGuildRank(AllGuildRank.QueryAllGuildRankReq req);

	@Yrpc(functionName="batchQueryCurrentRank",reqUri=109 << 8 | 111 ,resUri=110 << 8 | 111 )
	GuildRankI64.BatchQueryCurrentRankResp64 batchQueryCurrentRankNew(GuildRankI64.BatchQueryCurrentRankReq64 req);

	@Yrpc(functionName="batchQueryGuildRank",reqUri=111 << 8 | 111 ,resUri=112 << 8 | 111 )
	GuildRankI64.BatchQueryGuildRankResp64 batchQueryGuildRankNew(GuildRankI64.BatchQueryGuildRankReq64 req);

	/**
	 * 按条件排序返回公会uid
	 * @param req
	 * @return
	 */
	@Yrpc(functionName="queryAllGuildRankByOrderNew",reqUri=113 << 8 | 111 ,resUri=114 << 8 | 111 )
	AllGuildRankI64.QueryAllGuildRankByOrderResp queryAllGuildRankByOrderNew(AllGuildRankI64.QueryAllGuildRankByOrderReq req);

}
