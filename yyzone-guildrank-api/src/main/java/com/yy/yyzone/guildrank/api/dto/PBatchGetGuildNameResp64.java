package com.yy.yyzone.guildrank.api.dto;

import com.yy.ent.commons.protopack.util.Uint;
import com.yy.ent.commons.protopack.util.Ulong;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2019/9/16
 */
public class PBatchGetGuildNameResp64 {
    private Uint result;
    private Map<Ulong, String> nameInfo;
    private Map<String, String> extendData;

    public Uint getResult() {
        return result;
    }

    public void setResult(Uint result) {
        this.result = result;
    }

    public Map<Ulong, String> getNameInfo() {
        return nameInfo;
    }

    public void setNameInfo(Map<Ulong, String> nameInfo) {
        this.nameInfo = nameInfo;
    }

    public Map<String, String> getExtendData() {
        return extendData;
    }

    public void setExtendData(Map<String, String> extendData) {
        this.extendData = extendData;
    }

    @Override
    public String toString() {
        return "PBatchGetGuildNameResp64{" +
                "result=" + result +
                ", nameInfo=" + nameInfo +
                ", extendData=" + extendData +
                '}';
    }
}
