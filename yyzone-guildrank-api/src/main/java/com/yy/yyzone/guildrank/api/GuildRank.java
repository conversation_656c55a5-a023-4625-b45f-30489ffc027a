// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: GuildRank.proto

package com.yy.yyzone.guildrank.api;

public final class GuildRank {
  private GuildRank() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface QueryGuildRankReqOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.api.QueryGuildRankReq)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated uint64 owUids = 1;</code>
     */
    java.util.List<Long> getOwUidsList();
    /**
     * <code>repeated uint64 owUids = 1;</code>
     */
    int getOwUidsCount();
    /**
     * <code>repeated uint64 owUids = 1;</code>
     */
    long getOwUids(int index);

    /**
     * <pre>
     * 2019-07
     * </pre>
     *
     * <code>string month = 2;</code>
     */
    String getMonth();
    /**
     * <pre>
     * 2019-07
     * </pre>
     *
     * <code>string month = 2;</code>
     */
    com.google.protobuf.ByteString
        getMonthBytes();
  }
  /**
   * Protobuf type {@code com.yy.yyzone.guildrank.api.QueryGuildRankReq}
   */
  public  static final class QueryGuildRankReq extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.api.QueryGuildRankReq)
      QueryGuildRankReqOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QueryGuildRankReq.newBuilder() to construct.
    private QueryGuildRankReq(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QueryGuildRankReq() {
      owUids_ = java.util.Collections.emptyList();
      month_ = "";
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QueryGuildRankReq(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                owUids_ = new java.util.ArrayList<Long>();
                mutable_bitField0_ |= 0x00000001;
              }
              owUids_.add(input.readUInt64());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001) && input.getBytesUntilLimit() > 0) {
                owUids_ = new java.util.ArrayList<Long>();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                owUids_.add(input.readUInt64());
              }
              input.popLimit(limit);
              break;
            }
            case 18: {
              String s = input.readStringRequireUtf8();

              month_ = s;
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          owUids_ = java.util.Collections.unmodifiableList(owUids_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return GuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryGuildRankReq_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return GuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryGuildRankReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              QueryGuildRankReq.class, Builder.class);
    }

    private int bitField0_;
    public static final int OWUIDS_FIELD_NUMBER = 1;
    private java.util.List<Long> owUids_;
    /**
     * <code>repeated uint64 owUids = 1;</code>
     */
    public java.util.List<Long>
        getOwUidsList() {
      return owUids_;
    }
    /**
     * <code>repeated uint64 owUids = 1;</code>
     */
    public int getOwUidsCount() {
      return owUids_.size();
    }
    /**
     * <code>repeated uint64 owUids = 1;</code>
     */
    public long getOwUids(int index) {
      return owUids_.get(index);
    }
    private int owUidsMemoizedSerializedSize = -1;

    public static final int MONTH_FIELD_NUMBER = 2;
    private volatile Object month_;
    /**
     * <pre>
     * 2019-07
     * </pre>
     *
     * <code>string month = 2;</code>
     */
    public String getMonth() {
      Object ref = month_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        month_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 2019-07
     * </pre>
     *
     * <code>string month = 2;</code>
     */
    public com.google.protobuf.ByteString
        getMonthBytes() {
      Object ref = month_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        month_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (getOwUidsList().size() > 0) {
        output.writeUInt32NoTag(10);
        output.writeUInt32NoTag(owUidsMemoizedSerializedSize);
      }
      for (int i = 0; i < owUids_.size(); i++) {
        output.writeUInt64NoTag(owUids_.get(i));
      }
      if (!getMonthBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, month_);
      }
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < owUids_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt64SizeNoTag(owUids_.get(i));
        }
        size += dataSize;
        if (!getOwUidsList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        owUidsMemoizedSerializedSize = dataSize;
      }
      if (!getMonthBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, month_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof QueryGuildRankReq)) {
        return super.equals(obj);
      }
      QueryGuildRankReq other = (QueryGuildRankReq) obj;

      boolean result = true;
      result = result && getOwUidsList()
          .equals(other.getOwUidsList());
      result = result && getMonth()
          .equals(other.getMonth());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getOwUidsCount() > 0) {
        hash = (37 * hash) + OWUIDS_FIELD_NUMBER;
        hash = (53 * hash) + getOwUidsList().hashCode();
      }
      hash = (37 * hash) + MONTH_FIELD_NUMBER;
      hash = (53 * hash) + getMonth().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static QueryGuildRankReq parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryGuildRankReq parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryGuildRankReq parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryGuildRankReq parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryGuildRankReq parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryGuildRankReq parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryGuildRankReq parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static QueryGuildRankReq parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static QueryGuildRankReq parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static QueryGuildRankReq parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static QueryGuildRankReq parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static QueryGuildRankReq parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(QueryGuildRankReq prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.yyzone.guildrank.api.QueryGuildRankReq}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.api.QueryGuildRankReq)
        QueryGuildRankReqOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryGuildRankReq_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryGuildRankReq_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                QueryGuildRankReq.class, Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.api.GuildRank.QueryGuildRankReq.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        owUids_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        month_ = "";

        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryGuildRankReq_descriptor;
      }

      @Override
      public QueryGuildRankReq getDefaultInstanceForType() {
        return QueryGuildRankReq.getDefaultInstance();
      }

      @Override
      public QueryGuildRankReq build() {
        QueryGuildRankReq result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public QueryGuildRankReq buildPartial() {
        QueryGuildRankReq result = new QueryGuildRankReq(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((bitField0_ & 0x00000001) == 0x00000001)) {
          owUids_ = java.util.Collections.unmodifiableList(owUids_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.owUids_ = owUids_;
        result.month_ = month_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof QueryGuildRankReq) {
          return mergeFrom((QueryGuildRankReq)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(QueryGuildRankReq other) {
        if (other == QueryGuildRankReq.getDefaultInstance()) return this;
        if (!other.owUids_.isEmpty()) {
          if (owUids_.isEmpty()) {
            owUids_ = other.owUids_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureOwUidsIsMutable();
            owUids_.addAll(other.owUids_);
          }
          onChanged();
        }
        if (!other.getMonth().isEmpty()) {
          month_ = other.month_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        QueryGuildRankReq parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (QueryGuildRankReq) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<Long> owUids_ = java.util.Collections.emptyList();
      private void ensureOwUidsIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          owUids_ = new java.util.ArrayList<Long>(owUids_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated uint64 owUids = 1;</code>
       */
      public java.util.List<Long>
          getOwUidsList() {
        return java.util.Collections.unmodifiableList(owUids_);
      }
      /**
       * <code>repeated uint64 owUids = 1;</code>
       */
      public int getOwUidsCount() {
        return owUids_.size();
      }
      /**
       * <code>repeated uint64 owUids = 1;</code>
       */
      public long getOwUids(int index) {
        return owUids_.get(index);
      }
      /**
       * <code>repeated uint64 owUids = 1;</code>
       */
      public Builder setOwUids(
          int index, long value) {
        ensureOwUidsIsMutable();
        owUids_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint64 owUids = 1;</code>
       */
      public Builder addOwUids(long value) {
        ensureOwUidsIsMutable();
        owUids_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint64 owUids = 1;</code>
       */
      public Builder addAllOwUids(
          Iterable<? extends Long> values) {
        ensureOwUidsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, owUids_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint64 owUids = 1;</code>
       */
      public Builder clearOwUids() {
        owUids_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }

      private Object month_ = "";
      /**
       * <pre>
       * 2019-07
       * </pre>
       *
       * <code>string month = 2;</code>
       */
      public String getMonth() {
        Object ref = month_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          month_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       * 2019-07
       * </pre>
       *
       * <code>string month = 2;</code>
       */
      public com.google.protobuf.ByteString
          getMonthBytes() {
        Object ref = month_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          month_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 2019-07
       * </pre>
       *
       * <code>string month = 2;</code>
       */
      public Builder setMonth(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        month_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 2019-07
       * </pre>
       *
       * <code>string month = 2;</code>
       */
      public Builder clearMonth() {
        
        month_ = getDefaultInstance().getMonth();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 2019-07
       * </pre>
       *
       * <code>string month = 2;</code>
       */
      public Builder setMonthBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        month_ = value;
        onChanged();
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.api.QueryGuildRankReq)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.api.QueryGuildRankReq)
    private static final QueryGuildRankReq DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new QueryGuildRankReq();
    }

    public static QueryGuildRankReq getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QueryGuildRankReq>
        PARSER = new com.google.protobuf.AbstractParser<QueryGuildRankReq>() {
      @Override
      public QueryGuildRankReq parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QueryGuildRankReq(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QueryGuildRankReq> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<QueryGuildRankReq> getParserForType() {
      return PARSER;
    }

    @Override
    public QueryGuildRankReq getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryGuildRankRespOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.api.QueryGuildRankResp)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 result = 1;</code>
     */
    int getResult();

    /**
     * <pre>
     * uid-当前排名
     * </pre>
     *
     * <code>map&lt;uint64, uint32&gt; uidLevel = 2;</code>
     */
    int getUidLevelCount();
    /**
     * <pre>
     * uid-当前排名
     * </pre>
     *
     * <code>map&lt;uint64, uint32&gt; uidLevel = 2;</code>
     */
    boolean containsUidLevel(
            long key);
    /**
     * Use {@link #getUidLevelMap()} instead.
     */
    @Deprecated
    java.util.Map<Long, Integer>
    getUidLevel();
    /**
     * <pre>
     * uid-当前排名
     * </pre>
     *
     * <code>map&lt;uint64, uint32&gt; uidLevel = 2;</code>
     */
    java.util.Map<Long, Integer>
    getUidLevelMap();
    /**
     * <pre>
     * uid-当前排名
     * </pre>
     *
     * <code>map&lt;uint64, uint32&gt; uidLevel = 2;</code>
     */

    int getUidLevelOrDefault(
            long key,
            int defaultValue);
    /**
     * <pre>
     * uid-当前排名
     * </pre>
     *
     * <code>map&lt;uint64, uint32&gt; uidLevel = 2;</code>
     */

    int getUidLevelOrThrow(
            long key);

    /**
     * <pre>
     * uid-排名变化
     * </pre>
     *
     * <code>map&lt;uint64, uint32&gt; uidChange = 3;</code>
     */
    int getUidChangeCount();
    /**
     * <pre>
     * uid-排名变化
     * </pre>
     *
     * <code>map&lt;uint64, uint32&gt; uidChange = 3;</code>
     */
    boolean containsUidChange(
            long key);
    /**
     * Use {@link #getUidChangeMap()} instead.
     */
    @Deprecated
    java.util.Map<Long, Integer>
    getUidChange();
    /**
     * <pre>
     * uid-排名变化
     * </pre>
     *
     * <code>map&lt;uint64, uint32&gt; uidChange = 3;</code>
     */
    java.util.Map<Long, Integer>
    getUidChangeMap();
    /**
     * <pre>
     * uid-排名变化
     * </pre>
     *
     * <code>map&lt;uint64, uint32&gt; uidChange = 3;</code>
     */

    int getUidChangeOrDefault(
            long key,
            int defaultValue);
    /**
     * <pre>
     * uid-排名变化
     * </pre>
     *
     * <code>map&lt;uint64, uint32&gt; uidChange = 3;</code>
     */

    int getUidChangeOrThrow(
            long key);
  }
  /**
   * Protobuf type {@code com.yy.yyzone.guildrank.api.QueryGuildRankResp}
   */
  public  static final class QueryGuildRankResp extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.api.QueryGuildRankResp)
      QueryGuildRankRespOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QueryGuildRankResp.newBuilder() to construct.
    private QueryGuildRankResp(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QueryGuildRankResp() {
      result_ = 0;
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QueryGuildRankResp(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              result_ = input.readUInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                uidLevel_ = com.google.protobuf.MapField.newMapField(
                    UidLevelDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000002;
              }
              com.google.protobuf.MapEntry<Long, Integer>
              uidLevel__ = input.readMessage(
                  UidLevelDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              uidLevel_.getMutableMap().put(
                  uidLevel__.getKey(), uidLevel__.getValue());
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
                uidChange_ = com.google.protobuf.MapField.newMapField(
                    UidChangeDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000004;
              }
              com.google.protobuf.MapEntry<Long, Integer>
              uidChange__ = input.readMessage(
                  UidChangeDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              uidChange_.getMutableMap().put(
                  uidChange__.getKey(), uidChange__.getValue());
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return GuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryGuildRankResp_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 2:
          return internalGetUidLevel();
        case 3:
          return internalGetUidChange();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return GuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryGuildRankResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              QueryGuildRankResp.class, Builder.class);
    }

    private int bitField0_;
    public static final int RESULT_FIELD_NUMBER = 1;
    private int result_;
    /**
     * <code>uint32 result = 1;</code>
     */
    public int getResult() {
      return result_;
    }

    public static final int UIDLEVEL_FIELD_NUMBER = 2;
    private static final class UidLevelDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          Long, Integer> defaultEntry =
              com.google.protobuf.MapEntry
              .<Long, Integer>newDefaultInstance(
                  GuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryGuildRankResp_UidLevelEntry_descriptor,
                  com.google.protobuf.WireFormat.FieldType.UINT64,
                  0L,
                  com.google.protobuf.WireFormat.FieldType.UINT32,
                  0);
    }
    private com.google.protobuf.MapField<
        Long, Integer> uidLevel_;
    private com.google.protobuf.MapField<Long, Integer>
    internalGetUidLevel() {
      if (uidLevel_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            UidLevelDefaultEntryHolder.defaultEntry);
      }
      return uidLevel_;
    }

    public int getUidLevelCount() {
      return internalGetUidLevel().getMap().size();
    }
    /**
     * <pre>
     * uid-当前排名
     * </pre>
     *
     * <code>map&lt;uint64, uint32&gt; uidLevel = 2;</code>
     */

    public boolean containsUidLevel(
        long key) {
      
      return internalGetUidLevel().getMap().containsKey(key);
    }
    /**
     * Use {@link #getUidLevelMap()} instead.
     */
    @Deprecated
    public java.util.Map<Long, Integer> getUidLevel() {
      return getUidLevelMap();
    }
    /**
     * <pre>
     * uid-当前排名
     * </pre>
     *
     * <code>map&lt;uint64, uint32&gt; uidLevel = 2;</code>
     */

    public java.util.Map<Long, Integer> getUidLevelMap() {
      return internalGetUidLevel().getMap();
    }
    /**
     * <pre>
     * uid-当前排名
     * </pre>
     *
     * <code>map&lt;uint64, uint32&gt; uidLevel = 2;</code>
     */

    public int getUidLevelOrDefault(
        long key,
        int defaultValue) {
      
      java.util.Map<Long, Integer> map =
          internalGetUidLevel().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     * uid-当前排名
     * </pre>
     *
     * <code>map&lt;uint64, uint32&gt; uidLevel = 2;</code>
     */

    public int getUidLevelOrThrow(
        long key) {
      
      java.util.Map<Long, Integer> map =
          internalGetUidLevel().getMap();
      if (!map.containsKey(key)) {
        throw new IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int UIDCHANGE_FIELD_NUMBER = 3;
    private static final class UidChangeDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          Long, Integer> defaultEntry =
              com.google.protobuf.MapEntry
              .<Long, Integer>newDefaultInstance(
                  GuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryGuildRankResp_UidChangeEntry_descriptor,
                  com.google.protobuf.WireFormat.FieldType.UINT64,
                  0L,
                  com.google.protobuf.WireFormat.FieldType.UINT32,
                  0);
    }
    private com.google.protobuf.MapField<
        Long, Integer> uidChange_;
    private com.google.protobuf.MapField<Long, Integer>
    internalGetUidChange() {
      if (uidChange_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            UidChangeDefaultEntryHolder.defaultEntry);
      }
      return uidChange_;
    }

    public int getUidChangeCount() {
      return internalGetUidChange().getMap().size();
    }
    /**
     * <pre>
     * uid-排名变化
     * </pre>
     *
     * <code>map&lt;uint64, uint32&gt; uidChange = 3;</code>
     */

    public boolean containsUidChange(
        long key) {
      
      return internalGetUidChange().getMap().containsKey(key);
    }
    /**
     * Use {@link #getUidChangeMap()} instead.
     */
    @Deprecated
    public java.util.Map<Long, Integer> getUidChange() {
      return getUidChangeMap();
    }
    /**
     * <pre>
     * uid-排名变化
     * </pre>
     *
     * <code>map&lt;uint64, uint32&gt; uidChange = 3;</code>
     */

    public java.util.Map<Long, Integer> getUidChangeMap() {
      return internalGetUidChange().getMap();
    }
    /**
     * <pre>
     * uid-排名变化
     * </pre>
     *
     * <code>map&lt;uint64, uint32&gt; uidChange = 3;</code>
     */

    public int getUidChangeOrDefault(
        long key,
        int defaultValue) {
      
      java.util.Map<Long, Integer> map =
          internalGetUidChange().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     * uid-排名变化
     * </pre>
     *
     * <code>map&lt;uint64, uint32&gt; uidChange = 3;</code>
     */

    public int getUidChangeOrThrow(
        long key) {
      
      java.util.Map<Long, Integer> map =
          internalGetUidChange().getMap();
      if (!map.containsKey(key)) {
        throw new IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (result_ != 0) {
        output.writeUInt32(1, result_);
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeLongMapTo(
          output,
          internalGetUidLevel(),
          UidLevelDefaultEntryHolder.defaultEntry,
          2);
      com.google.protobuf.GeneratedMessageV3
        .serializeLongMapTo(
          output,
          internalGetUidChange(),
          UidChangeDefaultEntryHolder.defaultEntry,
          3);
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (result_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, result_);
      }
      for (java.util.Map.Entry<Long, Integer> entry
           : internalGetUidLevel().getMap().entrySet()) {
        com.google.protobuf.MapEntry<Long, Integer>
        uidLevel__ = UidLevelDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(2, uidLevel__);
      }
      for (java.util.Map.Entry<Long, Integer> entry
           : internalGetUidChange().getMap().entrySet()) {
        com.google.protobuf.MapEntry<Long, Integer>
        uidChange__ = UidChangeDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(3, uidChange__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof QueryGuildRankResp)) {
        return super.equals(obj);
      }
      QueryGuildRankResp other = (QueryGuildRankResp) obj;

      boolean result = true;
      result = result && (getResult()
          == other.getResult());
      result = result && internalGetUidLevel().equals(
          other.internalGetUidLevel());
      result = result && internalGetUidChange().equals(
          other.internalGetUidChange());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + getResult();
      if (!internalGetUidLevel().getMap().isEmpty()) {
        hash = (37 * hash) + UIDLEVEL_FIELD_NUMBER;
        hash = (53 * hash) + internalGetUidLevel().hashCode();
      }
      if (!internalGetUidChange().getMap().isEmpty()) {
        hash = (37 * hash) + UIDCHANGE_FIELD_NUMBER;
        hash = (53 * hash) + internalGetUidChange().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static QueryGuildRankResp parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryGuildRankResp parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryGuildRankResp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryGuildRankResp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryGuildRankResp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryGuildRankResp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryGuildRankResp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static QueryGuildRankResp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static QueryGuildRankResp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static QueryGuildRankResp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static QueryGuildRankResp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static QueryGuildRankResp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(QueryGuildRankResp prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.yyzone.guildrank.api.QueryGuildRankResp}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.api.QueryGuildRankResp)
        QueryGuildRankRespOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryGuildRankResp_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 2:
            return internalGetUidLevel();
          case 3:
            return internalGetUidChange();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 2:
            return internalGetMutableUidLevel();
          case 3:
            return internalGetMutableUidChange();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryGuildRankResp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                QueryGuildRankResp.class, Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.api.GuildRank.QueryGuildRankResp.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        result_ = 0;

        internalGetMutableUidLevel().clear();
        internalGetMutableUidChange().clear();
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryGuildRankResp_descriptor;
      }

      @Override
      public QueryGuildRankResp getDefaultInstanceForType() {
        return QueryGuildRankResp.getDefaultInstance();
      }

      @Override
      public QueryGuildRankResp build() {
        QueryGuildRankResp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public QueryGuildRankResp buildPartial() {
        QueryGuildRankResp result = new QueryGuildRankResp(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.result_ = result_;
        result.uidLevel_ = internalGetUidLevel();
        result.uidLevel_.makeImmutable();
        result.uidChange_ = internalGetUidChange();
        result.uidChange_.makeImmutable();
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof QueryGuildRankResp) {
          return mergeFrom((QueryGuildRankResp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(QueryGuildRankResp other) {
        if (other == QueryGuildRankResp.getDefaultInstance()) return this;
        if (other.getResult() != 0) {
          setResult(other.getResult());
        }
        internalGetMutableUidLevel().mergeFrom(
            other.internalGetUidLevel());
        internalGetMutableUidChange().mergeFrom(
            other.internalGetUidChange());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        QueryGuildRankResp parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (QueryGuildRankResp) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int result_ ;
      /**
       * <code>uint32 result = 1;</code>
       */
      public int getResult() {
        return result_;
      }
      /**
       * <code>uint32 result = 1;</code>
       */
      public Builder setResult(int value) {
        
        result_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 result = 1;</code>
       */
      public Builder clearResult() {
        
        result_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.MapField<
          Long, Integer> uidLevel_;
      private com.google.protobuf.MapField<Long, Integer>
      internalGetUidLevel() {
        if (uidLevel_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              UidLevelDefaultEntryHolder.defaultEntry);
        }
        return uidLevel_;
      }
      private com.google.protobuf.MapField<Long, Integer>
      internalGetMutableUidLevel() {
        onChanged();;
        if (uidLevel_ == null) {
          uidLevel_ = com.google.protobuf.MapField.newMapField(
              UidLevelDefaultEntryHolder.defaultEntry);
        }
        if (!uidLevel_.isMutable()) {
          uidLevel_ = uidLevel_.copy();
        }
        return uidLevel_;
      }

      public int getUidLevelCount() {
        return internalGetUidLevel().getMap().size();
      }
      /**
       * <pre>
       * uid-当前排名
       * </pre>
       *
       * <code>map&lt;uint64, uint32&gt; uidLevel = 2;</code>
       */

      public boolean containsUidLevel(
          long key) {
        
        return internalGetUidLevel().getMap().containsKey(key);
      }
      /**
       * Use {@link #getUidLevelMap()} instead.
       */
      @Deprecated
      public java.util.Map<Long, Integer> getUidLevel() {
        return getUidLevelMap();
      }
      /**
       * <pre>
       * uid-当前排名
       * </pre>
       *
       * <code>map&lt;uint64, uint32&gt; uidLevel = 2;</code>
       */

      public java.util.Map<Long, Integer> getUidLevelMap() {
        return internalGetUidLevel().getMap();
      }
      /**
       * <pre>
       * uid-当前排名
       * </pre>
       *
       * <code>map&lt;uint64, uint32&gt; uidLevel = 2;</code>
       */

      public int getUidLevelOrDefault(
          long key,
          int defaultValue) {
        
        java.util.Map<Long, Integer> map =
            internalGetUidLevel().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       * uid-当前排名
       * </pre>
       *
       * <code>map&lt;uint64, uint32&gt; uidLevel = 2;</code>
       */

      public int getUidLevelOrThrow(
          long key) {
        
        java.util.Map<Long, Integer> map =
            internalGetUidLevel().getMap();
        if (!map.containsKey(key)) {
          throw new IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearUidLevel() {
        internalGetMutableUidLevel().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       * uid-当前排名
       * </pre>
       *
       * <code>map&lt;uint64, uint32&gt; uidLevel = 2;</code>
       */

      public Builder removeUidLevel(
          long key) {
        
        internalGetMutableUidLevel().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @Deprecated
      public java.util.Map<Long, Integer>
      getMutableUidLevel() {
        return internalGetMutableUidLevel().getMutableMap();
      }
      /**
       * <pre>
       * uid-当前排名
       * </pre>
       *
       * <code>map&lt;uint64, uint32&gt; uidLevel = 2;</code>
       */
      public Builder putUidLevel(
          long key,
          int value) {
        
        
        internalGetMutableUidLevel().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       * uid-当前排名
       * </pre>
       *
       * <code>map&lt;uint64, uint32&gt; uidLevel = 2;</code>
       */

      public Builder putAllUidLevel(
          java.util.Map<Long, Integer> values) {
        internalGetMutableUidLevel().getMutableMap()
            .putAll(values);
        return this;
      }

      private com.google.protobuf.MapField<
          Long, Integer> uidChange_;
      private com.google.protobuf.MapField<Long, Integer>
      internalGetUidChange() {
        if (uidChange_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              UidChangeDefaultEntryHolder.defaultEntry);
        }
        return uidChange_;
      }
      private com.google.protobuf.MapField<Long, Integer>
      internalGetMutableUidChange() {
        onChanged();;
        if (uidChange_ == null) {
          uidChange_ = com.google.protobuf.MapField.newMapField(
              UidChangeDefaultEntryHolder.defaultEntry);
        }
        if (!uidChange_.isMutable()) {
          uidChange_ = uidChange_.copy();
        }
        return uidChange_;
      }

      public int getUidChangeCount() {
        return internalGetUidChange().getMap().size();
      }
      /**
       * <pre>
       * uid-排名变化
       * </pre>
       *
       * <code>map&lt;uint64, uint32&gt; uidChange = 3;</code>
       */

      public boolean containsUidChange(
          long key) {
        
        return internalGetUidChange().getMap().containsKey(key);
      }
      /**
       * Use {@link #getUidChangeMap()} instead.
       */
      @Deprecated
      public java.util.Map<Long, Integer> getUidChange() {
        return getUidChangeMap();
      }
      /**
       * <pre>
       * uid-排名变化
       * </pre>
       *
       * <code>map&lt;uint64, uint32&gt; uidChange = 3;</code>
       */

      public java.util.Map<Long, Integer> getUidChangeMap() {
        return internalGetUidChange().getMap();
      }
      /**
       * <pre>
       * uid-排名变化
       * </pre>
       *
       * <code>map&lt;uint64, uint32&gt; uidChange = 3;</code>
       */

      public int getUidChangeOrDefault(
          long key,
          int defaultValue) {
        
        java.util.Map<Long, Integer> map =
            internalGetUidChange().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       * uid-排名变化
       * </pre>
       *
       * <code>map&lt;uint64, uint32&gt; uidChange = 3;</code>
       */

      public int getUidChangeOrThrow(
          long key) {
        
        java.util.Map<Long, Integer> map =
            internalGetUidChange().getMap();
        if (!map.containsKey(key)) {
          throw new IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearUidChange() {
        internalGetMutableUidChange().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       * uid-排名变化
       * </pre>
       *
       * <code>map&lt;uint64, uint32&gt; uidChange = 3;</code>
       */

      public Builder removeUidChange(
          long key) {
        
        internalGetMutableUidChange().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @Deprecated
      public java.util.Map<Long, Integer>
      getMutableUidChange() {
        return internalGetMutableUidChange().getMutableMap();
      }
      /**
       * <pre>
       * uid-排名变化
       * </pre>
       *
       * <code>map&lt;uint64, uint32&gt; uidChange = 3;</code>
       */
      public Builder putUidChange(
          long key,
          int value) {
        
        
        internalGetMutableUidChange().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       * uid-排名变化
       * </pre>
       *
       * <code>map&lt;uint64, uint32&gt; uidChange = 3;</code>
       */

      public Builder putAllUidChange(
          java.util.Map<Long, Integer> values) {
        internalGetMutableUidChange().getMutableMap()
            .putAll(values);
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.api.QueryGuildRankResp)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.api.QueryGuildRankResp)
    private static final QueryGuildRankResp DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new QueryGuildRankResp();
    }

    public static QueryGuildRankResp getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QueryGuildRankResp>
        PARSER = new com.google.protobuf.AbstractParser<QueryGuildRankResp>() {
      @Override
      public QueryGuildRankResp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QueryGuildRankResp(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QueryGuildRankResp> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<QueryGuildRankResp> getParserForType() {
      return PARSER;
    }

    @Override
    public QueryGuildRankResp getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface updateFlagRespOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.api.updateFlagResp)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 1-已经更新 0还没更新
     * </pre>
     *
     * <code>uint32 result = 1;</code>
     */
    int getResult();
  }
  /**
   * Protobuf type {@code com.yy.yyzone.guildrank.api.updateFlagResp}
   */
  public  static final class updateFlagResp extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.api.updateFlagResp)
      updateFlagRespOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use updateFlagResp.newBuilder() to construct.
    private updateFlagResp(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private updateFlagResp() {
      result_ = 0;
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private updateFlagResp(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              result_ = input.readUInt32();
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return GuildRank.internal_static_com_yy_yyzone_guildrank_api_updateFlagResp_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return GuildRank.internal_static_com_yy_yyzone_guildrank_api_updateFlagResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              updateFlagResp.class, Builder.class);
    }

    public static final int RESULT_FIELD_NUMBER = 1;
    private int result_;
    /**
     * <pre>
     * 1-已经更新 0还没更新
     * </pre>
     *
     * <code>uint32 result = 1;</code>
     */
    public int getResult() {
      return result_;
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (result_ != 0) {
        output.writeUInt32(1, result_);
      }
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (result_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, result_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof updateFlagResp)) {
        return super.equals(obj);
      }
      updateFlagResp other = (updateFlagResp) obj;

      boolean result = true;
      result = result && (getResult()
          == other.getResult());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + getResult();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static updateFlagResp parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static updateFlagResp parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static updateFlagResp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static updateFlagResp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static updateFlagResp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static updateFlagResp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static updateFlagResp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static updateFlagResp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static updateFlagResp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static updateFlagResp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static updateFlagResp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static updateFlagResp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(updateFlagResp prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.yyzone.guildrank.api.updateFlagResp}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.api.updateFlagResp)
        updateFlagRespOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_updateFlagResp_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_updateFlagResp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                updateFlagResp.class, Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.api.GuildRank.updateFlagResp.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        result_ = 0;

        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_updateFlagResp_descriptor;
      }

      @Override
      public updateFlagResp getDefaultInstanceForType() {
        return updateFlagResp.getDefaultInstance();
      }

      @Override
      public updateFlagResp build() {
        updateFlagResp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public updateFlagResp buildPartial() {
        updateFlagResp result = new updateFlagResp(this);
        result.result_ = result_;
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof updateFlagResp) {
          return mergeFrom((updateFlagResp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(updateFlagResp other) {
        if (other == updateFlagResp.getDefaultInstance()) return this;
        if (other.getResult() != 0) {
          setResult(other.getResult());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        updateFlagResp parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (updateFlagResp) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int result_ ;
      /**
       * <pre>
       * 1-已经更新 0还没更新
       * </pre>
       *
       * <code>uint32 result = 1;</code>
       */
      public int getResult() {
        return result_;
      }
      /**
       * <pre>
       * 1-已经更新 0还没更新
       * </pre>
       *
       * <code>uint32 result = 1;</code>
       */
      public Builder setResult(int value) {
        
        result_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 1-已经更新 0还没更新
       * </pre>
       *
       * <code>uint32 result = 1;</code>
       */
      public Builder clearResult() {
        
        result_ = 0;
        onChanged();
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.api.updateFlagResp)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.api.updateFlagResp)
    private static final updateFlagResp DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new updateFlagResp();
    }

    public static updateFlagResp getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<updateFlagResp>
        PARSER = new com.google.protobuf.AbstractParser<updateFlagResp>() {
      @Override
      public updateFlagResp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new updateFlagResp(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<updateFlagResp> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<updateFlagResp> getParserForType() {
      return PARSER;
    }

    @Override
    public updateFlagResp getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface updateFlagReqOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.api.updateFlagReq)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 param = 1;</code>
     */
    int getParam();
  }
  /**
   * Protobuf type {@code com.yy.yyzone.guildrank.api.updateFlagReq}
   */
  public  static final class updateFlagReq extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.api.updateFlagReq)
      updateFlagReqOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use updateFlagReq.newBuilder() to construct.
    private updateFlagReq(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private updateFlagReq() {
      param_ = 0;
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private updateFlagReq(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              param_ = input.readUInt32();
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return GuildRank.internal_static_com_yy_yyzone_guildrank_api_updateFlagReq_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return GuildRank.internal_static_com_yy_yyzone_guildrank_api_updateFlagReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              updateFlagReq.class, Builder.class);
    }

    public static final int PARAM_FIELD_NUMBER = 1;
    private int param_;
    /**
     * <code>uint32 param = 1;</code>
     */
    public int getParam() {
      return param_;
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (param_ != 0) {
        output.writeUInt32(1, param_);
      }
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (param_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, param_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof updateFlagReq)) {
        return super.equals(obj);
      }
      updateFlagReq other = (updateFlagReq) obj;

      boolean result = true;
      result = result && (getParam()
          == other.getParam());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + PARAM_FIELD_NUMBER;
      hash = (53 * hash) + getParam();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static updateFlagReq parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static updateFlagReq parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static updateFlagReq parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static updateFlagReq parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static updateFlagReq parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static updateFlagReq parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static updateFlagReq parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static updateFlagReq parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static updateFlagReq parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static updateFlagReq parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static updateFlagReq parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static updateFlagReq parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(updateFlagReq prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.yyzone.guildrank.api.updateFlagReq}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.api.updateFlagReq)
        updateFlagReqOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_updateFlagReq_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_updateFlagReq_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                updateFlagReq.class, Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.api.GuildRank.updateFlagReq.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        param_ = 0;

        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_updateFlagReq_descriptor;
      }

      @Override
      public updateFlagReq getDefaultInstanceForType() {
        return updateFlagReq.getDefaultInstance();
      }

      @Override
      public updateFlagReq build() {
        updateFlagReq result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public updateFlagReq buildPartial() {
        updateFlagReq result = new updateFlagReq(this);
        result.param_ = param_;
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof updateFlagReq) {
          return mergeFrom((updateFlagReq)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(updateFlagReq other) {
        if (other == updateFlagReq.getDefaultInstance()) return this;
        if (other.getParam() != 0) {
          setParam(other.getParam());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        updateFlagReq parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (updateFlagReq) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int param_ ;
      /**
       * <code>uint32 param = 1;</code>
       */
      public int getParam() {
        return param_;
      }
      /**
       * <code>uint32 param = 1;</code>
       */
      public Builder setParam(int value) {
        
        param_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 param = 1;</code>
       */
      public Builder clearParam() {
        
        param_ = 0;
        onChanged();
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.api.updateFlagReq)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.api.updateFlagReq)
    private static final updateFlagReq DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new updateFlagReq();
    }

    public static updateFlagReq getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<updateFlagReq>
        PARSER = new com.google.protobuf.AbstractParser<updateFlagReq>() {
      @Override
      public updateFlagReq parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new updateFlagReq(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<updateFlagReq> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<updateFlagReq> getParserForType() {
      return PARSER;
    }

    @Override
    public updateFlagReq getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CurrentRankReqOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.api.CurrentRankReq)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint64 uid = 1;</code>
     */
    long getUid();
  }
  /**
   * Protobuf type {@code com.yy.yyzone.guildrank.api.CurrentRankReq}
   */
  public  static final class CurrentRankReq extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.api.CurrentRankReq)
      CurrentRankReqOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CurrentRankReq.newBuilder() to construct.
    private CurrentRankReq(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CurrentRankReq() {
      uid_ = 0L;
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CurrentRankReq(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              uid_ = input.readUInt64();
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return GuildRank.internal_static_com_yy_yyzone_guildrank_api_CurrentRankReq_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return GuildRank.internal_static_com_yy_yyzone_guildrank_api_CurrentRankReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              CurrentRankReq.class, Builder.class);
    }

    public static final int UID_FIELD_NUMBER = 1;
    private long uid_;
    /**
     * <code>uint64 uid = 1;</code>
     */
    public long getUid() {
      return uid_;
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (uid_ != 0L) {
        output.writeUInt64(1, uid_);
      }
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (uid_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, uid_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof CurrentRankReq)) {
        return super.equals(obj);
      }
      CurrentRankReq other = (CurrentRankReq) obj;

      boolean result = true;
      result = result && (getUid()
          == other.getUid());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + UID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUid());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static CurrentRankReq parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static CurrentRankReq parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static CurrentRankReq parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static CurrentRankReq parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static CurrentRankReq parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static CurrentRankReq parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static CurrentRankReq parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static CurrentRankReq parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static CurrentRankReq parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static CurrentRankReq parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static CurrentRankReq parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static CurrentRankReq parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(CurrentRankReq prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.yyzone.guildrank.api.CurrentRankReq}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.api.CurrentRankReq)
        CurrentRankReqOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_CurrentRankReq_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_CurrentRankReq_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                CurrentRankReq.class, Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.api.GuildRank.CurrentRankReq.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        uid_ = 0L;

        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_CurrentRankReq_descriptor;
      }

      @Override
      public CurrentRankReq getDefaultInstanceForType() {
        return CurrentRankReq.getDefaultInstance();
      }

      @Override
      public CurrentRankReq build() {
        CurrentRankReq result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public CurrentRankReq buildPartial() {
        CurrentRankReq result = new CurrentRankReq(this);
        result.uid_ = uid_;
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof CurrentRankReq) {
          return mergeFrom((CurrentRankReq)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(CurrentRankReq other) {
        if (other == CurrentRankReq.getDefaultInstance()) return this;
        if (other.getUid() != 0L) {
          setUid(other.getUid());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        CurrentRankReq parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (CurrentRankReq) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private long uid_ ;
      /**
       * <code>uint64 uid = 1;</code>
       */
      public long getUid() {
        return uid_;
      }
      /**
       * <code>uint64 uid = 1;</code>
       */
      public Builder setUid(long value) {
        
        uid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 uid = 1;</code>
       */
      public Builder clearUid() {
        
        uid_ = 0L;
        onChanged();
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.api.CurrentRankReq)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.api.CurrentRankReq)
    private static final CurrentRankReq DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new CurrentRankReq();
    }

    public static CurrentRankReq getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<CurrentRankReq>
        PARSER = new com.google.protobuf.AbstractParser<CurrentRankReq>() {
      @Override
      public CurrentRankReq parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CurrentRankReq(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CurrentRankReq> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<CurrentRankReq> getParserForType() {
      return PARSER;
    }

    @Override
    public CurrentRankReq getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CurrentRankRespOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.api.CurrentRankResp)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 result = 1;</code>
     */
    int getResult();

    /**
     * <code>uint32 level = 2;</code>
     */
    int getLevel();
  }
  /**
   * Protobuf type {@code com.yy.yyzone.guildrank.api.CurrentRankResp}
   */
  public  static final class CurrentRankResp extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.api.CurrentRankResp)
      CurrentRankRespOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CurrentRankResp.newBuilder() to construct.
    private CurrentRankResp(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CurrentRankResp() {
      result_ = 0;
      level_ = 0;
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CurrentRankResp(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              result_ = input.readUInt32();
              break;
            }
            case 16: {

              level_ = input.readUInt32();
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return GuildRank.internal_static_com_yy_yyzone_guildrank_api_CurrentRankResp_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return GuildRank.internal_static_com_yy_yyzone_guildrank_api_CurrentRankResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              CurrentRankResp.class, Builder.class);
    }

    public static final int RESULT_FIELD_NUMBER = 1;
    private int result_;
    /**
     * <code>uint32 result = 1;</code>
     */
    public int getResult() {
      return result_;
    }

    public static final int LEVEL_FIELD_NUMBER = 2;
    private int level_;
    /**
     * <code>uint32 level = 2;</code>
     */
    public int getLevel() {
      return level_;
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (result_ != 0) {
        output.writeUInt32(1, result_);
      }
      if (level_ != 0) {
        output.writeUInt32(2, level_);
      }
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (result_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, result_);
      }
      if (level_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, level_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof CurrentRankResp)) {
        return super.equals(obj);
      }
      CurrentRankResp other = (CurrentRankResp) obj;

      boolean result = true;
      result = result && (getResult()
          == other.getResult());
      result = result && (getLevel()
          == other.getLevel());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + getResult();
      hash = (37 * hash) + LEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getLevel();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static CurrentRankResp parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static CurrentRankResp parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static CurrentRankResp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static CurrentRankResp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static CurrentRankResp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static CurrentRankResp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static CurrentRankResp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static CurrentRankResp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static CurrentRankResp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static CurrentRankResp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static CurrentRankResp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static CurrentRankResp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(CurrentRankResp prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.yyzone.guildrank.api.CurrentRankResp}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.api.CurrentRankResp)
        CurrentRankRespOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_CurrentRankResp_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_CurrentRankResp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                CurrentRankResp.class, Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.api.GuildRank.CurrentRankResp.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        result_ = 0;

        level_ = 0;

        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_CurrentRankResp_descriptor;
      }

      @Override
      public CurrentRankResp getDefaultInstanceForType() {
        return CurrentRankResp.getDefaultInstance();
      }

      @Override
      public CurrentRankResp build() {
        CurrentRankResp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public CurrentRankResp buildPartial() {
        CurrentRankResp result = new CurrentRankResp(this);
        result.result_ = result_;
        result.level_ = level_;
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof CurrentRankResp) {
          return mergeFrom((CurrentRankResp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(CurrentRankResp other) {
        if (other == CurrentRankResp.getDefaultInstance()) return this;
        if (other.getResult() != 0) {
          setResult(other.getResult());
        }
        if (other.getLevel() != 0) {
          setLevel(other.getLevel());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        CurrentRankResp parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (CurrentRankResp) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int result_ ;
      /**
       * <code>uint32 result = 1;</code>
       */
      public int getResult() {
        return result_;
      }
      /**
       * <code>uint32 result = 1;</code>
       */
      public Builder setResult(int value) {
        
        result_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 result = 1;</code>
       */
      public Builder clearResult() {
        
        result_ = 0;
        onChanged();
        return this;
      }

      private int level_ ;
      /**
       * <code>uint32 level = 2;</code>
       */
      public int getLevel() {
        return level_;
      }
      /**
       * <code>uint32 level = 2;</code>
       */
      public Builder setLevel(int value) {
        
        level_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 level = 2;</code>
       */
      public Builder clearLevel() {
        
        level_ = 0;
        onChanged();
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.api.CurrentRankResp)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.api.CurrentRankResp)
    private static final CurrentRankResp DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new CurrentRankResp();
    }

    public static CurrentRankResp getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<CurrentRankResp>
        PARSER = new com.google.protobuf.AbstractParser<CurrentRankResp>() {
      @Override
      public CurrentRankResp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CurrentRankResp(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CurrentRankResp> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<CurrentRankResp> getParserForType() {
      return PARSER;
    }

    @Override
    public CurrentRankResp getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryCurrentRankReqOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.api.QueryCurrentRankReq)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int64 uid = 1;</code>
     */
    long getUid();

    /**
     * <pre>
     * appid 服务端分配
     * </pre>
     *
     * <code>string appid = 2;</code>
     */
    String getAppid();
    /**
     * <pre>
     * appid 服务端分配
     * </pre>
     *
     * <code>string appid = 2;</code>
     */
    com.google.protobuf.ByteString
        getAppidBytes();

    /**
     * <pre>
     * DigestUtils.md5Hex(uid+"_"+password)
     * </pre>
     *
     * <code>string sign = 3;</code>
     */
    String getSign();
    /**
     * <pre>
     * DigestUtils.md5Hex(uid+"_"+password)
     * </pre>
     *
     * <code>string sign = 3;</code>
     */
    com.google.protobuf.ByteString
        getSignBytes();
  }
  /**
   * Protobuf type {@code com.yy.yyzone.guildrank.api.QueryCurrentRankReq}
   */
  public  static final class QueryCurrentRankReq extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.api.QueryCurrentRankReq)
      QueryCurrentRankReqOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QueryCurrentRankReq.newBuilder() to construct.
    private QueryCurrentRankReq(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QueryCurrentRankReq() {
      uid_ = 0L;
      appid_ = "";
      sign_ = "";
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QueryCurrentRankReq(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              uid_ = input.readInt64();
              break;
            }
            case 18: {
              String s = input.readStringRequireUtf8();

              appid_ = s;
              break;
            }
            case 26: {
              String s = input.readStringRequireUtf8();

              sign_ = s;
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return GuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryCurrentRankReq_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return GuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryCurrentRankReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              QueryCurrentRankReq.class, Builder.class);
    }

    public static final int UID_FIELD_NUMBER = 1;
    private long uid_;
    /**
     * <code>int64 uid = 1;</code>
     */
    public long getUid() {
      return uid_;
    }

    public static final int APPID_FIELD_NUMBER = 2;
    private volatile Object appid_;
    /**
     * <pre>
     * appid 服务端分配
     * </pre>
     *
     * <code>string appid = 2;</code>
     */
    public String getAppid() {
      Object ref = appid_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        appid_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * appid 服务端分配
     * </pre>
     *
     * <code>string appid = 2;</code>
     */
    public com.google.protobuf.ByteString
        getAppidBytes() {
      Object ref = appid_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        appid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SIGN_FIELD_NUMBER = 3;
    private volatile Object sign_;
    /**
     * <pre>
     * DigestUtils.md5Hex(uid+"_"+password)
     * </pre>
     *
     * <code>string sign = 3;</code>
     */
    public String getSign() {
      Object ref = sign_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        sign_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * DigestUtils.md5Hex(uid+"_"+password)
     * </pre>
     *
     * <code>string sign = 3;</code>
     */
    public com.google.protobuf.ByteString
        getSignBytes() {
      Object ref = sign_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        sign_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (uid_ != 0L) {
        output.writeInt64(1, uid_);
      }
      if (!getAppidBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, appid_);
      }
      if (!getSignBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, sign_);
      }
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (uid_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, uid_);
      }
      if (!getAppidBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, appid_);
      }
      if (!getSignBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, sign_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof QueryCurrentRankReq)) {
        return super.equals(obj);
      }
      QueryCurrentRankReq other = (QueryCurrentRankReq) obj;

      boolean result = true;
      result = result && (getUid()
          == other.getUid());
      result = result && getAppid()
          .equals(other.getAppid());
      result = result && getSign()
          .equals(other.getSign());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + UID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUid());
      hash = (37 * hash) + APPID_FIELD_NUMBER;
      hash = (53 * hash) + getAppid().hashCode();
      hash = (37 * hash) + SIGN_FIELD_NUMBER;
      hash = (53 * hash) + getSign().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static QueryCurrentRankReq parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryCurrentRankReq parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryCurrentRankReq parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryCurrentRankReq parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryCurrentRankReq parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryCurrentRankReq parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryCurrentRankReq parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static QueryCurrentRankReq parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static QueryCurrentRankReq parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static QueryCurrentRankReq parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static QueryCurrentRankReq parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static QueryCurrentRankReq parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(QueryCurrentRankReq prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.yyzone.guildrank.api.QueryCurrentRankReq}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.api.QueryCurrentRankReq)
        QueryCurrentRankReqOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryCurrentRankReq_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryCurrentRankReq_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                QueryCurrentRankReq.class, Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.api.GuildRank.QueryCurrentRankReq.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        uid_ = 0L;

        appid_ = "";

        sign_ = "";

        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryCurrentRankReq_descriptor;
      }

      @Override
      public QueryCurrentRankReq getDefaultInstanceForType() {
        return QueryCurrentRankReq.getDefaultInstance();
      }

      @Override
      public QueryCurrentRankReq build() {
        QueryCurrentRankReq result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public QueryCurrentRankReq buildPartial() {
        QueryCurrentRankReq result = new QueryCurrentRankReq(this);
        result.uid_ = uid_;
        result.appid_ = appid_;
        result.sign_ = sign_;
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof QueryCurrentRankReq) {
          return mergeFrom((QueryCurrentRankReq)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(QueryCurrentRankReq other) {
        if (other == QueryCurrentRankReq.getDefaultInstance()) return this;
        if (other.getUid() != 0L) {
          setUid(other.getUid());
        }
        if (!other.getAppid().isEmpty()) {
          appid_ = other.appid_;
          onChanged();
        }
        if (!other.getSign().isEmpty()) {
          sign_ = other.sign_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        QueryCurrentRankReq parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (QueryCurrentRankReq) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private long uid_ ;
      /**
       * <code>int64 uid = 1;</code>
       */
      public long getUid() {
        return uid_;
      }
      /**
       * <code>int64 uid = 1;</code>
       */
      public Builder setUid(long value) {
        
        uid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 uid = 1;</code>
       */
      public Builder clearUid() {
        
        uid_ = 0L;
        onChanged();
        return this;
      }

      private Object appid_ = "";
      /**
       * <pre>
       * appid 服务端分配
       * </pre>
       *
       * <code>string appid = 2;</code>
       */
      public String getAppid() {
        Object ref = appid_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          appid_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       * appid 服务端分配
       * </pre>
       *
       * <code>string appid = 2;</code>
       */
      public com.google.protobuf.ByteString
          getAppidBytes() {
        Object ref = appid_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          appid_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * appid 服务端分配
       * </pre>
       *
       * <code>string appid = 2;</code>
       */
      public Builder setAppid(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        appid_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * appid 服务端分配
       * </pre>
       *
       * <code>string appid = 2;</code>
       */
      public Builder clearAppid() {
        
        appid_ = getDefaultInstance().getAppid();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * appid 服务端分配
       * </pre>
       *
       * <code>string appid = 2;</code>
       */
      public Builder setAppidBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        appid_ = value;
        onChanged();
        return this;
      }

      private Object sign_ = "";
      /**
       * <pre>
       * DigestUtils.md5Hex(uid+"_"+password)
       * </pre>
       *
       * <code>string sign = 3;</code>
       */
      public String getSign() {
        Object ref = sign_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          sign_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       * DigestUtils.md5Hex(uid+"_"+password)
       * </pre>
       *
       * <code>string sign = 3;</code>
       */
      public com.google.protobuf.ByteString
          getSignBytes() {
        Object ref = sign_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          sign_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * DigestUtils.md5Hex(uid+"_"+password)
       * </pre>
       *
       * <code>string sign = 3;</code>
       */
      public Builder setSign(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        sign_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * DigestUtils.md5Hex(uid+"_"+password)
       * </pre>
       *
       * <code>string sign = 3;</code>
       */
      public Builder clearSign() {
        
        sign_ = getDefaultInstance().getSign();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * DigestUtils.md5Hex(uid+"_"+password)
       * </pre>
       *
       * <code>string sign = 3;</code>
       */
      public Builder setSignBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        sign_ = value;
        onChanged();
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.api.QueryCurrentRankReq)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.api.QueryCurrentRankReq)
    private static final QueryCurrentRankReq DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new QueryCurrentRankReq();
    }

    public static QueryCurrentRankReq getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QueryCurrentRankReq>
        PARSER = new com.google.protobuf.AbstractParser<QueryCurrentRankReq>() {
      @Override
      public QueryCurrentRankReq parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QueryCurrentRankReq(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QueryCurrentRankReq> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<QueryCurrentRankReq> getParserForType() {
      return PARSER;
    }

    @Override
    public QueryCurrentRankReq getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryCurrentRankRespOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.api.QueryCurrentRankResp)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 result = 1;</code>
     */
    int getResult();

    /**
     * <code>uint32 level = 2;</code>
     */
    int getLevel();
  }
  /**
   * Protobuf type {@code com.yy.yyzone.guildrank.api.QueryCurrentRankResp}
   */
  public  static final class QueryCurrentRankResp extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.api.QueryCurrentRankResp)
      QueryCurrentRankRespOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QueryCurrentRankResp.newBuilder() to construct.
    private QueryCurrentRankResp(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QueryCurrentRankResp() {
      result_ = 0;
      level_ = 0;
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QueryCurrentRankResp(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              result_ = input.readUInt32();
              break;
            }
            case 16: {

              level_ = input.readUInt32();
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return GuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryCurrentRankResp_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return GuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryCurrentRankResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              QueryCurrentRankResp.class, Builder.class);
    }

    public static final int RESULT_FIELD_NUMBER = 1;
    private int result_;
    /**
     * <code>uint32 result = 1;</code>
     */
    public int getResult() {
      return result_;
    }

    public static final int LEVEL_FIELD_NUMBER = 2;
    private int level_;
    /**
     * <code>uint32 level = 2;</code>
     */
    public int getLevel() {
      return level_;
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (result_ != 0) {
        output.writeUInt32(1, result_);
      }
      if (level_ != 0) {
        output.writeUInt32(2, level_);
      }
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (result_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, result_);
      }
      if (level_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, level_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof QueryCurrentRankResp)) {
        return super.equals(obj);
      }
      QueryCurrentRankResp other = (QueryCurrentRankResp) obj;

      boolean result = true;
      result = result && (getResult()
          == other.getResult());
      result = result && (getLevel()
          == other.getLevel());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + getResult();
      hash = (37 * hash) + LEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getLevel();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static QueryCurrentRankResp parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryCurrentRankResp parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryCurrentRankResp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryCurrentRankResp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryCurrentRankResp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryCurrentRankResp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryCurrentRankResp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static QueryCurrentRankResp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static QueryCurrentRankResp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static QueryCurrentRankResp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static QueryCurrentRankResp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static QueryCurrentRankResp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(QueryCurrentRankResp prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.yyzone.guildrank.api.QueryCurrentRankResp}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.api.QueryCurrentRankResp)
        QueryCurrentRankRespOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryCurrentRankResp_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryCurrentRankResp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                QueryCurrentRankResp.class, Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.api.GuildRank.QueryCurrentRankResp.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        result_ = 0;

        level_ = 0;

        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryCurrentRankResp_descriptor;
      }

      @Override
      public QueryCurrentRankResp getDefaultInstanceForType() {
        return QueryCurrentRankResp.getDefaultInstance();
      }

      @Override
      public QueryCurrentRankResp build() {
        QueryCurrentRankResp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public QueryCurrentRankResp buildPartial() {
        QueryCurrentRankResp result = new QueryCurrentRankResp(this);
        result.result_ = result_;
        result.level_ = level_;
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof QueryCurrentRankResp) {
          return mergeFrom((QueryCurrentRankResp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(QueryCurrentRankResp other) {
        if (other == QueryCurrentRankResp.getDefaultInstance()) return this;
        if (other.getResult() != 0) {
          setResult(other.getResult());
        }
        if (other.getLevel() != 0) {
          setLevel(other.getLevel());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        QueryCurrentRankResp parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (QueryCurrentRankResp) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int result_ ;
      /**
       * <code>uint32 result = 1;</code>
       */
      public int getResult() {
        return result_;
      }
      /**
       * <code>uint32 result = 1;</code>
       */
      public Builder setResult(int value) {
        
        result_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 result = 1;</code>
       */
      public Builder clearResult() {
        
        result_ = 0;
        onChanged();
        return this;
      }

      private int level_ ;
      /**
       * <code>uint32 level = 2;</code>
       */
      public int getLevel() {
        return level_;
      }
      /**
       * <code>uint32 level = 2;</code>
       */
      public Builder setLevel(int value) {
        
        level_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 level = 2;</code>
       */
      public Builder clearLevel() {
        
        level_ = 0;
        onChanged();
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.api.QueryCurrentRankResp)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.api.QueryCurrentRankResp)
    private static final QueryCurrentRankResp DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new QueryCurrentRankResp();
    }

    public static QueryCurrentRankResp getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QueryCurrentRankResp>
        PARSER = new com.google.protobuf.AbstractParser<QueryCurrentRankResp>() {
      @Override
      public QueryCurrentRankResp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QueryCurrentRankResp(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QueryCurrentRankResp> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<QueryCurrentRankResp> getParserForType() {
      return PARSER;
    }

    @Override
    public QueryCurrentRankResp getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BatchQueryCurrentRankReqOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.api.BatchQueryCurrentRankReq)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated uint32 uid = 1;</code>
     */
    java.util.List<Integer> getUidList();
    /**
     * <code>repeated uint32 uid = 1;</code>
     */
    int getUidCount();
    /**
     * <code>repeated uint32 uid = 1;</code>
     */
    int getUid(int index);
  }
  /**
   * Protobuf type {@code com.yy.yyzone.guildrank.api.BatchQueryCurrentRankReq}
   */
  public  static final class BatchQueryCurrentRankReq extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.api.BatchQueryCurrentRankReq)
      BatchQueryCurrentRankReqOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BatchQueryCurrentRankReq.newBuilder() to construct.
    private BatchQueryCurrentRankReq(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BatchQueryCurrentRankReq() {
      uid_ = java.util.Collections.emptyList();
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BatchQueryCurrentRankReq(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                uid_ = new java.util.ArrayList<Integer>();
                mutable_bitField0_ |= 0x00000001;
              }
              uid_.add(input.readUInt32());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001) && input.getBytesUntilLimit() > 0) {
                uid_ = new java.util.ArrayList<Integer>();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                uid_.add(input.readUInt32());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          uid_ = java.util.Collections.unmodifiableList(uid_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return GuildRank.internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankReq_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return GuildRank.internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              BatchQueryCurrentRankReq.class, Builder.class);
    }

    public static final int UID_FIELD_NUMBER = 1;
    private java.util.List<Integer> uid_;
    /**
     * <code>repeated uint32 uid = 1;</code>
     */
    public java.util.List<Integer>
        getUidList() {
      return uid_;
    }
    /**
     * <code>repeated uint32 uid = 1;</code>
     */
    public int getUidCount() {
      return uid_.size();
    }
    /**
     * <code>repeated uint32 uid = 1;</code>
     */
    public int getUid(int index) {
      return uid_.get(index);
    }
    private int uidMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (getUidList().size() > 0) {
        output.writeUInt32NoTag(10);
        output.writeUInt32NoTag(uidMemoizedSerializedSize);
      }
      for (int i = 0; i < uid_.size(); i++) {
        output.writeUInt32NoTag(uid_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < uid_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(uid_.get(i));
        }
        size += dataSize;
        if (!getUidList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        uidMemoizedSerializedSize = dataSize;
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof BatchQueryCurrentRankReq)) {
        return super.equals(obj);
      }
      BatchQueryCurrentRankReq other = (BatchQueryCurrentRankReq) obj;

      boolean result = true;
      result = result && getUidList()
          .equals(other.getUidList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getUidCount() > 0) {
        hash = (37 * hash) + UID_FIELD_NUMBER;
        hash = (53 * hash) + getUidList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static BatchQueryCurrentRankReq parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static BatchQueryCurrentRankReq parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static BatchQueryCurrentRankReq parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static BatchQueryCurrentRankReq parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static BatchQueryCurrentRankReq parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static BatchQueryCurrentRankReq parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static BatchQueryCurrentRankReq parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static BatchQueryCurrentRankReq parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static BatchQueryCurrentRankReq parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static BatchQueryCurrentRankReq parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static BatchQueryCurrentRankReq parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static BatchQueryCurrentRankReq parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(BatchQueryCurrentRankReq prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.yyzone.guildrank.api.BatchQueryCurrentRankReq}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.api.BatchQueryCurrentRankReq)
        BatchQueryCurrentRankReqOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankReq_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankReq_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                BatchQueryCurrentRankReq.class, Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.api.GuildRank.BatchQueryCurrentRankReq.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        uid_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankReq_descriptor;
      }

      @Override
      public BatchQueryCurrentRankReq getDefaultInstanceForType() {
        return BatchQueryCurrentRankReq.getDefaultInstance();
      }

      @Override
      public BatchQueryCurrentRankReq build() {
        BatchQueryCurrentRankReq result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public BatchQueryCurrentRankReq buildPartial() {
        BatchQueryCurrentRankReq result = new BatchQueryCurrentRankReq(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) == 0x00000001)) {
          uid_ = java.util.Collections.unmodifiableList(uid_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.uid_ = uid_;
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof BatchQueryCurrentRankReq) {
          return mergeFrom((BatchQueryCurrentRankReq)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(BatchQueryCurrentRankReq other) {
        if (other == BatchQueryCurrentRankReq.getDefaultInstance()) return this;
        if (!other.uid_.isEmpty()) {
          if (uid_.isEmpty()) {
            uid_ = other.uid_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureUidIsMutable();
            uid_.addAll(other.uid_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        BatchQueryCurrentRankReq parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (BatchQueryCurrentRankReq) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<Integer> uid_ = java.util.Collections.emptyList();
      private void ensureUidIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          uid_ = new java.util.ArrayList<Integer>(uid_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated uint32 uid = 1;</code>
       */
      public java.util.List<Integer>
          getUidList() {
        return java.util.Collections.unmodifiableList(uid_);
      }
      /**
       * <code>repeated uint32 uid = 1;</code>
       */
      public int getUidCount() {
        return uid_.size();
      }
      /**
       * <code>repeated uint32 uid = 1;</code>
       */
      public int getUid(int index) {
        return uid_.get(index);
      }
      /**
       * <code>repeated uint32 uid = 1;</code>
       */
      public Builder setUid(
          int index, int value) {
        ensureUidIsMutable();
        uid_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 uid = 1;</code>
       */
      public Builder addUid(int value) {
        ensureUidIsMutable();
        uid_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 uid = 1;</code>
       */
      public Builder addAllUid(
          Iterable<? extends Integer> values) {
        ensureUidIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, uid_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 uid = 1;</code>
       */
      public Builder clearUid() {
        uid_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.api.BatchQueryCurrentRankReq)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.api.BatchQueryCurrentRankReq)
    private static final BatchQueryCurrentRankReq DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new BatchQueryCurrentRankReq();
    }

    public static BatchQueryCurrentRankReq getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<BatchQueryCurrentRankReq>
        PARSER = new com.google.protobuf.AbstractParser<BatchQueryCurrentRankReq>() {
      @Override
      public BatchQueryCurrentRankReq parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BatchQueryCurrentRankReq(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BatchQueryCurrentRankReq> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<BatchQueryCurrentRankReq> getParserForType() {
      return PARSER;
    }

    @Override
    public BatchQueryCurrentRankReq getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BatchQueryCurrentRankRespOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.api.BatchQueryCurrentRankResp)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 result = 1;</code>
     */
    int getResult();

    /**
     * <code>map&lt;uint32, int32&gt; uidLevel = 2;</code>
     */
    int getUidLevelCount();
    /**
     * <code>map&lt;uint32, int32&gt; uidLevel = 2;</code>
     */
    boolean containsUidLevel(
            int key);
    /**
     * Use {@link #getUidLevelMap()} instead.
     */
    @Deprecated
    java.util.Map<Integer, Integer>
    getUidLevel();
    /**
     * <code>map&lt;uint32, int32&gt; uidLevel = 2;</code>
     */
    java.util.Map<Integer, Integer>
    getUidLevelMap();
    /**
     * <code>map&lt;uint32, int32&gt; uidLevel = 2;</code>
     */

    int getUidLevelOrDefault(
            int key,
            int defaultValue);
    /**
     * <code>map&lt;uint32, int32&gt; uidLevel = 2;</code>
     */

    int getUidLevelOrThrow(
            int key);
  }
  /**
   * Protobuf type {@code com.yy.yyzone.guildrank.api.BatchQueryCurrentRankResp}
   */
  public  static final class BatchQueryCurrentRankResp extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.api.BatchQueryCurrentRankResp)
      BatchQueryCurrentRankRespOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BatchQueryCurrentRankResp.newBuilder() to construct.
    private BatchQueryCurrentRankResp(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BatchQueryCurrentRankResp() {
      result_ = 0;
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BatchQueryCurrentRankResp(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              result_ = input.readUInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                uidLevel_ = com.google.protobuf.MapField.newMapField(
                    UidLevelDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000002;
              }
              com.google.protobuf.MapEntry<Integer, Integer>
              uidLevel__ = input.readMessage(
                  UidLevelDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              uidLevel_.getMutableMap().put(
                  uidLevel__.getKey(), uidLevel__.getValue());
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return GuildRank.internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankResp_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 2:
          return internalGetUidLevel();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return GuildRank.internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              BatchQueryCurrentRankResp.class, Builder.class);
    }

    private int bitField0_;
    public static final int RESULT_FIELD_NUMBER = 1;
    private int result_;
    /**
     * <code>uint32 result = 1;</code>
     */
    public int getResult() {
      return result_;
    }

    public static final int UIDLEVEL_FIELD_NUMBER = 2;
    private static final class UidLevelDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          Integer, Integer> defaultEntry =
              com.google.protobuf.MapEntry
              .<Integer, Integer>newDefaultInstance(
                  GuildRank.internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankResp_UidLevelEntry_descriptor,
                  com.google.protobuf.WireFormat.FieldType.UINT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0);
    }
    private com.google.protobuf.MapField<
        Integer, Integer> uidLevel_;
    private com.google.protobuf.MapField<Integer, Integer>
    internalGetUidLevel() {
      if (uidLevel_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            UidLevelDefaultEntryHolder.defaultEntry);
      }
      return uidLevel_;
    }

    public int getUidLevelCount() {
      return internalGetUidLevel().getMap().size();
    }
    /**
     * <code>map&lt;uint32, int32&gt; uidLevel = 2;</code>
     */

    public boolean containsUidLevel(
        int key) {
      
      return internalGetUidLevel().getMap().containsKey(key);
    }
    /**
     * Use {@link #getUidLevelMap()} instead.
     */
    @Deprecated
    public java.util.Map<Integer, Integer> getUidLevel() {
      return getUidLevelMap();
    }
    /**
     * <code>map&lt;uint32, int32&gt; uidLevel = 2;</code>
     */

    public java.util.Map<Integer, Integer> getUidLevelMap() {
      return internalGetUidLevel().getMap();
    }
    /**
     * <code>map&lt;uint32, int32&gt; uidLevel = 2;</code>
     */

    public int getUidLevelOrDefault(
        int key,
        int defaultValue) {
      
      java.util.Map<Integer, Integer> map =
          internalGetUidLevel().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;uint32, int32&gt; uidLevel = 2;</code>
     */

    public int getUidLevelOrThrow(
        int key) {
      
      java.util.Map<Integer, Integer> map =
          internalGetUidLevel().getMap();
      if (!map.containsKey(key)) {
        throw new IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (result_ != 0) {
        output.writeUInt32(1, result_);
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetUidLevel(),
          UidLevelDefaultEntryHolder.defaultEntry,
          2);
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (result_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, result_);
      }
      for (java.util.Map.Entry<Integer, Integer> entry
           : internalGetUidLevel().getMap().entrySet()) {
        com.google.protobuf.MapEntry<Integer, Integer>
        uidLevel__ = UidLevelDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(2, uidLevel__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof BatchQueryCurrentRankResp)) {
        return super.equals(obj);
      }
      BatchQueryCurrentRankResp other = (BatchQueryCurrentRankResp) obj;

      boolean result = true;
      result = result && (getResult()
          == other.getResult());
      result = result && internalGetUidLevel().equals(
          other.internalGetUidLevel());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + getResult();
      if (!internalGetUidLevel().getMap().isEmpty()) {
        hash = (37 * hash) + UIDLEVEL_FIELD_NUMBER;
        hash = (53 * hash) + internalGetUidLevel().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static BatchQueryCurrentRankResp parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static BatchQueryCurrentRankResp parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static BatchQueryCurrentRankResp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static BatchQueryCurrentRankResp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static BatchQueryCurrentRankResp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static BatchQueryCurrentRankResp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static BatchQueryCurrentRankResp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static BatchQueryCurrentRankResp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static BatchQueryCurrentRankResp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static BatchQueryCurrentRankResp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static BatchQueryCurrentRankResp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static BatchQueryCurrentRankResp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(BatchQueryCurrentRankResp prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.yyzone.guildrank.api.BatchQueryCurrentRankResp}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.api.BatchQueryCurrentRankResp)
        BatchQueryCurrentRankRespOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankResp_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 2:
            return internalGetUidLevel();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 2:
            return internalGetMutableUidLevel();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankResp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                BatchQueryCurrentRankResp.class, Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.api.GuildRank.BatchQueryCurrentRankResp.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        result_ = 0;

        internalGetMutableUidLevel().clear();
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankResp_descriptor;
      }

      @Override
      public BatchQueryCurrentRankResp getDefaultInstanceForType() {
        return BatchQueryCurrentRankResp.getDefaultInstance();
      }

      @Override
      public BatchQueryCurrentRankResp build() {
        BatchQueryCurrentRankResp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public BatchQueryCurrentRankResp buildPartial() {
        BatchQueryCurrentRankResp result = new BatchQueryCurrentRankResp(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.result_ = result_;
        result.uidLevel_ = internalGetUidLevel();
        result.uidLevel_.makeImmutable();
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof BatchQueryCurrentRankResp) {
          return mergeFrom((BatchQueryCurrentRankResp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(BatchQueryCurrentRankResp other) {
        if (other == BatchQueryCurrentRankResp.getDefaultInstance()) return this;
        if (other.getResult() != 0) {
          setResult(other.getResult());
        }
        internalGetMutableUidLevel().mergeFrom(
            other.internalGetUidLevel());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        BatchQueryCurrentRankResp parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (BatchQueryCurrentRankResp) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int result_ ;
      /**
       * <code>uint32 result = 1;</code>
       */
      public int getResult() {
        return result_;
      }
      /**
       * <code>uint32 result = 1;</code>
       */
      public Builder setResult(int value) {
        
        result_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 result = 1;</code>
       */
      public Builder clearResult() {
        
        result_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.MapField<
          Integer, Integer> uidLevel_;
      private com.google.protobuf.MapField<Integer, Integer>
      internalGetUidLevel() {
        if (uidLevel_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              UidLevelDefaultEntryHolder.defaultEntry);
        }
        return uidLevel_;
      }
      private com.google.protobuf.MapField<Integer, Integer>
      internalGetMutableUidLevel() {
        onChanged();;
        if (uidLevel_ == null) {
          uidLevel_ = com.google.protobuf.MapField.newMapField(
              UidLevelDefaultEntryHolder.defaultEntry);
        }
        if (!uidLevel_.isMutable()) {
          uidLevel_ = uidLevel_.copy();
        }
        return uidLevel_;
      }

      public int getUidLevelCount() {
        return internalGetUidLevel().getMap().size();
      }
      /**
       * <code>map&lt;uint32, int32&gt; uidLevel = 2;</code>
       */

      public boolean containsUidLevel(
          int key) {
        
        return internalGetUidLevel().getMap().containsKey(key);
      }
      /**
       * Use {@link #getUidLevelMap()} instead.
       */
      @Deprecated
      public java.util.Map<Integer, Integer> getUidLevel() {
        return getUidLevelMap();
      }
      /**
       * <code>map&lt;uint32, int32&gt; uidLevel = 2;</code>
       */

      public java.util.Map<Integer, Integer> getUidLevelMap() {
        return internalGetUidLevel().getMap();
      }
      /**
       * <code>map&lt;uint32, int32&gt; uidLevel = 2;</code>
       */

      public int getUidLevelOrDefault(
          int key,
          int defaultValue) {
        
        java.util.Map<Integer, Integer> map =
            internalGetUidLevel().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;uint32, int32&gt; uidLevel = 2;</code>
       */

      public int getUidLevelOrThrow(
          int key) {
        
        java.util.Map<Integer, Integer> map =
            internalGetUidLevel().getMap();
        if (!map.containsKey(key)) {
          throw new IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearUidLevel() {
        internalGetMutableUidLevel().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;uint32, int32&gt; uidLevel = 2;</code>
       */

      public Builder removeUidLevel(
          int key) {
        
        internalGetMutableUidLevel().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @Deprecated
      public java.util.Map<Integer, Integer>
      getMutableUidLevel() {
        return internalGetMutableUidLevel().getMutableMap();
      }
      /**
       * <code>map&lt;uint32, int32&gt; uidLevel = 2;</code>
       */
      public Builder putUidLevel(
          int key,
          int value) {
        
        
        internalGetMutableUidLevel().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;uint32, int32&gt; uidLevel = 2;</code>
       */

      public Builder putAllUidLevel(
          java.util.Map<Integer, Integer> values) {
        internalGetMutableUidLevel().getMutableMap()
            .putAll(values);
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.api.BatchQueryCurrentRankResp)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.api.BatchQueryCurrentRankResp)
    private static final BatchQueryCurrentRankResp DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new BatchQueryCurrentRankResp();
    }

    public static BatchQueryCurrentRankResp getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<BatchQueryCurrentRankResp>
        PARSER = new com.google.protobuf.AbstractParser<BatchQueryCurrentRankResp>() {
      @Override
      public BatchQueryCurrentRankResp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BatchQueryCurrentRankResp(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BatchQueryCurrentRankResp> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<BatchQueryCurrentRankResp> getParserForType() {
      return PARSER;
    }

    @Override
    public BatchQueryCurrentRankResp getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BatchQueryGuildRankReqOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.api.BatchQueryGuildRankReq)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated uint32 uid = 1;</code>
     */
    java.util.List<Integer> getUidList();
    /**
     * <code>repeated uint32 uid = 1;</code>
     */
    int getUidCount();
    /**
     * <code>repeated uint32 uid = 1;</code>
     */
    int getUid(int index);
  }
  /**
   * Protobuf type {@code com.yy.yyzone.guildrank.api.BatchQueryGuildRankReq}
   */
  public  static final class BatchQueryGuildRankReq extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.api.BatchQueryGuildRankReq)
      BatchQueryGuildRankReqOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BatchQueryGuildRankReq.newBuilder() to construct.
    private BatchQueryGuildRankReq(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BatchQueryGuildRankReq() {
      uid_ = java.util.Collections.emptyList();
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BatchQueryGuildRankReq(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                uid_ = new java.util.ArrayList<Integer>();
                mutable_bitField0_ |= 0x00000001;
              }
              uid_.add(input.readUInt32());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001) && input.getBytesUntilLimit() > 0) {
                uid_ = new java.util.ArrayList<Integer>();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                uid_.add(input.readUInt32());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          uid_ = java.util.Collections.unmodifiableList(uid_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return GuildRank.internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankReq_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return GuildRank.internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              BatchQueryGuildRankReq.class, Builder.class);
    }

    public static final int UID_FIELD_NUMBER = 1;
    private java.util.List<Integer> uid_;
    /**
     * <code>repeated uint32 uid = 1;</code>
     */
    public java.util.List<Integer>
        getUidList() {
      return uid_;
    }
    /**
     * <code>repeated uint32 uid = 1;</code>
     */
    public int getUidCount() {
      return uid_.size();
    }
    /**
     * <code>repeated uint32 uid = 1;</code>
     */
    public int getUid(int index) {
      return uid_.get(index);
    }
    private int uidMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (getUidList().size() > 0) {
        output.writeUInt32NoTag(10);
        output.writeUInt32NoTag(uidMemoizedSerializedSize);
      }
      for (int i = 0; i < uid_.size(); i++) {
        output.writeUInt32NoTag(uid_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < uid_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(uid_.get(i));
        }
        size += dataSize;
        if (!getUidList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        uidMemoizedSerializedSize = dataSize;
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof BatchQueryGuildRankReq)) {
        return super.equals(obj);
      }
      BatchQueryGuildRankReq other = (BatchQueryGuildRankReq) obj;

      boolean result = true;
      result = result && getUidList()
          .equals(other.getUidList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getUidCount() > 0) {
        hash = (37 * hash) + UID_FIELD_NUMBER;
        hash = (53 * hash) + getUidList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static BatchQueryGuildRankReq parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static BatchQueryGuildRankReq parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static BatchQueryGuildRankReq parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static BatchQueryGuildRankReq parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static BatchQueryGuildRankReq parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static BatchQueryGuildRankReq parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static BatchQueryGuildRankReq parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static BatchQueryGuildRankReq parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static BatchQueryGuildRankReq parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static BatchQueryGuildRankReq parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static BatchQueryGuildRankReq parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static BatchQueryGuildRankReq parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(BatchQueryGuildRankReq prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.yyzone.guildrank.api.BatchQueryGuildRankReq}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.api.BatchQueryGuildRankReq)
        BatchQueryGuildRankReqOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankReq_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankReq_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                BatchQueryGuildRankReq.class, Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.api.GuildRank.BatchQueryGuildRankReq.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        uid_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankReq_descriptor;
      }

      @Override
      public BatchQueryGuildRankReq getDefaultInstanceForType() {
        return BatchQueryGuildRankReq.getDefaultInstance();
      }

      @Override
      public BatchQueryGuildRankReq build() {
        BatchQueryGuildRankReq result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public BatchQueryGuildRankReq buildPartial() {
        BatchQueryGuildRankReq result = new BatchQueryGuildRankReq(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) == 0x00000001)) {
          uid_ = java.util.Collections.unmodifiableList(uid_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.uid_ = uid_;
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof BatchQueryGuildRankReq) {
          return mergeFrom((BatchQueryGuildRankReq)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(BatchQueryGuildRankReq other) {
        if (other == BatchQueryGuildRankReq.getDefaultInstance()) return this;
        if (!other.uid_.isEmpty()) {
          if (uid_.isEmpty()) {
            uid_ = other.uid_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureUidIsMutable();
            uid_.addAll(other.uid_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        BatchQueryGuildRankReq parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (BatchQueryGuildRankReq) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<Integer> uid_ = java.util.Collections.emptyList();
      private void ensureUidIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          uid_ = new java.util.ArrayList<Integer>(uid_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated uint32 uid = 1;</code>
       */
      public java.util.List<Integer>
          getUidList() {
        return java.util.Collections.unmodifiableList(uid_);
      }
      /**
       * <code>repeated uint32 uid = 1;</code>
       */
      public int getUidCount() {
        return uid_.size();
      }
      /**
       * <code>repeated uint32 uid = 1;</code>
       */
      public int getUid(int index) {
        return uid_.get(index);
      }
      /**
       * <code>repeated uint32 uid = 1;</code>
       */
      public Builder setUid(
          int index, int value) {
        ensureUidIsMutable();
        uid_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 uid = 1;</code>
       */
      public Builder addUid(int value) {
        ensureUidIsMutable();
        uid_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 uid = 1;</code>
       */
      public Builder addAllUid(
          Iterable<? extends Integer> values) {
        ensureUidIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, uid_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 uid = 1;</code>
       */
      public Builder clearUid() {
        uid_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.api.BatchQueryGuildRankReq)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.api.BatchQueryGuildRankReq)
    private static final BatchQueryGuildRankReq DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new BatchQueryGuildRankReq();
    }

    public static BatchQueryGuildRankReq getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<BatchQueryGuildRankReq>
        PARSER = new com.google.protobuf.AbstractParser<BatchQueryGuildRankReq>() {
      @Override
      public BatchQueryGuildRankReq parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BatchQueryGuildRankReq(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BatchQueryGuildRankReq> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<BatchQueryGuildRankReq> getParserForType() {
      return PARSER;
    }

    @Override
    public BatchQueryGuildRankReq getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BatchQueryGuildRankRespOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.api.BatchQueryGuildRankResp)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 result = 1;</code>
     */
    int getResult();

    /**
     * <pre>
     *是否上个月最新的段位 1:是 0:不是
     * </pre>
     *
     * <code>uint32 isNew = 2;</code>
     */
    int getIsNew();

    /**
     * <code>map&lt;uint32, int32&gt; uidLevel = 3;</code>
     */
    int getUidLevelCount();
    /**
     * <code>map&lt;uint32, int32&gt; uidLevel = 3;</code>
     */
    boolean containsUidLevel(
            int key);
    /**
     * Use {@link #getUidLevelMap()} instead.
     */
    @Deprecated
    java.util.Map<Integer, Integer>
    getUidLevel();
    /**
     * <code>map&lt;uint32, int32&gt; uidLevel = 3;</code>
     */
    java.util.Map<Integer, Integer>
    getUidLevelMap();
    /**
     * <code>map&lt;uint32, int32&gt; uidLevel = 3;</code>
     */

    int getUidLevelOrDefault(
            int key,
            int defaultValue);
    /**
     * <code>map&lt;uint32, int32&gt; uidLevel = 3;</code>
     */

    int getUidLevelOrThrow(
            int key);
  }
  /**
   * Protobuf type {@code com.yy.yyzone.guildrank.api.BatchQueryGuildRankResp}
   */
  public  static final class BatchQueryGuildRankResp extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.api.BatchQueryGuildRankResp)
      BatchQueryGuildRankRespOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BatchQueryGuildRankResp.newBuilder() to construct.
    private BatchQueryGuildRankResp(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BatchQueryGuildRankResp() {
      result_ = 0;
      isNew_ = 0;
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BatchQueryGuildRankResp(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              result_ = input.readUInt32();
              break;
            }
            case 16: {

              isNew_ = input.readUInt32();
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
                uidLevel_ = com.google.protobuf.MapField.newMapField(
                    UidLevelDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000004;
              }
              com.google.protobuf.MapEntry<Integer, Integer>
              uidLevel__ = input.readMessage(
                  UidLevelDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              uidLevel_.getMutableMap().put(
                  uidLevel__.getKey(), uidLevel__.getValue());
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return GuildRank.internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankResp_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 3:
          return internalGetUidLevel();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return GuildRank.internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              BatchQueryGuildRankResp.class, Builder.class);
    }

    private int bitField0_;
    public static final int RESULT_FIELD_NUMBER = 1;
    private int result_;
    /**
     * <code>uint32 result = 1;</code>
     */
    public int getResult() {
      return result_;
    }

    public static final int ISNEW_FIELD_NUMBER = 2;
    private int isNew_;
    /**
     * <pre>
     *是否上个月最新的段位 1:是 0:不是
     * </pre>
     *
     * <code>uint32 isNew = 2;</code>
     */
    public int getIsNew() {
      return isNew_;
    }

    public static final int UIDLEVEL_FIELD_NUMBER = 3;
    private static final class UidLevelDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          Integer, Integer> defaultEntry =
              com.google.protobuf.MapEntry
              .<Integer, Integer>newDefaultInstance(
                  GuildRank.internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankResp_UidLevelEntry_descriptor,
                  com.google.protobuf.WireFormat.FieldType.UINT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0);
    }
    private com.google.protobuf.MapField<
        Integer, Integer> uidLevel_;
    private com.google.protobuf.MapField<Integer, Integer>
    internalGetUidLevel() {
      if (uidLevel_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            UidLevelDefaultEntryHolder.defaultEntry);
      }
      return uidLevel_;
    }

    public int getUidLevelCount() {
      return internalGetUidLevel().getMap().size();
    }
    /**
     * <code>map&lt;uint32, int32&gt; uidLevel = 3;</code>
     */

    public boolean containsUidLevel(
        int key) {
      
      return internalGetUidLevel().getMap().containsKey(key);
    }
    /**
     * Use {@link #getUidLevelMap()} instead.
     */
    @Deprecated
    public java.util.Map<Integer, Integer> getUidLevel() {
      return getUidLevelMap();
    }
    /**
     * <code>map&lt;uint32, int32&gt; uidLevel = 3;</code>
     */

    public java.util.Map<Integer, Integer> getUidLevelMap() {
      return internalGetUidLevel().getMap();
    }
    /**
     * <code>map&lt;uint32, int32&gt; uidLevel = 3;</code>
     */

    public int getUidLevelOrDefault(
        int key,
        int defaultValue) {
      
      java.util.Map<Integer, Integer> map =
          internalGetUidLevel().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;uint32, int32&gt; uidLevel = 3;</code>
     */

    public int getUidLevelOrThrow(
        int key) {
      
      java.util.Map<Integer, Integer> map =
          internalGetUidLevel().getMap();
      if (!map.containsKey(key)) {
        throw new IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (result_ != 0) {
        output.writeUInt32(1, result_);
      }
      if (isNew_ != 0) {
        output.writeUInt32(2, isNew_);
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetUidLevel(),
          UidLevelDefaultEntryHolder.defaultEntry,
          3);
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (result_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, result_);
      }
      if (isNew_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, isNew_);
      }
      for (java.util.Map.Entry<Integer, Integer> entry
           : internalGetUidLevel().getMap().entrySet()) {
        com.google.protobuf.MapEntry<Integer, Integer>
        uidLevel__ = UidLevelDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(3, uidLevel__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof BatchQueryGuildRankResp)) {
        return super.equals(obj);
      }
      BatchQueryGuildRankResp other = (BatchQueryGuildRankResp) obj;

      boolean result = true;
      result = result && (getResult()
          == other.getResult());
      result = result && (getIsNew()
          == other.getIsNew());
      result = result && internalGetUidLevel().equals(
          other.internalGetUidLevel());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + getResult();
      hash = (37 * hash) + ISNEW_FIELD_NUMBER;
      hash = (53 * hash) + getIsNew();
      if (!internalGetUidLevel().getMap().isEmpty()) {
        hash = (37 * hash) + UIDLEVEL_FIELD_NUMBER;
        hash = (53 * hash) + internalGetUidLevel().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static BatchQueryGuildRankResp parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static BatchQueryGuildRankResp parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static BatchQueryGuildRankResp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static BatchQueryGuildRankResp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static BatchQueryGuildRankResp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static BatchQueryGuildRankResp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static BatchQueryGuildRankResp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static BatchQueryGuildRankResp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static BatchQueryGuildRankResp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static BatchQueryGuildRankResp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static BatchQueryGuildRankResp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static BatchQueryGuildRankResp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(BatchQueryGuildRankResp prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.yyzone.guildrank.api.BatchQueryGuildRankResp}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.api.BatchQueryGuildRankResp)
        BatchQueryGuildRankRespOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankResp_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 3:
            return internalGetUidLevel();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 3:
            return internalGetMutableUidLevel();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankResp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                BatchQueryGuildRankResp.class, Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.api.GuildRank.BatchQueryGuildRankResp.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        result_ = 0;

        isNew_ = 0;

        internalGetMutableUidLevel().clear();
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return GuildRank.internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankResp_descriptor;
      }

      @Override
      public BatchQueryGuildRankResp getDefaultInstanceForType() {
        return BatchQueryGuildRankResp.getDefaultInstance();
      }

      @Override
      public BatchQueryGuildRankResp build() {
        BatchQueryGuildRankResp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public BatchQueryGuildRankResp buildPartial() {
        BatchQueryGuildRankResp result = new BatchQueryGuildRankResp(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.result_ = result_;
        result.isNew_ = isNew_;
        result.uidLevel_ = internalGetUidLevel();
        result.uidLevel_.makeImmutable();
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof BatchQueryGuildRankResp) {
          return mergeFrom((BatchQueryGuildRankResp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(BatchQueryGuildRankResp other) {
        if (other == BatchQueryGuildRankResp.getDefaultInstance()) return this;
        if (other.getResult() != 0) {
          setResult(other.getResult());
        }
        if (other.getIsNew() != 0) {
          setIsNew(other.getIsNew());
        }
        internalGetMutableUidLevel().mergeFrom(
            other.internalGetUidLevel());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        BatchQueryGuildRankResp parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (BatchQueryGuildRankResp) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int result_ ;
      /**
       * <code>uint32 result = 1;</code>
       */
      public int getResult() {
        return result_;
      }
      /**
       * <code>uint32 result = 1;</code>
       */
      public Builder setResult(int value) {
        
        result_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 result = 1;</code>
       */
      public Builder clearResult() {
        
        result_ = 0;
        onChanged();
        return this;
      }

      private int isNew_ ;
      /**
       * <pre>
       *是否上个月最新的段位 1:是 0:不是
       * </pre>
       *
       * <code>uint32 isNew = 2;</code>
       */
      public int getIsNew() {
        return isNew_;
      }
      /**
       * <pre>
       *是否上个月最新的段位 1:是 0:不是
       * </pre>
       *
       * <code>uint32 isNew = 2;</code>
       */
      public Builder setIsNew(int value) {
        
        isNew_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *是否上个月最新的段位 1:是 0:不是
       * </pre>
       *
       * <code>uint32 isNew = 2;</code>
       */
      public Builder clearIsNew() {
        
        isNew_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.MapField<
          Integer, Integer> uidLevel_;
      private com.google.protobuf.MapField<Integer, Integer>
      internalGetUidLevel() {
        if (uidLevel_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              UidLevelDefaultEntryHolder.defaultEntry);
        }
        return uidLevel_;
      }
      private com.google.protobuf.MapField<Integer, Integer>
      internalGetMutableUidLevel() {
        onChanged();;
        if (uidLevel_ == null) {
          uidLevel_ = com.google.protobuf.MapField.newMapField(
              UidLevelDefaultEntryHolder.defaultEntry);
        }
        if (!uidLevel_.isMutable()) {
          uidLevel_ = uidLevel_.copy();
        }
        return uidLevel_;
      }

      public int getUidLevelCount() {
        return internalGetUidLevel().getMap().size();
      }
      /**
       * <code>map&lt;uint32, int32&gt; uidLevel = 3;</code>
       */

      public boolean containsUidLevel(
          int key) {
        
        return internalGetUidLevel().getMap().containsKey(key);
      }
      /**
       * Use {@link #getUidLevelMap()} instead.
       */
      @Deprecated
      public java.util.Map<Integer, Integer> getUidLevel() {
        return getUidLevelMap();
      }
      /**
       * <code>map&lt;uint32, int32&gt; uidLevel = 3;</code>
       */

      public java.util.Map<Integer, Integer> getUidLevelMap() {
        return internalGetUidLevel().getMap();
      }
      /**
       * <code>map&lt;uint32, int32&gt; uidLevel = 3;</code>
       */

      public int getUidLevelOrDefault(
          int key,
          int defaultValue) {
        
        java.util.Map<Integer, Integer> map =
            internalGetUidLevel().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;uint32, int32&gt; uidLevel = 3;</code>
       */

      public int getUidLevelOrThrow(
          int key) {
        
        java.util.Map<Integer, Integer> map =
            internalGetUidLevel().getMap();
        if (!map.containsKey(key)) {
          throw new IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearUidLevel() {
        internalGetMutableUidLevel().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;uint32, int32&gt; uidLevel = 3;</code>
       */

      public Builder removeUidLevel(
          int key) {
        
        internalGetMutableUidLevel().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @Deprecated
      public java.util.Map<Integer, Integer>
      getMutableUidLevel() {
        return internalGetMutableUidLevel().getMutableMap();
      }
      /**
       * <code>map&lt;uint32, int32&gt; uidLevel = 3;</code>
       */
      public Builder putUidLevel(
          int key,
          int value) {
        
        
        internalGetMutableUidLevel().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;uint32, int32&gt; uidLevel = 3;</code>
       */

      public Builder putAllUidLevel(
          java.util.Map<Integer, Integer> values) {
        internalGetMutableUidLevel().getMutableMap()
            .putAll(values);
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.api.BatchQueryGuildRankResp)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.api.BatchQueryGuildRankResp)
    private static final BatchQueryGuildRankResp DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new BatchQueryGuildRankResp();
    }

    public static BatchQueryGuildRankResp getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<BatchQueryGuildRankResp>
        PARSER = new com.google.protobuf.AbstractParser<BatchQueryGuildRankResp>() {
      @Override
      public BatchQueryGuildRankResp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BatchQueryGuildRankResp(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BatchQueryGuildRankResp> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<BatchQueryGuildRankResp> getParserForType() {
      return PARSER;
    }

    @Override
    public BatchQueryGuildRankResp getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_QueryGuildRankReq_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_QueryGuildRankReq_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_QueryGuildRankResp_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_QueryGuildRankResp_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_QueryGuildRankResp_UidLevelEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_QueryGuildRankResp_UidLevelEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_QueryGuildRankResp_UidChangeEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_QueryGuildRankResp_UidChangeEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_updateFlagResp_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_updateFlagResp_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_updateFlagReq_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_updateFlagReq_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_CurrentRankReq_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_CurrentRankReq_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_CurrentRankResp_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_CurrentRankResp_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_QueryCurrentRankReq_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_QueryCurrentRankReq_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_QueryCurrentRankResp_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_QueryCurrentRankResp_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankReq_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankReq_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankResp_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankResp_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankResp_UidLevelEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankResp_UidLevelEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankReq_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankReq_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankResp_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankResp_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankResp_UidLevelEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankResp_UidLevelEntry_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    String[] descriptorData = {
      "\n\017GuildRank.proto\022\033com.yy.yyzone.guildra" +
      "nk.api\032\turi.proto\"2\n\021QueryGuildRankReq\022\016" +
      "\n\006owUids\030\001 \003(\004\022\r\n\005month\030\002 \001(\t\"\253\002\n\022QueryG" +
      "uildRankResp\022\016\n\006result\030\001 \001(\r\022O\n\010uidLevel" +
      "\030\002 \003(\0132=.com.yy.yyzone.guildrank.api.Que" +
      "ryGuildRankResp.UidLevelEntry\022Q\n\tuidChan" +
      "ge\030\003 \003(\0132>.com.yy.yyzone.guildrank.api.Q" +
      "ueryGuildRankResp.UidChangeEntry\032/\n\rUidL" +
      "evelEntry\022\013\n\003key\030\001 \001(\004\022\r\n\005value\030\002 \001(\r:\0028" +
      "\001\0320\n\016UidChangeEntry\022\013\n\003key\030\001 \001(\004\022\r\n\005valu" +
      "e\030\002 \001(\r:\0028\001\" \n\016updateFlagResp\022\016\n\006result\030" +
      "\001 \001(\r\"\036\n\rupdateFlagReq\022\r\n\005param\030\001 \001(\r\"\"\n" +
      "\016CurrentRankReq\022\013\n\003uid\030\001 \001(\004:\003\310>\001\"5\n\017Cur" +
      "rentRankResp\022\016\n\006result\030\001 \001(\r\022\r\n\005level\030\002 " +
      "\001(\r:\003\310>\001\"D\n\023QueryCurrentRankReq\022\013\n\003uid\030\001" +
      " \001(\003\022\r\n\005appid\030\002 \001(\t\022\014\n\004sign\030\003 \001(\t:\003\310>\001\":" +
      "\n\024QueryCurrentRankResp\022\016\n\006result\030\001 \001(\r\022\r" +
      "\n\005level\030\002 \001(\r:\003\310>\001\",\n\030BatchQueryCurrentR" +
      "ankReq\022\013\n\003uid\030\001 \003(\r:\003\310>\001\"\271\001\n\031BatchQueryC" +
      "urrentRankResp\022\016\n\006result\030\001 \001(\r\022V\n\010uidLev" +
      "el\030\002 \003(\0132D.com.yy.yyzone.guildrank.api.B" +
      "atchQueryCurrentRankResp.UidLevelEntry\032/" +
      "\n\rUidLevelEntry\022\013\n\003key\030\001 \001(\r\022\r\n\005value\030\002 " +
      "\001(\005:\0028\001:\003\310>\001\"*\n\026BatchQueryGuildRankReq\022\013" +
      "\n\003uid\030\001 \003(\r:\003\310>\001\"\304\001\n\027BatchQueryGuildRank" +
      "Resp\022\016\n\006result\030\001 \001(\r\022\r\n\005isNew\030\002 \001(\r\022T\n\010u" +
      "idLevel\030\003 \003(\0132B.com.yy.yyzone.guildrank." +
      "api.BatchQueryGuildRankResp.UidLevelEntr" +
      "y\032/\n\rUidLevelEntry\022\013\n\003key\030\001 \001(\r\022\r\n\005value" +
      "\030\002 \001(\005:\0028\001:\003\310>\0012\336\003\n\020GuildrankService\022q\n\016" +
      "queryGuildRank\022..com.yy.yyzone.guildrank" +
      ".api.QueryGuildRankReq\032/.com.yy.yyzone.g" +
      "uildrank.api.QueryGuildRankResp\022i\n\016query" +
      "HasUpdate\022*.com.yy.yyzone.guildrank.api." +
      "updateFlagReq\032+.com.yy.yyzone.guildrank." +
      "api.updateFlagResp\022m\n\020queryCurrentRank\022+" +
      ".com.yy.yyzone.guildrank.api.CurrentRank" +
      "Req\032,.com.yy.yyzone.guildrank.api.Curren" +
      "tRankResp\022}\n\026queryCurrentRankByFrom\0220.co" +
      "m.yy.yyzone.guildrank.api.QueryCurrentRa" +
      "nkReq\0321.com.yy.yyzone.guildrank.api.Quer" +
      "yCurrentRankRespb\006proto3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          yyp.Uri.getDescriptor(),
        }, assigner);
    internal_static_com_yy_yyzone_guildrank_api_QueryGuildRankReq_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yy_yyzone_guildrank_api_QueryGuildRankReq_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_QueryGuildRankReq_descriptor,
        new String[] { "OwUids", "Month", });
    internal_static_com_yy_yyzone_guildrank_api_QueryGuildRankResp_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yy_yyzone_guildrank_api_QueryGuildRankResp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_QueryGuildRankResp_descriptor,
        new String[] { "Result", "UidLevel", "UidChange", });
    internal_static_com_yy_yyzone_guildrank_api_QueryGuildRankResp_UidLevelEntry_descriptor =
      internal_static_com_yy_yyzone_guildrank_api_QueryGuildRankResp_descriptor.getNestedTypes().get(0);
    internal_static_com_yy_yyzone_guildrank_api_QueryGuildRankResp_UidLevelEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_QueryGuildRankResp_UidLevelEntry_descriptor,
        new String[] { "Key", "Value", });
    internal_static_com_yy_yyzone_guildrank_api_QueryGuildRankResp_UidChangeEntry_descriptor =
      internal_static_com_yy_yyzone_guildrank_api_QueryGuildRankResp_descriptor.getNestedTypes().get(1);
    internal_static_com_yy_yyzone_guildrank_api_QueryGuildRankResp_UidChangeEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_QueryGuildRankResp_UidChangeEntry_descriptor,
        new String[] { "Key", "Value", });
    internal_static_com_yy_yyzone_guildrank_api_updateFlagResp_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yy_yyzone_guildrank_api_updateFlagResp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_updateFlagResp_descriptor,
        new String[] { "Result", });
    internal_static_com_yy_yyzone_guildrank_api_updateFlagReq_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yy_yyzone_guildrank_api_updateFlagReq_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_updateFlagReq_descriptor,
        new String[] { "Param", });
    internal_static_com_yy_yyzone_guildrank_api_CurrentRankReq_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yy_yyzone_guildrank_api_CurrentRankReq_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_CurrentRankReq_descriptor,
        new String[] { "Uid", });
    internal_static_com_yy_yyzone_guildrank_api_CurrentRankResp_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yy_yyzone_guildrank_api_CurrentRankResp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_CurrentRankResp_descriptor,
        new String[] { "Result", "Level", });
    internal_static_com_yy_yyzone_guildrank_api_QueryCurrentRankReq_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yy_yyzone_guildrank_api_QueryCurrentRankReq_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_QueryCurrentRankReq_descriptor,
        new String[] { "Uid", "Appid", "Sign", });
    internal_static_com_yy_yyzone_guildrank_api_QueryCurrentRankResp_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yy_yyzone_guildrank_api_QueryCurrentRankResp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_QueryCurrentRankResp_descriptor,
        new String[] { "Result", "Level", });
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankReq_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankReq_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankReq_descriptor,
        new String[] { "Uid", });
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankResp_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankResp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankResp_descriptor,
        new String[] { "Result", "UidLevel", });
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankResp_UidLevelEntry_descriptor =
      internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankResp_descriptor.getNestedTypes().get(0);
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankResp_UidLevelEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankResp_UidLevelEntry_descriptor,
        new String[] { "Key", "Value", });
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankReq_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankReq_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankReq_descriptor,
        new String[] { "Uid", });
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankResp_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankResp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankResp_descriptor,
        new String[] { "Result", "IsNew", "UidLevel", });
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankResp_UidLevelEntry_descriptor =
      internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankResp_descriptor.getNestedTypes().get(0);
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankResp_UidLevelEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankResp_UidLevelEntry_descriptor,
        new String[] { "Key", "Value", });
    com.google.protobuf.ExtensionRegistry registry =
        com.google.protobuf.ExtensionRegistry.newInstance();
    registry.add(yyp.Uri.uri);
    com.google.protobuf.Descriptors.FileDescriptor
        .internalUpdateFileDescriptor(descriptor, registry);
    yyp.Uri.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
