package com.yy.yyzone.guildrank.api;

import com.yy.yyzone.guildrank.api.GuildRank.QueryGuildRankReq;
import com.yy.yyzone.guildrank.api.GuildRank.QueryGuildRankResp;
import com.yy.yyzone.guildrank.api.GuildRank.updateFlagReq;
import com.yy.yyzone.guildrank.api.GuildRank.updateFlagResp;
import org.apache.dubbo.common.annotation.Yrpc;

public interface GuildrankService {

	@Yrpc(functionName="queryGuildRank")
	QueryGuildRankResp queryGuildRank(QueryGuildRankReq req);

    /**
     * 与queryGuildRank一致，但实时查，不查缓存
     * @param req
     * @return
     */
    @Yrpc(functionName="queryGuildRankV2")
    QueryGuildRankResp queryGuildRankV2(QueryGuildRankReq req);

	@Yrpc(functionName="queryHasUpdate")
	updateFlagResp queryHasUpdate(updateFlagReq req);

	@Yrpc(functionName = "queryRankChange")
	RankChange.QueryRankChangeRes queryRankChange(RankChange.QueryRankChangeReq req);
}
