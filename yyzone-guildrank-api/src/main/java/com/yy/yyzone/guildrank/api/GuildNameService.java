package com.yy.yyzone.guildrank.api;

import com.yy.yyzone.guildrank.api.GuildName.PBatchGetGuildNameReq;
import com.yy.yyzone.guildrank.api.GuildName.PBatchGetGuildNameResp;
import org.apache.dubbo.common.annotation.Yrpc;

public interface GuildNameService {

	@Deprecated
	@Yrpc(functionName="batchGetGuildName",reqUri=26620 ,resUri=26876 )
	PBatchGetGuildNameResp batchGetGuildName(PBatchGetGuildNameReq req);

}
