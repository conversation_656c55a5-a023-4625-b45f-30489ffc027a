package com.yy.yyzone.guildrank.api;

import com.yy.yyzone.guildrank.api.dto.PBatchGetGuildNameReq64;
import com.yy.yyzone.guildrank.api.dto.PBatchGetGuildNameResp64;
import org.apache.dubbo.common.annotation.Yrpc;

/**
 * <AUTHOR>
 * @date 2021/12/23
 **/
public interface GuildNameClient {

    /**
     * 查询公会名称 64位
     *
     * @param req
     * @return
     */
    @Yrpc(functionName = "batchGetGuildName", reqUri = 103 << 8 | 253, resUri = 104 << 8 | 253)
    PBatchGetGuildNameResp64 batchGetGuildName(PBatchGetGuildNameReq64 req);
}
