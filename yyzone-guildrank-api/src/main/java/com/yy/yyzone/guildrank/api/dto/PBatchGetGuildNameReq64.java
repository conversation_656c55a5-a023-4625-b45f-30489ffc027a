package com.yy.yyzone.guildrank.api.dto;

import com.yy.ent.commons.protopack.util.Ulong;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2019/9/16
 */
public class PBatchGetGuildNameReq64 {
    private List<Ulong> owuids;
    private Map<String, String> extendData;

    public List<Ulong> getOwuids() {
        return owuids;
    }

    public void setOwuids(List<Ulong> owuids) {
        this.owuids = owuids;
    }

    public Map<String, String> getExtendData() {
        return extendData;
    }

    public void setExtendData(Map<String, String> extendData) {
        this.extendData = extendData;
    }

    @Override
    public String toString() {
        return "PBatchGetGuildNameReq64{" +
                "owuids=" + owuids +
                ", extendData=" + extendData +
                '}';
    }
}
