package com.yy.yyzone.guildrank.api.dto;

import com.yy.ent.commons.protopack.util.Uint;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Map;

/**
 * @author: create by jp
 * @version: v1.0
 * @description: com.yy.yyzone.contractinfo.domain
 * @date:2021/3/4
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class QueryContractInfoByAnchorUidsResV2 {

    /**
     * 结果
     **/
    private Uint result;

    /**
     * 主播uid->公会ow
     **/
    private Map<Long,Long> data;

    private Map<String,String> extendData;

}
