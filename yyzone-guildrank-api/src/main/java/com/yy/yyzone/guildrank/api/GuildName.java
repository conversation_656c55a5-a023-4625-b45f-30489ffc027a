// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: guildName.proto

package com.yy.yyzone.guildrank.api;

public final class GuildName {
  private GuildName() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface PBatchGetGuildNameReqOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.api.PBatchGetGuildNameReq)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated uint32 owuids = 1;</code>
     */
    java.util.List<Integer> getOwuidsList();
    /**
     * <code>repeated uint32 owuids = 1;</code>
     */
    int getOwuidsCount();
    /**
     * <code>repeated uint32 owuids = 1;</code>
     */
    int getOwuids(int index);

    /**
     * <code>map&lt;string, string&gt; extendData = 2;</code>
     */
    int getExtendDataCount();
    /**
     * <code>map&lt;string, string&gt; extendData = 2;</code>
     */
    boolean containsExtendData(
            String key);
    /**
     * Use {@link #getExtendDataMap()} instead.
     */
    @Deprecated
    java.util.Map<String, String>
    getExtendData();
    /**
     * <code>map&lt;string, string&gt; extendData = 2;</code>
     */
    java.util.Map<String, String>
    getExtendDataMap();
    /**
     * <code>map&lt;string, string&gt; extendData = 2;</code>
     */

    String getExtendDataOrDefault(
            String key,
            String defaultValue);
    /**
     * <code>map&lt;string, string&gt; extendData = 2;</code>
     */

    String getExtendDataOrThrow(
            String key);
  }
  /**
   * <pre>
   *批量查询公会名称 103&lt;&lt;8|252
   * </pre>
   *
   * Protobuf type {@code com.yy.yyzone.guildrank.api.PBatchGetGuildNameReq}
   */
  public  static final class PBatchGetGuildNameReq extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.api.PBatchGetGuildNameReq)
      PBatchGetGuildNameReqOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PBatchGetGuildNameReq.newBuilder() to construct.
    private PBatchGetGuildNameReq(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PBatchGetGuildNameReq() {
      owuids_ = java.util.Collections.emptyList();
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PBatchGetGuildNameReq(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                owuids_ = new java.util.ArrayList<Integer>();
                mutable_bitField0_ |= 0x00000001;
              }
              owuids_.add(input.readUInt32());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001) && input.getBytesUntilLimit() > 0) {
                owuids_ = new java.util.ArrayList<Integer>();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                owuids_.add(input.readUInt32());
              }
              input.popLimit(limit);
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                extendData_ = com.google.protobuf.MapField.newMapField(
                    ExtendDataDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000002;
              }
              com.google.protobuf.MapEntry<String, String>
              extendData__ = input.readMessage(
                  ExtendDataDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              extendData_.getMutableMap().put(
                  extendData__.getKey(), extendData__.getValue());
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          owuids_ = java.util.Collections.unmodifiableList(owuids_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return GuildName.internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameReq_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 2:
          return internalGetExtendData();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return GuildName.internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              PBatchGetGuildNameReq.class, Builder.class);
    }

    public static final int OWUIDS_FIELD_NUMBER = 1;
    private java.util.List<Integer> owuids_;
    /**
     * <code>repeated uint32 owuids = 1;</code>
     */
    public java.util.List<Integer>
        getOwuidsList() {
      return owuids_;
    }
    /**
     * <code>repeated uint32 owuids = 1;</code>
     */
    public int getOwuidsCount() {
      return owuids_.size();
    }
    /**
     * <code>repeated uint32 owuids = 1;</code>
     */
    public int getOwuids(int index) {
      return owuids_.get(index);
    }
    private int owuidsMemoizedSerializedSize = -1;

    public static final int EXTENDDATA_FIELD_NUMBER = 2;
    private static final class ExtendDataDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          String, String> defaultEntry =
              com.google.protobuf.MapEntry
              .<String, String>newDefaultInstance(
                  GuildName.internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameReq_ExtendDataEntry_descriptor,
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "",
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "");
    }
    private com.google.protobuf.MapField<
        String, String> extendData_;
    private com.google.protobuf.MapField<String, String>
    internalGetExtendData() {
      if (extendData_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ExtendDataDefaultEntryHolder.defaultEntry);
      }
      return extendData_;
    }

    public int getExtendDataCount() {
      return internalGetExtendData().getMap().size();
    }
    /**
     * <code>map&lt;string, string&gt; extendData = 2;</code>
     */

    public boolean containsExtendData(
        String key) {
      if (key == null) { throw new NullPointerException(); }
      return internalGetExtendData().getMap().containsKey(key);
    }
    /**
     * Use {@link #getExtendDataMap()} instead.
     */
    @Deprecated
    public java.util.Map<String, String> getExtendData() {
      return getExtendDataMap();
    }
    /**
     * <code>map&lt;string, string&gt; extendData = 2;</code>
     */

    public java.util.Map<String, String> getExtendDataMap() {
      return internalGetExtendData().getMap();
    }
    /**
     * <code>map&lt;string, string&gt; extendData = 2;</code>
     */

    public String getExtendDataOrDefault(
        String key,
        String defaultValue) {
      if (key == null) { throw new NullPointerException(); }
      java.util.Map<String, String> map =
          internalGetExtendData().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;string, string&gt; extendData = 2;</code>
     */

    public String getExtendDataOrThrow(
        String key) {
      if (key == null) { throw new NullPointerException(); }
      java.util.Map<String, String> map =
          internalGetExtendData().getMap();
      if (!map.containsKey(key)) {
        throw new IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (getOwuidsList().size() > 0) {
        output.writeUInt32NoTag(10);
        output.writeUInt32NoTag(owuidsMemoizedSerializedSize);
      }
      for (int i = 0; i < owuids_.size(); i++) {
        output.writeUInt32NoTag(owuids_.get(i));
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeStringMapTo(
          output,
          internalGetExtendData(),
          ExtendDataDefaultEntryHolder.defaultEntry,
          2);
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < owuids_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(owuids_.get(i));
        }
        size += dataSize;
        if (!getOwuidsList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        owuidsMemoizedSerializedSize = dataSize;
      }
      for (java.util.Map.Entry<String, String> entry
           : internalGetExtendData().getMap().entrySet()) {
        com.google.protobuf.MapEntry<String, String>
        extendData__ = ExtendDataDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(2, extendData__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof PBatchGetGuildNameReq)) {
        return super.equals(obj);
      }
      PBatchGetGuildNameReq other = (PBatchGetGuildNameReq) obj;

      boolean result = true;
      result = result && getOwuidsList()
          .equals(other.getOwuidsList());
      result = result && internalGetExtendData().equals(
          other.internalGetExtendData());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getOwuidsCount() > 0) {
        hash = (37 * hash) + OWUIDS_FIELD_NUMBER;
        hash = (53 * hash) + getOwuidsList().hashCode();
      }
      if (!internalGetExtendData().getMap().isEmpty()) {
        hash = (37 * hash) + EXTENDDATA_FIELD_NUMBER;
        hash = (53 * hash) + internalGetExtendData().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static PBatchGetGuildNameReq parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static PBatchGetGuildNameReq parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static PBatchGetGuildNameReq parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static PBatchGetGuildNameReq parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static PBatchGetGuildNameReq parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static PBatchGetGuildNameReq parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static PBatchGetGuildNameReq parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static PBatchGetGuildNameReq parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static PBatchGetGuildNameReq parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static PBatchGetGuildNameReq parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static PBatchGetGuildNameReq parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static PBatchGetGuildNameReq parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(PBatchGetGuildNameReq prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *批量查询公会名称 103&lt;&lt;8|252
     * </pre>
     *
     * Protobuf type {@code com.yy.yyzone.guildrank.api.PBatchGetGuildNameReq}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.api.PBatchGetGuildNameReq)
        PBatchGetGuildNameReqOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return GuildName.internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameReq_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 2:
            return internalGetExtendData();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 2:
            return internalGetMutableExtendData();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return GuildName.internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameReq_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                PBatchGetGuildNameReq.class, Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.api.GuildName.PBatchGetGuildNameReq.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        owuids_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        internalGetMutableExtendData().clear();
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return GuildName.internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameReq_descriptor;
      }

      @Override
      public PBatchGetGuildNameReq getDefaultInstanceForType() {
        return PBatchGetGuildNameReq.getDefaultInstance();
      }

      @Override
      public PBatchGetGuildNameReq build() {
        PBatchGetGuildNameReq result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public PBatchGetGuildNameReq buildPartial() {
        PBatchGetGuildNameReq result = new PBatchGetGuildNameReq(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) == 0x00000001)) {
          owuids_ = java.util.Collections.unmodifiableList(owuids_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.owuids_ = owuids_;
        result.extendData_ = internalGetExtendData();
        result.extendData_.makeImmutable();
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof PBatchGetGuildNameReq) {
          return mergeFrom((PBatchGetGuildNameReq)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(PBatchGetGuildNameReq other) {
        if (other == PBatchGetGuildNameReq.getDefaultInstance()) return this;
        if (!other.owuids_.isEmpty()) {
          if (owuids_.isEmpty()) {
            owuids_ = other.owuids_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureOwuidsIsMutable();
            owuids_.addAll(other.owuids_);
          }
          onChanged();
        }
        internalGetMutableExtendData().mergeFrom(
            other.internalGetExtendData());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        PBatchGetGuildNameReq parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (PBatchGetGuildNameReq) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<Integer> owuids_ = java.util.Collections.emptyList();
      private void ensureOwuidsIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          owuids_ = new java.util.ArrayList<Integer>(owuids_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated uint32 owuids = 1;</code>
       */
      public java.util.List<Integer>
          getOwuidsList() {
        return java.util.Collections.unmodifiableList(owuids_);
      }
      /**
       * <code>repeated uint32 owuids = 1;</code>
       */
      public int getOwuidsCount() {
        return owuids_.size();
      }
      /**
       * <code>repeated uint32 owuids = 1;</code>
       */
      public int getOwuids(int index) {
        return owuids_.get(index);
      }
      /**
       * <code>repeated uint32 owuids = 1;</code>
       */
      public Builder setOwuids(
          int index, int value) {
        ensureOwuidsIsMutable();
        owuids_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 owuids = 1;</code>
       */
      public Builder addOwuids(int value) {
        ensureOwuidsIsMutable();
        owuids_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 owuids = 1;</code>
       */
      public Builder addAllOwuids(
          Iterable<? extends Integer> values) {
        ensureOwuidsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, owuids_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 owuids = 1;</code>
       */
      public Builder clearOwuids() {
        owuids_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }

      private com.google.protobuf.MapField<
          String, String> extendData_;
      private com.google.protobuf.MapField<String, String>
      internalGetExtendData() {
        if (extendData_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ExtendDataDefaultEntryHolder.defaultEntry);
        }
        return extendData_;
      }
      private com.google.protobuf.MapField<String, String>
      internalGetMutableExtendData() {
        onChanged();;
        if (extendData_ == null) {
          extendData_ = com.google.protobuf.MapField.newMapField(
              ExtendDataDefaultEntryHolder.defaultEntry);
        }
        if (!extendData_.isMutable()) {
          extendData_ = extendData_.copy();
        }
        return extendData_;
      }

      public int getExtendDataCount() {
        return internalGetExtendData().getMap().size();
      }
      /**
       * <code>map&lt;string, string&gt; extendData = 2;</code>
       */

      public boolean containsExtendData(
          String key) {
        if (key == null) { throw new NullPointerException(); }
        return internalGetExtendData().getMap().containsKey(key);
      }
      /**
       * Use {@link #getExtendDataMap()} instead.
       */
      @Deprecated
      public java.util.Map<String, String> getExtendData() {
        return getExtendDataMap();
      }
      /**
       * <code>map&lt;string, string&gt; extendData = 2;</code>
       */

      public java.util.Map<String, String> getExtendDataMap() {
        return internalGetExtendData().getMap();
      }
      /**
       * <code>map&lt;string, string&gt; extendData = 2;</code>
       */

      public String getExtendDataOrDefault(
          String key,
          String defaultValue) {
        if (key == null) { throw new NullPointerException(); }
        java.util.Map<String, String> map =
            internalGetExtendData().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;string, string&gt; extendData = 2;</code>
       */

      public String getExtendDataOrThrow(
          String key) {
        if (key == null) { throw new NullPointerException(); }
        java.util.Map<String, String> map =
            internalGetExtendData().getMap();
        if (!map.containsKey(key)) {
          throw new IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearExtendData() {
        internalGetMutableExtendData().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;string, string&gt; extendData = 2;</code>
       */

      public Builder removeExtendData(
          String key) {
        if (key == null) { throw new NullPointerException(); }
        internalGetMutableExtendData().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @Deprecated
      public java.util.Map<String, String>
      getMutableExtendData() {
        return internalGetMutableExtendData().getMutableMap();
      }
      /**
       * <code>map&lt;string, string&gt; extendData = 2;</code>
       */
      public Builder putExtendData(
          String key,
          String value) {
        if (key == null) { throw new NullPointerException(); }
        if (value == null) { throw new NullPointerException(); }
        internalGetMutableExtendData().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;string, string&gt; extendData = 2;</code>
       */

      public Builder putAllExtendData(
          java.util.Map<String, String> values) {
        internalGetMutableExtendData().getMutableMap()
            .putAll(values);
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.api.PBatchGetGuildNameReq)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.api.PBatchGetGuildNameReq)
    private static final PBatchGetGuildNameReq DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new PBatchGetGuildNameReq();
    }

    public static PBatchGetGuildNameReq getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<PBatchGetGuildNameReq>
        PARSER = new com.google.protobuf.AbstractParser<PBatchGetGuildNameReq>() {
      @Override
      public PBatchGetGuildNameReq parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PBatchGetGuildNameReq(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PBatchGetGuildNameReq> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<PBatchGetGuildNameReq> getParserForType() {
      return PARSER;
    }

    @Override
    public PBatchGetGuildNameReq getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface PBatchGetGuildNameRespOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.api.PBatchGetGuildNameResp)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 result = 1;</code>
     */
    int getResult();

    /**
     * <code>map&lt;uint32, string&gt; nameInfo = 2;</code>
     */
    int getNameInfoCount();
    /**
     * <code>map&lt;uint32, string&gt; nameInfo = 2;</code>
     */
    boolean containsNameInfo(
            int key);
    /**
     * Use {@link #getNameInfoMap()} instead.
     */
    @Deprecated
    java.util.Map<Integer, String>
    getNameInfo();
    /**
     * <code>map&lt;uint32, string&gt; nameInfo = 2;</code>
     */
    java.util.Map<Integer, String>
    getNameInfoMap();
    /**
     * <code>map&lt;uint32, string&gt; nameInfo = 2;</code>
     */

    String getNameInfoOrDefault(
            int key,
            String defaultValue);
    /**
     * <code>map&lt;uint32, string&gt; nameInfo = 2;</code>
     */

    String getNameInfoOrThrow(
            int key);

    /**
     * <code>map&lt;string, string&gt; extendData = 3;</code>
     */
    int getExtendDataCount();
    /**
     * <code>map&lt;string, string&gt; extendData = 3;</code>
     */
    boolean containsExtendData(
            String key);
    /**
     * Use {@link #getExtendDataMap()} instead.
     */
    @Deprecated
    java.util.Map<String, String>
    getExtendData();
    /**
     * <code>map&lt;string, string&gt; extendData = 3;</code>
     */
    java.util.Map<String, String>
    getExtendDataMap();
    /**
     * <code>map&lt;string, string&gt; extendData = 3;</code>
     */

    String getExtendDataOrDefault(
            String key,
            String defaultValue);
    /**
     * <code>map&lt;string, string&gt; extendData = 3;</code>
     */

    String getExtendDataOrThrow(
            String key);
  }
  /**
   * <pre>
   *104&lt;&lt;8|252
   * </pre>
   *
   * Protobuf type {@code com.yy.yyzone.guildrank.api.PBatchGetGuildNameResp}
   */
  public  static final class PBatchGetGuildNameResp extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.api.PBatchGetGuildNameResp)
      PBatchGetGuildNameRespOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PBatchGetGuildNameResp.newBuilder() to construct.
    private PBatchGetGuildNameResp(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PBatchGetGuildNameResp() {
      result_ = 0;
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PBatchGetGuildNameResp(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              result_ = input.readUInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                nameInfo_ = com.google.protobuf.MapField.newMapField(
                    NameInfoDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000002;
              }
              com.google.protobuf.MapEntry<Integer, String>
              nameInfo__ = input.readMessage(
                  NameInfoDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              nameInfo_.getMutableMap().put(
                  nameInfo__.getKey(), nameInfo__.getValue());
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
                extendData_ = com.google.protobuf.MapField.newMapField(
                    ExtendDataDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000004;
              }
              com.google.protobuf.MapEntry<String, String>
              extendData__ = input.readMessage(
                  ExtendDataDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              extendData_.getMutableMap().put(
                  extendData__.getKey(), extendData__.getValue());
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return GuildName.internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameResp_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 2:
          return internalGetNameInfo();
        case 3:
          return internalGetExtendData();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return GuildName.internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              PBatchGetGuildNameResp.class, Builder.class);
    }

    private int bitField0_;
    public static final int RESULT_FIELD_NUMBER = 1;
    private int result_;
    /**
     * <code>uint32 result = 1;</code>
     */
    public int getResult() {
      return result_;
    }

    public static final int NAMEINFO_FIELD_NUMBER = 2;
    private static final class NameInfoDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          Integer, String> defaultEntry =
              com.google.protobuf.MapEntry
              .<Integer, String>newDefaultInstance(
                  GuildName.internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameResp_NameInfoEntry_descriptor,
                  com.google.protobuf.WireFormat.FieldType.UINT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "");
    }
    private com.google.protobuf.MapField<
        Integer, String> nameInfo_;
    private com.google.protobuf.MapField<Integer, String>
    internalGetNameInfo() {
      if (nameInfo_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            NameInfoDefaultEntryHolder.defaultEntry);
      }
      return nameInfo_;
    }

    public int getNameInfoCount() {
      return internalGetNameInfo().getMap().size();
    }
    /**
     * <code>map&lt;uint32, string&gt; nameInfo = 2;</code>
     */

    public boolean containsNameInfo(
        int key) {
      
      return internalGetNameInfo().getMap().containsKey(key);
    }
    /**
     * Use {@link #getNameInfoMap()} instead.
     */
    @Deprecated
    public java.util.Map<Integer, String> getNameInfo() {
      return getNameInfoMap();
    }
    /**
     * <code>map&lt;uint32, string&gt; nameInfo = 2;</code>
     */

    public java.util.Map<Integer, String> getNameInfoMap() {
      return internalGetNameInfo().getMap();
    }
    /**
     * <code>map&lt;uint32, string&gt; nameInfo = 2;</code>
     */

    public String getNameInfoOrDefault(
        int key,
        String defaultValue) {
      
      java.util.Map<Integer, String> map =
          internalGetNameInfo().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;uint32, string&gt; nameInfo = 2;</code>
     */

    public String getNameInfoOrThrow(
        int key) {
      
      java.util.Map<Integer, String> map =
          internalGetNameInfo().getMap();
      if (!map.containsKey(key)) {
        throw new IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int EXTENDDATA_FIELD_NUMBER = 3;
    private static final class ExtendDataDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          String, String> defaultEntry =
              com.google.protobuf.MapEntry
              .<String, String>newDefaultInstance(
                  GuildName.internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameResp_ExtendDataEntry_descriptor,
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "",
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "");
    }
    private com.google.protobuf.MapField<
        String, String> extendData_;
    private com.google.protobuf.MapField<String, String>
    internalGetExtendData() {
      if (extendData_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ExtendDataDefaultEntryHolder.defaultEntry);
      }
      return extendData_;
    }

    public int getExtendDataCount() {
      return internalGetExtendData().getMap().size();
    }
    /**
     * <code>map&lt;string, string&gt; extendData = 3;</code>
     */

    public boolean containsExtendData(
        String key) {
      if (key == null) { throw new NullPointerException(); }
      return internalGetExtendData().getMap().containsKey(key);
    }
    /**
     * Use {@link #getExtendDataMap()} instead.
     */
    @Deprecated
    public java.util.Map<String, String> getExtendData() {
      return getExtendDataMap();
    }
    /**
     * <code>map&lt;string, string&gt; extendData = 3;</code>
     */

    public java.util.Map<String, String> getExtendDataMap() {
      return internalGetExtendData().getMap();
    }
    /**
     * <code>map&lt;string, string&gt; extendData = 3;</code>
     */

    public String getExtendDataOrDefault(
        String key,
        String defaultValue) {
      if (key == null) { throw new NullPointerException(); }
      java.util.Map<String, String> map =
          internalGetExtendData().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;string, string&gt; extendData = 3;</code>
     */

    public String getExtendDataOrThrow(
        String key) {
      if (key == null) { throw new NullPointerException(); }
      java.util.Map<String, String> map =
          internalGetExtendData().getMap();
      if (!map.containsKey(key)) {
        throw new IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (result_ != 0) {
        output.writeUInt32(1, result_);
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetNameInfo(),
          NameInfoDefaultEntryHolder.defaultEntry,
          2);
      com.google.protobuf.GeneratedMessageV3
        .serializeStringMapTo(
          output,
          internalGetExtendData(),
          ExtendDataDefaultEntryHolder.defaultEntry,
          3);
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (result_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, result_);
      }
      for (java.util.Map.Entry<Integer, String> entry
           : internalGetNameInfo().getMap().entrySet()) {
        com.google.protobuf.MapEntry<Integer, String>
        nameInfo__ = NameInfoDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(2, nameInfo__);
      }
      for (java.util.Map.Entry<String, String> entry
           : internalGetExtendData().getMap().entrySet()) {
        com.google.protobuf.MapEntry<String, String>
        extendData__ = ExtendDataDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(3, extendData__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof PBatchGetGuildNameResp)) {
        return super.equals(obj);
      }
      PBatchGetGuildNameResp other = (PBatchGetGuildNameResp) obj;

      boolean result = true;
      result = result && (getResult()
          == other.getResult());
      result = result && internalGetNameInfo().equals(
          other.internalGetNameInfo());
      result = result && internalGetExtendData().equals(
          other.internalGetExtendData());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + getResult();
      if (!internalGetNameInfo().getMap().isEmpty()) {
        hash = (37 * hash) + NAMEINFO_FIELD_NUMBER;
        hash = (53 * hash) + internalGetNameInfo().hashCode();
      }
      if (!internalGetExtendData().getMap().isEmpty()) {
        hash = (37 * hash) + EXTENDDATA_FIELD_NUMBER;
        hash = (53 * hash) + internalGetExtendData().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static PBatchGetGuildNameResp parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static PBatchGetGuildNameResp parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static PBatchGetGuildNameResp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static PBatchGetGuildNameResp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static PBatchGetGuildNameResp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static PBatchGetGuildNameResp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static PBatchGetGuildNameResp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static PBatchGetGuildNameResp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static PBatchGetGuildNameResp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static PBatchGetGuildNameResp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static PBatchGetGuildNameResp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static PBatchGetGuildNameResp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(PBatchGetGuildNameResp prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *104&lt;&lt;8|252
     * </pre>
     *
     * Protobuf type {@code com.yy.yyzone.guildrank.api.PBatchGetGuildNameResp}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.api.PBatchGetGuildNameResp)
        PBatchGetGuildNameRespOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return GuildName.internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameResp_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 2:
            return internalGetNameInfo();
          case 3:
            return internalGetExtendData();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 2:
            return internalGetMutableNameInfo();
          case 3:
            return internalGetMutableExtendData();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return GuildName.internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameResp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                PBatchGetGuildNameResp.class, Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.api.GuildName.PBatchGetGuildNameResp.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        result_ = 0;

        internalGetMutableNameInfo().clear();
        internalGetMutableExtendData().clear();
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return GuildName.internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameResp_descriptor;
      }

      @Override
      public PBatchGetGuildNameResp getDefaultInstanceForType() {
        return PBatchGetGuildNameResp.getDefaultInstance();
      }

      @Override
      public PBatchGetGuildNameResp build() {
        PBatchGetGuildNameResp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public PBatchGetGuildNameResp buildPartial() {
        PBatchGetGuildNameResp result = new PBatchGetGuildNameResp(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.result_ = result_;
        result.nameInfo_ = internalGetNameInfo();
        result.nameInfo_.makeImmutable();
        result.extendData_ = internalGetExtendData();
        result.extendData_.makeImmutable();
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof PBatchGetGuildNameResp) {
          return mergeFrom((PBatchGetGuildNameResp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(PBatchGetGuildNameResp other) {
        if (other == PBatchGetGuildNameResp.getDefaultInstance()) return this;
        if (other.getResult() != 0) {
          setResult(other.getResult());
        }
        internalGetMutableNameInfo().mergeFrom(
            other.internalGetNameInfo());
        internalGetMutableExtendData().mergeFrom(
            other.internalGetExtendData());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        PBatchGetGuildNameResp parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (PBatchGetGuildNameResp) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int result_ ;
      /**
       * <code>uint32 result = 1;</code>
       */
      public int getResult() {
        return result_;
      }
      /**
       * <code>uint32 result = 1;</code>
       */
      public Builder setResult(int value) {
        
        result_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 result = 1;</code>
       */
      public Builder clearResult() {
        
        result_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.MapField<
          Integer, String> nameInfo_;
      private com.google.protobuf.MapField<Integer, String>
      internalGetNameInfo() {
        if (nameInfo_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              NameInfoDefaultEntryHolder.defaultEntry);
        }
        return nameInfo_;
      }
      private com.google.protobuf.MapField<Integer, String>
      internalGetMutableNameInfo() {
        onChanged();;
        if (nameInfo_ == null) {
          nameInfo_ = com.google.protobuf.MapField.newMapField(
              NameInfoDefaultEntryHolder.defaultEntry);
        }
        if (!nameInfo_.isMutable()) {
          nameInfo_ = nameInfo_.copy();
        }
        return nameInfo_;
      }

      public int getNameInfoCount() {
        return internalGetNameInfo().getMap().size();
      }
      /**
       * <code>map&lt;uint32, string&gt; nameInfo = 2;</code>
       */

      public boolean containsNameInfo(
          int key) {
        
        return internalGetNameInfo().getMap().containsKey(key);
      }
      /**
       * Use {@link #getNameInfoMap()} instead.
       */
      @Deprecated
      public java.util.Map<Integer, String> getNameInfo() {
        return getNameInfoMap();
      }
      /**
       * <code>map&lt;uint32, string&gt; nameInfo = 2;</code>
       */

      public java.util.Map<Integer, String> getNameInfoMap() {
        return internalGetNameInfo().getMap();
      }
      /**
       * <code>map&lt;uint32, string&gt; nameInfo = 2;</code>
       */

      public String getNameInfoOrDefault(
          int key,
          String defaultValue) {
        
        java.util.Map<Integer, String> map =
            internalGetNameInfo().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;uint32, string&gt; nameInfo = 2;</code>
       */

      public String getNameInfoOrThrow(
          int key) {
        
        java.util.Map<Integer, String> map =
            internalGetNameInfo().getMap();
        if (!map.containsKey(key)) {
          throw new IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearNameInfo() {
        internalGetMutableNameInfo().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;uint32, string&gt; nameInfo = 2;</code>
       */

      public Builder removeNameInfo(
          int key) {
        
        internalGetMutableNameInfo().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @Deprecated
      public java.util.Map<Integer, String>
      getMutableNameInfo() {
        return internalGetMutableNameInfo().getMutableMap();
      }
      /**
       * <code>map&lt;uint32, string&gt; nameInfo = 2;</code>
       */
      public Builder putNameInfo(
          int key,
          String value) {
        
        if (value == null) { throw new NullPointerException(); }
        internalGetMutableNameInfo().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;uint32, string&gt; nameInfo = 2;</code>
       */

      public Builder putAllNameInfo(
          java.util.Map<Integer, String> values) {
        internalGetMutableNameInfo().getMutableMap()
            .putAll(values);
        return this;
      }

      private com.google.protobuf.MapField<
          String, String> extendData_;
      private com.google.protobuf.MapField<String, String>
      internalGetExtendData() {
        if (extendData_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ExtendDataDefaultEntryHolder.defaultEntry);
        }
        return extendData_;
      }
      private com.google.protobuf.MapField<String, String>
      internalGetMutableExtendData() {
        onChanged();;
        if (extendData_ == null) {
          extendData_ = com.google.protobuf.MapField.newMapField(
              ExtendDataDefaultEntryHolder.defaultEntry);
        }
        if (!extendData_.isMutable()) {
          extendData_ = extendData_.copy();
        }
        return extendData_;
      }

      public int getExtendDataCount() {
        return internalGetExtendData().getMap().size();
      }
      /**
       * <code>map&lt;string, string&gt; extendData = 3;</code>
       */

      public boolean containsExtendData(
          String key) {
        if (key == null) { throw new NullPointerException(); }
        return internalGetExtendData().getMap().containsKey(key);
      }
      /**
       * Use {@link #getExtendDataMap()} instead.
       */
      @Deprecated
      public java.util.Map<String, String> getExtendData() {
        return getExtendDataMap();
      }
      /**
       * <code>map&lt;string, string&gt; extendData = 3;</code>
       */

      public java.util.Map<String, String> getExtendDataMap() {
        return internalGetExtendData().getMap();
      }
      /**
       * <code>map&lt;string, string&gt; extendData = 3;</code>
       */

      public String getExtendDataOrDefault(
          String key,
          String defaultValue) {
        if (key == null) { throw new NullPointerException(); }
        java.util.Map<String, String> map =
            internalGetExtendData().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;string, string&gt; extendData = 3;</code>
       */

      public String getExtendDataOrThrow(
          String key) {
        if (key == null) { throw new NullPointerException(); }
        java.util.Map<String, String> map =
            internalGetExtendData().getMap();
        if (!map.containsKey(key)) {
          throw new IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearExtendData() {
        internalGetMutableExtendData().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;string, string&gt; extendData = 3;</code>
       */

      public Builder removeExtendData(
          String key) {
        if (key == null) { throw new NullPointerException(); }
        internalGetMutableExtendData().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @Deprecated
      public java.util.Map<String, String>
      getMutableExtendData() {
        return internalGetMutableExtendData().getMutableMap();
      }
      /**
       * <code>map&lt;string, string&gt; extendData = 3;</code>
       */
      public Builder putExtendData(
          String key,
          String value) {
        if (key == null) { throw new NullPointerException(); }
        if (value == null) { throw new NullPointerException(); }
        internalGetMutableExtendData().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;string, string&gt; extendData = 3;</code>
       */

      public Builder putAllExtendData(
          java.util.Map<String, String> values) {
        internalGetMutableExtendData().getMutableMap()
            .putAll(values);
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.api.PBatchGetGuildNameResp)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.api.PBatchGetGuildNameResp)
    private static final PBatchGetGuildNameResp DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new PBatchGetGuildNameResp();
    }

    public static PBatchGetGuildNameResp getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<PBatchGetGuildNameResp>
        PARSER = new com.google.protobuf.AbstractParser<PBatchGetGuildNameResp>() {
      @Override
      public PBatchGetGuildNameResp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PBatchGetGuildNameResp(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PBatchGetGuildNameResp> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<PBatchGetGuildNameResp> getParserForType() {
      return PARSER;
    }

    @Override
    public PBatchGetGuildNameResp getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameReq_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameReq_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameReq_ExtendDataEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameReq_ExtendDataEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameResp_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameResp_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameResp_NameInfoEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameResp_NameInfoEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameResp_ExtendDataEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameResp_ExtendDataEntry_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    String[] descriptorData = {
      "\n\017guildName.proto\022\033com.yy.yyzone.guildra" +
      "nk.api\032\turi.proto\"\271\001\n\025PBatchGetGuildName" +
      "Req\022\016\n\006owuids\030\001 \003(\r\022V\n\nextendData\030\002 \003(\0132" +
      "B.com.yy.yyzone.guildrank.api.PBatchGetG" +
      "uildNameReq.ExtendDataEntry\0321\n\017ExtendDat" +
      "aEntry\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001:\005" +
      "\310>\374\317\001\"\301\002\n\026PBatchGetGuildNameResp\022\016\n\006resu" +
      "lt\030\001 \001(\r\022S\n\010nameInfo\030\002 \003(\0132A.com.yy.yyzo" +
      "ne.guildrank.api.PBatchGetGuildNameResp." +
      "NameInfoEntry\022W\n\nextendData\030\003 \003(\0132C.com." +
      "yy.yyzone.guildrank.api.PBatchGetGuildNa" +
      "meResp.ExtendDataEntry\032/\n\rNameInfoEntry\022" +
      "\013\n\003key\030\001 \001(\r\022\r\n\005value\030\002 \001(\t:\0028\001\0321\n\017Exten" +
      "dDataEntry\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\002" +
      "8\001:\005\310>\374\321\0012\220\001\n\020GuildNameService\022|\n\021batchG" +
      "etGuildName\0222.com.yy.yyzone.guildrank.ap" +
      "i.PBatchGetGuildNameReq\0323.com.yy.yyzone." +
      "guildrank.api.PBatchGetGuildNameRespb\006pr" +
      "oto3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          yyp.Uri.getDescriptor(),
        }, assigner);
    internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameReq_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameReq_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameReq_descriptor,
        new String[] { "Owuids", "ExtendData", });
    internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameReq_ExtendDataEntry_descriptor =
      internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameReq_descriptor.getNestedTypes().get(0);
    internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameReq_ExtendDataEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameReq_ExtendDataEntry_descriptor,
        new String[] { "Key", "Value", });
    internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameResp_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameResp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameResp_descriptor,
        new String[] { "Result", "NameInfo", "ExtendData", });
    internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameResp_NameInfoEntry_descriptor =
      internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameResp_descriptor.getNestedTypes().get(0);
    internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameResp_NameInfoEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameResp_NameInfoEntry_descriptor,
        new String[] { "Key", "Value", });
    internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameResp_ExtendDataEntry_descriptor =
      internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameResp_descriptor.getNestedTypes().get(1);
    internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameResp_ExtendDataEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_PBatchGetGuildNameResp_ExtendDataEntry_descriptor,
        new String[] { "Key", "Value", });
    com.google.protobuf.ExtensionRegistry registry =
        com.google.protobuf.ExtensionRegistry.newInstance();
    registry.add(yyp.Uri.uri);
    com.google.protobuf.Descriptors.FileDescriptor
        .internalUpdateFileDescriptor(descriptor, registry);
    yyp.Uri.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
