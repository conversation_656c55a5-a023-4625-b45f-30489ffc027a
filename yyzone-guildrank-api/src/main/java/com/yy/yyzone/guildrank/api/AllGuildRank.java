// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: AllGuildRank.proto

package com.yy.yyzone.guildrank.api;

public final class AllGuildRank {
  private AllGuildRank() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface QueryAllGuildRankByOrderReqOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.api.QueryAllGuildRankByOrderReq)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 2019-07
     * </pre>
     *
     * <code>string month = 1;</code>
     */
    String getMonth();
    /**
     * <pre>
     * 2019-07
     * </pre>
     *
     * <code>string month = 1;</code>
     */
    com.google.protobuf.ByteString
        getMonthBytes();

    /**
     * <pre>
     * 排序标识： 1蓝钻
     * </pre>
     *
     * <code>uint32 orderType = 2;</code>
     */
    int getOrderType();
  }
  /**
   * Protobuf type {@code com.yy.yyzone.guildrank.api.QueryAllGuildRankByOrderReq}
   */
  public  static final class QueryAllGuildRankByOrderReq extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.api.QueryAllGuildRankByOrderReq)
      QueryAllGuildRankByOrderReqOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QueryAllGuildRankByOrderReq.newBuilder() to construct.
    private QueryAllGuildRankByOrderReq(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QueryAllGuildRankByOrderReq() {
      month_ = "";
      orderType_ = 0;
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QueryAllGuildRankByOrderReq(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              String s = input.readStringRequireUtf8();

              month_ = s;
              break;
            }
            case 16: {

              orderType_ = input.readUInt32();
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return AllGuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderReq_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return AllGuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              QueryAllGuildRankByOrderReq.class, Builder.class);
    }

    public static final int MONTH_FIELD_NUMBER = 1;
    private volatile Object month_;
    /**
     * <pre>
     * 2019-07
     * </pre>
     *
     * <code>string month = 1;</code>
     */
    public String getMonth() {
      Object ref = month_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        month_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 2019-07
     * </pre>
     *
     * <code>string month = 1;</code>
     */
    public com.google.protobuf.ByteString
        getMonthBytes() {
      Object ref = month_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        month_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ORDERTYPE_FIELD_NUMBER = 2;
    private int orderType_;
    /**
     * <pre>
     * 排序标识： 1蓝钻
     * </pre>
     *
     * <code>uint32 orderType = 2;</code>
     */
    public int getOrderType() {
      return orderType_;
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getMonthBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, month_);
      }
      if (orderType_ != 0) {
        output.writeUInt32(2, orderType_);
      }
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getMonthBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, month_);
      }
      if (orderType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, orderType_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof QueryAllGuildRankByOrderReq)) {
        return super.equals(obj);
      }
      QueryAllGuildRankByOrderReq other = (QueryAllGuildRankByOrderReq) obj;

      boolean result = true;
      result = result && getMonth()
          .equals(other.getMonth());
      result = result && (getOrderType()
          == other.getOrderType());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MONTH_FIELD_NUMBER;
      hash = (53 * hash) + getMonth().hashCode();
      hash = (37 * hash) + ORDERTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getOrderType();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static QueryAllGuildRankByOrderReq parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryAllGuildRankByOrderReq parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryAllGuildRankByOrderReq parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryAllGuildRankByOrderReq parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryAllGuildRankByOrderReq parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryAllGuildRankByOrderReq parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryAllGuildRankByOrderReq parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static QueryAllGuildRankByOrderReq parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static QueryAllGuildRankByOrderReq parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static QueryAllGuildRankByOrderReq parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static QueryAllGuildRankByOrderReq parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static QueryAllGuildRankByOrderReq parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(QueryAllGuildRankByOrderReq prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.yyzone.guildrank.api.QueryAllGuildRankByOrderReq}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.api.QueryAllGuildRankByOrderReq)
        QueryAllGuildRankByOrderReqOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return AllGuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderReq_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return AllGuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderReq_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                QueryAllGuildRankByOrderReq.class, Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.api.AllGuildRank.QueryAllGuildRankByOrderReq.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        month_ = "";

        orderType_ = 0;

        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return AllGuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderReq_descriptor;
      }

      @Override
      public QueryAllGuildRankByOrderReq getDefaultInstanceForType() {
        return QueryAllGuildRankByOrderReq.getDefaultInstance();
      }

      @Override
      public QueryAllGuildRankByOrderReq build() {
        QueryAllGuildRankByOrderReq result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public QueryAllGuildRankByOrderReq buildPartial() {
        QueryAllGuildRankByOrderReq result = new QueryAllGuildRankByOrderReq(this);
        result.month_ = month_;
        result.orderType_ = orderType_;
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof QueryAllGuildRankByOrderReq) {
          return mergeFrom((QueryAllGuildRankByOrderReq)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(QueryAllGuildRankByOrderReq other) {
        if (other == QueryAllGuildRankByOrderReq.getDefaultInstance()) return this;
        if (!other.getMonth().isEmpty()) {
          month_ = other.month_;
          onChanged();
        }
        if (other.getOrderType() != 0) {
          setOrderType(other.getOrderType());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        QueryAllGuildRankByOrderReq parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (QueryAllGuildRankByOrderReq) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private Object month_ = "";
      /**
       * <pre>
       * 2019-07
       * </pre>
       *
       * <code>string month = 1;</code>
       */
      public String getMonth() {
        Object ref = month_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          month_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       * 2019-07
       * </pre>
       *
       * <code>string month = 1;</code>
       */
      public com.google.protobuf.ByteString
          getMonthBytes() {
        Object ref = month_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          month_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 2019-07
       * </pre>
       *
       * <code>string month = 1;</code>
       */
      public Builder setMonth(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        month_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 2019-07
       * </pre>
       *
       * <code>string month = 1;</code>
       */
      public Builder clearMonth() {
        
        month_ = getDefaultInstance().getMonth();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 2019-07
       * </pre>
       *
       * <code>string month = 1;</code>
       */
      public Builder setMonthBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        month_ = value;
        onChanged();
        return this;
      }

      private int orderType_ ;
      /**
       * <pre>
       * 排序标识： 1蓝钻
       * </pre>
       *
       * <code>uint32 orderType = 2;</code>
       */
      public int getOrderType() {
        return orderType_;
      }
      /**
       * <pre>
       * 排序标识： 1蓝钻
       * </pre>
       *
       * <code>uint32 orderType = 2;</code>
       */
      public Builder setOrderType(int value) {
        
        orderType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 排序标识： 1蓝钻
       * </pre>
       *
       * <code>uint32 orderType = 2;</code>
       */
      public Builder clearOrderType() {
        
        orderType_ = 0;
        onChanged();
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.api.QueryAllGuildRankByOrderReq)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.api.QueryAllGuildRankByOrderReq)
    private static final QueryAllGuildRankByOrderReq DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new QueryAllGuildRankByOrderReq();
    }

    public static QueryAllGuildRankByOrderReq getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QueryAllGuildRankByOrderReq>
        PARSER = new com.google.protobuf.AbstractParser<QueryAllGuildRankByOrderReq>() {
      @Override
      public QueryAllGuildRankByOrderReq parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QueryAllGuildRankByOrderReq(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QueryAllGuildRankByOrderReq> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<QueryAllGuildRankByOrderReq> getParserForType() {
      return PARSER;
    }

    @Override
    public QueryAllGuildRankByOrderReq getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryAllGuildRankByOrderRespOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.api.QueryAllGuildRankByOrderResp)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 result = 1;</code>
     */
    int getResult();

    /**
     * <pre>
     *是否上个月最新的数据 1:是 0:不是
     * </pre>
     *
     * <code>uint32 isNew = 2;</code>
     */
    int getIsNew();

    /**
     * <code>repeated uint32 owuids = 3;</code>
     */
    java.util.List<Integer> getOwuidsList();
    /**
     * <code>repeated uint32 owuids = 3;</code>
     */
    int getOwuidsCount();
    /**
     * <code>repeated uint32 owuids = 3;</code>
     */
    int getOwuids(int index);
  }
  /**
   * Protobuf type {@code com.yy.yyzone.guildrank.api.QueryAllGuildRankByOrderResp}
   */
  public  static final class QueryAllGuildRankByOrderResp extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.api.QueryAllGuildRankByOrderResp)
      QueryAllGuildRankByOrderRespOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QueryAllGuildRankByOrderResp.newBuilder() to construct.
    private QueryAllGuildRankByOrderResp(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QueryAllGuildRankByOrderResp() {
      result_ = 0;
      isNew_ = 0;
      owuids_ = java.util.Collections.emptyList();
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QueryAllGuildRankByOrderResp(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              result_ = input.readUInt32();
              break;
            }
            case 16: {

              isNew_ = input.readUInt32();
              break;
            }
            case 24: {
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
                owuids_ = new java.util.ArrayList<Integer>();
                mutable_bitField0_ |= 0x00000004;
              }
              owuids_.add(input.readUInt32());
              break;
            }
            case 26: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004) && input.getBytesUntilLimit() > 0) {
                owuids_ = new java.util.ArrayList<Integer>();
                mutable_bitField0_ |= 0x00000004;
              }
              while (input.getBytesUntilLimit() > 0) {
                owuids_.add(input.readUInt32());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
          owuids_ = java.util.Collections.unmodifiableList(owuids_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return AllGuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderResp_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return AllGuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              QueryAllGuildRankByOrderResp.class, Builder.class);
    }

    private int bitField0_;
    public static final int RESULT_FIELD_NUMBER = 1;
    private int result_;
    /**
     * <code>uint32 result = 1;</code>
     */
    public int getResult() {
      return result_;
    }

    public static final int ISNEW_FIELD_NUMBER = 2;
    private int isNew_;
    /**
     * <pre>
     *是否上个月最新的数据 1:是 0:不是
     * </pre>
     *
     * <code>uint32 isNew = 2;</code>
     */
    public int getIsNew() {
      return isNew_;
    }

    public static final int OWUIDS_FIELD_NUMBER = 3;
    private java.util.List<Integer> owuids_;
    /**
     * <code>repeated uint32 owuids = 3;</code>
     */
    public java.util.List<Integer>
        getOwuidsList() {
      return owuids_;
    }
    /**
     * <code>repeated uint32 owuids = 3;</code>
     */
    public int getOwuidsCount() {
      return owuids_.size();
    }
    /**
     * <code>repeated uint32 owuids = 3;</code>
     */
    public int getOwuids(int index) {
      return owuids_.get(index);
    }
    private int owuidsMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (result_ != 0) {
        output.writeUInt32(1, result_);
      }
      if (isNew_ != 0) {
        output.writeUInt32(2, isNew_);
      }
      if (getOwuidsList().size() > 0) {
        output.writeUInt32NoTag(26);
        output.writeUInt32NoTag(owuidsMemoizedSerializedSize);
      }
      for (int i = 0; i < owuids_.size(); i++) {
        output.writeUInt32NoTag(owuids_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (result_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, result_);
      }
      if (isNew_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, isNew_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < owuids_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(owuids_.get(i));
        }
        size += dataSize;
        if (!getOwuidsList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        owuidsMemoizedSerializedSize = dataSize;
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof QueryAllGuildRankByOrderResp)) {
        return super.equals(obj);
      }
      QueryAllGuildRankByOrderResp other = (QueryAllGuildRankByOrderResp) obj;

      boolean result = true;
      result = result && (getResult()
          == other.getResult());
      result = result && (getIsNew()
          == other.getIsNew());
      result = result && getOwuidsList()
          .equals(other.getOwuidsList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + getResult();
      hash = (37 * hash) + ISNEW_FIELD_NUMBER;
      hash = (53 * hash) + getIsNew();
      if (getOwuidsCount() > 0) {
        hash = (37 * hash) + OWUIDS_FIELD_NUMBER;
        hash = (53 * hash) + getOwuidsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static QueryAllGuildRankByOrderResp parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryAllGuildRankByOrderResp parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryAllGuildRankByOrderResp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryAllGuildRankByOrderResp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryAllGuildRankByOrderResp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryAllGuildRankByOrderResp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryAllGuildRankByOrderResp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static QueryAllGuildRankByOrderResp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static QueryAllGuildRankByOrderResp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static QueryAllGuildRankByOrderResp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static QueryAllGuildRankByOrderResp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static QueryAllGuildRankByOrderResp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(QueryAllGuildRankByOrderResp prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.yyzone.guildrank.api.QueryAllGuildRankByOrderResp}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.api.QueryAllGuildRankByOrderResp)
        QueryAllGuildRankByOrderRespOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return AllGuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderResp_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return AllGuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderResp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                QueryAllGuildRankByOrderResp.class, Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.api.AllGuildRank.QueryAllGuildRankByOrderResp.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        result_ = 0;

        isNew_ = 0;

        owuids_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return AllGuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderResp_descriptor;
      }

      @Override
      public QueryAllGuildRankByOrderResp getDefaultInstanceForType() {
        return QueryAllGuildRankByOrderResp.getDefaultInstance();
      }

      @Override
      public QueryAllGuildRankByOrderResp build() {
        QueryAllGuildRankByOrderResp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public QueryAllGuildRankByOrderResp buildPartial() {
        QueryAllGuildRankByOrderResp result = new QueryAllGuildRankByOrderResp(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.result_ = result_;
        result.isNew_ = isNew_;
        if (((bitField0_ & 0x00000004) == 0x00000004)) {
          owuids_ = java.util.Collections.unmodifiableList(owuids_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.owuids_ = owuids_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof QueryAllGuildRankByOrderResp) {
          return mergeFrom((QueryAllGuildRankByOrderResp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(QueryAllGuildRankByOrderResp other) {
        if (other == QueryAllGuildRankByOrderResp.getDefaultInstance()) return this;
        if (other.getResult() != 0) {
          setResult(other.getResult());
        }
        if (other.getIsNew() != 0) {
          setIsNew(other.getIsNew());
        }
        if (!other.owuids_.isEmpty()) {
          if (owuids_.isEmpty()) {
            owuids_ = other.owuids_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureOwuidsIsMutable();
            owuids_.addAll(other.owuids_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        QueryAllGuildRankByOrderResp parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (QueryAllGuildRankByOrderResp) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int result_ ;
      /**
       * <code>uint32 result = 1;</code>
       */
      public int getResult() {
        return result_;
      }
      /**
       * <code>uint32 result = 1;</code>
       */
      public Builder setResult(int value) {
        
        result_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 result = 1;</code>
       */
      public Builder clearResult() {
        
        result_ = 0;
        onChanged();
        return this;
      }

      private int isNew_ ;
      /**
       * <pre>
       *是否上个月最新的数据 1:是 0:不是
       * </pre>
       *
       * <code>uint32 isNew = 2;</code>
       */
      public int getIsNew() {
        return isNew_;
      }
      /**
       * <pre>
       *是否上个月最新的数据 1:是 0:不是
       * </pre>
       *
       * <code>uint32 isNew = 2;</code>
       */
      public Builder setIsNew(int value) {
        
        isNew_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *是否上个月最新的数据 1:是 0:不是
       * </pre>
       *
       * <code>uint32 isNew = 2;</code>
       */
      public Builder clearIsNew() {
        
        isNew_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<Integer> owuids_ = java.util.Collections.emptyList();
      private void ensureOwuidsIsMutable() {
        if (!((bitField0_ & 0x00000004) == 0x00000004)) {
          owuids_ = new java.util.ArrayList<Integer>(owuids_);
          bitField0_ |= 0x00000004;
         }
      }
      /**
       * <code>repeated uint32 owuids = 3;</code>
       */
      public java.util.List<Integer>
          getOwuidsList() {
        return java.util.Collections.unmodifiableList(owuids_);
      }
      /**
       * <code>repeated uint32 owuids = 3;</code>
       */
      public int getOwuidsCount() {
        return owuids_.size();
      }
      /**
       * <code>repeated uint32 owuids = 3;</code>
       */
      public int getOwuids(int index) {
        return owuids_.get(index);
      }
      /**
       * <code>repeated uint32 owuids = 3;</code>
       */
      public Builder setOwuids(
          int index, int value) {
        ensureOwuidsIsMutable();
        owuids_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 owuids = 3;</code>
       */
      public Builder addOwuids(int value) {
        ensureOwuidsIsMutable();
        owuids_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 owuids = 3;</code>
       */
      public Builder addAllOwuids(
          Iterable<? extends Integer> values) {
        ensureOwuidsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, owuids_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 owuids = 3;</code>
       */
      public Builder clearOwuids() {
        owuids_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.api.QueryAllGuildRankByOrderResp)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.api.QueryAllGuildRankByOrderResp)
    private static final QueryAllGuildRankByOrderResp DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new QueryAllGuildRankByOrderResp();
    }

    public static QueryAllGuildRankByOrderResp getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QueryAllGuildRankByOrderResp>
        PARSER = new com.google.protobuf.AbstractParser<QueryAllGuildRankByOrderResp>() {
      @Override
      public QueryAllGuildRankByOrderResp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QueryAllGuildRankByOrderResp(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QueryAllGuildRankByOrderResp> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<QueryAllGuildRankByOrderResp> getParserForType() {
      return PARSER;
    }

    @Override
    public QueryAllGuildRankByOrderResp getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GuildRankVOOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.api.GuildRankVO)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>map&lt;string, string&gt; data = 1;</code>
     */
    int getDataCount();
    /**
     * <code>map&lt;string, string&gt; data = 1;</code>
     */
    boolean containsData(
            String key);
    /**
     * Use {@link #getDataMap()} instead.
     */
    @Deprecated
    java.util.Map<String, String>
    getData();
    /**
     * <code>map&lt;string, string&gt; data = 1;</code>
     */
    java.util.Map<String, String>
    getDataMap();
    /**
     * <code>map&lt;string, string&gt; data = 1;</code>
     */

    String getDataOrDefault(
            String key,
            String defaultValue);
    /**
     * <code>map&lt;string, string&gt; data = 1;</code>
     */

    String getDataOrThrow(
            String key);
  }
  /**
   * Protobuf type {@code com.yy.yyzone.guildrank.api.GuildRankVO}
   */
  public  static final class GuildRankVO extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.api.GuildRankVO)
      GuildRankVOOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GuildRankVO.newBuilder() to construct.
    private GuildRankVO(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GuildRankVO() {
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GuildRankVO(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                data_ = com.google.protobuf.MapField.newMapField(
                    DataDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<String, String>
              data__ = input.readMessage(
                  DataDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              data_.getMutableMap().put(
                  data__.getKey(), data__.getValue());
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return AllGuildRank.internal_static_com_yy_yyzone_guildrank_api_GuildRankVO_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetData();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return AllGuildRank.internal_static_com_yy_yyzone_guildrank_api_GuildRankVO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              GuildRankVO.class, Builder.class);
    }

    public static final int DATA_FIELD_NUMBER = 1;
    private static final class DataDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          String, String> defaultEntry =
              com.google.protobuf.MapEntry
              .<String, String>newDefaultInstance(
                  AllGuildRank.internal_static_com_yy_yyzone_guildrank_api_GuildRankVO_DataEntry_descriptor,
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "",
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "");
    }
    private com.google.protobuf.MapField<
        String, String> data_;
    private com.google.protobuf.MapField<String, String>
    internalGetData() {
      if (data_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            DataDefaultEntryHolder.defaultEntry);
      }
      return data_;
    }

    public int getDataCount() {
      return internalGetData().getMap().size();
    }
    /**
     * <code>map&lt;string, string&gt; data = 1;</code>
     */

    public boolean containsData(
        String key) {
      if (key == null) { throw new NullPointerException(); }
      return internalGetData().getMap().containsKey(key);
    }
    /**
     * Use {@link #getDataMap()} instead.
     */
    @Deprecated
    public java.util.Map<String, String> getData() {
      return getDataMap();
    }
    /**
     * <code>map&lt;string, string&gt; data = 1;</code>
     */

    public java.util.Map<String, String> getDataMap() {
      return internalGetData().getMap();
    }
    /**
     * <code>map&lt;string, string&gt; data = 1;</code>
     */

    public String getDataOrDefault(
        String key,
        String defaultValue) {
      if (key == null) { throw new NullPointerException(); }
      java.util.Map<String, String> map =
          internalGetData().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;string, string&gt; data = 1;</code>
     */

    public String getDataOrThrow(
        String key) {
      if (key == null) { throw new NullPointerException(); }
      java.util.Map<String, String> map =
          internalGetData().getMap();
      if (!map.containsKey(key)) {
        throw new IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeStringMapTo(
          output,
          internalGetData(),
          DataDefaultEntryHolder.defaultEntry,
          1);
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<String, String> entry
           : internalGetData().getMap().entrySet()) {
        com.google.protobuf.MapEntry<String, String>
        data__ = DataDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, data__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof GuildRankVO)) {
        return super.equals(obj);
      }
      GuildRankVO other = (GuildRankVO) obj;

      boolean result = true;
      result = result && internalGetData().equals(
          other.internalGetData());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetData().getMap().isEmpty()) {
        hash = (37 * hash) + DATA_FIELD_NUMBER;
        hash = (53 * hash) + internalGetData().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static GuildRankVO parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static GuildRankVO parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static GuildRankVO parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static GuildRankVO parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static GuildRankVO parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static GuildRankVO parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static GuildRankVO parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static GuildRankVO parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static GuildRankVO parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static GuildRankVO parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static GuildRankVO parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static GuildRankVO parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(GuildRankVO prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.yyzone.guildrank.api.GuildRankVO}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.api.GuildRankVO)
        GuildRankVOOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return AllGuildRank.internal_static_com_yy_yyzone_guildrank_api_GuildRankVO_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetData();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableData();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return AllGuildRank.internal_static_com_yy_yyzone_guildrank_api_GuildRankVO_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                GuildRankVO.class, Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.api.AllGuildRank.GuildRankVO.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        internalGetMutableData().clear();
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return AllGuildRank.internal_static_com_yy_yyzone_guildrank_api_GuildRankVO_descriptor;
      }

      @Override
      public GuildRankVO getDefaultInstanceForType() {
        return GuildRankVO.getDefaultInstance();
      }

      @Override
      public GuildRankVO build() {
        GuildRankVO result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public GuildRankVO buildPartial() {
        GuildRankVO result = new GuildRankVO(this);
        int from_bitField0_ = bitField0_;
        result.data_ = internalGetData();
        result.data_.makeImmutable();
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof GuildRankVO) {
          return mergeFrom((GuildRankVO)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(GuildRankVO other) {
        if (other == GuildRankVO.getDefaultInstance()) return this;
        internalGetMutableData().mergeFrom(
            other.internalGetData());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        GuildRankVO parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (GuildRankVO) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          String, String> data_;
      private com.google.protobuf.MapField<String, String>
      internalGetData() {
        if (data_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              DataDefaultEntryHolder.defaultEntry);
        }
        return data_;
      }
      private com.google.protobuf.MapField<String, String>
      internalGetMutableData() {
        onChanged();;
        if (data_ == null) {
          data_ = com.google.protobuf.MapField.newMapField(
              DataDefaultEntryHolder.defaultEntry);
        }
        if (!data_.isMutable()) {
          data_ = data_.copy();
        }
        return data_;
      }

      public int getDataCount() {
        return internalGetData().getMap().size();
      }
      /**
       * <code>map&lt;string, string&gt; data = 1;</code>
       */

      public boolean containsData(
          String key) {
        if (key == null) { throw new NullPointerException(); }
        return internalGetData().getMap().containsKey(key);
      }
      /**
       * Use {@link #getDataMap()} instead.
       */
      @Deprecated
      public java.util.Map<String, String> getData() {
        return getDataMap();
      }
      /**
       * <code>map&lt;string, string&gt; data = 1;</code>
       */

      public java.util.Map<String, String> getDataMap() {
        return internalGetData().getMap();
      }
      /**
       * <code>map&lt;string, string&gt; data = 1;</code>
       */

      public String getDataOrDefault(
          String key,
          String defaultValue) {
        if (key == null) { throw new NullPointerException(); }
        java.util.Map<String, String> map =
            internalGetData().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;string, string&gt; data = 1;</code>
       */

      public String getDataOrThrow(
          String key) {
        if (key == null) { throw new NullPointerException(); }
        java.util.Map<String, String> map =
            internalGetData().getMap();
        if (!map.containsKey(key)) {
          throw new IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearData() {
        internalGetMutableData().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;string, string&gt; data = 1;</code>
       */

      public Builder removeData(
          String key) {
        if (key == null) { throw new NullPointerException(); }
        internalGetMutableData().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @Deprecated
      public java.util.Map<String, String>
      getMutableData() {
        return internalGetMutableData().getMutableMap();
      }
      /**
       * <code>map&lt;string, string&gt; data = 1;</code>
       */
      public Builder putData(
          String key,
          String value) {
        if (key == null) { throw new NullPointerException(); }
        if (value == null) { throw new NullPointerException(); }
        internalGetMutableData().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;string, string&gt; data = 1;</code>
       */

      public Builder putAllData(
          java.util.Map<String, String> values) {
        internalGetMutableData().getMutableMap()
            .putAll(values);
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.api.GuildRankVO)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.api.GuildRankVO)
    private static final GuildRankVO DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new GuildRankVO();
    }

    public static GuildRankVO getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<GuildRankVO>
        PARSER = new com.google.protobuf.AbstractParser<GuildRankVO>() {
      @Override
      public GuildRankVO parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GuildRankVO(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GuildRankVO> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<GuildRankVO> getParserForType() {
      return PARSER;
    }

    @Override
    public GuildRankVO getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryAllGuildRankReqOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.api.QueryAllGuildRankReq)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 2019-07
     * </pre>
     *
     * <code>string month = 1;</code>
     */
    String getMonth();
    /**
     * <pre>
     * 2019-07
     * </pre>
     *
     * <code>string month = 1;</code>
     */
    com.google.protobuf.ByteString
        getMonthBytes();
  }
  /**
   * Protobuf type {@code com.yy.yyzone.guildrank.api.QueryAllGuildRankReq}
   */
  public  static final class QueryAllGuildRankReq extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.api.QueryAllGuildRankReq)
      QueryAllGuildRankReqOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QueryAllGuildRankReq.newBuilder() to construct.
    private QueryAllGuildRankReq(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QueryAllGuildRankReq() {
      month_ = "";
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QueryAllGuildRankReq(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              String s = input.readStringRequireUtf8();

              month_ = s;
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return AllGuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankReq_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return AllGuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              QueryAllGuildRankReq.class, Builder.class);
    }

    public static final int MONTH_FIELD_NUMBER = 1;
    private volatile Object month_;
    /**
     * <pre>
     * 2019-07
     * </pre>
     *
     * <code>string month = 1;</code>
     */
    public String getMonth() {
      Object ref = month_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        month_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 2019-07
     * </pre>
     *
     * <code>string month = 1;</code>
     */
    public com.google.protobuf.ByteString
        getMonthBytes() {
      Object ref = month_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        month_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getMonthBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, month_);
      }
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getMonthBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, month_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof QueryAllGuildRankReq)) {
        return super.equals(obj);
      }
      QueryAllGuildRankReq other = (QueryAllGuildRankReq) obj;

      boolean result = true;
      result = result && getMonth()
          .equals(other.getMonth());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MONTH_FIELD_NUMBER;
      hash = (53 * hash) + getMonth().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static QueryAllGuildRankReq parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryAllGuildRankReq parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryAllGuildRankReq parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryAllGuildRankReq parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryAllGuildRankReq parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryAllGuildRankReq parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryAllGuildRankReq parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static QueryAllGuildRankReq parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static QueryAllGuildRankReq parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static QueryAllGuildRankReq parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static QueryAllGuildRankReq parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static QueryAllGuildRankReq parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(QueryAllGuildRankReq prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.yyzone.guildrank.api.QueryAllGuildRankReq}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.api.QueryAllGuildRankReq)
        QueryAllGuildRankReqOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return AllGuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankReq_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return AllGuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankReq_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                QueryAllGuildRankReq.class, Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.api.AllGuildRank.QueryAllGuildRankReq.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        month_ = "";

        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return AllGuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankReq_descriptor;
      }

      @Override
      public QueryAllGuildRankReq getDefaultInstanceForType() {
        return QueryAllGuildRankReq.getDefaultInstance();
      }

      @Override
      public QueryAllGuildRankReq build() {
        QueryAllGuildRankReq result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public QueryAllGuildRankReq buildPartial() {
        QueryAllGuildRankReq result = new QueryAllGuildRankReq(this);
        result.month_ = month_;
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof QueryAllGuildRankReq) {
          return mergeFrom((QueryAllGuildRankReq)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(QueryAllGuildRankReq other) {
        if (other == QueryAllGuildRankReq.getDefaultInstance()) return this;
        if (!other.getMonth().isEmpty()) {
          month_ = other.month_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        QueryAllGuildRankReq parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (QueryAllGuildRankReq) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private Object month_ = "";
      /**
       * <pre>
       * 2019-07
       * </pre>
       *
       * <code>string month = 1;</code>
       */
      public String getMonth() {
        Object ref = month_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          month_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       * 2019-07
       * </pre>
       *
       * <code>string month = 1;</code>
       */
      public com.google.protobuf.ByteString
          getMonthBytes() {
        Object ref = month_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          month_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 2019-07
       * </pre>
       *
       * <code>string month = 1;</code>
       */
      public Builder setMonth(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        month_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 2019-07
       * </pre>
       *
       * <code>string month = 1;</code>
       */
      public Builder clearMonth() {
        
        month_ = getDefaultInstance().getMonth();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 2019-07
       * </pre>
       *
       * <code>string month = 1;</code>
       */
      public Builder setMonthBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        month_ = value;
        onChanged();
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.api.QueryAllGuildRankReq)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.api.QueryAllGuildRankReq)
    private static final QueryAllGuildRankReq DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new QueryAllGuildRankReq();
    }

    public static QueryAllGuildRankReq getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QueryAllGuildRankReq>
        PARSER = new com.google.protobuf.AbstractParser<QueryAllGuildRankReq>() {
      @Override
      public QueryAllGuildRankReq parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QueryAllGuildRankReq(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QueryAllGuildRankReq> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<QueryAllGuildRankReq> getParserForType() {
      return PARSER;
    }

    @Override
    public QueryAllGuildRankReq getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryAllGuildRankRespOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.api.QueryAllGuildRankResp)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 result = 1;</code>
     */
    int getResult();

    /**
     * <pre>
     *是否上个月最新的数据 1:是 0:不是
     * </pre>
     *
     * <code>uint32 isNew = 2;</code>
     */
    int getIsNew();

    /**
     * <code>repeated .com.yy.yyzone.guildrank.api.GuildRankVO dataList = 3;</code>
     */
    java.util.List<GuildRankVO>
        getDataListList();
    /**
     * <code>repeated .com.yy.yyzone.guildrank.api.GuildRankVO dataList = 3;</code>
     */
    GuildRankVO getDataList(int index);
    /**
     * <code>repeated .com.yy.yyzone.guildrank.api.GuildRankVO dataList = 3;</code>
     */
    int getDataListCount();
    /**
     * <code>repeated .com.yy.yyzone.guildrank.api.GuildRankVO dataList = 3;</code>
     */
    java.util.List<? extends GuildRankVOOrBuilder>
        getDataListOrBuilderList();
    /**
     * <code>repeated .com.yy.yyzone.guildrank.api.GuildRankVO dataList = 3;</code>
     */
    GuildRankVOOrBuilder getDataListOrBuilder(
            int index);
  }
  /**
   * Protobuf type {@code com.yy.yyzone.guildrank.api.QueryAllGuildRankResp}
   */
  public  static final class QueryAllGuildRankResp extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.api.QueryAllGuildRankResp)
      QueryAllGuildRankRespOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QueryAllGuildRankResp.newBuilder() to construct.
    private QueryAllGuildRankResp(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QueryAllGuildRankResp() {
      result_ = 0;
      isNew_ = 0;
      dataList_ = java.util.Collections.emptyList();
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QueryAllGuildRankResp(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              result_ = input.readUInt32();
              break;
            }
            case 16: {

              isNew_ = input.readUInt32();
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
                dataList_ = new java.util.ArrayList<GuildRankVO>();
                mutable_bitField0_ |= 0x00000004;
              }
              dataList_.add(
                  input.readMessage(GuildRankVO.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
          dataList_ = java.util.Collections.unmodifiableList(dataList_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return AllGuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankResp_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return AllGuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              QueryAllGuildRankResp.class, Builder.class);
    }

    private int bitField0_;
    public static final int RESULT_FIELD_NUMBER = 1;
    private int result_;
    /**
     * <code>uint32 result = 1;</code>
     */
    public int getResult() {
      return result_;
    }

    public static final int ISNEW_FIELD_NUMBER = 2;
    private int isNew_;
    /**
     * <pre>
     *是否上个月最新的数据 1:是 0:不是
     * </pre>
     *
     * <code>uint32 isNew = 2;</code>
     */
    public int getIsNew() {
      return isNew_;
    }

    public static final int DATALIST_FIELD_NUMBER = 3;
    private java.util.List<GuildRankVO> dataList_;
    /**
     * <code>repeated .com.yy.yyzone.guildrank.api.GuildRankVO dataList = 3;</code>
     */
    public java.util.List<GuildRankVO> getDataListList() {
      return dataList_;
    }
    /**
     * <code>repeated .com.yy.yyzone.guildrank.api.GuildRankVO dataList = 3;</code>
     */
    public java.util.List<? extends GuildRankVOOrBuilder>
        getDataListOrBuilderList() {
      return dataList_;
    }
    /**
     * <code>repeated .com.yy.yyzone.guildrank.api.GuildRankVO dataList = 3;</code>
     */
    public int getDataListCount() {
      return dataList_.size();
    }
    /**
     * <code>repeated .com.yy.yyzone.guildrank.api.GuildRankVO dataList = 3;</code>
     */
    public GuildRankVO getDataList(int index) {
      return dataList_.get(index);
    }
    /**
     * <code>repeated .com.yy.yyzone.guildrank.api.GuildRankVO dataList = 3;</code>
     */
    public GuildRankVOOrBuilder getDataListOrBuilder(
        int index) {
      return dataList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (result_ != 0) {
        output.writeUInt32(1, result_);
      }
      if (isNew_ != 0) {
        output.writeUInt32(2, isNew_);
      }
      for (int i = 0; i < dataList_.size(); i++) {
        output.writeMessage(3, dataList_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (result_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, result_);
      }
      if (isNew_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, isNew_);
      }
      for (int i = 0; i < dataList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, dataList_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof QueryAllGuildRankResp)) {
        return super.equals(obj);
      }
      QueryAllGuildRankResp other = (QueryAllGuildRankResp) obj;

      boolean result = true;
      result = result && (getResult()
          == other.getResult());
      result = result && (getIsNew()
          == other.getIsNew());
      result = result && getDataListList()
          .equals(other.getDataListList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + getResult();
      hash = (37 * hash) + ISNEW_FIELD_NUMBER;
      hash = (53 * hash) + getIsNew();
      if (getDataListCount() > 0) {
        hash = (37 * hash) + DATALIST_FIELD_NUMBER;
        hash = (53 * hash) + getDataListList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static QueryAllGuildRankResp parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryAllGuildRankResp parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryAllGuildRankResp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryAllGuildRankResp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryAllGuildRankResp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static QueryAllGuildRankResp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static QueryAllGuildRankResp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static QueryAllGuildRankResp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static QueryAllGuildRankResp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static QueryAllGuildRankResp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static QueryAllGuildRankResp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static QueryAllGuildRankResp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(QueryAllGuildRankResp prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.yyzone.guildrank.api.QueryAllGuildRankResp}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.api.QueryAllGuildRankResp)
        QueryAllGuildRankRespOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return AllGuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankResp_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return AllGuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankResp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                QueryAllGuildRankResp.class, Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.api.AllGuildRank.QueryAllGuildRankResp.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getDataListFieldBuilder();
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        result_ = 0;

        isNew_ = 0;

        if (dataListBuilder_ == null) {
          dataList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
        } else {
          dataListBuilder_.clear();
        }
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return AllGuildRank.internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankResp_descriptor;
      }

      @Override
      public QueryAllGuildRankResp getDefaultInstanceForType() {
        return QueryAllGuildRankResp.getDefaultInstance();
      }

      @Override
      public QueryAllGuildRankResp build() {
        QueryAllGuildRankResp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public QueryAllGuildRankResp buildPartial() {
        QueryAllGuildRankResp result = new QueryAllGuildRankResp(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.result_ = result_;
        result.isNew_ = isNew_;
        if (dataListBuilder_ == null) {
          if (((bitField0_ & 0x00000004) == 0x00000004)) {
            dataList_ = java.util.Collections.unmodifiableList(dataList_);
            bitField0_ = (bitField0_ & ~0x00000004);
          }
          result.dataList_ = dataList_;
        } else {
          result.dataList_ = dataListBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof QueryAllGuildRankResp) {
          return mergeFrom((QueryAllGuildRankResp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(QueryAllGuildRankResp other) {
        if (other == QueryAllGuildRankResp.getDefaultInstance()) return this;
        if (other.getResult() != 0) {
          setResult(other.getResult());
        }
        if (other.getIsNew() != 0) {
          setIsNew(other.getIsNew());
        }
        if (dataListBuilder_ == null) {
          if (!other.dataList_.isEmpty()) {
            if (dataList_.isEmpty()) {
              dataList_ = other.dataList_;
              bitField0_ = (bitField0_ & ~0x00000004);
            } else {
              ensureDataListIsMutable();
              dataList_.addAll(other.dataList_);
            }
            onChanged();
          }
        } else {
          if (!other.dataList_.isEmpty()) {
            if (dataListBuilder_.isEmpty()) {
              dataListBuilder_.dispose();
              dataListBuilder_ = null;
              dataList_ = other.dataList_;
              bitField0_ = (bitField0_ & ~0x00000004);
              dataListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getDataListFieldBuilder() : null;
            } else {
              dataListBuilder_.addAllMessages(other.dataList_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        QueryAllGuildRankResp parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (QueryAllGuildRankResp) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int result_ ;
      /**
       * <code>uint32 result = 1;</code>
       */
      public int getResult() {
        return result_;
      }
      /**
       * <code>uint32 result = 1;</code>
       */
      public Builder setResult(int value) {
        
        result_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 result = 1;</code>
       */
      public Builder clearResult() {
        
        result_ = 0;
        onChanged();
        return this;
      }

      private int isNew_ ;
      /**
       * <pre>
       *是否上个月最新的数据 1:是 0:不是
       * </pre>
       *
       * <code>uint32 isNew = 2;</code>
       */
      public int getIsNew() {
        return isNew_;
      }
      /**
       * <pre>
       *是否上个月最新的数据 1:是 0:不是
       * </pre>
       *
       * <code>uint32 isNew = 2;</code>
       */
      public Builder setIsNew(int value) {
        
        isNew_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *是否上个月最新的数据 1:是 0:不是
       * </pre>
       *
       * <code>uint32 isNew = 2;</code>
       */
      public Builder clearIsNew() {
        
        isNew_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<GuildRankVO> dataList_ =
        java.util.Collections.emptyList();
      private void ensureDataListIsMutable() {
        if (!((bitField0_ & 0x00000004) == 0x00000004)) {
          dataList_ = new java.util.ArrayList<GuildRankVO>(dataList_);
          bitField0_ |= 0x00000004;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          GuildRankVO, GuildRankVO.Builder, GuildRankVOOrBuilder> dataListBuilder_;

      /**
       * <code>repeated .com.yy.yyzone.guildrank.api.GuildRankVO dataList = 3;</code>
       */
      public java.util.List<GuildRankVO> getDataListList() {
        if (dataListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(dataList_);
        } else {
          return dataListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .com.yy.yyzone.guildrank.api.GuildRankVO dataList = 3;</code>
       */
      public int getDataListCount() {
        if (dataListBuilder_ == null) {
          return dataList_.size();
        } else {
          return dataListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .com.yy.yyzone.guildrank.api.GuildRankVO dataList = 3;</code>
       */
      public GuildRankVO getDataList(int index) {
        if (dataListBuilder_ == null) {
          return dataList_.get(index);
        } else {
          return dataListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .com.yy.yyzone.guildrank.api.GuildRankVO dataList = 3;</code>
       */
      public Builder setDataList(
          int index, GuildRankVO value) {
        if (dataListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDataListIsMutable();
          dataList_.set(index, value);
          onChanged();
        } else {
          dataListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yy.yyzone.guildrank.api.GuildRankVO dataList = 3;</code>
       */
      public Builder setDataList(
          int index, GuildRankVO.Builder builderForValue) {
        if (dataListBuilder_ == null) {
          ensureDataListIsMutable();
          dataList_.set(index, builderForValue.build());
          onChanged();
        } else {
          dataListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yy.yyzone.guildrank.api.GuildRankVO dataList = 3;</code>
       */
      public Builder addDataList(GuildRankVO value) {
        if (dataListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDataListIsMutable();
          dataList_.add(value);
          onChanged();
        } else {
          dataListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yy.yyzone.guildrank.api.GuildRankVO dataList = 3;</code>
       */
      public Builder addDataList(
          int index, GuildRankVO value) {
        if (dataListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDataListIsMutable();
          dataList_.add(index, value);
          onChanged();
        } else {
          dataListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yy.yyzone.guildrank.api.GuildRankVO dataList = 3;</code>
       */
      public Builder addDataList(
          GuildRankVO.Builder builderForValue) {
        if (dataListBuilder_ == null) {
          ensureDataListIsMutable();
          dataList_.add(builderForValue.build());
          onChanged();
        } else {
          dataListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yy.yyzone.guildrank.api.GuildRankVO dataList = 3;</code>
       */
      public Builder addDataList(
          int index, GuildRankVO.Builder builderForValue) {
        if (dataListBuilder_ == null) {
          ensureDataListIsMutable();
          dataList_.add(index, builderForValue.build());
          onChanged();
        } else {
          dataListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yy.yyzone.guildrank.api.GuildRankVO dataList = 3;</code>
       */
      public Builder addAllDataList(
          Iterable<? extends GuildRankVO> values) {
        if (dataListBuilder_ == null) {
          ensureDataListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, dataList_);
          onChanged();
        } else {
          dataListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .com.yy.yyzone.guildrank.api.GuildRankVO dataList = 3;</code>
       */
      public Builder clearDataList() {
        if (dataListBuilder_ == null) {
          dataList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
          onChanged();
        } else {
          dataListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .com.yy.yyzone.guildrank.api.GuildRankVO dataList = 3;</code>
       */
      public Builder removeDataList(int index) {
        if (dataListBuilder_ == null) {
          ensureDataListIsMutable();
          dataList_.remove(index);
          onChanged();
        } else {
          dataListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .com.yy.yyzone.guildrank.api.GuildRankVO dataList = 3;</code>
       */
      public GuildRankVO.Builder getDataListBuilder(
          int index) {
        return getDataListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .com.yy.yyzone.guildrank.api.GuildRankVO dataList = 3;</code>
       */
      public GuildRankVOOrBuilder getDataListOrBuilder(
          int index) {
        if (dataListBuilder_ == null) {
          return dataList_.get(index);  } else {
          return dataListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .com.yy.yyzone.guildrank.api.GuildRankVO dataList = 3;</code>
       */
      public java.util.List<? extends GuildRankVOOrBuilder>
           getDataListOrBuilderList() {
        if (dataListBuilder_ != null) {
          return dataListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(dataList_);
        }
      }
      /**
       * <code>repeated .com.yy.yyzone.guildrank.api.GuildRankVO dataList = 3;</code>
       */
      public GuildRankVO.Builder addDataListBuilder() {
        return getDataListFieldBuilder().addBuilder(
            GuildRankVO.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yy.yyzone.guildrank.api.GuildRankVO dataList = 3;</code>
       */
      public GuildRankVO.Builder addDataListBuilder(
          int index) {
        return getDataListFieldBuilder().addBuilder(
            index, GuildRankVO.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yy.yyzone.guildrank.api.GuildRankVO dataList = 3;</code>
       */
      public java.util.List<GuildRankVO.Builder>
           getDataListBuilderList() {
        return getDataListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          GuildRankVO, GuildRankVO.Builder, GuildRankVOOrBuilder>
          getDataListFieldBuilder() {
        if (dataListBuilder_ == null) {
          dataListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              GuildRankVO, GuildRankVO.Builder, GuildRankVOOrBuilder>(
                  dataList_,
                  ((bitField0_ & 0x00000004) == 0x00000004),
                  getParentForChildren(),
                  isClean());
          dataList_ = null;
        }
        return dataListBuilder_;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.api.QueryAllGuildRankResp)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.api.QueryAllGuildRankResp)
    private static final QueryAllGuildRankResp DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new QueryAllGuildRankResp();
    }

    public static QueryAllGuildRankResp getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QueryAllGuildRankResp>
        PARSER = new com.google.protobuf.AbstractParser<QueryAllGuildRankResp>() {
      @Override
      public QueryAllGuildRankResp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QueryAllGuildRankResp(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QueryAllGuildRankResp> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<QueryAllGuildRankResp> getParserForType() {
      return PARSER;
    }

    @Override
    public QueryAllGuildRankResp getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderReq_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderReq_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderResp_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderResp_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_GuildRankVO_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_GuildRankVO_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_GuildRankVO_DataEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_GuildRankVO_DataEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankReq_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankReq_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankResp_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankResp_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    String[] descriptorData = {
      "\n\022AllGuildRank.proto\022\033com.yy.yyzone.guil" +
      "drank.api\032\turi.proto\"?\n\033QueryAllGuildRan" +
      "kByOrderReq\022\r\n\005month\030\001 \001(\t\022\021\n\torderType\030" +
      "\002 \001(\r\"M\n\034QueryAllGuildRankByOrderResp\022\016\n" +
      "\006result\030\001 \001(\r\022\r\n\005isNew\030\002 \001(\r\022\016\n\006owuids\030\003" +
      " \003(\r\"|\n\013GuildRankVO\022@\n\004data\030\001 \003(\01322.com." +
      "yy.yyzone.guildrank.api.GuildRankVO.Data" +
      "Entry\032+\n\tDataEntry\022\013\n\003key\030\001 \001(\t\022\r\n\005value" +
      "\030\002 \001(\t:\0028\001\"%\n\024QueryAllGuildRankReq\022\r\n\005mo" +
      "nth\030\001 \001(\t\"r\n\025QueryAllGuildRankResp\022\016\n\006re" +
      "sult\030\001 \001(\r\022\r\n\005isNew\030\002 \001(\r\022:\n\010dataList\030\003 " +
      "\003(\0132(.com.yy.yyzone.guildrank.api.GuildR" +
      "ankVOb\006proto3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          yyp.Uri.getDescriptor(),
        }, assigner);
    internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderReq_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderReq_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderReq_descriptor,
        new String[] { "Month", "OrderType", });
    internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderResp_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderResp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankByOrderResp_descriptor,
        new String[] { "Result", "IsNew", "Owuids", });
    internal_static_com_yy_yyzone_guildrank_api_GuildRankVO_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yy_yyzone_guildrank_api_GuildRankVO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_GuildRankVO_descriptor,
        new String[] { "Data", });
    internal_static_com_yy_yyzone_guildrank_api_GuildRankVO_DataEntry_descriptor =
      internal_static_com_yy_yyzone_guildrank_api_GuildRankVO_descriptor.getNestedTypes().get(0);
    internal_static_com_yy_yyzone_guildrank_api_GuildRankVO_DataEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_GuildRankVO_DataEntry_descriptor,
        new String[] { "Key", "Value", });
    internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankReq_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankReq_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankReq_descriptor,
        new String[] { "Month", });
    internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankResp_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankResp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_QueryAllGuildRankResp_descriptor,
        new String[] { "Result", "IsNew", "DataList", });
    yyp.Uri.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
