// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: GuildRankI64.proto

package com.yy.yyzone.guildrank.api;

public final class GuildRankI64 {
  private GuildRankI64() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface BatchQueryCurrentRankReq64OrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.api.BatchQueryCurrentRankReq64)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated int64 uid = 1;</code>
     */
    java.util.List<Long> getUidList();
    /**
     * <code>repeated int64 uid = 1;</code>
     */
    int getUidCount();
    /**
     * <code>repeated int64 uid = 1;</code>
     */
    long getUid(int index);
  }
  /**
   * Protobuf type {@code com.yy.yyzone.guildrank.api.BatchQueryCurrentRankReq64}
   */
  public  static final class BatchQueryCurrentRankReq64 extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.api.BatchQueryCurrentRankReq64)
      BatchQueryCurrentRankReq64OrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BatchQueryCurrentRankReq64.newBuilder() to construct.
    private BatchQueryCurrentRankReq64(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BatchQueryCurrentRankReq64() {
      uid_ = java.util.Collections.emptyList();
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BatchQueryCurrentRankReq64(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                uid_ = new java.util.ArrayList<Long>();
                mutable_bitField0_ |= 0x00000001;
              }
              uid_.add(input.readInt64());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001) && input.getBytesUntilLimit() > 0) {
                uid_ = new java.util.ArrayList<Long>();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                uid_.add(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          uid_ = java.util.Collections.unmodifiableList(uid_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return GuildRankI64.internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankReq64_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return GuildRankI64.internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankReq64_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              BatchQueryCurrentRankReq64.class, Builder.class);
    }

    public static final int UID_FIELD_NUMBER = 1;
    private java.util.List<Long> uid_;
    /**
     * <code>repeated int64 uid = 1;</code>
     */
    public java.util.List<Long>
        getUidList() {
      return uid_;
    }
    /**
     * <code>repeated int64 uid = 1;</code>
     */
    public int getUidCount() {
      return uid_.size();
    }
    /**
     * <code>repeated int64 uid = 1;</code>
     */
    public long getUid(int index) {
      return uid_.get(index);
    }
    private int uidMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (getUidList().size() > 0) {
        output.writeUInt32NoTag(10);
        output.writeUInt32NoTag(uidMemoizedSerializedSize);
      }
      for (int i = 0; i < uid_.size(); i++) {
        output.writeInt64NoTag(uid_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < uid_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(uid_.get(i));
        }
        size += dataSize;
        if (!getUidList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        uidMemoizedSerializedSize = dataSize;
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof BatchQueryCurrentRankReq64)) {
        return super.equals(obj);
      }
      BatchQueryCurrentRankReq64 other = (BatchQueryCurrentRankReq64) obj;

      boolean result = true;
      result = result && getUidList()
          .equals(other.getUidList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getUidCount() > 0) {
        hash = (37 * hash) + UID_FIELD_NUMBER;
        hash = (53 * hash) + getUidList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static BatchQueryCurrentRankReq64 parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static BatchQueryCurrentRankReq64 parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static BatchQueryCurrentRankReq64 parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static BatchQueryCurrentRankReq64 parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static BatchQueryCurrentRankReq64 parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static BatchQueryCurrentRankReq64 parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static BatchQueryCurrentRankReq64 parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static BatchQueryCurrentRankReq64 parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static BatchQueryCurrentRankReq64 parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static BatchQueryCurrentRankReq64 parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static BatchQueryCurrentRankReq64 parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static BatchQueryCurrentRankReq64 parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(BatchQueryCurrentRankReq64 prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.yyzone.guildrank.api.BatchQueryCurrentRankReq64}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.api.BatchQueryCurrentRankReq64)
        BatchQueryCurrentRankReq64OrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return GuildRankI64.internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankReq64_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return GuildRankI64.internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankReq64_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                BatchQueryCurrentRankReq64.class, Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.api.GuildRankI64.BatchQueryCurrentRankReq64.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        uid_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return GuildRankI64.internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankReq64_descriptor;
      }

      @Override
      public BatchQueryCurrentRankReq64 getDefaultInstanceForType() {
        return BatchQueryCurrentRankReq64.getDefaultInstance();
      }

      @Override
      public BatchQueryCurrentRankReq64 build() {
        BatchQueryCurrentRankReq64 result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public BatchQueryCurrentRankReq64 buildPartial() {
        BatchQueryCurrentRankReq64 result = new BatchQueryCurrentRankReq64(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) == 0x00000001)) {
          uid_ = java.util.Collections.unmodifiableList(uid_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.uid_ = uid_;
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof BatchQueryCurrentRankReq64) {
          return mergeFrom((BatchQueryCurrentRankReq64)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(BatchQueryCurrentRankReq64 other) {
        if (other == BatchQueryCurrentRankReq64.getDefaultInstance()) return this;
        if (!other.uid_.isEmpty()) {
          if (uid_.isEmpty()) {
            uid_ = other.uid_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureUidIsMutable();
            uid_.addAll(other.uid_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        BatchQueryCurrentRankReq64 parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (BatchQueryCurrentRankReq64) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<Long> uid_ = java.util.Collections.emptyList();
      private void ensureUidIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          uid_ = new java.util.ArrayList<Long>(uid_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated int64 uid = 1;</code>
       */
      public java.util.List<Long>
          getUidList() {
        return java.util.Collections.unmodifiableList(uid_);
      }
      /**
       * <code>repeated int64 uid = 1;</code>
       */
      public int getUidCount() {
        return uid_.size();
      }
      /**
       * <code>repeated int64 uid = 1;</code>
       */
      public long getUid(int index) {
        return uid_.get(index);
      }
      /**
       * <code>repeated int64 uid = 1;</code>
       */
      public Builder setUid(
          int index, long value) {
        ensureUidIsMutable();
        uid_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 uid = 1;</code>
       */
      public Builder addUid(long value) {
        ensureUidIsMutable();
        uid_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 uid = 1;</code>
       */
      public Builder addAllUid(
          Iterable<? extends Long> values) {
        ensureUidIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, uid_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 uid = 1;</code>
       */
      public Builder clearUid() {
        uid_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.api.BatchQueryCurrentRankReq64)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.api.BatchQueryCurrentRankReq64)
    private static final BatchQueryCurrentRankReq64 DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new BatchQueryCurrentRankReq64();
    }

    public static BatchQueryCurrentRankReq64 getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<BatchQueryCurrentRankReq64>
        PARSER = new com.google.protobuf.AbstractParser<BatchQueryCurrentRankReq64>() {
      @Override
      public BatchQueryCurrentRankReq64 parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BatchQueryCurrentRankReq64(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BatchQueryCurrentRankReq64> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<BatchQueryCurrentRankReq64> getParserForType() {
      return PARSER;
    }

    @Override
    public BatchQueryCurrentRankReq64 getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BatchQueryCurrentRankResp64OrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.api.BatchQueryCurrentRankResp64)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 result = 1;</code>
     */
    int getResult();

    /**
     * <code>map&lt;int64, int32&gt; uidLevel = 2;</code>
     */
    int getUidLevelCount();
    /**
     * <code>map&lt;int64, int32&gt; uidLevel = 2;</code>
     */
    boolean containsUidLevel(
        long key);
    /**
     * Use {@link #getUidLevelMap()} instead.
     */
    @Deprecated
    java.util.Map<Long, Integer>
    getUidLevel();
    /**
     * <code>map&lt;int64, int32&gt; uidLevel = 2;</code>
     */
    java.util.Map<Long, Integer>
    getUidLevelMap();
    /**
     * <code>map&lt;int64, int32&gt; uidLevel = 2;</code>
     */

    int getUidLevelOrDefault(
        long key,
        int defaultValue);
    /**
     * <code>map&lt;int64, int32&gt; uidLevel = 2;</code>
     */

    int getUidLevelOrThrow(
        long key);
  }
  /**
   * Protobuf type {@code com.yy.yyzone.guildrank.api.BatchQueryCurrentRankResp64}
   */
  public  static final class BatchQueryCurrentRankResp64 extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.api.BatchQueryCurrentRankResp64)
      BatchQueryCurrentRankResp64OrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BatchQueryCurrentRankResp64.newBuilder() to construct.
    private BatchQueryCurrentRankResp64(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BatchQueryCurrentRankResp64() {
      result_ = 0;
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BatchQueryCurrentRankResp64(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              result_ = input.readUInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                uidLevel_ = com.google.protobuf.MapField.newMapField(
                    UidLevelDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000002;
              }
              com.google.protobuf.MapEntry<Long, Integer>
              uidLevel__ = input.readMessage(
                  UidLevelDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              uidLevel_.getMutableMap().put(
                  uidLevel__.getKey(), uidLevel__.getValue());
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return GuildRankI64.internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankResp64_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 2:
          return internalGetUidLevel();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return GuildRankI64.internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankResp64_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              BatchQueryCurrentRankResp64.class, Builder.class);
    }

    private int bitField0_;
    public static final int RESULT_FIELD_NUMBER = 1;
    private int result_;
    /**
     * <code>uint32 result = 1;</code>
     */
    public int getResult() {
      return result_;
    }

    public static final int UIDLEVEL_FIELD_NUMBER = 2;
    private static final class UidLevelDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          Long, Integer> defaultEntry =
              com.google.protobuf.MapEntry
              .<Long, Integer>newDefaultInstance(
                  GuildRankI64.internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankResp64_UidLevelEntry_descriptor,
                  com.google.protobuf.WireFormat.FieldType.INT64,
                  0L,
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0);
    }
    private com.google.protobuf.MapField<
        Long, Integer> uidLevel_;
    private com.google.protobuf.MapField<Long, Integer>
    internalGetUidLevel() {
      if (uidLevel_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            UidLevelDefaultEntryHolder.defaultEntry);
      }
      return uidLevel_;
    }

    public int getUidLevelCount() {
      return internalGetUidLevel().getMap().size();
    }
    /**
     * <code>map&lt;int64, int32&gt; uidLevel = 2;</code>
     */

    public boolean containsUidLevel(
        long key) {
      
      return internalGetUidLevel().getMap().containsKey(key);
    }
    /**
     * Use {@link #getUidLevelMap()} instead.
     */
    @Deprecated
    public java.util.Map<Long, Integer> getUidLevel() {
      return getUidLevelMap();
    }
    /**
     * <code>map&lt;int64, int32&gt; uidLevel = 2;</code>
     */

    public java.util.Map<Long, Integer> getUidLevelMap() {
      return internalGetUidLevel().getMap();
    }
    /**
     * <code>map&lt;int64, int32&gt; uidLevel = 2;</code>
     */

    public int getUidLevelOrDefault(
        long key,
        int defaultValue) {
      
      java.util.Map<Long, Integer> map =
          internalGetUidLevel().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;int64, int32&gt; uidLevel = 2;</code>
     */

    public int getUidLevelOrThrow(
        long key) {
      
      java.util.Map<Long, Integer> map =
          internalGetUidLevel().getMap();
      if (!map.containsKey(key)) {
        throw new IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (result_ != 0) {
        output.writeUInt32(1, result_);
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeLongMapTo(
          output,
          internalGetUidLevel(),
          UidLevelDefaultEntryHolder.defaultEntry,
          2);
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (result_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, result_);
      }
      for (java.util.Map.Entry<Long, Integer> entry
           : internalGetUidLevel().getMap().entrySet()) {
        com.google.protobuf.MapEntry<Long, Integer>
        uidLevel__ = UidLevelDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(2, uidLevel__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof BatchQueryCurrentRankResp64)) {
        return super.equals(obj);
      }
      BatchQueryCurrentRankResp64 other = (BatchQueryCurrentRankResp64) obj;

      boolean result = true;
      result = result && (getResult()
          == other.getResult());
      result = result && internalGetUidLevel().equals(
          other.internalGetUidLevel());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + getResult();
      if (!internalGetUidLevel().getMap().isEmpty()) {
        hash = (37 * hash) + UIDLEVEL_FIELD_NUMBER;
        hash = (53 * hash) + internalGetUidLevel().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static BatchQueryCurrentRankResp64 parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static BatchQueryCurrentRankResp64 parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static BatchQueryCurrentRankResp64 parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static BatchQueryCurrentRankResp64 parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static BatchQueryCurrentRankResp64 parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static BatchQueryCurrentRankResp64 parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static BatchQueryCurrentRankResp64 parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static BatchQueryCurrentRankResp64 parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static BatchQueryCurrentRankResp64 parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static BatchQueryCurrentRankResp64 parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static BatchQueryCurrentRankResp64 parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static BatchQueryCurrentRankResp64 parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(BatchQueryCurrentRankResp64 prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.yyzone.guildrank.api.BatchQueryCurrentRankResp64}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.api.BatchQueryCurrentRankResp64)
        BatchQueryCurrentRankResp64OrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return GuildRankI64.internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankResp64_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 2:
            return internalGetUidLevel();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 2:
            return internalGetMutableUidLevel();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return GuildRankI64.internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankResp64_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                BatchQueryCurrentRankResp64.class, Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.api.GuildRankI64.BatchQueryCurrentRankResp64.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        result_ = 0;

        internalGetMutableUidLevel().clear();
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return GuildRankI64.internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankResp64_descriptor;
      }

      @Override
      public BatchQueryCurrentRankResp64 getDefaultInstanceForType() {
        return BatchQueryCurrentRankResp64.getDefaultInstance();
      }

      @Override
      public BatchQueryCurrentRankResp64 build() {
        BatchQueryCurrentRankResp64 result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public BatchQueryCurrentRankResp64 buildPartial() {
        BatchQueryCurrentRankResp64 result = new BatchQueryCurrentRankResp64(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.result_ = result_;
        result.uidLevel_ = internalGetUidLevel();
        result.uidLevel_.makeImmutable();
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof BatchQueryCurrentRankResp64) {
          return mergeFrom((BatchQueryCurrentRankResp64)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(BatchQueryCurrentRankResp64 other) {
        if (other == BatchQueryCurrentRankResp64.getDefaultInstance()) return this;
        if (other.getResult() != 0) {
          setResult(other.getResult());
        }
        internalGetMutableUidLevel().mergeFrom(
            other.internalGetUidLevel());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        BatchQueryCurrentRankResp64 parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (BatchQueryCurrentRankResp64) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int result_ ;
      /**
       * <code>uint32 result = 1;</code>
       */
      public int getResult() {
        return result_;
      }
      /**
       * <code>uint32 result = 1;</code>
       */
      public Builder setResult(int value) {
        
        result_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 result = 1;</code>
       */
      public Builder clearResult() {
        
        result_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.MapField<
          Long, Integer> uidLevel_;
      private com.google.protobuf.MapField<Long, Integer>
      internalGetUidLevel() {
        if (uidLevel_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              UidLevelDefaultEntryHolder.defaultEntry);
        }
        return uidLevel_;
      }
      private com.google.protobuf.MapField<Long, Integer>
      internalGetMutableUidLevel() {
        onChanged();;
        if (uidLevel_ == null) {
          uidLevel_ = com.google.protobuf.MapField.newMapField(
              UidLevelDefaultEntryHolder.defaultEntry);
        }
        if (!uidLevel_.isMutable()) {
          uidLevel_ = uidLevel_.copy();
        }
        return uidLevel_;
      }

      public int getUidLevelCount() {
        return internalGetUidLevel().getMap().size();
      }
      /**
       * <code>map&lt;int64, int32&gt; uidLevel = 2;</code>
       */

      public boolean containsUidLevel(
          long key) {
        
        return internalGetUidLevel().getMap().containsKey(key);
      }
      /**
       * Use {@link #getUidLevelMap()} instead.
       */
      @Deprecated
      public java.util.Map<Long, Integer> getUidLevel() {
        return getUidLevelMap();
      }
      /**
       * <code>map&lt;int64, int32&gt; uidLevel = 2;</code>
       */

      public java.util.Map<Long, Integer> getUidLevelMap() {
        return internalGetUidLevel().getMap();
      }
      /**
       * <code>map&lt;int64, int32&gt; uidLevel = 2;</code>
       */

      public int getUidLevelOrDefault(
          long key,
          int defaultValue) {
        
        java.util.Map<Long, Integer> map =
            internalGetUidLevel().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;int64, int32&gt; uidLevel = 2;</code>
       */

      public int getUidLevelOrThrow(
          long key) {
        
        java.util.Map<Long, Integer> map =
            internalGetUidLevel().getMap();
        if (!map.containsKey(key)) {
          throw new IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearUidLevel() {
        internalGetMutableUidLevel().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;int64, int32&gt; uidLevel = 2;</code>
       */

      public Builder removeUidLevel(
          long key) {
        
        internalGetMutableUidLevel().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @Deprecated
      public java.util.Map<Long, Integer>
      getMutableUidLevel() {
        return internalGetMutableUidLevel().getMutableMap();
      }
      /**
       * <code>map&lt;int64, int32&gt; uidLevel = 2;</code>
       */
      public Builder putUidLevel(
          long key,
          int value) {
        
        
        internalGetMutableUidLevel().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;int64, int32&gt; uidLevel = 2;</code>
       */

      public Builder putAllUidLevel(
          java.util.Map<Long, Integer> values) {
        internalGetMutableUidLevel().getMutableMap()
            .putAll(values);
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.api.BatchQueryCurrentRankResp64)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.api.BatchQueryCurrentRankResp64)
    private static final BatchQueryCurrentRankResp64 DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new BatchQueryCurrentRankResp64();
    }

    public static BatchQueryCurrentRankResp64 getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<BatchQueryCurrentRankResp64>
        PARSER = new com.google.protobuf.AbstractParser<BatchQueryCurrentRankResp64>() {
      @Override
      public BatchQueryCurrentRankResp64 parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BatchQueryCurrentRankResp64(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BatchQueryCurrentRankResp64> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<BatchQueryCurrentRankResp64> getParserForType() {
      return PARSER;
    }

    @Override
    public BatchQueryCurrentRankResp64 getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BatchQueryGuildRankReq64OrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.api.BatchQueryGuildRankReq64)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated int64 uid = 1;</code>
     */
    java.util.List<Long> getUidList();
    /**
     * <code>repeated int64 uid = 1;</code>
     */
    int getUidCount();
    /**
     * <code>repeated int64 uid = 1;</code>
     */
    long getUid(int index);
  }
  /**
   * Protobuf type {@code com.yy.yyzone.guildrank.api.BatchQueryGuildRankReq64}
   */
  public  static final class BatchQueryGuildRankReq64 extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.api.BatchQueryGuildRankReq64)
      BatchQueryGuildRankReq64OrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BatchQueryGuildRankReq64.newBuilder() to construct.
    private BatchQueryGuildRankReq64(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BatchQueryGuildRankReq64() {
      uid_ = java.util.Collections.emptyList();
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BatchQueryGuildRankReq64(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                uid_ = new java.util.ArrayList<Long>();
                mutable_bitField0_ |= 0x00000001;
              }
              uid_.add(input.readInt64());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001) && input.getBytesUntilLimit() > 0) {
                uid_ = new java.util.ArrayList<Long>();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                uid_.add(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          uid_ = java.util.Collections.unmodifiableList(uid_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return GuildRankI64.internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankReq64_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return GuildRankI64.internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankReq64_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              BatchQueryGuildRankReq64.class, Builder.class);
    }

    public static final int UID_FIELD_NUMBER = 1;
    private java.util.List<Long> uid_;
    /**
     * <code>repeated int64 uid = 1;</code>
     */
    public java.util.List<Long>
        getUidList() {
      return uid_;
    }
    /**
     * <code>repeated int64 uid = 1;</code>
     */
    public int getUidCount() {
      return uid_.size();
    }
    /**
     * <code>repeated int64 uid = 1;</code>
     */
    public long getUid(int index) {
      return uid_.get(index);
    }
    private int uidMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (getUidList().size() > 0) {
        output.writeUInt32NoTag(10);
        output.writeUInt32NoTag(uidMemoizedSerializedSize);
      }
      for (int i = 0; i < uid_.size(); i++) {
        output.writeInt64NoTag(uid_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < uid_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(uid_.get(i));
        }
        size += dataSize;
        if (!getUidList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        uidMemoizedSerializedSize = dataSize;
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof BatchQueryGuildRankReq64)) {
        return super.equals(obj);
      }
      BatchQueryGuildRankReq64 other = (BatchQueryGuildRankReq64) obj;

      boolean result = true;
      result = result && getUidList()
          .equals(other.getUidList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getUidCount() > 0) {
        hash = (37 * hash) + UID_FIELD_NUMBER;
        hash = (53 * hash) + getUidList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static BatchQueryGuildRankReq64 parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static BatchQueryGuildRankReq64 parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static BatchQueryGuildRankReq64 parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static BatchQueryGuildRankReq64 parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static BatchQueryGuildRankReq64 parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static BatchQueryGuildRankReq64 parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static BatchQueryGuildRankReq64 parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static BatchQueryGuildRankReq64 parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static BatchQueryGuildRankReq64 parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static BatchQueryGuildRankReq64 parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static BatchQueryGuildRankReq64 parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static BatchQueryGuildRankReq64 parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(BatchQueryGuildRankReq64 prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.yyzone.guildrank.api.BatchQueryGuildRankReq64}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.api.BatchQueryGuildRankReq64)
        BatchQueryGuildRankReq64OrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return GuildRankI64.internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankReq64_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return GuildRankI64.internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankReq64_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                BatchQueryGuildRankReq64.class, Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.api.GuildRankI64.BatchQueryGuildRankReq64.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        uid_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return GuildRankI64.internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankReq64_descriptor;
      }

      @Override
      public BatchQueryGuildRankReq64 getDefaultInstanceForType() {
        return BatchQueryGuildRankReq64.getDefaultInstance();
      }

      @Override
      public BatchQueryGuildRankReq64 build() {
        BatchQueryGuildRankReq64 result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public BatchQueryGuildRankReq64 buildPartial() {
        BatchQueryGuildRankReq64 result = new BatchQueryGuildRankReq64(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) == 0x00000001)) {
          uid_ = java.util.Collections.unmodifiableList(uid_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.uid_ = uid_;
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof BatchQueryGuildRankReq64) {
          return mergeFrom((BatchQueryGuildRankReq64)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(BatchQueryGuildRankReq64 other) {
        if (other == BatchQueryGuildRankReq64.getDefaultInstance()) return this;
        if (!other.uid_.isEmpty()) {
          if (uid_.isEmpty()) {
            uid_ = other.uid_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureUidIsMutable();
            uid_.addAll(other.uid_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        BatchQueryGuildRankReq64 parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (BatchQueryGuildRankReq64) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<Long> uid_ = java.util.Collections.emptyList();
      private void ensureUidIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          uid_ = new java.util.ArrayList<Long>(uid_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated int64 uid = 1;</code>
       */
      public java.util.List<Long>
          getUidList() {
        return java.util.Collections.unmodifiableList(uid_);
      }
      /**
       * <code>repeated int64 uid = 1;</code>
       */
      public int getUidCount() {
        return uid_.size();
      }
      /**
       * <code>repeated int64 uid = 1;</code>
       */
      public long getUid(int index) {
        return uid_.get(index);
      }
      /**
       * <code>repeated int64 uid = 1;</code>
       */
      public Builder setUid(
          int index, long value) {
        ensureUidIsMutable();
        uid_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 uid = 1;</code>
       */
      public Builder addUid(long value) {
        ensureUidIsMutable();
        uid_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 uid = 1;</code>
       */
      public Builder addAllUid(
          Iterable<? extends Long> values) {
        ensureUidIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, uid_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 uid = 1;</code>
       */
      public Builder clearUid() {
        uid_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.api.BatchQueryGuildRankReq64)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.api.BatchQueryGuildRankReq64)
    private static final BatchQueryGuildRankReq64 DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new BatchQueryGuildRankReq64();
    }

    public static BatchQueryGuildRankReq64 getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<BatchQueryGuildRankReq64>
        PARSER = new com.google.protobuf.AbstractParser<BatchQueryGuildRankReq64>() {
      @Override
      public BatchQueryGuildRankReq64 parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BatchQueryGuildRankReq64(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BatchQueryGuildRankReq64> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<BatchQueryGuildRankReq64> getParserForType() {
      return PARSER;
    }

    @Override
    public BatchQueryGuildRankReq64 getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BatchQueryGuildRankResp64OrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.api.BatchQueryGuildRankResp64)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 result = 1;</code>
     */
    int getResult();

    /**
     * <pre>
     *是否上个月最新的段位 1:是 0:不是
     * </pre>
     *
     * <code>uint32 isNew = 2;</code>
     */
    int getIsNew();

    /**
     * <code>map&lt;int64, int32&gt; uidLevel = 3;</code>
     */
    int getUidLevelCount();
    /**
     * <code>map&lt;int64, int32&gt; uidLevel = 3;</code>
     */
    boolean containsUidLevel(
        long key);
    /**
     * Use {@link #getUidLevelMap()} instead.
     */
    @Deprecated
    java.util.Map<Long, Integer>
    getUidLevel();
    /**
     * <code>map&lt;int64, int32&gt; uidLevel = 3;</code>
     */
    java.util.Map<Long, Integer>
    getUidLevelMap();
    /**
     * <code>map&lt;int64, int32&gt; uidLevel = 3;</code>
     */

    int getUidLevelOrDefault(
        long key,
        int defaultValue);
    /**
     * <code>map&lt;int64, int32&gt; uidLevel = 3;</code>
     */

    int getUidLevelOrThrow(
        long key);
  }
  /**
   * Protobuf type {@code com.yy.yyzone.guildrank.api.BatchQueryGuildRankResp64}
   */
  public  static final class BatchQueryGuildRankResp64 extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.api.BatchQueryGuildRankResp64)
      BatchQueryGuildRankResp64OrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BatchQueryGuildRankResp64.newBuilder() to construct.
    private BatchQueryGuildRankResp64(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BatchQueryGuildRankResp64() {
      result_ = 0;
      isNew_ = 0;
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BatchQueryGuildRankResp64(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              result_ = input.readUInt32();
              break;
            }
            case 16: {

              isNew_ = input.readUInt32();
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
                uidLevel_ = com.google.protobuf.MapField.newMapField(
                    UidLevelDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000004;
              }
              com.google.protobuf.MapEntry<Long, Integer>
              uidLevel__ = input.readMessage(
                  UidLevelDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              uidLevel_.getMutableMap().put(
                  uidLevel__.getKey(), uidLevel__.getValue());
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return GuildRankI64.internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankResp64_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 3:
          return internalGetUidLevel();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return GuildRankI64.internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankResp64_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              BatchQueryGuildRankResp64.class, Builder.class);
    }

    private int bitField0_;
    public static final int RESULT_FIELD_NUMBER = 1;
    private int result_;
    /**
     * <code>uint32 result = 1;</code>
     */
    public int getResult() {
      return result_;
    }

    public static final int ISNEW_FIELD_NUMBER = 2;
    private int isNew_;
    /**
     * <pre>
     *是否上个月最新的段位 1:是 0:不是
     * </pre>
     *
     * <code>uint32 isNew = 2;</code>
     */
    public int getIsNew() {
      return isNew_;
    }

    public static final int UIDLEVEL_FIELD_NUMBER = 3;
    private static final class UidLevelDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          Long, Integer> defaultEntry =
              com.google.protobuf.MapEntry
              .<Long, Integer>newDefaultInstance(
                  GuildRankI64.internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankResp64_UidLevelEntry_descriptor,
                  com.google.protobuf.WireFormat.FieldType.INT64,
                  0L,
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0);
    }
    private com.google.protobuf.MapField<
        Long, Integer> uidLevel_;
    private com.google.protobuf.MapField<Long, Integer>
    internalGetUidLevel() {
      if (uidLevel_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            UidLevelDefaultEntryHolder.defaultEntry);
      }
      return uidLevel_;
    }

    public int getUidLevelCount() {
      return internalGetUidLevel().getMap().size();
    }
    /**
     * <code>map&lt;int64, int32&gt; uidLevel = 3;</code>
     */

    public boolean containsUidLevel(
        long key) {
      
      return internalGetUidLevel().getMap().containsKey(key);
    }
    /**
     * Use {@link #getUidLevelMap()} instead.
     */
    @Deprecated
    public java.util.Map<Long, Integer> getUidLevel() {
      return getUidLevelMap();
    }
    /**
     * <code>map&lt;int64, int32&gt; uidLevel = 3;</code>
     */

    public java.util.Map<Long, Integer> getUidLevelMap() {
      return internalGetUidLevel().getMap();
    }
    /**
     * <code>map&lt;int64, int32&gt; uidLevel = 3;</code>
     */

    public int getUidLevelOrDefault(
        long key,
        int defaultValue) {
      
      java.util.Map<Long, Integer> map =
          internalGetUidLevel().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;int64, int32&gt; uidLevel = 3;</code>
     */

    public int getUidLevelOrThrow(
        long key) {
      
      java.util.Map<Long, Integer> map =
          internalGetUidLevel().getMap();
      if (!map.containsKey(key)) {
        throw new IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (result_ != 0) {
        output.writeUInt32(1, result_);
      }
      if (isNew_ != 0) {
        output.writeUInt32(2, isNew_);
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeLongMapTo(
          output,
          internalGetUidLevel(),
          UidLevelDefaultEntryHolder.defaultEntry,
          3);
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (result_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, result_);
      }
      if (isNew_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, isNew_);
      }
      for (java.util.Map.Entry<Long, Integer> entry
           : internalGetUidLevel().getMap().entrySet()) {
        com.google.protobuf.MapEntry<Long, Integer>
        uidLevel__ = UidLevelDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(3, uidLevel__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof BatchQueryGuildRankResp64)) {
        return super.equals(obj);
      }
      BatchQueryGuildRankResp64 other = (BatchQueryGuildRankResp64) obj;

      boolean result = true;
      result = result && (getResult()
          == other.getResult());
      result = result && (getIsNew()
          == other.getIsNew());
      result = result && internalGetUidLevel().equals(
          other.internalGetUidLevel());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + getResult();
      hash = (37 * hash) + ISNEW_FIELD_NUMBER;
      hash = (53 * hash) + getIsNew();
      if (!internalGetUidLevel().getMap().isEmpty()) {
        hash = (37 * hash) + UIDLEVEL_FIELD_NUMBER;
        hash = (53 * hash) + internalGetUidLevel().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static BatchQueryGuildRankResp64 parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static BatchQueryGuildRankResp64 parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static BatchQueryGuildRankResp64 parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static BatchQueryGuildRankResp64 parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static BatchQueryGuildRankResp64 parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static BatchQueryGuildRankResp64 parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static BatchQueryGuildRankResp64 parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static BatchQueryGuildRankResp64 parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static BatchQueryGuildRankResp64 parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static BatchQueryGuildRankResp64 parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static BatchQueryGuildRankResp64 parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static BatchQueryGuildRankResp64 parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(BatchQueryGuildRankResp64 prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.yyzone.guildrank.api.BatchQueryGuildRankResp64}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.api.BatchQueryGuildRankResp64)
        BatchQueryGuildRankResp64OrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return GuildRankI64.internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankResp64_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 3:
            return internalGetUidLevel();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 3:
            return internalGetMutableUidLevel();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return GuildRankI64.internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankResp64_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                BatchQueryGuildRankResp64.class, Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.api.GuildRankI64.BatchQueryGuildRankResp64.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        result_ = 0;

        isNew_ = 0;

        internalGetMutableUidLevel().clear();
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return GuildRankI64.internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankResp64_descriptor;
      }

      @Override
      public BatchQueryGuildRankResp64 getDefaultInstanceForType() {
        return BatchQueryGuildRankResp64.getDefaultInstance();
      }

      @Override
      public BatchQueryGuildRankResp64 build() {
        BatchQueryGuildRankResp64 result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public BatchQueryGuildRankResp64 buildPartial() {
        BatchQueryGuildRankResp64 result = new BatchQueryGuildRankResp64(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.result_ = result_;
        result.isNew_ = isNew_;
        result.uidLevel_ = internalGetUidLevel();
        result.uidLevel_.makeImmutable();
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof BatchQueryGuildRankResp64) {
          return mergeFrom((BatchQueryGuildRankResp64)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(BatchQueryGuildRankResp64 other) {
        if (other == BatchQueryGuildRankResp64.getDefaultInstance()) return this;
        if (other.getResult() != 0) {
          setResult(other.getResult());
        }
        if (other.getIsNew() != 0) {
          setIsNew(other.getIsNew());
        }
        internalGetMutableUidLevel().mergeFrom(
            other.internalGetUidLevel());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        BatchQueryGuildRankResp64 parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (BatchQueryGuildRankResp64) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int result_ ;
      /**
       * <code>uint32 result = 1;</code>
       */
      public int getResult() {
        return result_;
      }
      /**
       * <code>uint32 result = 1;</code>
       */
      public Builder setResult(int value) {
        
        result_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 result = 1;</code>
       */
      public Builder clearResult() {
        
        result_ = 0;
        onChanged();
        return this;
      }

      private int isNew_ ;
      /**
       * <pre>
       *是否上个月最新的段位 1:是 0:不是
       * </pre>
       *
       * <code>uint32 isNew = 2;</code>
       */
      public int getIsNew() {
        return isNew_;
      }
      /**
       * <pre>
       *是否上个月最新的段位 1:是 0:不是
       * </pre>
       *
       * <code>uint32 isNew = 2;</code>
       */
      public Builder setIsNew(int value) {
        
        isNew_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *是否上个月最新的段位 1:是 0:不是
       * </pre>
       *
       * <code>uint32 isNew = 2;</code>
       */
      public Builder clearIsNew() {
        
        isNew_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.MapField<
          Long, Integer> uidLevel_;
      private com.google.protobuf.MapField<Long, Integer>
      internalGetUidLevel() {
        if (uidLevel_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              UidLevelDefaultEntryHolder.defaultEntry);
        }
        return uidLevel_;
      }
      private com.google.protobuf.MapField<Long, Integer>
      internalGetMutableUidLevel() {
        onChanged();;
        if (uidLevel_ == null) {
          uidLevel_ = com.google.protobuf.MapField.newMapField(
              UidLevelDefaultEntryHolder.defaultEntry);
        }
        if (!uidLevel_.isMutable()) {
          uidLevel_ = uidLevel_.copy();
        }
        return uidLevel_;
      }

      public int getUidLevelCount() {
        return internalGetUidLevel().getMap().size();
      }
      /**
       * <code>map&lt;int64, int32&gt; uidLevel = 3;</code>
       */

      public boolean containsUidLevel(
          long key) {
        
        return internalGetUidLevel().getMap().containsKey(key);
      }
      /**
       * Use {@link #getUidLevelMap()} instead.
       */
      @Deprecated
      public java.util.Map<Long, Integer> getUidLevel() {
        return getUidLevelMap();
      }
      /**
       * <code>map&lt;int64, int32&gt; uidLevel = 3;</code>
       */

      public java.util.Map<Long, Integer> getUidLevelMap() {
        return internalGetUidLevel().getMap();
      }
      /**
       * <code>map&lt;int64, int32&gt; uidLevel = 3;</code>
       */

      public int getUidLevelOrDefault(
          long key,
          int defaultValue) {
        
        java.util.Map<Long, Integer> map =
            internalGetUidLevel().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;int64, int32&gt; uidLevel = 3;</code>
       */

      public int getUidLevelOrThrow(
          long key) {
        
        java.util.Map<Long, Integer> map =
            internalGetUidLevel().getMap();
        if (!map.containsKey(key)) {
          throw new IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearUidLevel() {
        internalGetMutableUidLevel().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;int64, int32&gt; uidLevel = 3;</code>
       */

      public Builder removeUidLevel(
          long key) {
        
        internalGetMutableUidLevel().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @Deprecated
      public java.util.Map<Long, Integer>
      getMutableUidLevel() {
        return internalGetMutableUidLevel().getMutableMap();
      }
      /**
       * <code>map&lt;int64, int32&gt; uidLevel = 3;</code>
       */
      public Builder putUidLevel(
          long key,
          int value) {
        
        
        internalGetMutableUidLevel().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;int64, int32&gt; uidLevel = 3;</code>
       */

      public Builder putAllUidLevel(
          java.util.Map<Long, Integer> values) {
        internalGetMutableUidLevel().getMutableMap()
            .putAll(values);
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.api.BatchQueryGuildRankResp64)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.api.BatchQueryGuildRankResp64)
    private static final BatchQueryGuildRankResp64 DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new BatchQueryGuildRankResp64();
    }

    public static BatchQueryGuildRankResp64 getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<BatchQueryGuildRankResp64>
        PARSER = new com.google.protobuf.AbstractParser<BatchQueryGuildRankResp64>() {
      @Override
      public BatchQueryGuildRankResp64 parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BatchQueryGuildRankResp64(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BatchQueryGuildRankResp64> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<BatchQueryGuildRankResp64> getParserForType() {
      return PARSER;
    }

    @Override
    public BatchQueryGuildRankResp64 getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankReq64_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankReq64_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankResp64_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankResp64_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankResp64_UidLevelEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankResp64_UidLevelEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankReq64_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankReq64_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankResp64_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankResp64_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankResp64_UidLevelEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankResp64_UidLevelEntry_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    String[] descriptorData = {
      "\n\022GuildRankI64.proto\022\033com.yy.yyzone.guil" +
      "drank.api\032\turi.proto\".\n\032BatchQueryCurren" +
      "tRankReq64\022\013\n\003uid\030\001 \003(\003:\003\310>\001\"\275\001\n\033BatchQu" +
      "eryCurrentRankResp64\022\016\n\006result\030\001 \001(\r\022X\n\010" +
      "uidLevel\030\002 \003(\0132F.com.yy.yyzone.guildrank" +
      ".api.BatchQueryCurrentRankResp64.UidLeve" +
      "lEntry\032/\n\rUidLevelEntry\022\013\n\003key\030\001 \001(\003\022\r\n\005" +
      "value\030\002 \001(\005:\0028\001:\003\310>\001\",\n\030BatchQueryGuildR" +
      "ankReq64\022\013\n\003uid\030\001 \003(\003:\003\310>\001\"\310\001\n\031BatchQuer" +
      "yGuildRankResp64\022\016\n\006result\030\001 \001(\r\022\r\n\005isNe" +
      "w\030\002 \001(\r\022V\n\010uidLevel\030\003 \003(\0132D.com.yy.yyzon" +
      "e.guildrank.api.BatchQueryGuildRankResp6" +
      "4.UidLevelEntry\032/\n\rUidLevelEntry\022\013\n\003key\030" +
      "\001 \001(\003\022\r\n\005value\030\002 \001(\005:\0028\001:\003\310>\0012\254\002\n\020Guildr" +
      "ankService\022\215\001\n\030batchQueryCurrentRankNew\022" +
      "7.com.yy.yyzone.guildrank.api.BatchQuery" +
      "CurrentRankReq64\0328.com.yy.yyzone.guildra" +
      "nk.api.BatchQueryCurrentRankResp64\022\207\001\n\026b" +
      "atchQueryGuildRankNew\0225.com.yy.yyzone.gu" +
      "ildrank.api.BatchQueryGuildRankReq64\0326.c" +
      "om.yy.yyzone.guildrank.api.BatchQueryGui" +
      "ldRankResp64b\006proto3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          yyp.Uri.getDescriptor(),
        }, assigner);
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankReq64_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankReq64_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankReq64_descriptor,
        new String[] { "Uid", });
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankResp64_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankResp64_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankResp64_descriptor,
        new String[] { "Result", "UidLevel", });
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankResp64_UidLevelEntry_descriptor =
      internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankResp64_descriptor.getNestedTypes().get(0);
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankResp64_UidLevelEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_BatchQueryCurrentRankResp64_UidLevelEntry_descriptor,
        new String[] { "Key", "Value", });
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankReq64_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankReq64_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankReq64_descriptor,
        new String[] { "Uid", });
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankResp64_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankResp64_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankResp64_descriptor,
        new String[] { "Result", "IsNew", "UidLevel", });
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankResp64_UidLevelEntry_descriptor =
      internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankResp64_descriptor.getNestedTypes().get(0);
    internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankResp64_UidLevelEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_BatchQueryGuildRankResp64_UidLevelEntry_descriptor,
        new String[] { "Key", "Value", });
    com.google.protobuf.ExtensionRegistry registry =
        com.google.protobuf.ExtensionRegistry.newInstance();
    registry.add(yyp.Uri.uri);
    com.google.protobuf.Descriptors.FileDescriptor
        .internalUpdateFileDescriptor(descriptor, registry);
    yyp.Uri.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
