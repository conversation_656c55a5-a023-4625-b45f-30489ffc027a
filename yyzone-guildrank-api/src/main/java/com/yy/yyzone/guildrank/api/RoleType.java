// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: roleType.proto

package com.yy.yyzone.guildrank.api;

public final class RoleType {
  private RoleType() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface DetailOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.api.Detail)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated int64 channel = 1;</code>
     */
    java.util.List<java.lang.Long> getChannelList();
    /**
     * <code>repeated int64 channel = 1;</code>
     */
    int getChannelCount();
    /**
     * <code>repeated int64 channel = 1;</code>
     */
    long getChannel(int index);
  }
  /**
   * Protobuf type {@code com.yy.yyzone.guildrank.api.Detail}
   */
  public  static final class Detail extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.api.Detail)
      DetailOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Detail.newBuilder() to construct.
    private Detail(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Detail() {
      channel_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Detail(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                channel_ = new java.util.ArrayList<java.lang.Long>();
                mutable_bitField0_ |= 0x00000001;
              }
              channel_.add(input.readInt64());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001) && input.getBytesUntilLimit() > 0) {
                channel_ = new java.util.ArrayList<java.lang.Long>();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                channel_.add(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          channel_ = java.util.Collections.unmodifiableList(channel_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yy.yyzone.guildrank.api.RoleType.internal_static_com_yy_yyzone_guildrank_api_Detail_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yy.yyzone.guildrank.api.RoleType.internal_static_com_yy_yyzone_guildrank_api_Detail_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yy.yyzone.guildrank.api.RoleType.Detail.class, com.yy.yyzone.guildrank.api.RoleType.Detail.Builder.class);
    }

    public static final int CHANNEL_FIELD_NUMBER = 1;
    private java.util.List<java.lang.Long> channel_;
    /**
     * <code>repeated int64 channel = 1;</code>
     */
    public java.util.List<java.lang.Long>
        getChannelList() {
      return channel_;
    }
    /**
     * <code>repeated int64 channel = 1;</code>
     */
    public int getChannelCount() {
      return channel_.size();
    }
    /**
     * <code>repeated int64 channel = 1;</code>
     */
    public long getChannel(int index) {
      return channel_.get(index);
    }
    private int channelMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (getChannelList().size() > 0) {
        output.writeUInt32NoTag(10);
        output.writeUInt32NoTag(channelMemoizedSerializedSize);
      }
      for (int i = 0; i < channel_.size(); i++) {
        output.writeInt64NoTag(channel_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < channel_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(channel_.get(i));
        }
        size += dataSize;
        if (!getChannelList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        channelMemoizedSerializedSize = dataSize;
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yy.yyzone.guildrank.api.RoleType.Detail)) {
        return super.equals(obj);
      }
      com.yy.yyzone.guildrank.api.RoleType.Detail other = (com.yy.yyzone.guildrank.api.RoleType.Detail) obj;

      boolean result = true;
      result = result && getChannelList()
          .equals(other.getChannelList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getChannelCount() > 0) {
        hash = (37 * hash) + CHANNEL_FIELD_NUMBER;
        hash = (53 * hash) + getChannelList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yy.yyzone.guildrank.api.RoleType.Detail parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.yyzone.guildrank.api.RoleType.Detail parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.yyzone.guildrank.api.RoleType.Detail parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.yyzone.guildrank.api.RoleType.Detail parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.yyzone.guildrank.api.RoleType.Detail parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.yyzone.guildrank.api.RoleType.Detail parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.yyzone.guildrank.api.RoleType.Detail parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yy.yyzone.guildrank.api.RoleType.Detail parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yy.yyzone.guildrank.api.RoleType.Detail parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yy.yyzone.guildrank.api.RoleType.Detail parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yy.yyzone.guildrank.api.RoleType.Detail parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yy.yyzone.guildrank.api.RoleType.Detail parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yy.yyzone.guildrank.api.RoleType.Detail prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.yyzone.guildrank.api.Detail}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.api.Detail)
        com.yy.yyzone.guildrank.api.RoleType.DetailOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yy.yyzone.guildrank.api.RoleType.internal_static_com_yy_yyzone_guildrank_api_Detail_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yy.yyzone.guildrank.api.RoleType.internal_static_com_yy_yyzone_guildrank_api_Detail_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yy.yyzone.guildrank.api.RoleType.Detail.class, com.yy.yyzone.guildrank.api.RoleType.Detail.Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.api.RoleType.Detail.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        channel_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yy.yyzone.guildrank.api.RoleType.internal_static_com_yy_yyzone_guildrank_api_Detail_descriptor;
      }

      @java.lang.Override
      public com.yy.yyzone.guildrank.api.RoleType.Detail getDefaultInstanceForType() {
        return com.yy.yyzone.guildrank.api.RoleType.Detail.getDefaultInstance();
      }

      @java.lang.Override
      public com.yy.yyzone.guildrank.api.RoleType.Detail build() {
        com.yy.yyzone.guildrank.api.RoleType.Detail result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yy.yyzone.guildrank.api.RoleType.Detail buildPartial() {
        com.yy.yyzone.guildrank.api.RoleType.Detail result = new com.yy.yyzone.guildrank.api.RoleType.Detail(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) == 0x00000001)) {
          channel_ = java.util.Collections.unmodifiableList(channel_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.channel_ = channel_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yy.yyzone.guildrank.api.RoleType.Detail) {
          return mergeFrom((com.yy.yyzone.guildrank.api.RoleType.Detail)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yy.yyzone.guildrank.api.RoleType.Detail other) {
        if (other == com.yy.yyzone.guildrank.api.RoleType.Detail.getDefaultInstance()) return this;
        if (!other.channel_.isEmpty()) {
          if (channel_.isEmpty()) {
            channel_ = other.channel_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureChannelIsMutable();
            channel_.addAll(other.channel_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yy.yyzone.guildrank.api.RoleType.Detail parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yy.yyzone.guildrank.api.RoleType.Detail) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<java.lang.Long> channel_ = java.util.Collections.emptyList();
      private void ensureChannelIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          channel_ = new java.util.ArrayList<java.lang.Long>(channel_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated int64 channel = 1;</code>
       */
      public java.util.List<java.lang.Long>
          getChannelList() {
        return java.util.Collections.unmodifiableList(channel_);
      }
      /**
       * <code>repeated int64 channel = 1;</code>
       */
      public int getChannelCount() {
        return channel_.size();
      }
      /**
       * <code>repeated int64 channel = 1;</code>
       */
      public long getChannel(int index) {
        return channel_.get(index);
      }
      /**
       * <code>repeated int64 channel = 1;</code>
       */
      public Builder setChannel(
          int index, long value) {
        ensureChannelIsMutable();
        channel_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 channel = 1;</code>
       */
      public Builder addChannel(long value) {
        ensureChannelIsMutable();
        channel_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 channel = 1;</code>
       */
      public Builder addAllChannel(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureChannelIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, channel_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 channel = 1;</code>
       */
      public Builder clearChannel() {
        channel_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.api.Detail)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.api.Detail)
    private static final com.yy.yyzone.guildrank.api.RoleType.Detail DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yy.yyzone.guildrank.api.RoleType.Detail();
    }

    public static com.yy.yyzone.guildrank.api.RoleType.Detail getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Detail>
        PARSER = new com.google.protobuf.AbstractParser<Detail>() {
      @java.lang.Override
      public Detail parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Detail(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Detail> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Detail> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yy.yyzone.guildrank.api.RoleType.Detail getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GetRoleTypeReqOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.api.GetRoleTypeReq)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int64 uid = 1;</code>
     */
    long getUid();

    /**
     * <pre>
     *type=1时查询后设置，type=2只查询
     * </pre>
     *
     * <code>uint32 type = 2;</code>
     */
    int getType();

    /**
     * <code>map&lt;string, string&gt; extends = 3;</code>
     */
    int getExtendsCount();
    /**
     * <code>map&lt;string, string&gt; extends = 3;</code>
     */
    boolean containsExtends(
        java.lang.String key);
    /**
     * Use {@link #getExtendsMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.String, java.lang.String>
    getExtends();
    /**
     * <code>map&lt;string, string&gt; extends = 3;</code>
     */
    java.util.Map<java.lang.String, java.lang.String>
    getExtendsMap();
    /**
     * <code>map&lt;string, string&gt; extends = 3;</code>
     */

    java.lang.String getExtendsOrDefault(
        java.lang.String key,
        java.lang.String defaultValue);
    /**
     * <code>map&lt;string, string&gt; extends = 3;</code>
     */

    java.lang.String getExtendsOrThrow(
        java.lang.String key);
  }
  /**
   * Protobuf type {@code com.yy.yyzone.guildrank.api.GetRoleTypeReq}
   */
  public  static final class GetRoleTypeReq extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.api.GetRoleTypeReq)
      GetRoleTypeReqOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetRoleTypeReq.newBuilder() to construct.
    private GetRoleTypeReq(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetRoleTypeReq() {
      uid_ = 0L;
      type_ = 0;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GetRoleTypeReq(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              uid_ = input.readInt64();
              break;
            }
            case 16: {

              type_ = input.readUInt32();
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
                extends_ = com.google.protobuf.MapField.newMapField(
                    ExtendsDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000004;
              }
              com.google.protobuf.MapEntry<java.lang.String, java.lang.String>
              extends__ = input.readMessage(
                  ExtendsDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              extends_.getMutableMap().put(
                  extends__.getKey(), extends__.getValue());
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yy.yyzone.guildrank.api.RoleType.internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeReq_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 3:
          return internalGetExtends();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yy.yyzone.guildrank.api.RoleType.internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq.class, com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq.Builder.class);
    }

    private int bitField0_;
    public static final int UID_FIELD_NUMBER = 1;
    private long uid_;
    /**
     * <code>int64 uid = 1;</code>
     */
    public long getUid() {
      return uid_;
    }

    public static final int TYPE_FIELD_NUMBER = 2;
    private int type_;
    /**
     * <pre>
     *type=1时查询后设置，type=2只查询
     * </pre>
     *
     * <code>uint32 type = 2;</code>
     */
    public int getType() {
      return type_;
    }

    public static final int EXTENDS_FIELD_NUMBER = 3;
    private static final class ExtendsDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.String, java.lang.String> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.String, java.lang.String>newDefaultInstance(
                  com.yy.yyzone.guildrank.api.RoleType.internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeReq_ExtendsEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "",
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "");
    }
    private com.google.protobuf.MapField<
        java.lang.String, java.lang.String> extends_;
    private com.google.protobuf.MapField<java.lang.String, java.lang.String>
    internalGetExtends() {
      if (extends_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ExtendsDefaultEntryHolder.defaultEntry);
      }
      return extends_;
    }

    public int getExtendsCount() {
      return internalGetExtends().getMap().size();
    }
    /**
     * <code>map&lt;string, string&gt; extends = 3;</code>
     */

    public boolean containsExtends(
        java.lang.String key) {
      if (key == null) { throw new java.lang.NullPointerException(); }
      return internalGetExtends().getMap().containsKey(key);
    }
    /**
     * Use {@link #getExtendsMap()} instead.
     */
    @java.lang.Deprecated
    public java.util.Map<java.lang.String, java.lang.String> getExtends() {
      return getExtendsMap();
    }
    /**
     * <code>map&lt;string, string&gt; extends = 3;</code>
     */

    public java.util.Map<java.lang.String, java.lang.String> getExtendsMap() {
      return internalGetExtends().getMap();
    }
    /**
     * <code>map&lt;string, string&gt; extends = 3;</code>
     */

    public java.lang.String getExtendsOrDefault(
        java.lang.String key,
        java.lang.String defaultValue) {
      if (key == null) { throw new java.lang.NullPointerException(); }
      java.util.Map<java.lang.String, java.lang.String> map =
          internalGetExtends().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;string, string&gt; extends = 3;</code>
     */

    public java.lang.String getExtendsOrThrow(
        java.lang.String key) {
      if (key == null) { throw new java.lang.NullPointerException(); }
      java.util.Map<java.lang.String, java.lang.String> map =
          internalGetExtends().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (uid_ != 0L) {
        output.writeInt64(1, uid_);
      }
      if (type_ != 0) {
        output.writeUInt32(2, type_);
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeStringMapTo(
          output,
          internalGetExtends(),
          ExtendsDefaultEntryHolder.defaultEntry,
          3);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (uid_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, uid_);
      }
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, type_);
      }
      for (java.util.Map.Entry<java.lang.String, java.lang.String> entry
           : internalGetExtends().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.String, java.lang.String>
        extends__ = ExtendsDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(3, extends__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq)) {
        return super.equals(obj);
      }
      com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq other = (com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq) obj;

      boolean result = true;
      result = result && (getUid()
          == other.getUid());
      result = result && (getType()
          == other.getType());
      result = result && internalGetExtends().equals(
          other.internalGetExtends());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + UID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUid());
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      if (!internalGetExtends().getMap().isEmpty()) {
        hash = (37 * hash) + EXTENDS_FIELD_NUMBER;
        hash = (53 * hash) + internalGetExtends().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.yyzone.guildrank.api.GetRoleTypeReq}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.api.GetRoleTypeReq)
        com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReqOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yy.yyzone.guildrank.api.RoleType.internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeReq_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 3:
            return internalGetExtends();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 3:
            return internalGetMutableExtends();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yy.yyzone.guildrank.api.RoleType.internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeReq_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq.class, com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq.Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        uid_ = 0L;

        type_ = 0;

        internalGetMutableExtends().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yy.yyzone.guildrank.api.RoleType.internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeReq_descriptor;
      }

      @java.lang.Override
      public com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq getDefaultInstanceForType() {
        return com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq.getDefaultInstance();
      }

      @java.lang.Override
      public com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq build() {
        com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq buildPartial() {
        com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq result = new com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.uid_ = uid_;
        result.type_ = type_;
        result.extends_ = internalGetExtends();
        result.extends_.makeImmutable();
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq) {
          return mergeFrom((com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq other) {
        if (other == com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq.getDefaultInstance()) return this;
        if (other.getUid() != 0L) {
          setUid(other.getUid());
        }
        if (other.getType() != 0) {
          setType(other.getType());
        }
        internalGetMutableExtends().mergeFrom(
            other.internalGetExtends());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long uid_ ;
      /**
       * <code>int64 uid = 1;</code>
       */
      public long getUid() {
        return uid_;
      }
      /**
       * <code>int64 uid = 1;</code>
       */
      public Builder setUid(long value) {
        
        uid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 uid = 1;</code>
       */
      public Builder clearUid() {
        
        uid_ = 0L;
        onChanged();
        return this;
      }

      private int type_ ;
      /**
       * <pre>
       *type=1时查询后设置，type=2只查询
       * </pre>
       *
       * <code>uint32 type = 2;</code>
       */
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       *type=1时查询后设置，type=2只查询
       * </pre>
       *
       * <code>uint32 type = 2;</code>
       */
      public Builder setType(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *type=1时查询后设置，type=2只查询
       * </pre>
       *
       * <code>uint32 type = 2;</code>
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.MapField<
          java.lang.String, java.lang.String> extends_;
      private com.google.protobuf.MapField<java.lang.String, java.lang.String>
      internalGetExtends() {
        if (extends_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ExtendsDefaultEntryHolder.defaultEntry);
        }
        return extends_;
      }
      private com.google.protobuf.MapField<java.lang.String, java.lang.String>
      internalGetMutableExtends() {
        onChanged();;
        if (extends_ == null) {
          extends_ = com.google.protobuf.MapField.newMapField(
              ExtendsDefaultEntryHolder.defaultEntry);
        }
        if (!extends_.isMutable()) {
          extends_ = extends_.copy();
        }
        return extends_;
      }

      public int getExtendsCount() {
        return internalGetExtends().getMap().size();
      }
      /**
       * <code>map&lt;string, string&gt; extends = 3;</code>
       */

      public boolean containsExtends(
          java.lang.String key) {
        if (key == null) { throw new java.lang.NullPointerException(); }
        return internalGetExtends().getMap().containsKey(key);
      }
      /**
       * Use {@link #getExtendsMap()} instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.String, java.lang.String> getExtends() {
        return getExtendsMap();
      }
      /**
       * <code>map&lt;string, string&gt; extends = 3;</code>
       */

      public java.util.Map<java.lang.String, java.lang.String> getExtendsMap() {
        return internalGetExtends().getMap();
      }
      /**
       * <code>map&lt;string, string&gt; extends = 3;</code>
       */

      public java.lang.String getExtendsOrDefault(
          java.lang.String key,
          java.lang.String defaultValue) {
        if (key == null) { throw new java.lang.NullPointerException(); }
        java.util.Map<java.lang.String, java.lang.String> map =
            internalGetExtends().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;string, string&gt; extends = 3;</code>
       */

      public java.lang.String getExtendsOrThrow(
          java.lang.String key) {
        if (key == null) { throw new java.lang.NullPointerException(); }
        java.util.Map<java.lang.String, java.lang.String> map =
            internalGetExtends().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearExtends() {
        internalGetMutableExtends().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;string, string&gt; extends = 3;</code>
       */

      public Builder removeExtends(
          java.lang.String key) {
        if (key == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableExtends().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.String, java.lang.String>
      getMutableExtends() {
        return internalGetMutableExtends().getMutableMap();
      }
      /**
       * <code>map&lt;string, string&gt; extends = 3;</code>
       */
      public Builder putExtends(
          java.lang.String key,
          java.lang.String value) {
        if (key == null) { throw new java.lang.NullPointerException(); }
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableExtends().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;string, string&gt; extends = 3;</code>
       */

      public Builder putAllExtends(
          java.util.Map<java.lang.String, java.lang.String> values) {
        internalGetMutableExtends().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.api.GetRoleTypeReq)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.api.GetRoleTypeReq)
    private static final com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq();
    }

    public static com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<GetRoleTypeReq>
        PARSER = new com.google.protobuf.AbstractParser<GetRoleTypeReq>() {
      @java.lang.Override
      public GetRoleTypeReq parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetRoleTypeReq(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GetRoleTypeReq> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetRoleTypeReq> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeReq getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GetRoleTypeRespOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.yyzone.guildrank.api.GetRoleTypeResp)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 result = 1;</code>
     */
    int getResult();

    /**
     * <pre>
     **
     * R_ANCHOR         = 1; //主播（新）
     * R_CHANNEL        = 2; //公会（新）
     * R_NORUSER        = 3; //用户（新）
     * R_ANCHOR_CHANNEL = 4; //主播公会（过渡期角色）
     * R_SINGER         = 5; //歌手或常驻艺人（旧）
     * </pre>
     *
     * <code>uint32 role = 2;</code>
     */
    int getRole();

    /**
     * <pre>
     *key=0:uid下所有频道，key=1:旗下所有音乐频道，key=2:旗下所有普通频道, key=3:旗下所有直播间
     * </pre>
     *
     * <code>map&lt;int64, .com.yy.yyzone.guildrank.api.Detail&gt; detail = 3;</code>
     */
    int getDetailCount();
    /**
     * <pre>
     *key=0:uid下所有频道，key=1:旗下所有音乐频道，key=2:旗下所有普通频道, key=3:旗下所有直播间
     * </pre>
     *
     * <code>map&lt;int64, .com.yy.yyzone.guildrank.api.Detail&gt; detail = 3;</code>
     */
    boolean containsDetail(
        long key);
    /**
     * Use {@link #getDetailMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Long, com.yy.yyzone.guildrank.api.RoleType.Detail>
    getDetail();
    /**
     * <pre>
     *key=0:uid下所有频道，key=1:旗下所有音乐频道，key=2:旗下所有普通频道, key=3:旗下所有直播间
     * </pre>
     *
     * <code>map&lt;int64, .com.yy.yyzone.guildrank.api.Detail&gt; detail = 3;</code>
     */
    java.util.Map<java.lang.Long, com.yy.yyzone.guildrank.api.RoleType.Detail>
    getDetailMap();
    /**
     * <pre>
     *key=0:uid下所有频道，key=1:旗下所有音乐频道，key=2:旗下所有普通频道, key=3:旗下所有直播间
     * </pre>
     *
     * <code>map&lt;int64, .com.yy.yyzone.guildrank.api.Detail&gt; detail = 3;</code>
     */

    com.yy.yyzone.guildrank.api.RoleType.Detail getDetailOrDefault(
        long key,
        com.yy.yyzone.guildrank.api.RoleType.Detail defaultValue);
    /**
     * <pre>
     *key=0:uid下所有频道，key=1:旗下所有音乐频道，key=2:旗下所有普通频道, key=3:旗下所有直播间
     * </pre>
     *
     * <code>map&lt;int64, .com.yy.yyzone.guildrank.api.Detail&gt; detail = 3;</code>
     */

    com.yy.yyzone.guildrank.api.RoleType.Detail getDetailOrThrow(
        long key);

    /**
     * <code>map&lt;string, string&gt; extends = 4;</code>
     */
    int getExtendsCount();
    /**
     * <code>map&lt;string, string&gt; extends = 4;</code>
     */
    boolean containsExtends(
        java.lang.String key);
    /**
     * Use {@link #getExtendsMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.String, java.lang.String>
    getExtends();
    /**
     * <code>map&lt;string, string&gt; extends = 4;</code>
     */
    java.util.Map<java.lang.String, java.lang.String>
    getExtendsMap();
    /**
     * <code>map&lt;string, string&gt; extends = 4;</code>
     */

    java.lang.String getExtendsOrDefault(
        java.lang.String key,
        java.lang.String defaultValue);
    /**
     * <code>map&lt;string, string&gt; extends = 4;</code>
     */

    java.lang.String getExtendsOrThrow(
        java.lang.String key);
  }
  /**
   * Protobuf type {@code com.yy.yyzone.guildrank.api.GetRoleTypeResp}
   */
  public  static final class GetRoleTypeResp extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.yyzone.guildrank.api.GetRoleTypeResp)
      GetRoleTypeRespOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetRoleTypeResp.newBuilder() to construct.
    private GetRoleTypeResp(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetRoleTypeResp() {
      result_ = 0;
      role_ = 0;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GetRoleTypeResp(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              result_ = input.readUInt32();
              break;
            }
            case 16: {

              role_ = input.readUInt32();
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
                detail_ = com.google.protobuf.MapField.newMapField(
                    DetailDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000004;
              }
              com.google.protobuf.MapEntry<java.lang.Long, com.yy.yyzone.guildrank.api.RoleType.Detail>
              detail__ = input.readMessage(
                  DetailDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              detail_.getMutableMap().put(
                  detail__.getKey(), detail__.getValue());
              break;
            }
            case 34: {
              if (!((mutable_bitField0_ & 0x00000008) == 0x00000008)) {
                extends_ = com.google.protobuf.MapField.newMapField(
                    ExtendsDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000008;
              }
              com.google.protobuf.MapEntry<java.lang.String, java.lang.String>
              extends__ = input.readMessage(
                  ExtendsDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              extends_.getMutableMap().put(
                  extends__.getKey(), extends__.getValue());
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yy.yyzone.guildrank.api.RoleType.internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeResp_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 3:
          return internalGetDetail();
        case 4:
          return internalGetExtends();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yy.yyzone.guildrank.api.RoleType.internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp.class, com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp.Builder.class);
    }

    private int bitField0_;
    public static final int RESULT_FIELD_NUMBER = 1;
    private int result_;
    /**
     * <code>uint32 result = 1;</code>
     */
    public int getResult() {
      return result_;
    }

    public static final int ROLE_FIELD_NUMBER = 2;
    private int role_;
    /**
     * <pre>
     **
     * R_ANCHOR         = 1; //主播（新）
     * R_CHANNEL        = 2; //公会（新）
     * R_NORUSER        = 3; //用户（新）
     * R_ANCHOR_CHANNEL = 4; //主播公会（过渡期角色）
     * R_SINGER         = 5; //歌手或常驻艺人（旧）
     * </pre>
     *
     * <code>uint32 role = 2;</code>
     */
    public int getRole() {
      return role_;
    }

    public static final int DETAIL_FIELD_NUMBER = 3;
    private static final class DetailDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Long, com.yy.yyzone.guildrank.api.RoleType.Detail> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Long, com.yy.yyzone.guildrank.api.RoleType.Detail>newDefaultInstance(
                  com.yy.yyzone.guildrank.api.RoleType.internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeResp_DetailEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT64,
                  0L,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.yy.yyzone.guildrank.api.RoleType.Detail.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Long, com.yy.yyzone.guildrank.api.RoleType.Detail> detail_;
    private com.google.protobuf.MapField<java.lang.Long, com.yy.yyzone.guildrank.api.RoleType.Detail>
    internalGetDetail() {
      if (detail_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            DetailDefaultEntryHolder.defaultEntry);
      }
      return detail_;
    }

    public int getDetailCount() {
      return internalGetDetail().getMap().size();
    }
    /**
     * <pre>
     *key=0:uid下所有频道，key=1:旗下所有音乐频道，key=2:旗下所有普通频道, key=3:旗下所有直播间
     * </pre>
     *
     * <code>map&lt;int64, .com.yy.yyzone.guildrank.api.Detail&gt; detail = 3;</code>
     */

    public boolean containsDetail(
        long key) {
      
      return internalGetDetail().getMap().containsKey(key);
    }
    /**
     * Use {@link #getDetailMap()} instead.
     */
    @java.lang.Deprecated
    public java.util.Map<java.lang.Long, com.yy.yyzone.guildrank.api.RoleType.Detail> getDetail() {
      return getDetailMap();
    }
    /**
     * <pre>
     *key=0:uid下所有频道，key=1:旗下所有音乐频道，key=2:旗下所有普通频道, key=3:旗下所有直播间
     * </pre>
     *
     * <code>map&lt;int64, .com.yy.yyzone.guildrank.api.Detail&gt; detail = 3;</code>
     */

    public java.util.Map<java.lang.Long, com.yy.yyzone.guildrank.api.RoleType.Detail> getDetailMap() {
      return internalGetDetail().getMap();
    }
    /**
     * <pre>
     *key=0:uid下所有频道，key=1:旗下所有音乐频道，key=2:旗下所有普通频道, key=3:旗下所有直播间
     * </pre>
     *
     * <code>map&lt;int64, .com.yy.yyzone.guildrank.api.Detail&gt; detail = 3;</code>
     */

    public com.yy.yyzone.guildrank.api.RoleType.Detail getDetailOrDefault(
        long key,
        com.yy.yyzone.guildrank.api.RoleType.Detail defaultValue) {
      
      java.util.Map<java.lang.Long, com.yy.yyzone.guildrank.api.RoleType.Detail> map =
          internalGetDetail().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     *key=0:uid下所有频道，key=1:旗下所有音乐频道，key=2:旗下所有普通频道, key=3:旗下所有直播间
     * </pre>
     *
     * <code>map&lt;int64, .com.yy.yyzone.guildrank.api.Detail&gt; detail = 3;</code>
     */

    public com.yy.yyzone.guildrank.api.RoleType.Detail getDetailOrThrow(
        long key) {
      
      java.util.Map<java.lang.Long, com.yy.yyzone.guildrank.api.RoleType.Detail> map =
          internalGetDetail().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int EXTENDS_FIELD_NUMBER = 4;
    private static final class ExtendsDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.String, java.lang.String> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.String, java.lang.String>newDefaultInstance(
                  com.yy.yyzone.guildrank.api.RoleType.internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeResp_ExtendsEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "",
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "");
    }
    private com.google.protobuf.MapField<
        java.lang.String, java.lang.String> extends_;
    private com.google.protobuf.MapField<java.lang.String, java.lang.String>
    internalGetExtends() {
      if (extends_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ExtendsDefaultEntryHolder.defaultEntry);
      }
      return extends_;
    }

    public int getExtendsCount() {
      return internalGetExtends().getMap().size();
    }
    /**
     * <code>map&lt;string, string&gt; extends = 4;</code>
     */

    public boolean containsExtends(
        java.lang.String key) {
      if (key == null) { throw new java.lang.NullPointerException(); }
      return internalGetExtends().getMap().containsKey(key);
    }
    /**
     * Use {@link #getExtendsMap()} instead.
     */
    @java.lang.Deprecated
    public java.util.Map<java.lang.String, java.lang.String> getExtends() {
      return getExtendsMap();
    }
    /**
     * <code>map&lt;string, string&gt; extends = 4;</code>
     */

    public java.util.Map<java.lang.String, java.lang.String> getExtendsMap() {
      return internalGetExtends().getMap();
    }
    /**
     * <code>map&lt;string, string&gt; extends = 4;</code>
     */

    public java.lang.String getExtendsOrDefault(
        java.lang.String key,
        java.lang.String defaultValue) {
      if (key == null) { throw new java.lang.NullPointerException(); }
      java.util.Map<java.lang.String, java.lang.String> map =
          internalGetExtends().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;string, string&gt; extends = 4;</code>
     */

    public java.lang.String getExtendsOrThrow(
        java.lang.String key) {
      if (key == null) { throw new java.lang.NullPointerException(); }
      java.util.Map<java.lang.String, java.lang.String> map =
          internalGetExtends().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (result_ != 0) {
        output.writeUInt32(1, result_);
      }
      if (role_ != 0) {
        output.writeUInt32(2, role_);
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeLongMapTo(
          output,
          internalGetDetail(),
          DetailDefaultEntryHolder.defaultEntry,
          3);
      com.google.protobuf.GeneratedMessageV3
        .serializeStringMapTo(
          output,
          internalGetExtends(),
          ExtendsDefaultEntryHolder.defaultEntry,
          4);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (result_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, result_);
      }
      if (role_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, role_);
      }
      for (java.util.Map.Entry<java.lang.Long, com.yy.yyzone.guildrank.api.RoleType.Detail> entry
           : internalGetDetail().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Long, com.yy.yyzone.guildrank.api.RoleType.Detail>
        detail__ = DetailDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(3, detail__);
      }
      for (java.util.Map.Entry<java.lang.String, java.lang.String> entry
           : internalGetExtends().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.String, java.lang.String>
        extends__ = ExtendsDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(4, extends__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp)) {
        return super.equals(obj);
      }
      com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp other = (com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp) obj;

      boolean result = true;
      result = result && (getResult()
          == other.getResult());
      result = result && (getRole()
          == other.getRole());
      result = result && internalGetDetail().equals(
          other.internalGetDetail());
      result = result && internalGetExtends().equals(
          other.internalGetExtends());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + getResult();
      hash = (37 * hash) + ROLE_FIELD_NUMBER;
      hash = (53 * hash) + getRole();
      if (!internalGetDetail().getMap().isEmpty()) {
        hash = (37 * hash) + DETAIL_FIELD_NUMBER;
        hash = (53 * hash) + internalGetDetail().hashCode();
      }
      if (!internalGetExtends().getMap().isEmpty()) {
        hash = (37 * hash) + EXTENDS_FIELD_NUMBER;
        hash = (53 * hash) + internalGetExtends().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.yyzone.guildrank.api.GetRoleTypeResp}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.yyzone.guildrank.api.GetRoleTypeResp)
        com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeRespOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yy.yyzone.guildrank.api.RoleType.internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeResp_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 3:
            return internalGetDetail();
          case 4:
            return internalGetExtends();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 3:
            return internalGetMutableDetail();
          case 4:
            return internalGetMutableExtends();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yy.yyzone.guildrank.api.RoleType.internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeResp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp.class, com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp.Builder.class);
      }

      // Construct using com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        result_ = 0;

        role_ = 0;

        internalGetMutableDetail().clear();
        internalGetMutableExtends().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yy.yyzone.guildrank.api.RoleType.internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeResp_descriptor;
      }

      @java.lang.Override
      public com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp getDefaultInstanceForType() {
        return com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp.getDefaultInstance();
      }

      @java.lang.Override
      public com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp build() {
        com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp buildPartial() {
        com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp result = new com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.result_ = result_;
        result.role_ = role_;
        result.detail_ = internalGetDetail();
        result.detail_.makeImmutable();
        result.extends_ = internalGetExtends();
        result.extends_.makeImmutable();
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp) {
          return mergeFrom((com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp other) {
        if (other == com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp.getDefaultInstance()) return this;
        if (other.getResult() != 0) {
          setResult(other.getResult());
        }
        if (other.getRole() != 0) {
          setRole(other.getRole());
        }
        internalGetMutableDetail().mergeFrom(
            other.internalGetDetail());
        internalGetMutableExtends().mergeFrom(
            other.internalGetExtends());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int result_ ;
      /**
       * <code>uint32 result = 1;</code>
       */
      public int getResult() {
        return result_;
      }
      /**
       * <code>uint32 result = 1;</code>
       */
      public Builder setResult(int value) {
        
        result_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 result = 1;</code>
       */
      public Builder clearResult() {
        
        result_ = 0;
        onChanged();
        return this;
      }

      private int role_ ;
      /**
       * <pre>
       **
       * R_ANCHOR         = 1; //主播（新）
       * R_CHANNEL        = 2; //公会（新）
       * R_NORUSER        = 3; //用户（新）
       * R_ANCHOR_CHANNEL = 4; //主播公会（过渡期角色）
       * R_SINGER         = 5; //歌手或常驻艺人（旧）
       * </pre>
       *
       * <code>uint32 role = 2;</code>
       */
      public int getRole() {
        return role_;
      }
      /**
       * <pre>
       **
       * R_ANCHOR         = 1; //主播（新）
       * R_CHANNEL        = 2; //公会（新）
       * R_NORUSER        = 3; //用户（新）
       * R_ANCHOR_CHANNEL = 4; //主播公会（过渡期角色）
       * R_SINGER         = 5; //歌手或常驻艺人（旧）
       * </pre>
       *
       * <code>uint32 role = 2;</code>
       */
      public Builder setRole(int value) {
        
        role_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       **
       * R_ANCHOR         = 1; //主播（新）
       * R_CHANNEL        = 2; //公会（新）
       * R_NORUSER        = 3; //用户（新）
       * R_ANCHOR_CHANNEL = 4; //主播公会（过渡期角色）
       * R_SINGER         = 5; //歌手或常驻艺人（旧）
       * </pre>
       *
       * <code>uint32 role = 2;</code>
       */
      public Builder clearRole() {
        
        role_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.MapField<
          java.lang.Long, com.yy.yyzone.guildrank.api.RoleType.Detail> detail_;
      private com.google.protobuf.MapField<java.lang.Long, com.yy.yyzone.guildrank.api.RoleType.Detail>
      internalGetDetail() {
        if (detail_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              DetailDefaultEntryHolder.defaultEntry);
        }
        return detail_;
      }
      private com.google.protobuf.MapField<java.lang.Long, com.yy.yyzone.guildrank.api.RoleType.Detail>
      internalGetMutableDetail() {
        onChanged();;
        if (detail_ == null) {
          detail_ = com.google.protobuf.MapField.newMapField(
              DetailDefaultEntryHolder.defaultEntry);
        }
        if (!detail_.isMutable()) {
          detail_ = detail_.copy();
        }
        return detail_;
      }

      public int getDetailCount() {
        return internalGetDetail().getMap().size();
      }
      /**
       * <pre>
       *key=0:uid下所有频道，key=1:旗下所有音乐频道，key=2:旗下所有普通频道, key=3:旗下所有直播间
       * </pre>
       *
       * <code>map&lt;int64, .com.yy.yyzone.guildrank.api.Detail&gt; detail = 3;</code>
       */

      public boolean containsDetail(
          long key) {
        
        return internalGetDetail().getMap().containsKey(key);
      }
      /**
       * Use {@link #getDetailMap()} instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Long, com.yy.yyzone.guildrank.api.RoleType.Detail> getDetail() {
        return getDetailMap();
      }
      /**
       * <pre>
       *key=0:uid下所有频道，key=1:旗下所有音乐频道，key=2:旗下所有普通频道, key=3:旗下所有直播间
       * </pre>
       *
       * <code>map&lt;int64, .com.yy.yyzone.guildrank.api.Detail&gt; detail = 3;</code>
       */

      public java.util.Map<java.lang.Long, com.yy.yyzone.guildrank.api.RoleType.Detail> getDetailMap() {
        return internalGetDetail().getMap();
      }
      /**
       * <pre>
       *key=0:uid下所有频道，key=1:旗下所有音乐频道，key=2:旗下所有普通频道, key=3:旗下所有直播间
       * </pre>
       *
       * <code>map&lt;int64, .com.yy.yyzone.guildrank.api.Detail&gt; detail = 3;</code>
       */

      public com.yy.yyzone.guildrank.api.RoleType.Detail getDetailOrDefault(
          long key,
          com.yy.yyzone.guildrank.api.RoleType.Detail defaultValue) {
        
        java.util.Map<java.lang.Long, com.yy.yyzone.guildrank.api.RoleType.Detail> map =
            internalGetDetail().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       *key=0:uid下所有频道，key=1:旗下所有音乐频道，key=2:旗下所有普通频道, key=3:旗下所有直播间
       * </pre>
       *
       * <code>map&lt;int64, .com.yy.yyzone.guildrank.api.Detail&gt; detail = 3;</code>
       */

      public com.yy.yyzone.guildrank.api.RoleType.Detail getDetailOrThrow(
          long key) {
        
        java.util.Map<java.lang.Long, com.yy.yyzone.guildrank.api.RoleType.Detail> map =
            internalGetDetail().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearDetail() {
        internalGetMutableDetail().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       *key=0:uid下所有频道，key=1:旗下所有音乐频道，key=2:旗下所有普通频道, key=3:旗下所有直播间
       * </pre>
       *
       * <code>map&lt;int64, .com.yy.yyzone.guildrank.api.Detail&gt; detail = 3;</code>
       */

      public Builder removeDetail(
          long key) {
        
        internalGetMutableDetail().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Long, com.yy.yyzone.guildrank.api.RoleType.Detail>
      getMutableDetail() {
        return internalGetMutableDetail().getMutableMap();
      }
      /**
       * <pre>
       *key=0:uid下所有频道，key=1:旗下所有音乐频道，key=2:旗下所有普通频道, key=3:旗下所有直播间
       * </pre>
       *
       * <code>map&lt;int64, .com.yy.yyzone.guildrank.api.Detail&gt; detail = 3;</code>
       */
      public Builder putDetail(
          long key,
          com.yy.yyzone.guildrank.api.RoleType.Detail value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableDetail().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       *key=0:uid下所有频道，key=1:旗下所有音乐频道，key=2:旗下所有普通频道, key=3:旗下所有直播间
       * </pre>
       *
       * <code>map&lt;int64, .com.yy.yyzone.guildrank.api.Detail&gt; detail = 3;</code>
       */

      public Builder putAllDetail(
          java.util.Map<java.lang.Long, com.yy.yyzone.guildrank.api.RoleType.Detail> values) {
        internalGetMutableDetail().getMutableMap()
            .putAll(values);
        return this;
      }

      private com.google.protobuf.MapField<
          java.lang.String, java.lang.String> extends_;
      private com.google.protobuf.MapField<java.lang.String, java.lang.String>
      internalGetExtends() {
        if (extends_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ExtendsDefaultEntryHolder.defaultEntry);
        }
        return extends_;
      }
      private com.google.protobuf.MapField<java.lang.String, java.lang.String>
      internalGetMutableExtends() {
        onChanged();;
        if (extends_ == null) {
          extends_ = com.google.protobuf.MapField.newMapField(
              ExtendsDefaultEntryHolder.defaultEntry);
        }
        if (!extends_.isMutable()) {
          extends_ = extends_.copy();
        }
        return extends_;
      }

      public int getExtendsCount() {
        return internalGetExtends().getMap().size();
      }
      /**
       * <code>map&lt;string, string&gt; extends = 4;</code>
       */

      public boolean containsExtends(
          java.lang.String key) {
        if (key == null) { throw new java.lang.NullPointerException(); }
        return internalGetExtends().getMap().containsKey(key);
      }
      /**
       * Use {@link #getExtendsMap()} instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.String, java.lang.String> getExtends() {
        return getExtendsMap();
      }
      /**
       * <code>map&lt;string, string&gt; extends = 4;</code>
       */

      public java.util.Map<java.lang.String, java.lang.String> getExtendsMap() {
        return internalGetExtends().getMap();
      }
      /**
       * <code>map&lt;string, string&gt; extends = 4;</code>
       */

      public java.lang.String getExtendsOrDefault(
          java.lang.String key,
          java.lang.String defaultValue) {
        if (key == null) { throw new java.lang.NullPointerException(); }
        java.util.Map<java.lang.String, java.lang.String> map =
            internalGetExtends().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;string, string&gt; extends = 4;</code>
       */

      public java.lang.String getExtendsOrThrow(
          java.lang.String key) {
        if (key == null) { throw new java.lang.NullPointerException(); }
        java.util.Map<java.lang.String, java.lang.String> map =
            internalGetExtends().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearExtends() {
        internalGetMutableExtends().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;string, string&gt; extends = 4;</code>
       */

      public Builder removeExtends(
          java.lang.String key) {
        if (key == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableExtends().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.String, java.lang.String>
      getMutableExtends() {
        return internalGetMutableExtends().getMutableMap();
      }
      /**
       * <code>map&lt;string, string&gt; extends = 4;</code>
       */
      public Builder putExtends(
          java.lang.String key,
          java.lang.String value) {
        if (key == null) { throw new java.lang.NullPointerException(); }
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableExtends().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;string, string&gt; extends = 4;</code>
       */

      public Builder putAllExtends(
          java.util.Map<java.lang.String, java.lang.String> values) {
        internalGetMutableExtends().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.yyzone.guildrank.api.GetRoleTypeResp)
    }

    // @@protoc_insertion_point(class_scope:com.yy.yyzone.guildrank.api.GetRoleTypeResp)
    private static final com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp();
    }

    public static com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<GetRoleTypeResp>
        PARSER = new com.google.protobuf.AbstractParser<GetRoleTypeResp>() {
      @java.lang.Override
      public GetRoleTypeResp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetRoleTypeResp(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GetRoleTypeResp> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetRoleTypeResp> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yy.yyzone.guildrank.api.RoleType.GetRoleTypeResp getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_Detail_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_Detail_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeReq_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeReq_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeReq_ExtendsEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeReq_ExtendsEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeResp_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeResp_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeResp_DetailEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeResp_DetailEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeResp_ExtendsEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeResp_ExtendsEntry_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\016roleType.proto\022\033com.yy.yyzone.guildran" +
      "k.api\032\turi.proto\"\031\n\006Detail\022\017\n\007channel\030\001 " +
      "\003(\003\"\253\001\n\016GetRoleTypeReq\022\013\n\003uid\030\001 \001(\003\022\014\n\004t" +
      "ype\030\002 \001(\r\022I\n\007extends\030\003 \003(\01328.com.yy.yyzo" +
      "ne.guildrank.api.GetRoleTypeReq.ExtendsE" +
      "ntry\032.\n\014ExtendsEntry\022\013\n\003key\030\001 \001(\t\022\r\n\005val" +
      "ue\030\002 \001(\t:\0028\001:\003\310>\001\"\316\002\n\017GetRoleTypeResp\022\016\n" +
      "\006result\030\001 \001(\r\022\014\n\004role\030\002 \001(\r\022H\n\006detail\030\003 " +
      "\003(\01328.com.yy.yyzone.guildrank.api.GetRol" +
      "eTypeResp.DetailEntry\022J\n\007extends\030\004 \003(\01329" +
      ".com.yy.yyzone.guildrank.api.GetRoleType" +
      "Resp.ExtendsEntry\032R\n\013DetailEntry\022\013\n\003key\030" +
      "\001 \001(\003\0222\n\005value\030\002 \001(\0132#.com.yy.yyzone.gui" +
      "ldrank.api.Detail:\0028\001\032.\n\014ExtendsEntry\022\013\n" +
      "\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001:\003\310>\0022w\n\013Ro" +
      "leService\022h\n\013getRoleType\022+.com.yy.yyzone" +
      ".guildrank.api.GetRoleTypeReq\032,.com.yy.y" +
      "yzone.guildrank.api.GetRoleTypeRespb\006pro" +
      "to3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          yyp.Uri.getDescriptor(),
        }, assigner);
    internal_static_com_yy_yyzone_guildrank_api_Detail_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yy_yyzone_guildrank_api_Detail_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_Detail_descriptor,
        new java.lang.String[] { "Channel", });
    internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeReq_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeReq_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeReq_descriptor,
        new java.lang.String[] { "Uid", "Type", "Extends", });
    internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeReq_ExtendsEntry_descriptor =
      internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeReq_descriptor.getNestedTypes().get(0);
    internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeReq_ExtendsEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeReq_ExtendsEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeResp_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeResp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeResp_descriptor,
        new java.lang.String[] { "Result", "Role", "Detail", "Extends", });
    internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeResp_DetailEntry_descriptor =
      internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeResp_descriptor.getNestedTypes().get(0);
    internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeResp_DetailEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeResp_DetailEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeResp_ExtendsEntry_descriptor =
      internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeResp_descriptor.getNestedTypes().get(1);
    internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeResp_ExtendsEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_yyzone_guildrank_api_GetRoleTypeResp_ExtendsEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    com.google.protobuf.ExtensionRegistry registry =
        com.google.protobuf.ExtensionRegistry.newInstance();
    registry.add(yyp.Uri.uri);
    com.google.protobuf.Descriptors.FileDescriptor
        .internalUpdateFileDescriptor(descriptor, registry);
    yyp.Uri.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
