syntax = "proto3";

package com.yy.yyzone.guildrank.api;
import "uri.proto";

message QueryRankChangeReq {
  string appData = 1; //调用s2sName
  int32 type = 2; //类型 0-海度数据 1-海度数据+段位变更计算时当月升级星级默认1星
  string month = 3; //查询月份 yyyy-MM
  option (yyp.uri) = 1;
}
message QueryRankChangeRes {
  int32 result = 1; //返回结果 0-成功 1-当月段位未更新
  map<int64, int32> rankMap = 2; //段位
  map<int64, int32> changeMap = 3; //当月段位比上月变更值
  option (yyp.uri) = 1;
}

service RankChangeService {
  rpc batchQueryCurrentRankNew(QueryRankChangeReq) returns (QueryRankChangeRes);
}
