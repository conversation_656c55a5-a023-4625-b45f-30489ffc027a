syntax = "proto3";

package com.yy.yyzone.guildrank.api;
import "uri.proto";

message BatchQueryCurrentRankReq64 {
	repeated int64 uid = 1;
	option (yyp.uri) = 1;
}
message BatchQueryCurrentRankResp64 {
	uint32 result = 1;
	map<int64, int32> uidLevel = 2;
	option (yyp.uri) = 1;
}

message BatchQueryGuildRankReq64 {
	repeated int64 uid = 1;
	option (yyp.uri) = 1;
}
message BatchQueryGuildRankResp64 {
	uint32 result = 1;
	uint32 isNew = 2; //是否上个月最新的段位 1:是 0:不是
	map<int64, int32> uidLevel = 3;
	option (yyp.uri) = 1;
}

service GuildrankService {
	rpc batchQueryCurrentRankNew(BatchQueryCurrentRankReq64) returns (BatchQueryCurrentRankResp64);
	rpc batchQueryGuildRankNew(BatchQueryGuildRankReq64) returns (BatchQueryGuildRankResp64);
}
