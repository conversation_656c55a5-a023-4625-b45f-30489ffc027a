syntax = "proto3";

package com.yy.yyzone.guildrank.api;
import "uri.proto";

message QueryContractInfoByAnchorUidsReq {
	repeated uint32 owUids = 1; // 批量查询的uid集合
    map<string,string> extendData = 2; // 扩展字段，暂未使用
    option (yyp.uri) = 58222;
}
message QueryContractInfoByAnchorUidsRes {
	uint32 result = 1; // 响应码    0：成功    1：失败
	map<uint32,uint32> data = 2; //返回信息   key：uid value:owUid
    map<string,string> extendData = 3; // 扩展字段，暂未使用
    option (yyp.uri) = 58478;
}

service ContractInfoService {
	rpc queryContractInfoByAnchorUids(QueryContractInfoByAnchorUidsReq) returns (QueryContractInfoByAnchorUidsRes);
}
