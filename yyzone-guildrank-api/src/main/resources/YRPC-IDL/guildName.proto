syntax = "proto3";

package com.yy.yyzone.guildrank.api;
import "uri.proto";

//批量查询公会名称 103<<8|252
message PBatchGetGuildNameReq{
    repeated uint32 owuids = 1;
    map<string,string> extendData = 2;
    option (yyp.uri) = 26620;
}

//104<<8|252
message PBatchGetGuildNameResp{
    uint32 result = 1;
    map<uint32,string> nameInfo = 2;
    map<string,string> extendData = 3;
    option (yyp.uri) = 26876;
}

service GuildNameService {
	rpc batchGetGuildName(PBatchGetGuildNameReq) returns (PBatchGetGuildNameResp);
}
