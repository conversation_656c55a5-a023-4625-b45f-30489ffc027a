syntax = "proto3";

package com.yy.yyzone.guildrank.api;
import "uri.proto";

message QueryGuildRankReq {
	repeated uint64 owUids = 1;
	string month = 2; // 2019-07
}
message QueryGuildRankResp {
	uint32 result = 1;
	map<uint64,uint32> uidLevel = 2; // uid-当前排名
	map<uint64,uint32> uidChange = 3; // uid-排名变化
}

message updateFlagResp {
	uint32 result = 1; // 1-已经更新 0还没更新
}
message updateFlagReq {
	uint32 param = 1;
}

message CurrentRankReq {
	uint64 uid = 1;
	option (yyp.uri) = 1;
}
message CurrentRankResp {
	uint32 result = 1;
	uint32 level = 2;
	option (yyp.uri) = 1;
}

message QueryCurrentRankReq {
	int64 uid = 1;
	string appid = 2; // appid 服务端分配
	string sign = 3;  // DigestUtils.md5Hex(uid+"_"+password)
	option (yyp.uri) = 1;
}
message QueryCurrentRankResp {
	uint32 result = 1;
	uint32 level = 2;
	option (yyp.uri) = 1;
}


message BatchQueryCurrentRankReq {
	repeated uint32 uid = 1;
	option (yyp.uri) = 1;
}
message BatchQueryCurrentRankResp {
	uint32 result = 1;
	map<uint32, int32> uidLevel = 2;
	option (yyp.uri) = 1;
}

message BatchQueryGuildRankReq {
	repeated uint32 uid = 1;
	option (yyp.uri) = 1;
}
message BatchQueryGuildRankResp {
	uint32 result = 1;
	uint32 isNew = 2; //是否上个月最新的段位 1:是 0:不是
	map<uint32, int32> uidLevel = 3;
	option (yyp.uri) = 1;
}

service GuildrankService {
	rpc queryGuildRank(QueryGuildRankReq) returns (QueryGuildRankResp);
	rpc queryHasUpdate(updateFlagReq) returns (updateFlagResp);
	rpc queryCurrentRank(CurrentRankReq) returns (CurrentRankResp);
	rpc queryCurrentRankByFrom(QueryCurrentRankReq) returns (QueryCurrentRankResp);
}
