syntax = "proto3";

package com.yy.yyzone.guildrank.api;
import "uri.proto";

message QueryAllGuildRankByOrderReq {
	string month = 1; // 2019-07
	uint32 orderType = 2; // 排序标识： 1蓝钻
}
message QueryAllGuildRankByOrderResp {
	uint32 result = 1;
	uint32 isNew = 2; //是否上个月最新的数据 1:是 0:不是
	repeated uint32 owuids = 3;
}

message GuildRankVO {
    map<string, string> data = 1;
}

message QueryAllGuildRankReq {
	string month = 1; // 2019-07
}
message QueryAllGuildRankResp {
	uint32 result = 1;
	uint32 isNew = 2; //是否上个月最新的数据 1:是 0:不是
	repeated GuildRankVO dataList = 3;
}