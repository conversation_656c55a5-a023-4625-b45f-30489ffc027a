syntax = "proto3";

package com.yy.yyzone.guildrank.api;
import "uri.proto";

message Detail {
	repeated int64 channel = 1;
}

message GetRoleTypeReq {
    int64 uid = 1;
	uint32 type = 2; //type=1时查询后设置，type=2只查询
	map<string,string> extends = 3;
	option (yyp.uri) = 1;
}
message GetRoleTypeResp {
	uint32 result = 1;
	/**
 * R_ANCHOR         = 1; //主播（新）
 * R_CHANNEL        = 2; //公会（新）
 * R_NORUSER        = 3; //用户（新）
 * R_ANCHOR_CHANNEL = 4; //主播公会（过渡期角色）
 * R_SINGER         = 5; //歌手或常驻艺人（旧）
 */
	uint32 role = 2;
	map<int64, Detail> detail = 3; //key=0:uid下所有频道，key=1:旗下所有音乐频道，key=2:旗下所有普通频道, key=3:旗下所有直播间
	map<string, string> extends = 4;
	option (yyp.uri) = 2;
}

service RoleService {
	rpc getRoleType(GetRoleTypeReq) returns (GetRoleTypeResp);
}
