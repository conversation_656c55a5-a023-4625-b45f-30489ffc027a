<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd" >
<generatorConfiguration>

    <context id="entmobile" defaultModelType="flat" targetRuntime="MyBatis3">

        <plugin type="org.mybatis.generator.plugins.AddLimitOffsetPlugin"/>
        <!--EqualsHash,生成model 的 equalsHash方法-->
        <!--        <plugin type="org.mybatis.generator.plugins.EqualsHashCodePlugin"/>-->
        <!--toString,生成model 的 toString方法-->
        <plugin type="org.mybatis.generator.plugins.ToStringPlugin"/>

        <commentGenerator type="org.mybatis.generator.MyCommentGenerator">
            <property name="suppressDate" value="true"/>
            <!-- remove comments -->
            <property name="suppressAllComments" value="false"/>
        </commentGenerator>

        <!-- //////////////////////////////////////////////////////////////////////////////////////////////////////////////// -->
        <!-- jdbc connection -->
        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="**********************************************************************************"
                        userId="yyzonetest@livetag_server@rws" password="3g4cAR29" />

        <!-- //////////////////////////////////////////////////////////////////////////////////////////////////////////////// -->
        <!-- java type resolver -->
        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <!-- //////////////////////////////////////////////////////////////////////////////////////////////////////////////// -->
        <!-- gem entity -->
        <!--data -->
        <javaModelGenerator targetPackage="com.yy.yyzone.guildrank.db.gen.model"
                            targetProject="./yyzone-guildrank-persist/src/main/java">
            <property name="enableSubPackages" value="true" />
            <property name="trimStrings" value="false" />
        </javaModelGenerator>

        <!-- //////////////////////////////////////////////////////////////////////////////////////////////////////////////// -->
        <!-- gem annotated mapper -->
        <!-- data -->
        <sqlMapGenerator targetPackage="com.yy.yyzone.guildrank.db.gen.mapper"
                         targetProject="./yyzone-guildrank-persist/src/main/java">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <!-- //////////////////////////////////////////////////////////////////////////////////////////////////////////////// -->
        <!-- gem annotated mapper -->
        <!-- data -->
        <javaClientGenerator type="XMLMAPPER"
                             targetPackage="com.yy.yyzone.guildrank.db.gen.mapper"
                             targetProject="./yyzone-guildrank-persist/src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>

        <table tableName="dm_yy_guild_segment_score_month">
            <generatedKey column="id" sqlStatement="JDBC"/>
        </table>

        <table tableName="dm_yy_guild_segment_score_month_tmp">
            <generatedKey column="id" sqlStatement="JDBC"/>
        </table>
        <table tableName="dm_yy_sid_segment_origin_star_day">
            <generatedKey column="id" sqlStatement="JDBC"/>
        </table>

    </context>
</generatorConfiguration>