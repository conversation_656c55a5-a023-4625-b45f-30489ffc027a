package com.yy.yyzone.guildrank.db.gen.model;

import java.util.Date;

public class GuildRankPlatformActivityTaskUploadHistory {
    private Integer dt;
    private String filename;
    private Date uploadDate;
    private String filePath;
    private String operator;

    public Integer getDt() {
        return dt;
    }

    public void setDt(Integer dt) {
        this.dt = dt;
    }

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public Date getUploadDate() {
        return uploadDate;
    }

    public void setUploadDate(Date uploadDate) {
        this.uploadDate = uploadDate;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    @Override
    public String toString() {
        return "GuildRankPlatformActivityTaskUploadHistory{" +
                "dt=" + dt +
                ", filename='" + filename + '\'' +
                ", uploadDate=" + uploadDate +
                ", filePath='" + filePath + '\'' +
                ", operator='" + operator + '\'' +
                '}';
    }
}