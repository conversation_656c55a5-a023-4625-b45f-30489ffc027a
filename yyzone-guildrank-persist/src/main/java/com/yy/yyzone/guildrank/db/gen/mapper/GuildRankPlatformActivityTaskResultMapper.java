package com.yy.yyzone.guildrank.db.gen.mapper;

import com.yy.yyzone.guildrank.db.gen.model.GuildRankPlatformActivityTaskResult;
import org.apache.ibatis.annotations.*;

import java.util.List;


public interface GuildRankPlatformActivityTaskResultMapper {

    @Insert({
            "INSERT INTO guild_rank_platform_activity_task_result (",
            "dt, uid, yy, guild_name,",
            "spring_headline_task_detail, spring_headline_completion, spring_headline_score,",
            "summer_headline_task_detail, summer_headline_completion, summer_headline_score,",
            "guild_race_s1_task_detail, guild_race_s1_completion, guild_race_s1_score,",
            "guild_race_s2_task_detail, guild_race_s2_completion, guild_race_s2_score,",
            "guild_race_main_task_detail, guild_race_main_completion, guild_race_main_score,",
            "personal_race_main_task_detail, personal_race_main_completion, personal_race_main_score,",
            "remark, operator",
            ") VALUES (",
            "#{dt}, #{uid}, #{yy}, #{guildName},",
            "#{springHeadlineTaskDetail}, #{springHeadlineCompletion}, #{springHeadlineScore},",
            "#{summerHeadlineTaskDetail}, #{summerHeadlineCompletion}, #{summerHeadlineScore},",
            "#{guildRaceS1TaskDetail}, #{guildRaceS1Completion}, #{guildRaceS1Score},",
            "#{guildRaceS2TaskDetail}, #{guildRaceS2Completion}, #{guildRaceS2Score},",
            "#{guildRaceMainTaskDetail}, #{guildRaceMainCompletion}, #{guildRaceMainScore},",
            "#{personalRaceMainTaskDetail}, #{personalRaceMainCompletion}, #{personalRaceMainScore},",
            "#{remark}, #{operator}",
            ")"
    })
    void insertOrUpdate(GuildRankPlatformActivityTaskResult entity);

    @Delete("delete from guild_rank_platform_activity_task_result where dt = #{dt}")
    int deleteByDt(long dt);

    @Select({
            "SELECT",
            "dt AS dt,",
            "uid AS uid,",
            "yy AS yy,",
            "guild_name AS guildName,",
            "spring_headline_task_detail AS springHeadlineTaskDetail,",
            "spring_headline_completion AS springHeadlineCompletion,",
            "spring_headline_score AS springHeadlineScore,",
            "summer_headline_task_detail AS summerHeadlineTaskDetail,",
            "summer_headline_completion AS summerHeadlineCompletion,",
            "summer_headline_score AS summerHeadlineScore,",
            "guild_race_s1_task_detail AS guildRaceS1TaskDetail,",
            "guild_race_s1_completion AS guildRaceS1Completion,",
            "guild_race_s1_score AS guildRaceS1Score,",
            "guild_race_s2_task_detail AS guildRaceS2TaskDetail,",
            "guild_race_s2_completion AS guildRaceS2Completion,",
            "guild_race_s2_score AS guildRaceS2Score,",
            "guild_race_main_task_detail AS guildRaceMainTaskDetail,",
            "guild_race_main_completion AS guildRaceMainCompletion,",
            "guild_race_main_score AS guildRaceMainScore,",
            "personal_race_main_task_detail AS personalRaceMainTaskDetail,",
            "personal_race_main_completion AS personalRaceMainCompletion,",
            "personal_race_main_score AS personalRaceMainScore,",
            "remark AS remark, operator,create_time as createTime ",
            "FROM guild_rank_platform_activity_task_result",
            "WHERE dt = #{month}"
    })
    List<GuildRankPlatformActivityTaskResult> selectByMonth(@Param("month") int month);



    @Select({
            "<script>",
            "SELECT",
            "dt AS dt,",
            "uid AS uid,",
            "yy AS yy,",
            "guild_name AS guildName,",
            "spring_headline_task_detail AS springHeadlineTaskDetail,",
            "spring_headline_completion AS springHeadlineCompletion,",
            "spring_headline_score AS springHeadlineScore,",
            "summer_headline_task_detail AS summerHeadlineTaskDetail,",
            "summer_headline_completion AS summerHeadlineCompletion,",
            "summer_headline_score AS summerHeadlineScore,",
            "guild_race_s1_task_detail AS guildRaceS1TaskDetail,",
            "guild_race_s1_completion AS guildRaceS1Completion,",
            "guild_race_s1_score AS guildRaceS1Score,",
            "guild_race_s2_task_detail AS guildRaceS2TaskDetail,",
            "guild_race_s2_completion AS guildRaceS2Completion,",
            "guild_race_s2_score AS guildRaceS2Score,",
            "guild_race_main_task_detail AS guildRaceMainTaskDetail,",
            "guild_race_main_completion AS guildRaceMainCompletion,",
            "guild_race_main_score AS guildRaceMainScore,",
            "personal_race_main_task_detail AS personalRaceMainTaskDetail,",
            "personal_race_main_completion AS personalRaceMainCompletion,",
            "personal_race_main_score AS personalRaceMainScore,",
            "remark AS remark,",
            "operator AS operator,",
            "create_time AS createTime",
            "FROM guild_rank_platform_activity_task_result",
            "WHERE 1= 1 <if test='dt != null'> AND dt = #{dt} </if>",
            "<if test='operator != null and operator != \"\"'> AND operator = #{operator}</if>",
            "<if test='uids != null and uids.size() > 0'> AND uid IN (<foreach collection='uids' item='uid' separator=','>#{uid}</foreach>) </if>",
            "<if test='notNullDetailColumnName != null'> AND #{notNullDetailColumnName} IS NOT NULL</if>",
            "LIMIT #{size} OFFSET #{offset}",
            "</script>"
    })
    List<GuildRankPlatformActivityTaskResult> selectByMonthWithPagination(
            @Param("dt") Integer dt,
            @Param("offset") int offset,
            @Param("size") int size,
            @Param("operator") String operator,
            @Param("uids") List<Long> uids,
            @Param("notNullDetailColumnName") String notNullDetailColumnName);


    @Select({
            "<script>",
            "SELECT",
            "dt AS dt,",
            "uid AS uid,",
            "yy AS yy,",
            "guild_name AS guildName,",
            "spring_headline_task_detail AS springHeadlineTaskDetail,",
            "spring_headline_completion AS springHeadlineCompletion,",
            "spring_headline_score AS springHeadlineScore,",
            "summer_headline_task_detail AS summerHeadlineTaskDetail,",
            "summer_headline_completion AS summerHeadlineCompletion,",
            "summer_headline_score AS summerHeadlineScore,",
            "guild_race_s1_task_detail AS guildRaceS1TaskDetail,",
            "guild_race_s1_completion AS guildRaceS1Completion,",
            "guild_race_s1_score AS guildRaceS1Score,",
            "guild_race_s2_task_detail AS guildRaceS2TaskDetail,",
            "guild_race_s2_completion AS guildRaceS2Completion,",
            "guild_race_s2_score AS guildRaceS2Score,",
            "guild_race_main_task_detail AS guildRaceMainTaskDetail,",
            "guild_race_main_completion AS guildRaceMainCompletion,",
            "guild_race_main_score AS guildRaceMainScore,",
            "personal_race_main_task_detail AS personalRaceMainTaskDetail,",
            "personal_race_main_completion AS personalRaceMainCompletion,",
            "personal_race_main_score AS personalRaceMainScore,",
            "remark AS remark,",
            "operator AS operator,",
            "create_time AS createTime",
            "FROM guild_rank_platform_activity_task_result",
            "WHERE dt = #{dt} ",
            " AND uid = #{uid} ",
            "</script>"
    })
    GuildRankPlatformActivityTaskResult selectByUidAndDt(@Param("uid") long uid,@Param("dt") int dt);

    @Select({
            "<script>",
            "SELECT COUNT(*) AS count",
            "FROM guild_rank_platform_activity_task_result",
            "WHERE 1= 1 <if test='dt != null'> AND dt = #{dt} </if>",
            "<if test='operator != null and operator != \"\"' > AND operator = #{operator}</if>",
            "<if test='uids != null and uids.size() > 0'> AND uid IN (<foreach collection='uids' item='uid' separator=','>#{uid}</foreach>) </if>",
            "<if test='notNullDetailColumnName != null'> AND #{notNullDetailColumnName} IS NOT NULL</if>",
            "</script>"
    })
    long countByMonth(
            @Param("dt") Integer dt,
            @Param("operator") String operator,
            @Param("uids") List<Long> uids,
            @Param("notNullDetailColumnName") String notNullDetailColumnName);
}