package com.yy.yyzone.guildrank.db.custom.mapper;

import com.yy.yyzone.guildrank.db.gen.model.GuildrankConfigLog;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankRenewArtistCount;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankTemporaryChat;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankTemporaryChatExample;
import org.apache.ibatis.annotations.Param;
import sun.rmi.server.InactiveGroupException;

import java.util.List;

public interface CustomMapper {
    int getMaxinsertNum();
    int batchAddTemporaryChat(@Param("list") List<GuildrankTemporaryChat> list);
    List<GuildrankTemporaryChat> listTemporaryChat(@Param("offset") int offset, @Param("limit")int limit);

    int insertBatchRenewArtistCount(@Param("list")List<GuildrankRenewArtistCount> list);
}