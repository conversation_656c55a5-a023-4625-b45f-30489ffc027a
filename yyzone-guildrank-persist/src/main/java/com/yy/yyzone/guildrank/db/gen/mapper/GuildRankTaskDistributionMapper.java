package com.yy.yyzone.guildrank.db.gen.mapper;


import com.yy.yyzone.guildrank.db.gen.model.GuildRankTaskDistribution;
import com.yy.yyzone.guildrank.db.gen.model.GuildRankTaskDistributionUidAndDt;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;
import java.util.Map;


public interface GuildRankTaskDistributionMapper {
    @Insert("INSERT INTO guild_rank_task_distribution (uid, dt, task_type, target_value, operator, create_time, update_time, history, op_type,reason,previous_id) " +
            "VALUES (#{uid}, #{dt}, #{taskType}, #{targetValue}, #{operator}, #{createTime}, #{updateTime}, #{history}, #{opType} , #{reason},#{previousId})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insert(GuildRankTaskDistribution task);

    @Select("SELECT * FROM guild_rank_task_distribution WHERE uid = #{uid} AND dt = #{dt} AND history = 0 " +
            "AND id = (SELECT MAX(id) FROM guild_rank_task_distribution WHERE uid = #{uid} AND dt = #{dt} AND task_type = #{taskType} AND history = 0)")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "uid", column = "uid"),
            @Result(property = "dt", column = "dt"),
            @Result(property = "taskType", column = "task_type"),
            @Result(property = "targetValue", column = "target_value"),
            @Result(property = "operator", column = "operator"),
            @Result(property = "createTime", column = "create_time", javaType = Date.class),
            @Result(property = "updateTime", column = "update_time", javaType = Date.class),
            @Result(property = "history", column = "history"),
            @Result(property = "reason", column = "reason"),
            @Result(property = "previous_id", column = "previousId"),
            @Result(property = "opType", column = "op_type")
    })
    GuildRankTaskDistribution findLatestByUidAndDtAndType(@Param("uid") Long uid, @Param("dt") Integer dt, @Param("taskType") String taskType);

    @Select({
            "<script>",
            "SELECT * FROM guild_rank_task_distribution",
            "WHERE id IN",
            "<foreach item='id' collection='ids' open='(' separator=',' close=')'>",
            "#{id}",
            "</foreach>",
            "</script>"
    })
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "uid", column = "uid"),
            @Result(property = "dt", column = "dt"),
            @Result(property = "taskType", column = "task_type"),
            @Result(property = "targetValue", column = "target_value"),
            @Result(property = "operator", column = "operator"),
            @Result(property = "createTime", column = "create_time", javaType = Date.class),
            @Result(property = "updateTime", column = "update_time", javaType = Date.class),
            @Result(property = "history", column = "history"),
            @Result(property = "reason", column = "reason"),
            @Result(property = "previousId", column = "previous_id"),
            @Result(property = "opType", column = "op_type")
    })
    List<GuildRankTaskDistribution> selectBatchIds(@Param("ids") List<Long> ids);

    @Select("<script>SELECT DISTINCT uid, dt FROM guild_rank_task_distribution WHERE history = 0 " +
            "<if test='uids != null and uids.size() > 0'>AND uid IN (<foreach collection='uids' item='uid' separator=','>#{uid}</foreach>) </if>" +
            "<if test='dtStart != null'>AND dt &gt;= #{dtStart} </if>" +
            "<if test='dtEnd != null'>AND dt &lt;= #{dtEnd} </if>" +
            "<if test='taskType != null and taskType != \"\"'>AND task_type = #{taskType} </if>" +
            "ORDER BY id desc " +
            "LIMIT #{pageSize} OFFSET #{offset}</script>")
    List<GuildRankTaskDistributionUidAndDt> findDistinctUidAndDt(@Param("uids") List<Long> uids, @Param("dtStart") Integer dtStart,
                                                                 @Param("dtEnd") Integer dtEnd, @Param("taskType") String taskType,
                                                                 @Param("pageSize") int pageSize, @Param("offset") long offset);

    @Select("<script>SELECT COUNT(DISTINCT uid, dt) FROM guild_rank_task_distribution WHERE history = 0 " +
            "<if test='uids != null and uids.size() > 0'>AND uid IN (<foreach collection='uids' item='uid' separator=','>#{uid}</foreach>) </if>" +
            "<if test='dtStart != null'>AND dt &gt;= #{dtStart} </if>" +
            "<if test='dtEnd != null'>AND dt &lt;= #{dtEnd} </if>" +
            "<if test='taskType != null and taskType != \"\"'>AND task_type = #{taskType} </if></script>")
    long countDistinctUidAndDt(@Param("uids") List<Long> uids, @Param("dtStart") Integer dtStart,
                               @Param("dtEnd") Integer dtEnd, @Param("taskType") String taskType);


    @Select("SELECT * FROM guild_rank_task_distribution WHERE uid = #{uid} AND dt = #{dt} AND history = 0")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "uid", column = "uid"),
            @Result(property = "dt", column = "dt"),
            @Result(property = "taskType", column = "task_type"),
            @Result(property = "targetValue", column = "target_value"),
            @Result(property = "operator", column = "operator"),
            @Result(property = "createTime", column = "create_time", javaType = Date.class),
            @Result(property = "updateTime", column = "update_time", javaType = Date.class),
            @Result(property = "history", column = "history"),
            @Result(property = "reason", column = "reason"),
            @Result(property = "previousId", column = "previous_id"),
            @Result(property = "opType", column = "op_type")
    })
    List<GuildRankTaskDistribution> findTasksByUidAndDt(@Param("uid") Long uid, @Param("dt") Integer dt);

    @Select("<script>SELECT * FROM guild_rank_task_distribution WHERE 1 = 1 " +
            "<if test='uids != null and uids.size > 0'>AND uid IN (<foreach collection='uids' item='uid' separator=','>#{uid}</foreach>) </if>" +
            "<if test='dtStart != null'>AND dt &gt;= #{dtStart} </if>" +
            "<if test='dtEnd != null'>AND dt &lt;= #{dtEnd} </if>" +
            "<if test='taskType != null and taskType != \"\"'>AND task_type = #{taskType} </if>" +
            "<if test='opType != null and opType != \"\"'>AND op_type = #{opType} </if>" +
            "<if test='operator != null and operator != \"\"'>AND operator = #{operator} </if>" +
            "ORDER BY id DESC " +
            "LIMIT #{pageSize} OFFSET #{offset}</script>")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "uid", column = "uid"),
            @Result(property = "dt", column = "dt"),
            @Result(property = "taskType", column = "task_type"),
            @Result(property = "targetValue", column = "target_value"),
            @Result(property = "operator", column = "operator"),
            @Result(property = "createTime", column = "create_time", javaType = Date.class),
            @Result(property = "updateTime", column = "update_time", javaType = Date.class),
            @Result(property = "history", column = "history"),
            @Result(property = "reason", column = "reason"),
            @Result(property = "previousId", column = "previous_id"),
            @Result(property = "opType", column = "op_type")
    })
    List<GuildRankTaskDistribution> findHistoryTasks(@Param("uids") List<Long> uids, @Param("dtStart") Integer dtStart,
                                                     @Param("dtEnd") Integer dtEnd, @Param("taskType") String taskType,
                                                     @Param("opType") String opType,@Param("operator") String operator, @Param("pageSize") int pageSize,
                                                     @Param("offset") long offset);

    @Select("<script>SELECT COUNT(*) FROM guild_rank_task_distribution WHERE 1 = 1 " +
            "<if test='uids != null and uids.size > 0'>AND uid IN (<foreach collection='uids' item='uid' separator=','>#{uid}</foreach>) </if>" +
            "<if test='dtStart != null'>AND dt &gt;= #{dtStart} </if>" +
            "<if test='dtEnd != null'>AND dt &lt;= #{dtEnd} </if>" +
            "<if test='taskType != null and taskType != \"\"'>AND task_type = #{taskType} </if>" +
            "<if test='opType != null and opType != \"\"'>AND op_type = #{opType} </if>" +
            "<if test='operator != null and operator != \"\"'>AND operator = #{operator} </if>" +
            "</script>")
    long countHistoryTasks(@Param("uids") List<Long> uids, @Param("dtStart") Integer dtStart,
                           @Param("dtEnd") Integer dtEnd, @Param("taskType") String taskType,
                           @Param("opType") String opType,@Param("operator") String operator);

    @Update("UPDATE guild_rank_task_distribution SET history = 1, update_time = now() WHERE id = #{id}")
    void markAsHistory(@Param("id") Long id);
}