package com.yy.yyzone.guildrank.db.gen.mapper;

import com.yy.yyzone.guildrank.db.gen.model.GuildrankHistory;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankHistoryExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface GuildrankHistoryMapper {
    int countByExample(GuildrankHistoryExample example);

    int deleteByExample(GuildrankHistoryExample example);

    int deleteByPrimaryKey(Long id);

    int insert(GuildrankHistory record);

    int insertSelective(GuildrankHistory record);

    List<GuildrankHistory> selectByExample(GuildrankHistoryExample example);

    GuildrankHistory selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") GuildrankHistory record, @Param("example") GuildrankHistoryExample example);

    int updateByExample(@Param("record") GuildrankHistory record, @Param("example") GuildrankHistoryExample example);

    int updateByPrimaryKeySelective(GuildrankHistory record);

    int updateByPrimaryKey(GuildrankHistory record);
}