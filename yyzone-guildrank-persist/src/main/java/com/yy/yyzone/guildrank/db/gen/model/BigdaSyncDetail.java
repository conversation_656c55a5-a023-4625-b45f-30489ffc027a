package com.yy.yyzone.guildrank.db.gen.model;

import java.util.Date;

public class BigdaSyncDetail {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 日期分区
     */
    private String dt;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 数据更新状态 0海度同步完成 1数据计算完成
     */
    private Integer status;

    /**
     * 海度同步完成时间
     */
    private Date syncTime;

    /**
     * 数据生成完成时间
     */
    private Date dataTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 获取自增主键
     * @return id 自增主键
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置自增主键
     * @param id 自增主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取日期分区
     * @return dt 日期分区
     */
    public String getDt() {
        return dt;
    }

    /**
     * 设置日期分区
     * @param dt 日期分区
     */
    public void setDt(String dt) {
        this.dt = dt;
    }

    /**
     * 获取表名
     * @return table_name 表名
     */
    public String getTableName() {
        return tableName;
    }

    /**
     * 设置表名
     * @param tableName 表名
     */
    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    /**
     * 获取数据更新状态 0海度同步完成 1数据计算完成
     * @return status 数据更新状态 0海度同步完成 1数据计算完成
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 设置数据更新状态 0海度同步完成 1数据计算完成
     * @param status 数据更新状态 0海度同步完成 1数据计算完成
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 获取海度同步完成时间
     * @return sync_time 海度同步完成时间
     */
    public Date getSyncTime() {
        return syncTime;
    }

    /**
     * 设置海度同步完成时间
     * @param syncTime 海度同步完成时间
     */
    public void setSyncTime(Date syncTime) {
        this.syncTime = syncTime;
    }

    /**
     * 获取数据生成完成时间
     * @return data_time 数据生成完成时间
     */
    public Date getDataTime() {
        return dataTime;
    }

    /**
     * 设置数据生成完成时间
     * @param dataTime 数据生成完成时间
     */
    public void setDataTime(Date dataTime) {
        this.dataTime = dataTime;
    }

    /**
     * 获取更新时间
     * @return update_time 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", dt=").append(dt);
        sb.append(", tableName=").append(tableName);
        sb.append(", status=").append(status);
        sb.append(", syncTime=").append(syncTime);
        sb.append(", dataTime=").append(dataTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }
}