<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yy.yyzone.guildrank.db.gen.mapper.DmYyGuildSegmentScoreMonthTmpMapper" >
  <resultMap id="BaseResultMap" type="com.yy.yyzone.guildrank.db.gen.model.DmYyGuildSegmentScoreMonthTmp" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="sid_ownerid" property="sidOwnerid" jdbcType="BIGINT" />
    <result column="sid_owyyid" property="sidOwyyid" jdbcType="BIGINT" />
    <result column="month_diamond" property="monthDiamond" jdbcType="DOUBLE" />
    <result column="month_diamond_rr" property="monthDiamondRr" jdbcType="DOUBLE" />
    <result column="valid_live_uv" property="validLiveUv" jdbcType="DOUBLE" />
    <result column="valid_live_uv_rr" property="validLiveUvRr" jdbcType="DOUBLE" />
    <result column="acu300_uv" property="acu300Uv" jdbcType="DOUBLE" />
    <result column="acu300_uv_rr" property="acu300UvRr" jdbcType="DOUBLE" />
    <result column="acu50_300_uv" property="acu50300Uv" jdbcType="DOUBLE" />
    <result column="acu50_300_uv_rr" property="acu50300UvRr" jdbcType="DOUBLE" />
    <result column="acu10_50_uv" property="acu1050Uv" jdbcType="DOUBLE" />
    <result column="acu10_50_uv_rr" property="acu1050UvRr" jdbcType="DOUBLE" />
    <result column="acu10_uv" property="acu10Uv" jdbcType="DOUBLE" />
    <result column="acu10_uv_rr" property="acu10UvRr" jdbcType="DOUBLE" />
    <result column="break_a_uv" property="breakAUv" jdbcType="BIGINT" />
    <result column="break_b_uv" property="breakBUv" jdbcType="BIGINT" />
    <result column="break_c_uv" property="breakCUv" jdbcType="BIGINT" />
    <result column="break_e_uv" property="breakEUv" jdbcType="BIGINT" />
    <result column="live_uv" property="liveUv" jdbcType="BIGINT" />
    <result column="old_break_uv" property="oldBreakUv" jdbcType="BIGINT" />
    <result column="break_uv_pp" property="breakUvPp" jdbcType="DOUBLE" />
    <result column="break_uv_pp_rr" property="breakUvPpRr" jdbcType="DOUBLE" />
    <result column="ps_s_all" property="psSAll" jdbcType="INTEGER" />
    <result column="ps_s_all_rr" property="psSAllRr" jdbcType="INTEGER" />
    <result column="ps_s_month_diamond" property="psSMonthDiamond" jdbcType="INTEGER" />
    <result column="spr_month_diamond" property="sprMonthDiamond" jdbcType="DOUBLE" />
    <result column="ps_s_valid_live_uv" property="psSValidLiveUv" jdbcType="INTEGER" />
    <result column="spr_valid_live_uv" property="sprValidLiveUv" jdbcType="DOUBLE" />
    <result column="ps_acu" property="psAcu" jdbcType="DOUBLE" />
    <result column="ps_s_acu" property="psSAcu" jdbcType="INTEGER" />
    <result column="spr_acu" property="sprAcu" jdbcType="DOUBLE" />
    <result column="ps_s_live_uv" property="psSLiveUv" jdbcType="INTEGER" />
    <result column="ps_s_break_uv_pp" property="psSBreakUvPp" jdbcType="INTEGER" />
    <result column="spr_break_uv_pp" property="sprBreakUvPp" jdbcType="DOUBLE" />
    <result column="dt" property="dt" jdbcType="VARCHAR" />
    <result column="avg_high_aid_num" property="avgHighAidNum" jdbcType="DOUBLE" />
    <result column="avg_high_aid_num_rr" property="avgHighAidNumRr" jdbcType="DOUBLE" />
    <result column="avg_waist_aid_num" property="avgWaistAidNum" jdbcType="DOUBLE" />
    <result column="avg_waist_aid_num_rr" property="avgWaistAidNumRr" jdbcType="DOUBLE" />
    <result column="avg_tail_aid_num" property="avgTailAidNum" jdbcType="DOUBLE" />
    <result column="avg_tail_aid_num_rr" property="avgTailAidNumRr" jdbcType="DOUBLE" />
    <result column="aid_value_score" property="aidValueScore" jdbcType="DOUBLE" />
    <result column="aid_value_score_star_lvl" property="aidValueScoreStarLvl" jdbcType="INTEGER" />
    <result column="aid_value_score_sprint_value" property="aidValueScoreSprintValue" jdbcType="DOUBLE" />
    <result column="is_mars_guild" property="isMarsGuild" jdbcType="INTEGER" />
    <result column="mars_sid_ownerid" property="marsSidOwnerid" jdbcType="BIGINT" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, sid_ownerid, sid_owyyid, month_diamond, month_diamond_rr, valid_live_uv, valid_live_uv_rr, 
    acu300_uv, acu300_uv_rr, acu50_300_uv, acu50_300_uv_rr, acu10_50_uv, acu10_50_uv_rr, 
    acu10_uv, acu10_uv_rr, break_a_uv, break_b_uv, break_c_uv, break_e_uv, live_uv, old_break_uv, 
    break_uv_pp, break_uv_pp_rr, ps_s_all, ps_s_all_rr, ps_s_month_diamond, spr_month_diamond, 
    ps_s_valid_live_uv, spr_valid_live_uv, ps_acu, ps_s_acu, spr_acu, ps_s_live_uv, ps_s_break_uv_pp, 
    spr_break_uv_pp, dt, avg_high_aid_num, avg_high_aid_num_rr, avg_waist_aid_num, avg_waist_aid_num_rr, 
    avg_tail_aid_num, avg_tail_aid_num_rr, aid_value_score, aid_value_score_star_lvl, 
    aid_value_score_sprint_value, is_mars_guild, mars_sid_ownerid
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYyGuildSegmentScoreMonthTmpExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from dm_yy_guild_segment_score_month_tmp
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit > 0" >
      limit ${limit}
    </if>
    <if test="offset > 0" >
      offset ${offset}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from dm_yy_guild_segment_score_month_tmp
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from dm_yy_guild_segment_score_month_tmp
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYyGuildSegmentScoreMonthTmpExample" >
    delete from dm_yy_guild_segment_score_month_tmp
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYyGuildSegmentScoreMonthTmp" useGeneratedKeys="true" keyProperty="id" >
    insert into dm_yy_guild_segment_score_month_tmp (sid_ownerid, sid_owyyid, month_diamond, 
      month_diamond_rr, valid_live_uv, valid_live_uv_rr, 
      acu300_uv, acu300_uv_rr, acu50_300_uv, 
      acu50_300_uv_rr, acu10_50_uv, acu10_50_uv_rr, 
      acu10_uv, acu10_uv_rr, break_a_uv, 
      break_b_uv, break_c_uv, break_e_uv, 
      live_uv, old_break_uv, break_uv_pp, 
      break_uv_pp_rr, ps_s_all, ps_s_all_rr, 
      ps_s_month_diamond, spr_month_diamond, ps_s_valid_live_uv, 
      spr_valid_live_uv, ps_acu, ps_s_acu, 
      spr_acu, ps_s_live_uv, ps_s_break_uv_pp, 
      spr_break_uv_pp, dt, avg_high_aid_num, 
      avg_high_aid_num_rr, avg_waist_aid_num, avg_waist_aid_num_rr, 
      avg_tail_aid_num, avg_tail_aid_num_rr, aid_value_score, 
      aid_value_score_star_lvl, aid_value_score_sprint_value, 
      is_mars_guild, mars_sid_ownerid)
    values (#{sidOwnerid,jdbcType=BIGINT}, #{sidOwyyid,jdbcType=BIGINT}, #{monthDiamond,jdbcType=DOUBLE}, 
      #{monthDiamondRr,jdbcType=DOUBLE}, #{validLiveUv,jdbcType=DOUBLE}, #{validLiveUvRr,jdbcType=DOUBLE}, 
      #{acu300Uv,jdbcType=DOUBLE}, #{acu300UvRr,jdbcType=DOUBLE}, #{acu50300Uv,jdbcType=DOUBLE}, 
      #{acu50300UvRr,jdbcType=DOUBLE}, #{acu1050Uv,jdbcType=DOUBLE}, #{acu1050UvRr,jdbcType=DOUBLE}, 
      #{acu10Uv,jdbcType=DOUBLE}, #{acu10UvRr,jdbcType=DOUBLE}, #{breakAUv,jdbcType=BIGINT}, 
      #{breakBUv,jdbcType=BIGINT}, #{breakCUv,jdbcType=BIGINT}, #{breakEUv,jdbcType=BIGINT}, 
      #{liveUv,jdbcType=BIGINT}, #{oldBreakUv,jdbcType=BIGINT}, #{breakUvPp,jdbcType=DOUBLE}, 
      #{breakUvPpRr,jdbcType=DOUBLE}, #{psSAll,jdbcType=INTEGER}, #{psSAllRr,jdbcType=INTEGER}, 
      #{psSMonthDiamond,jdbcType=INTEGER}, #{sprMonthDiamond,jdbcType=DOUBLE}, #{psSValidLiveUv,jdbcType=INTEGER}, 
      #{sprValidLiveUv,jdbcType=DOUBLE}, #{psAcu,jdbcType=DOUBLE}, #{psSAcu,jdbcType=INTEGER}, 
      #{sprAcu,jdbcType=DOUBLE}, #{psSLiveUv,jdbcType=INTEGER}, #{psSBreakUvPp,jdbcType=INTEGER}, 
      #{sprBreakUvPp,jdbcType=DOUBLE}, #{dt,jdbcType=VARCHAR}, #{avgHighAidNum,jdbcType=DOUBLE}, 
      #{avgHighAidNumRr,jdbcType=DOUBLE}, #{avgWaistAidNum,jdbcType=DOUBLE}, #{avgWaistAidNumRr,jdbcType=DOUBLE}, 
      #{avgTailAidNum,jdbcType=DOUBLE}, #{avgTailAidNumRr,jdbcType=DOUBLE}, #{aidValueScore,jdbcType=DOUBLE}, 
      #{aidValueScoreStarLvl,jdbcType=INTEGER}, #{aidValueScoreSprintValue,jdbcType=DOUBLE}, 
      #{isMarsGuild,jdbcType=INTEGER}, #{marsSidOwnerid,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYyGuildSegmentScoreMonthTmp" useGeneratedKeys="true" keyProperty="id" >
    insert into dm_yy_guild_segment_score_month_tmp
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="sidOwnerid != null" >
        sid_ownerid,
      </if>
      <if test="sidOwyyid != null" >
        sid_owyyid,
      </if>
      <if test="monthDiamond != null" >
        month_diamond,
      </if>
      <if test="monthDiamondRr != null" >
        month_diamond_rr,
      </if>
      <if test="validLiveUv != null" >
        valid_live_uv,
      </if>
      <if test="validLiveUvRr != null" >
        valid_live_uv_rr,
      </if>
      <if test="acu300Uv != null" >
        acu300_uv,
      </if>
      <if test="acu300UvRr != null" >
        acu300_uv_rr,
      </if>
      <if test="acu50300Uv != null" >
        acu50_300_uv,
      </if>
      <if test="acu50300UvRr != null" >
        acu50_300_uv_rr,
      </if>
      <if test="acu1050Uv != null" >
        acu10_50_uv,
      </if>
      <if test="acu1050UvRr != null" >
        acu10_50_uv_rr,
      </if>
      <if test="acu10Uv != null" >
        acu10_uv,
      </if>
      <if test="acu10UvRr != null" >
        acu10_uv_rr,
      </if>
      <if test="breakAUv != null" >
        break_a_uv,
      </if>
      <if test="breakBUv != null" >
        break_b_uv,
      </if>
      <if test="breakCUv != null" >
        break_c_uv,
      </if>
      <if test="breakEUv != null" >
        break_e_uv,
      </if>
      <if test="liveUv != null" >
        live_uv,
      </if>
      <if test="oldBreakUv != null" >
        old_break_uv,
      </if>
      <if test="breakUvPp != null" >
        break_uv_pp,
      </if>
      <if test="breakUvPpRr != null" >
        break_uv_pp_rr,
      </if>
      <if test="psSAll != null" >
        ps_s_all,
      </if>
      <if test="psSAllRr != null" >
        ps_s_all_rr,
      </if>
      <if test="psSMonthDiamond != null" >
        ps_s_month_diamond,
      </if>
      <if test="sprMonthDiamond != null" >
        spr_month_diamond,
      </if>
      <if test="psSValidLiveUv != null" >
        ps_s_valid_live_uv,
      </if>
      <if test="sprValidLiveUv != null" >
        spr_valid_live_uv,
      </if>
      <if test="psAcu != null" >
        ps_acu,
      </if>
      <if test="psSAcu != null" >
        ps_s_acu,
      </if>
      <if test="sprAcu != null" >
        spr_acu,
      </if>
      <if test="psSLiveUv != null" >
        ps_s_live_uv,
      </if>
      <if test="psSBreakUvPp != null" >
        ps_s_break_uv_pp,
      </if>
      <if test="sprBreakUvPp != null" >
        spr_break_uv_pp,
      </if>
      <if test="dt != null" >
        dt,
      </if>
      <if test="avgHighAidNum != null" >
        avg_high_aid_num,
      </if>
      <if test="avgHighAidNumRr != null" >
        avg_high_aid_num_rr,
      </if>
      <if test="avgWaistAidNum != null" >
        avg_waist_aid_num,
      </if>
      <if test="avgWaistAidNumRr != null" >
        avg_waist_aid_num_rr,
      </if>
      <if test="avgTailAidNum != null" >
        avg_tail_aid_num,
      </if>
      <if test="avgTailAidNumRr != null" >
        avg_tail_aid_num_rr,
      </if>
      <if test="aidValueScore != null" >
        aid_value_score,
      </if>
      <if test="aidValueScoreStarLvl != null" >
        aid_value_score_star_lvl,
      </if>
      <if test="aidValueScoreSprintValue != null" >
        aid_value_score_sprint_value,
      </if>
      <if test="isMarsGuild != null" >
        is_mars_guild,
      </if>
      <if test="marsSidOwnerid != null" >
        mars_sid_ownerid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="sidOwnerid != null" >
        #{sidOwnerid,jdbcType=BIGINT},
      </if>
      <if test="sidOwyyid != null" >
        #{sidOwyyid,jdbcType=BIGINT},
      </if>
      <if test="monthDiamond != null" >
        #{monthDiamond,jdbcType=DOUBLE},
      </if>
      <if test="monthDiamondRr != null" >
        #{monthDiamondRr,jdbcType=DOUBLE},
      </if>
      <if test="validLiveUv != null" >
        #{validLiveUv,jdbcType=DOUBLE},
      </if>
      <if test="validLiveUvRr != null" >
        #{validLiveUvRr,jdbcType=DOUBLE},
      </if>
      <if test="acu300Uv != null" >
        #{acu300Uv,jdbcType=DOUBLE},
      </if>
      <if test="acu300UvRr != null" >
        #{acu300UvRr,jdbcType=DOUBLE},
      </if>
      <if test="acu50300Uv != null" >
        #{acu50300Uv,jdbcType=DOUBLE},
      </if>
      <if test="acu50300UvRr != null" >
        #{acu50300UvRr,jdbcType=DOUBLE},
      </if>
      <if test="acu1050Uv != null" >
        #{acu1050Uv,jdbcType=DOUBLE},
      </if>
      <if test="acu1050UvRr != null" >
        #{acu1050UvRr,jdbcType=DOUBLE},
      </if>
      <if test="acu10Uv != null" >
        #{acu10Uv,jdbcType=DOUBLE},
      </if>
      <if test="acu10UvRr != null" >
        #{acu10UvRr,jdbcType=DOUBLE},
      </if>
      <if test="breakAUv != null" >
        #{breakAUv,jdbcType=BIGINT},
      </if>
      <if test="breakBUv != null" >
        #{breakBUv,jdbcType=BIGINT},
      </if>
      <if test="breakCUv != null" >
        #{breakCUv,jdbcType=BIGINT},
      </if>
      <if test="breakEUv != null" >
        #{breakEUv,jdbcType=BIGINT},
      </if>
      <if test="liveUv != null" >
        #{liveUv,jdbcType=BIGINT},
      </if>
      <if test="oldBreakUv != null" >
        #{oldBreakUv,jdbcType=BIGINT},
      </if>
      <if test="breakUvPp != null" >
        #{breakUvPp,jdbcType=DOUBLE},
      </if>
      <if test="breakUvPpRr != null" >
        #{breakUvPpRr,jdbcType=DOUBLE},
      </if>
      <if test="psSAll != null" >
        #{psSAll,jdbcType=INTEGER},
      </if>
      <if test="psSAllRr != null" >
        #{psSAllRr,jdbcType=INTEGER},
      </if>
      <if test="psSMonthDiamond != null" >
        #{psSMonthDiamond,jdbcType=INTEGER},
      </if>
      <if test="sprMonthDiamond != null" >
        #{sprMonthDiamond,jdbcType=DOUBLE},
      </if>
      <if test="psSValidLiveUv != null" >
        #{psSValidLiveUv,jdbcType=INTEGER},
      </if>
      <if test="sprValidLiveUv != null" >
        #{sprValidLiveUv,jdbcType=DOUBLE},
      </if>
      <if test="psAcu != null" >
        #{psAcu,jdbcType=DOUBLE},
      </if>
      <if test="psSAcu != null" >
        #{psSAcu,jdbcType=INTEGER},
      </if>
      <if test="sprAcu != null" >
        #{sprAcu,jdbcType=DOUBLE},
      </if>
      <if test="psSLiveUv != null" >
        #{psSLiveUv,jdbcType=INTEGER},
      </if>
      <if test="psSBreakUvPp != null" >
        #{psSBreakUvPp,jdbcType=INTEGER},
      </if>
      <if test="sprBreakUvPp != null" >
        #{sprBreakUvPp,jdbcType=DOUBLE},
      </if>
      <if test="dt != null" >
        #{dt,jdbcType=VARCHAR},
      </if>
      <if test="avgHighAidNum != null" >
        #{avgHighAidNum,jdbcType=DOUBLE},
      </if>
      <if test="avgHighAidNumRr != null" >
        #{avgHighAidNumRr,jdbcType=DOUBLE},
      </if>
      <if test="avgWaistAidNum != null" >
        #{avgWaistAidNum,jdbcType=DOUBLE},
      </if>
      <if test="avgWaistAidNumRr != null" >
        #{avgWaistAidNumRr,jdbcType=DOUBLE},
      </if>
      <if test="avgTailAidNum != null" >
        #{avgTailAidNum,jdbcType=DOUBLE},
      </if>
      <if test="avgTailAidNumRr != null" >
        #{avgTailAidNumRr,jdbcType=DOUBLE},
      </if>
      <if test="aidValueScore != null" >
        #{aidValueScore,jdbcType=DOUBLE},
      </if>
      <if test="aidValueScoreStarLvl != null" >
        #{aidValueScoreStarLvl,jdbcType=INTEGER},
      </if>
      <if test="aidValueScoreSprintValue != null" >
        #{aidValueScoreSprintValue,jdbcType=DOUBLE},
      </if>
      <if test="isMarsGuild != null" >
        #{isMarsGuild,jdbcType=INTEGER},
      </if>
      <if test="marsSidOwnerid != null" >
        #{marsSidOwnerid,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYyGuildSegmentScoreMonthTmpExample" resultType="java.lang.Integer" >
    select count(*) from dm_yy_guild_segment_score_month_tmp
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update dm_yy_guild_segment_score_month_tmp
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.sidOwnerid != null" >
        sid_ownerid = #{record.sidOwnerid,jdbcType=BIGINT},
      </if>
      <if test="record.sidOwyyid != null" >
        sid_owyyid = #{record.sidOwyyid,jdbcType=BIGINT},
      </if>
      <if test="record.monthDiamond != null" >
        month_diamond = #{record.monthDiamond,jdbcType=DOUBLE},
      </if>
      <if test="record.monthDiamondRr != null" >
        month_diamond_rr = #{record.monthDiamondRr,jdbcType=DOUBLE},
      </if>
      <if test="record.validLiveUv != null" >
        valid_live_uv = #{record.validLiveUv,jdbcType=DOUBLE},
      </if>
      <if test="record.validLiveUvRr != null" >
        valid_live_uv_rr = #{record.validLiveUvRr,jdbcType=DOUBLE},
      </if>
      <if test="record.acu300Uv != null" >
        acu300_uv = #{record.acu300Uv,jdbcType=DOUBLE},
      </if>
      <if test="record.acu300UvRr != null" >
        acu300_uv_rr = #{record.acu300UvRr,jdbcType=DOUBLE},
      </if>
      <if test="record.acu50300Uv != null" >
        acu50_300_uv = #{record.acu50300Uv,jdbcType=DOUBLE},
      </if>
      <if test="record.acu50300UvRr != null" >
        acu50_300_uv_rr = #{record.acu50300UvRr,jdbcType=DOUBLE},
      </if>
      <if test="record.acu1050Uv != null" >
        acu10_50_uv = #{record.acu1050Uv,jdbcType=DOUBLE},
      </if>
      <if test="record.acu1050UvRr != null" >
        acu10_50_uv_rr = #{record.acu1050UvRr,jdbcType=DOUBLE},
      </if>
      <if test="record.acu10Uv != null" >
        acu10_uv = #{record.acu10Uv,jdbcType=DOUBLE},
      </if>
      <if test="record.acu10UvRr != null" >
        acu10_uv_rr = #{record.acu10UvRr,jdbcType=DOUBLE},
      </if>
      <if test="record.breakAUv != null" >
        break_a_uv = #{record.breakAUv,jdbcType=BIGINT},
      </if>
      <if test="record.breakBUv != null" >
        break_b_uv = #{record.breakBUv,jdbcType=BIGINT},
      </if>
      <if test="record.breakCUv != null" >
        break_c_uv = #{record.breakCUv,jdbcType=BIGINT},
      </if>
      <if test="record.breakEUv != null" >
        break_e_uv = #{record.breakEUv,jdbcType=BIGINT},
      </if>
      <if test="record.liveUv != null" >
        live_uv = #{record.liveUv,jdbcType=BIGINT},
      </if>
      <if test="record.oldBreakUv != null" >
        old_break_uv = #{record.oldBreakUv,jdbcType=BIGINT},
      </if>
      <if test="record.breakUvPp != null" >
        break_uv_pp = #{record.breakUvPp,jdbcType=DOUBLE},
      </if>
      <if test="record.breakUvPpRr != null" >
        break_uv_pp_rr = #{record.breakUvPpRr,jdbcType=DOUBLE},
      </if>
      <if test="record.psSAll != null" >
        ps_s_all = #{record.psSAll,jdbcType=INTEGER},
      </if>
      <if test="record.psSAllRr != null" >
        ps_s_all_rr = #{record.psSAllRr,jdbcType=INTEGER},
      </if>
      <if test="record.psSMonthDiamond != null" >
        ps_s_month_diamond = #{record.psSMonthDiamond,jdbcType=INTEGER},
      </if>
      <if test="record.sprMonthDiamond != null" >
        spr_month_diamond = #{record.sprMonthDiamond,jdbcType=DOUBLE},
      </if>
      <if test="record.psSValidLiveUv != null" >
        ps_s_valid_live_uv = #{record.psSValidLiveUv,jdbcType=INTEGER},
      </if>
      <if test="record.sprValidLiveUv != null" >
        spr_valid_live_uv = #{record.sprValidLiveUv,jdbcType=DOUBLE},
      </if>
      <if test="record.psAcu != null" >
        ps_acu = #{record.psAcu,jdbcType=DOUBLE},
      </if>
      <if test="record.psSAcu != null" >
        ps_s_acu = #{record.psSAcu,jdbcType=INTEGER},
      </if>
      <if test="record.sprAcu != null" >
        spr_acu = #{record.sprAcu,jdbcType=DOUBLE},
      </if>
      <if test="record.psSLiveUv != null" >
        ps_s_live_uv = #{record.psSLiveUv,jdbcType=INTEGER},
      </if>
      <if test="record.psSBreakUvPp != null" >
        ps_s_break_uv_pp = #{record.psSBreakUvPp,jdbcType=INTEGER},
      </if>
      <if test="record.sprBreakUvPp != null" >
        spr_break_uv_pp = #{record.sprBreakUvPp,jdbcType=DOUBLE},
      </if>
      <if test="record.dt != null" >
        dt = #{record.dt,jdbcType=VARCHAR},
      </if>
      <if test="record.avgHighAidNum != null" >
        avg_high_aid_num = #{record.avgHighAidNum,jdbcType=DOUBLE},
      </if>
      <if test="record.avgHighAidNumRr != null" >
        avg_high_aid_num_rr = #{record.avgHighAidNumRr,jdbcType=DOUBLE},
      </if>
      <if test="record.avgWaistAidNum != null" >
        avg_waist_aid_num = #{record.avgWaistAidNum,jdbcType=DOUBLE},
      </if>
      <if test="record.avgWaistAidNumRr != null" >
        avg_waist_aid_num_rr = #{record.avgWaistAidNumRr,jdbcType=DOUBLE},
      </if>
      <if test="record.avgTailAidNum != null" >
        avg_tail_aid_num = #{record.avgTailAidNum,jdbcType=DOUBLE},
      </if>
      <if test="record.avgTailAidNumRr != null" >
        avg_tail_aid_num_rr = #{record.avgTailAidNumRr,jdbcType=DOUBLE},
      </if>
      <if test="record.aidValueScore != null" >
        aid_value_score = #{record.aidValueScore,jdbcType=DOUBLE},
      </if>
      <if test="record.aidValueScoreStarLvl != null" >
        aid_value_score_star_lvl = #{record.aidValueScoreStarLvl,jdbcType=INTEGER},
      </if>
      <if test="record.aidValueScoreSprintValue != null" >
        aid_value_score_sprint_value = #{record.aidValueScoreSprintValue,jdbcType=DOUBLE},
      </if>
      <if test="record.isMarsGuild != null" >
        is_mars_guild = #{record.isMarsGuild,jdbcType=INTEGER},
      </if>
      <if test="record.marsSidOwnerid != null" >
        mars_sid_ownerid = #{record.marsSidOwnerid,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update dm_yy_guild_segment_score_month_tmp
    set id = #{record.id,jdbcType=BIGINT},
      sid_ownerid = #{record.sidOwnerid,jdbcType=BIGINT},
      sid_owyyid = #{record.sidOwyyid,jdbcType=BIGINT},
      month_diamond = #{record.monthDiamond,jdbcType=DOUBLE},
      month_diamond_rr = #{record.monthDiamondRr,jdbcType=DOUBLE},
      valid_live_uv = #{record.validLiveUv,jdbcType=DOUBLE},
      valid_live_uv_rr = #{record.validLiveUvRr,jdbcType=DOUBLE},
      acu300_uv = #{record.acu300Uv,jdbcType=DOUBLE},
      acu300_uv_rr = #{record.acu300UvRr,jdbcType=DOUBLE},
      acu50_300_uv = #{record.acu50300Uv,jdbcType=DOUBLE},
      acu50_300_uv_rr = #{record.acu50300UvRr,jdbcType=DOUBLE},
      acu10_50_uv = #{record.acu1050Uv,jdbcType=DOUBLE},
      acu10_50_uv_rr = #{record.acu1050UvRr,jdbcType=DOUBLE},
      acu10_uv = #{record.acu10Uv,jdbcType=DOUBLE},
      acu10_uv_rr = #{record.acu10UvRr,jdbcType=DOUBLE},
      break_a_uv = #{record.breakAUv,jdbcType=BIGINT},
      break_b_uv = #{record.breakBUv,jdbcType=BIGINT},
      break_c_uv = #{record.breakCUv,jdbcType=BIGINT},
      break_e_uv = #{record.breakEUv,jdbcType=BIGINT},
      live_uv = #{record.liveUv,jdbcType=BIGINT},
      old_break_uv = #{record.oldBreakUv,jdbcType=BIGINT},
      break_uv_pp = #{record.breakUvPp,jdbcType=DOUBLE},
      break_uv_pp_rr = #{record.breakUvPpRr,jdbcType=DOUBLE},
      ps_s_all = #{record.psSAll,jdbcType=INTEGER},
      ps_s_all_rr = #{record.psSAllRr,jdbcType=INTEGER},
      ps_s_month_diamond = #{record.psSMonthDiamond,jdbcType=INTEGER},
      spr_month_diamond = #{record.sprMonthDiamond,jdbcType=DOUBLE},
      ps_s_valid_live_uv = #{record.psSValidLiveUv,jdbcType=INTEGER},
      spr_valid_live_uv = #{record.sprValidLiveUv,jdbcType=DOUBLE},
      ps_acu = #{record.psAcu,jdbcType=DOUBLE},
      ps_s_acu = #{record.psSAcu,jdbcType=INTEGER},
      spr_acu = #{record.sprAcu,jdbcType=DOUBLE},
      ps_s_live_uv = #{record.psSLiveUv,jdbcType=INTEGER},
      ps_s_break_uv_pp = #{record.psSBreakUvPp,jdbcType=INTEGER},
      spr_break_uv_pp = #{record.sprBreakUvPp,jdbcType=DOUBLE},
      dt = #{record.dt,jdbcType=VARCHAR},
      avg_high_aid_num = #{record.avgHighAidNum,jdbcType=DOUBLE},
      avg_high_aid_num_rr = #{record.avgHighAidNumRr,jdbcType=DOUBLE},
      avg_waist_aid_num = #{record.avgWaistAidNum,jdbcType=DOUBLE},
      avg_waist_aid_num_rr = #{record.avgWaistAidNumRr,jdbcType=DOUBLE},
      avg_tail_aid_num = #{record.avgTailAidNum,jdbcType=DOUBLE},
      avg_tail_aid_num_rr = #{record.avgTailAidNumRr,jdbcType=DOUBLE},
      aid_value_score = #{record.aidValueScore,jdbcType=DOUBLE},
      aid_value_score_star_lvl = #{record.aidValueScoreStarLvl,jdbcType=INTEGER},
      aid_value_score_sprint_value = #{record.aidValueScoreSprintValue,jdbcType=DOUBLE},
      is_mars_guild = #{record.isMarsGuild,jdbcType=INTEGER},
      mars_sid_ownerid = #{record.marsSidOwnerid,jdbcType=BIGINT}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYyGuildSegmentScoreMonthTmp" >
    update dm_yy_guild_segment_score_month_tmp
    <set >
      <if test="sidOwnerid != null" >
        sid_ownerid = #{sidOwnerid,jdbcType=BIGINT},
      </if>
      <if test="sidOwyyid != null" >
        sid_owyyid = #{sidOwyyid,jdbcType=BIGINT},
      </if>
      <if test="monthDiamond != null" >
        month_diamond = #{monthDiamond,jdbcType=DOUBLE},
      </if>
      <if test="monthDiamondRr != null" >
        month_diamond_rr = #{monthDiamondRr,jdbcType=DOUBLE},
      </if>
      <if test="validLiveUv != null" >
        valid_live_uv = #{validLiveUv,jdbcType=DOUBLE},
      </if>
      <if test="validLiveUvRr != null" >
        valid_live_uv_rr = #{validLiveUvRr,jdbcType=DOUBLE},
      </if>
      <if test="acu300Uv != null" >
        acu300_uv = #{acu300Uv,jdbcType=DOUBLE},
      </if>
      <if test="acu300UvRr != null" >
        acu300_uv_rr = #{acu300UvRr,jdbcType=DOUBLE},
      </if>
      <if test="acu50300Uv != null" >
        acu50_300_uv = #{acu50300Uv,jdbcType=DOUBLE},
      </if>
      <if test="acu50300UvRr != null" >
        acu50_300_uv_rr = #{acu50300UvRr,jdbcType=DOUBLE},
      </if>
      <if test="acu1050Uv != null" >
        acu10_50_uv = #{acu1050Uv,jdbcType=DOUBLE},
      </if>
      <if test="acu1050UvRr != null" >
        acu10_50_uv_rr = #{acu1050UvRr,jdbcType=DOUBLE},
      </if>
      <if test="acu10Uv != null" >
        acu10_uv = #{acu10Uv,jdbcType=DOUBLE},
      </if>
      <if test="acu10UvRr != null" >
        acu10_uv_rr = #{acu10UvRr,jdbcType=DOUBLE},
      </if>
      <if test="breakAUv != null" >
        break_a_uv = #{breakAUv,jdbcType=BIGINT},
      </if>
      <if test="breakBUv != null" >
        break_b_uv = #{breakBUv,jdbcType=BIGINT},
      </if>
      <if test="breakCUv != null" >
        break_c_uv = #{breakCUv,jdbcType=BIGINT},
      </if>
      <if test="breakEUv != null" >
        break_e_uv = #{breakEUv,jdbcType=BIGINT},
      </if>
      <if test="liveUv != null" >
        live_uv = #{liveUv,jdbcType=BIGINT},
      </if>
      <if test="oldBreakUv != null" >
        old_break_uv = #{oldBreakUv,jdbcType=BIGINT},
      </if>
      <if test="breakUvPp != null" >
        break_uv_pp = #{breakUvPp,jdbcType=DOUBLE},
      </if>
      <if test="breakUvPpRr != null" >
        break_uv_pp_rr = #{breakUvPpRr,jdbcType=DOUBLE},
      </if>
      <if test="psSAll != null" >
        ps_s_all = #{psSAll,jdbcType=INTEGER},
      </if>
      <if test="psSAllRr != null" >
        ps_s_all_rr = #{psSAllRr,jdbcType=INTEGER},
      </if>
      <if test="psSMonthDiamond != null" >
        ps_s_month_diamond = #{psSMonthDiamond,jdbcType=INTEGER},
      </if>
      <if test="sprMonthDiamond != null" >
        spr_month_diamond = #{sprMonthDiamond,jdbcType=DOUBLE},
      </if>
      <if test="psSValidLiveUv != null" >
        ps_s_valid_live_uv = #{psSValidLiveUv,jdbcType=INTEGER},
      </if>
      <if test="sprValidLiveUv != null" >
        spr_valid_live_uv = #{sprValidLiveUv,jdbcType=DOUBLE},
      </if>
      <if test="psAcu != null" >
        ps_acu = #{psAcu,jdbcType=DOUBLE},
      </if>
      <if test="psSAcu != null" >
        ps_s_acu = #{psSAcu,jdbcType=INTEGER},
      </if>
      <if test="sprAcu != null" >
        spr_acu = #{sprAcu,jdbcType=DOUBLE},
      </if>
      <if test="psSLiveUv != null" >
        ps_s_live_uv = #{psSLiveUv,jdbcType=INTEGER},
      </if>
      <if test="psSBreakUvPp != null" >
        ps_s_break_uv_pp = #{psSBreakUvPp,jdbcType=INTEGER},
      </if>
      <if test="sprBreakUvPp != null" >
        spr_break_uv_pp = #{sprBreakUvPp,jdbcType=DOUBLE},
      </if>
      <if test="dt != null" >
        dt = #{dt,jdbcType=VARCHAR},
      </if>
      <if test="avgHighAidNum != null" >
        avg_high_aid_num = #{avgHighAidNum,jdbcType=DOUBLE},
      </if>
      <if test="avgHighAidNumRr != null" >
        avg_high_aid_num_rr = #{avgHighAidNumRr,jdbcType=DOUBLE},
      </if>
      <if test="avgWaistAidNum != null" >
        avg_waist_aid_num = #{avgWaistAidNum,jdbcType=DOUBLE},
      </if>
      <if test="avgWaistAidNumRr != null" >
        avg_waist_aid_num_rr = #{avgWaistAidNumRr,jdbcType=DOUBLE},
      </if>
      <if test="avgTailAidNum != null" >
        avg_tail_aid_num = #{avgTailAidNum,jdbcType=DOUBLE},
      </if>
      <if test="avgTailAidNumRr != null" >
        avg_tail_aid_num_rr = #{avgTailAidNumRr,jdbcType=DOUBLE},
      </if>
      <if test="aidValueScore != null" >
        aid_value_score = #{aidValueScore,jdbcType=DOUBLE},
      </if>
      <if test="aidValueScoreStarLvl != null" >
        aid_value_score_star_lvl = #{aidValueScoreStarLvl,jdbcType=INTEGER},
      </if>
      <if test="aidValueScoreSprintValue != null" >
        aid_value_score_sprint_value = #{aidValueScoreSprintValue,jdbcType=DOUBLE},
      </if>
      <if test="isMarsGuild != null" >
        is_mars_guild = #{isMarsGuild,jdbcType=INTEGER},
      </if>
      <if test="marsSidOwnerid != null" >
        mars_sid_ownerid = #{marsSidOwnerid,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYyGuildSegmentScoreMonthTmp" >
    update dm_yy_guild_segment_score_month_tmp
    set sid_ownerid = #{sidOwnerid,jdbcType=BIGINT},
      sid_owyyid = #{sidOwyyid,jdbcType=BIGINT},
      month_diamond = #{monthDiamond,jdbcType=DOUBLE},
      month_diamond_rr = #{monthDiamondRr,jdbcType=DOUBLE},
      valid_live_uv = #{validLiveUv,jdbcType=DOUBLE},
      valid_live_uv_rr = #{validLiveUvRr,jdbcType=DOUBLE},
      acu300_uv = #{acu300Uv,jdbcType=DOUBLE},
      acu300_uv_rr = #{acu300UvRr,jdbcType=DOUBLE},
      acu50_300_uv = #{acu50300Uv,jdbcType=DOUBLE},
      acu50_300_uv_rr = #{acu50300UvRr,jdbcType=DOUBLE},
      acu10_50_uv = #{acu1050Uv,jdbcType=DOUBLE},
      acu10_50_uv_rr = #{acu1050UvRr,jdbcType=DOUBLE},
      acu10_uv = #{acu10Uv,jdbcType=DOUBLE},
      acu10_uv_rr = #{acu10UvRr,jdbcType=DOUBLE},
      break_a_uv = #{breakAUv,jdbcType=BIGINT},
      break_b_uv = #{breakBUv,jdbcType=BIGINT},
      break_c_uv = #{breakCUv,jdbcType=BIGINT},
      break_e_uv = #{breakEUv,jdbcType=BIGINT},
      live_uv = #{liveUv,jdbcType=BIGINT},
      old_break_uv = #{oldBreakUv,jdbcType=BIGINT},
      break_uv_pp = #{breakUvPp,jdbcType=DOUBLE},
      break_uv_pp_rr = #{breakUvPpRr,jdbcType=DOUBLE},
      ps_s_all = #{psSAll,jdbcType=INTEGER},
      ps_s_all_rr = #{psSAllRr,jdbcType=INTEGER},
      ps_s_month_diamond = #{psSMonthDiamond,jdbcType=INTEGER},
      spr_month_diamond = #{sprMonthDiamond,jdbcType=DOUBLE},
      ps_s_valid_live_uv = #{psSValidLiveUv,jdbcType=INTEGER},
      spr_valid_live_uv = #{sprValidLiveUv,jdbcType=DOUBLE},
      ps_acu = #{psAcu,jdbcType=DOUBLE},
      ps_s_acu = #{psSAcu,jdbcType=INTEGER},
      spr_acu = #{sprAcu,jdbcType=DOUBLE},
      ps_s_live_uv = #{psSLiveUv,jdbcType=INTEGER},
      ps_s_break_uv_pp = #{psSBreakUvPp,jdbcType=INTEGER},
      spr_break_uv_pp = #{sprBreakUvPp,jdbcType=DOUBLE},
      dt = #{dt,jdbcType=VARCHAR},
      avg_high_aid_num = #{avgHighAidNum,jdbcType=DOUBLE},
      avg_high_aid_num_rr = #{avgHighAidNumRr,jdbcType=DOUBLE},
      avg_waist_aid_num = #{avgWaistAidNum,jdbcType=DOUBLE},
      avg_waist_aid_num_rr = #{avgWaistAidNumRr,jdbcType=DOUBLE},
      avg_tail_aid_num = #{avgTailAidNum,jdbcType=DOUBLE},
      avg_tail_aid_num_rr = #{avgTailAidNumRr,jdbcType=DOUBLE},
      aid_value_score = #{aidValueScore,jdbcType=DOUBLE},
      aid_value_score_star_lvl = #{aidValueScoreStarLvl,jdbcType=INTEGER},
      aid_value_score_sprint_value = #{aidValueScoreSprintValue,jdbcType=DOUBLE},
      is_mars_guild = #{isMarsGuild,jdbcType=INTEGER},
      mars_sid_ownerid = #{marsSidOwnerid,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>