package com.yy.yyzone.guildrank.db.gen.model;

import java.util.Date;

public class GuildRankPointOpsLog {
    /**
     * 自增id
     */
    private Long id;

    /**
     * 操作类型，1-新增，2-删除，3-修改
     */
    private Byte opeType;

    /**
     * 主体uid
     */
    private Long entityUid;

    /**
     * 操作类型，1-加，2-减
     */
    private Byte modifyType;

    /**
     * 目标月份
     */
    private Date targetMonth;

    /**
     * 修改分值
     */
    private Integer modifyNum;

    /**
     * 修改原因
     */
    private String modifyReason;

    /**
     * 操作人
     */
    private String passport;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 获取自增id
     * @return id 自增id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置自增id
     * @param id 自增id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取操作类型，1-新增，2-删除，3-修改
     * @return ope_type 操作类型，1-新增，2-删除，3-修改
     */
    public Byte getOpeType() {
        return opeType;
    }

    /**
     * 设置操作类型，1-新增，2-删除，3-修改
     * @param opeType 操作类型，1-新增，2-删除，3-修改
     */
    public void setOpeType(Byte opeType) {
        this.opeType = opeType;
    }

    /**
     * 获取主体uid
     * @return entity_uid 主体uid
     */
    public Long getEntityUid() {
        return entityUid;
    }

    /**
     * 设置主体uid
     * @param entityUid 主体uid
     */
    public void setEntityUid(Long entityUid) {
        this.entityUid = entityUid;
    }

    /**
     * 获取操作类型，1-加，2-减
     * @return modify_type 操作类型，1-加，2-减
     */
    public Byte getModifyType() {
        return modifyType;
    }

    /**
     * 设置操作类型，1-加，2-减
     * @param modifyType 操作类型，1-加，2-减
     */
    public void setModifyType(Byte modifyType) {
        this.modifyType = modifyType;
    }

    /**
     * 获取目标月份
     * @return target_month 目标月份
     */
    public Date getTargetMonth() {
        return targetMonth;
    }

    /**
     * 设置目标月份
     * @param targetMonth 目标月份
     */
    public void setTargetMonth(Date targetMonth) {
        this.targetMonth = targetMonth;
    }

    /**
     * 获取修改分值
     * @return modify_num 修改分值
     */
    public Integer getModifyNum() {
        return modifyNum;
    }

    /**
     * 设置修改分值
     * @param modifyNum 修改分值
     */
    public void setModifyNum(Integer modifyNum) {
        this.modifyNum = modifyNum;
    }

    /**
     * 获取修改原因
     * @return modify_reason 修改原因
     */
    public String getModifyReason() {
        return modifyReason;
    }

    /**
     * 设置修改原因
     * @param modifyReason 修改原因
     */
    public void setModifyReason(String modifyReason) {
        this.modifyReason = modifyReason;
    }

    /**
     * 获取操作人
     * @return passport 操作人
     */
    public String getPassport() {
        return passport;
    }

    /**
     * 设置操作人
     * @param passport 操作人
     */
    public void setPassport(String passport) {
        this.passport = passport;
    }

    /**
     * 获取创建时间
     * @return create_time 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取修改时间
     * @return update_time 修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置修改时间
     * @param updateTime 修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", opeType=").append(opeType);
        sb.append(", entityUid=").append(entityUid);
        sb.append(", modifyType=").append(modifyType);
        sb.append(", targetMonth=").append(targetMonth);
        sb.append(", modifyNum=").append(modifyNum);
        sb.append(", modifyReason=").append(modifyReason);
        sb.append(", passport=").append(passport);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }
}