package com.yy.yyzone.guildrank.db.gen.mapper;

import com.yy.yyzone.guildrank.db.gen.model.GuildrankRenewArtistData;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankRenewArtistDataExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface GuildrankRenewArtistDataMapper {
    int countByExample(GuildrankRenewArtistDataExample example);

    int deleteByExample(GuildrankRenewArtistDataExample example);

    int deleteByPrimaryKey(Long id);

    int insert(GuildrankRenewArtistData record);

    int insertSelective(GuildrankRenewArtistData record);

    List<GuildrankRenewArtistData> selectByExample(GuildrankRenewArtistDataExample example);

    GuildrankRenewArtistData selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") GuildrankRenewArtistData record, @Param("example") GuildrankRenewArtistDataExample example);

    int updateByExample(@Param("record") GuildrankRenewArtistData record, @Param("example") GuildrankRenewArtistDataExample example);

    int updateByPrimaryKeySelective(GuildrankRenewArtistData record);

    int updateByPrimaryKey(GuildrankRenewArtistData record);
}