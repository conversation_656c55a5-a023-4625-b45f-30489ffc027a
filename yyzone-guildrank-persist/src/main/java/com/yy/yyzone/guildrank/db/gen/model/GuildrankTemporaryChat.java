package com.yy.yyzone.guildrank.db.gen.model;

import java.util.Date;

public class GuildrankTemporaryChat {
    /**
     * 
     */
    private Long id;

    /**
     * 运营YY号
     */
    private Long serviceyy;

    /**
     * 公会UID
     */
    private Long guilduid;

    /**
     * 新增时间
     */
    private Date createtime;

    /**
     * 更新时间
     */
    private Date updatetime;

    /**
     * 添加人
     */
    private String addby;

    /**
     * 失效状态(0-未失效 1-已失效)
     */
    private Integer status;

    /**
     * 录入批次,递增， 点失效用
     */
    private Integer insertnum;

    /**
     * 业务类型
     */
    private Integer biztype;

    /**
     * 运营昵称
     */
    private String servicenick;

    /**
     * 获取
     * @return id 
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置
     * @param id 
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取运营YY号
     * @return serviceYY 运营YY号
     */
    public Long getServiceyy() {
        return serviceyy;
    }

    /**
     * 设置运营YY号
     * @param serviceyy 运营YY号
     */
    public void setServiceyy(Long serviceyy) {
        this.serviceyy = serviceyy;
    }

    /**
     * 获取公会UID
     * @return guildUid 公会UID
     */
    public Long getGuilduid() {
        return guilduid;
    }

    /**
     * 设置公会UID
     * @param guilduid 公会UID
     */
    public void setGuilduid(Long guilduid) {
        this.guilduid = guilduid;
    }

    /**
     * 获取新增时间
     * @return createTime 新增时间
     */
    public Date getCreatetime() {
        return createtime;
    }

    /**
     * 设置新增时间
     * @param createtime 新增时间
     */
    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    /**
     * 获取更新时间
     * @return updateTime 更新时间
     */
    public Date getUpdatetime() {
        return updatetime;
    }

    /**
     * 设置更新时间
     * @param updatetime 更新时间
     */
    public void setUpdatetime(Date updatetime) {
        this.updatetime = updatetime;
    }

    /**
     * 获取添加人
     * @return addBy 添加人
     */
    public String getAddby() {
        return addby;
    }

    /**
     * 设置添加人
     * @param addby 添加人
     */
    public void setAddby(String addby) {
        this.addby = addby;
    }

    /**
     * 获取失效状态(0-未失效 1-已失效)
     * @return status 失效状态(0-未失效 1-已失效)
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 设置失效状态(0-未失效 1-已失效)
     * @param status 失效状态(0-未失效 1-已失效)
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 获取录入批次,递增， 点失效用
     * @return insertNum 录入批次,递增， 点失效用
     */
    public Integer getInsertnum() {
        return insertnum;
    }

    /**
     * 设置录入批次,递增， 点失效用
     * @param insertnum 录入批次,递增， 点失效用
     */
    public void setInsertnum(Integer insertnum) {
        this.insertnum = insertnum;
    }

    /**
     * 获取业务类型
     * @return bizType 业务类型
     */
    public Integer getBiztype() {
        return biztype;
    }

    /**
     * 设置业务类型
     * @param biztype 业务类型
     */
    public void setBiztype(Integer biztype) {
        this.biztype = biztype;
    }

    /**
     * 获取运营昵称
     * @return serviceNick 运营昵称
     */
    public String getServicenick() {
        return servicenick;
    }

    /**
     * 设置运营昵称
     * @param servicenick 运营昵称
     */
    public void setServicenick(String servicenick) {
        this.servicenick = servicenick;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", serviceyy=").append(serviceyy);
        sb.append(", guilduid=").append(guilduid);
        sb.append(", createtime=").append(createtime);
        sb.append(", updatetime=").append(updatetime);
        sb.append(", addby=").append(addby);
        sb.append(", status=").append(status);
        sb.append(", insertnum=").append(insertnum);
        sb.append(", biztype=").append(biztype);
        sb.append(", servicenick=").append(servicenick);
        sb.append("]");
        return sb.toString();
    }
}