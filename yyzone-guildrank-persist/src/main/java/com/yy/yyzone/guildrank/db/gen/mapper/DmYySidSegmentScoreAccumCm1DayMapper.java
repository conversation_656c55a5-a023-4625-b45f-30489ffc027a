package com.yy.yyzone.guildrank.db.gen.mapper;

import com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreAccumCm1Day;
import com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreAccumCm1DayExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DmYySidSegmentScoreAccumCm1DayMapper {
    int countByExample(DmYySidSegmentScoreAccumCm1DayExample example);

    int deleteByExample(DmYySidSegmentScoreAccumCm1DayExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DmYySidSegmentScoreAccumCm1Day record);

    int insertSelective(DmYySidSegmentScoreAccumCm1Day record);

    List<DmYySidSegmentScoreAccumCm1Day> selectByExample(DmYySidSegmentScoreAccumCm1DayExample example);

    DmYySidSegmentScoreAccumCm1Day selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DmYySidSegmentScoreAccumCm1Day record, @Param("example") DmYySidSegmentScoreAccumCm1DayExample example);

    int updateByExample(@Param("record") DmYySidSegmentScoreAccumCm1Day record, @Param("example") DmYySidSegmentScoreAccumCm1DayExample example);

    int updateByPrimaryKeySelective(DmYySidSegmentScoreAccumCm1Day record);

    int updateByPrimaryKey(DmYySidSegmentScoreAccumCm1Day record);
}