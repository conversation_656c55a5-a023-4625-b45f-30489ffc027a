package com.yy.yyzone.guildrank.db.gen.model;

public class JsonConfigSnapshot {
    private Integer dt;          // 日期，格式 yyyyMM
    private String configName;   // 配置名称
    private Long configId;    // 对应的 guild_rank_json_config 表的 id

    public Integer getDt() {
        return dt;
    }

    public void setDt(Integer dt) {
        this.dt = dt;
    }

    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public Long getConfigId() {
        return configId;
    }

    public void setConfigId(Long configId) {
        this.configId = configId;
    }

    @Override
    public String toString() {
        return "GuildRankJsonConfigSnapshot{" +
                "dt=" + dt +
                ", configName='" + configName + '\'' +
                ", configId=" + configId +
                '}';
    }
}