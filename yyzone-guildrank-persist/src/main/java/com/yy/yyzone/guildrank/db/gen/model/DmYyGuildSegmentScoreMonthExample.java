package com.yy.yyzone.guildrank.db.gen.model;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated("dm_yy_guild_segment_score_month")
public class DmYyGuildSegmentScoreMonthExample {
    /**
     * dm_yy_guild_segment_score_month
     */
    protected String orderByClause;

    /**
     * dm_yy_guild_segment_score_month
     */
    protected boolean distinct;

    /**
     * dm_yy_guild_segment_score_month
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public DmYyGuildSegmentScoreMonthExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    /**
     * dm_yy_guild_segment_score_month null
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSidOwneridIsNull() {
            addCriterion("sid_ownerid is null");
            return (Criteria) this;
        }

        public Criteria andSidOwneridIsNotNull() {
            addCriterion("sid_ownerid is not null");
            return (Criteria) this;
        }

        public Criteria andSidOwneridEqualTo(Long value) {
            addCriterion("sid_ownerid =", value, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridNotEqualTo(Long value) {
            addCriterion("sid_ownerid <>", value, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridGreaterThan(Long value) {
            addCriterion("sid_ownerid >", value, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridGreaterThanOrEqualTo(Long value) {
            addCriterion("sid_ownerid >=", value, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridLessThan(Long value) {
            addCriterion("sid_ownerid <", value, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridLessThanOrEqualTo(Long value) {
            addCriterion("sid_ownerid <=", value, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridIn(List<Long> values) {
            addCriterion("sid_ownerid in", values, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridNotIn(List<Long> values) {
            addCriterion("sid_ownerid not in", values, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridBetween(Long value1, Long value2) {
            addCriterion("sid_ownerid between", value1, value2, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridNotBetween(Long value1, Long value2) {
            addCriterion("sid_ownerid not between", value1, value2, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidIsNull() {
            addCriterion("sid_owyyid is null");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidIsNotNull() {
            addCriterion("sid_owyyid is not null");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidEqualTo(Long value) {
            addCriterion("sid_owyyid =", value, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidNotEqualTo(Long value) {
            addCriterion("sid_owyyid <>", value, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidGreaterThan(Long value) {
            addCriterion("sid_owyyid >", value, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidGreaterThanOrEqualTo(Long value) {
            addCriterion("sid_owyyid >=", value, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidLessThan(Long value) {
            addCriterion("sid_owyyid <", value, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidLessThanOrEqualTo(Long value) {
            addCriterion("sid_owyyid <=", value, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidIn(List<Long> values) {
            addCriterion("sid_owyyid in", values, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidNotIn(List<Long> values) {
            addCriterion("sid_owyyid not in", values, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidBetween(Long value1, Long value2) {
            addCriterion("sid_owyyid between", value1, value2, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidNotBetween(Long value1, Long value2) {
            addCriterion("sid_owyyid not between", value1, value2, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondIsNull() {
            addCriterion("month_diamond is null");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondIsNotNull() {
            addCriterion("month_diamond is not null");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondEqualTo(Double value) {
            addCriterion("month_diamond =", value, "monthDiamond");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondNotEqualTo(Double value) {
            addCriterion("month_diamond <>", value, "monthDiamond");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondGreaterThan(Double value) {
            addCriterion("month_diamond >", value, "monthDiamond");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondGreaterThanOrEqualTo(Double value) {
            addCriterion("month_diamond >=", value, "monthDiamond");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondLessThan(Double value) {
            addCriterion("month_diamond <", value, "monthDiamond");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondLessThanOrEqualTo(Double value) {
            addCriterion("month_diamond <=", value, "monthDiamond");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondIn(List<Double> values) {
            addCriterion("month_diamond in", values, "monthDiamond");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondNotIn(List<Double> values) {
            addCriterion("month_diamond not in", values, "monthDiamond");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondBetween(Double value1, Double value2) {
            addCriterion("month_diamond between", value1, value2, "monthDiamond");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondNotBetween(Double value1, Double value2) {
            addCriterion("month_diamond not between", value1, value2, "monthDiamond");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondRrIsNull() {
            addCriterion("month_diamond_rr is null");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondRrIsNotNull() {
            addCriterion("month_diamond_rr is not null");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondRrEqualTo(Double value) {
            addCriterion("month_diamond_rr =", value, "monthDiamondRr");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondRrNotEqualTo(Double value) {
            addCriterion("month_diamond_rr <>", value, "monthDiamondRr");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondRrGreaterThan(Double value) {
            addCriterion("month_diamond_rr >", value, "monthDiamondRr");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondRrGreaterThanOrEqualTo(Double value) {
            addCriterion("month_diamond_rr >=", value, "monthDiamondRr");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondRrLessThan(Double value) {
            addCriterion("month_diamond_rr <", value, "monthDiamondRr");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondRrLessThanOrEqualTo(Double value) {
            addCriterion("month_diamond_rr <=", value, "monthDiamondRr");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondRrIn(List<Double> values) {
            addCriterion("month_diamond_rr in", values, "monthDiamondRr");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondRrNotIn(List<Double> values) {
            addCriterion("month_diamond_rr not in", values, "monthDiamondRr");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondRrBetween(Double value1, Double value2) {
            addCriterion("month_diamond_rr between", value1, value2, "monthDiamondRr");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondRrNotBetween(Double value1, Double value2) {
            addCriterion("month_diamond_rr not between", value1, value2, "monthDiamondRr");
            return (Criteria) this;
        }

        public Criteria andValidLiveUvIsNull() {
            addCriterion("valid_live_uv is null");
            return (Criteria) this;
        }

        public Criteria andValidLiveUvIsNotNull() {
            addCriterion("valid_live_uv is not null");
            return (Criteria) this;
        }

        public Criteria andValidLiveUvEqualTo(Double value) {
            addCriterion("valid_live_uv =", value, "validLiveUv");
            return (Criteria) this;
        }

        public Criteria andValidLiveUvNotEqualTo(Double value) {
            addCriterion("valid_live_uv <>", value, "validLiveUv");
            return (Criteria) this;
        }

        public Criteria andValidLiveUvGreaterThan(Double value) {
            addCriterion("valid_live_uv >", value, "validLiveUv");
            return (Criteria) this;
        }

        public Criteria andValidLiveUvGreaterThanOrEqualTo(Double value) {
            addCriterion("valid_live_uv >=", value, "validLiveUv");
            return (Criteria) this;
        }

        public Criteria andValidLiveUvLessThan(Double value) {
            addCriterion("valid_live_uv <", value, "validLiveUv");
            return (Criteria) this;
        }

        public Criteria andValidLiveUvLessThanOrEqualTo(Double value) {
            addCriterion("valid_live_uv <=", value, "validLiveUv");
            return (Criteria) this;
        }

        public Criteria andValidLiveUvIn(List<Double> values) {
            addCriterion("valid_live_uv in", values, "validLiveUv");
            return (Criteria) this;
        }

        public Criteria andValidLiveUvNotIn(List<Double> values) {
            addCriterion("valid_live_uv not in", values, "validLiveUv");
            return (Criteria) this;
        }

        public Criteria andValidLiveUvBetween(Double value1, Double value2) {
            addCriterion("valid_live_uv between", value1, value2, "validLiveUv");
            return (Criteria) this;
        }

        public Criteria andValidLiveUvNotBetween(Double value1, Double value2) {
            addCriterion("valid_live_uv not between", value1, value2, "validLiveUv");
            return (Criteria) this;
        }

        public Criteria andValidLiveUvRrIsNull() {
            addCriterion("valid_live_uv_rr is null");
            return (Criteria) this;
        }

        public Criteria andValidLiveUvRrIsNotNull() {
            addCriterion("valid_live_uv_rr is not null");
            return (Criteria) this;
        }

        public Criteria andValidLiveUvRrEqualTo(Double value) {
            addCriterion("valid_live_uv_rr =", value, "validLiveUvRr");
            return (Criteria) this;
        }

        public Criteria andValidLiveUvRrNotEqualTo(Double value) {
            addCriterion("valid_live_uv_rr <>", value, "validLiveUvRr");
            return (Criteria) this;
        }

        public Criteria andValidLiveUvRrGreaterThan(Double value) {
            addCriterion("valid_live_uv_rr >", value, "validLiveUvRr");
            return (Criteria) this;
        }

        public Criteria andValidLiveUvRrGreaterThanOrEqualTo(Double value) {
            addCriterion("valid_live_uv_rr >=", value, "validLiveUvRr");
            return (Criteria) this;
        }

        public Criteria andValidLiveUvRrLessThan(Double value) {
            addCriterion("valid_live_uv_rr <", value, "validLiveUvRr");
            return (Criteria) this;
        }

        public Criteria andValidLiveUvRrLessThanOrEqualTo(Double value) {
            addCriterion("valid_live_uv_rr <=", value, "validLiveUvRr");
            return (Criteria) this;
        }

        public Criteria andValidLiveUvRrIn(List<Double> values) {
            addCriterion("valid_live_uv_rr in", values, "validLiveUvRr");
            return (Criteria) this;
        }

        public Criteria andValidLiveUvRrNotIn(List<Double> values) {
            addCriterion("valid_live_uv_rr not in", values, "validLiveUvRr");
            return (Criteria) this;
        }

        public Criteria andValidLiveUvRrBetween(Double value1, Double value2) {
            addCriterion("valid_live_uv_rr between", value1, value2, "validLiveUvRr");
            return (Criteria) this;
        }

        public Criteria andValidLiveUvRrNotBetween(Double value1, Double value2) {
            addCriterion("valid_live_uv_rr not between", value1, value2, "validLiveUvRr");
            return (Criteria) this;
        }

        public Criteria andAcu300UvIsNull() {
            addCriterion("acu300_uv is null");
            return (Criteria) this;
        }

        public Criteria andAcu300UvIsNotNull() {
            addCriterion("acu300_uv is not null");
            return (Criteria) this;
        }

        public Criteria andAcu300UvEqualTo(Double value) {
            addCriterion("acu300_uv =", value, "acu300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu300UvNotEqualTo(Double value) {
            addCriterion("acu300_uv <>", value, "acu300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu300UvGreaterThan(Double value) {
            addCriterion("acu300_uv >", value, "acu300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu300UvGreaterThanOrEqualTo(Double value) {
            addCriterion("acu300_uv >=", value, "acu300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu300UvLessThan(Double value) {
            addCriterion("acu300_uv <", value, "acu300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu300UvLessThanOrEqualTo(Double value) {
            addCriterion("acu300_uv <=", value, "acu300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu300UvIn(List<Double> values) {
            addCriterion("acu300_uv in", values, "acu300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu300UvNotIn(List<Double> values) {
            addCriterion("acu300_uv not in", values, "acu300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu300UvBetween(Double value1, Double value2) {
            addCriterion("acu300_uv between", value1, value2, "acu300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu300UvNotBetween(Double value1, Double value2) {
            addCriterion("acu300_uv not between", value1, value2, "acu300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu300UvRrIsNull() {
            addCriterion("acu300_uv_rr is null");
            return (Criteria) this;
        }

        public Criteria andAcu300UvRrIsNotNull() {
            addCriterion("acu300_uv_rr is not null");
            return (Criteria) this;
        }

        public Criteria andAcu300UvRrEqualTo(Double value) {
            addCriterion("acu300_uv_rr =", value, "acu300UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu300UvRrNotEqualTo(Double value) {
            addCriterion("acu300_uv_rr <>", value, "acu300UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu300UvRrGreaterThan(Double value) {
            addCriterion("acu300_uv_rr >", value, "acu300UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu300UvRrGreaterThanOrEqualTo(Double value) {
            addCriterion("acu300_uv_rr >=", value, "acu300UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu300UvRrLessThan(Double value) {
            addCriterion("acu300_uv_rr <", value, "acu300UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu300UvRrLessThanOrEqualTo(Double value) {
            addCriterion("acu300_uv_rr <=", value, "acu300UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu300UvRrIn(List<Double> values) {
            addCriterion("acu300_uv_rr in", values, "acu300UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu300UvRrNotIn(List<Double> values) {
            addCriterion("acu300_uv_rr not in", values, "acu300UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu300UvRrBetween(Double value1, Double value2) {
            addCriterion("acu300_uv_rr between", value1, value2, "acu300UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu300UvRrNotBetween(Double value1, Double value2) {
            addCriterion("acu300_uv_rr not between", value1, value2, "acu300UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvIsNull() {
            addCriterion("acu50_300_uv is null");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvIsNotNull() {
            addCriterion("acu50_300_uv is not null");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvEqualTo(Double value) {
            addCriterion("acu50_300_uv =", value, "acu50300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvNotEqualTo(Double value) {
            addCriterion("acu50_300_uv <>", value, "acu50300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvGreaterThan(Double value) {
            addCriterion("acu50_300_uv >", value, "acu50300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvGreaterThanOrEqualTo(Double value) {
            addCriterion("acu50_300_uv >=", value, "acu50300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvLessThan(Double value) {
            addCriterion("acu50_300_uv <", value, "acu50300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvLessThanOrEqualTo(Double value) {
            addCriterion("acu50_300_uv <=", value, "acu50300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvIn(List<Double> values) {
            addCriterion("acu50_300_uv in", values, "acu50300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvNotIn(List<Double> values) {
            addCriterion("acu50_300_uv not in", values, "acu50300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvBetween(Double value1, Double value2) {
            addCriterion("acu50_300_uv between", value1, value2, "acu50300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvNotBetween(Double value1, Double value2) {
            addCriterion("acu50_300_uv not between", value1, value2, "acu50300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvRrIsNull() {
            addCriterion("acu50_300_uv_rr is null");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvRrIsNotNull() {
            addCriterion("acu50_300_uv_rr is not null");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvRrEqualTo(Double value) {
            addCriterion("acu50_300_uv_rr =", value, "acu50300UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvRrNotEqualTo(Double value) {
            addCriterion("acu50_300_uv_rr <>", value, "acu50300UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvRrGreaterThan(Double value) {
            addCriterion("acu50_300_uv_rr >", value, "acu50300UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvRrGreaterThanOrEqualTo(Double value) {
            addCriterion("acu50_300_uv_rr >=", value, "acu50300UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvRrLessThan(Double value) {
            addCriterion("acu50_300_uv_rr <", value, "acu50300UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvRrLessThanOrEqualTo(Double value) {
            addCriterion("acu50_300_uv_rr <=", value, "acu50300UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvRrIn(List<Double> values) {
            addCriterion("acu50_300_uv_rr in", values, "acu50300UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvRrNotIn(List<Double> values) {
            addCriterion("acu50_300_uv_rr not in", values, "acu50300UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvRrBetween(Double value1, Double value2) {
            addCriterion("acu50_300_uv_rr between", value1, value2, "acu50300UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvRrNotBetween(Double value1, Double value2) {
            addCriterion("acu50_300_uv_rr not between", value1, value2, "acu50300UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvIsNull() {
            addCriterion("acu10_50_uv is null");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvIsNotNull() {
            addCriterion("acu10_50_uv is not null");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvEqualTo(Double value) {
            addCriterion("acu10_50_uv =", value, "acu1050Uv");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvNotEqualTo(Double value) {
            addCriterion("acu10_50_uv <>", value, "acu1050Uv");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvGreaterThan(Double value) {
            addCriterion("acu10_50_uv >", value, "acu1050Uv");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvGreaterThanOrEqualTo(Double value) {
            addCriterion("acu10_50_uv >=", value, "acu1050Uv");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvLessThan(Double value) {
            addCriterion("acu10_50_uv <", value, "acu1050Uv");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvLessThanOrEqualTo(Double value) {
            addCriterion("acu10_50_uv <=", value, "acu1050Uv");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvIn(List<Double> values) {
            addCriterion("acu10_50_uv in", values, "acu1050Uv");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvNotIn(List<Double> values) {
            addCriterion("acu10_50_uv not in", values, "acu1050Uv");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvBetween(Double value1, Double value2) {
            addCriterion("acu10_50_uv between", value1, value2, "acu1050Uv");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvNotBetween(Double value1, Double value2) {
            addCriterion("acu10_50_uv not between", value1, value2, "acu1050Uv");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvRrIsNull() {
            addCriterion("acu10_50_uv_rr is null");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvRrIsNotNull() {
            addCriterion("acu10_50_uv_rr is not null");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvRrEqualTo(Double value) {
            addCriterion("acu10_50_uv_rr =", value, "acu1050UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvRrNotEqualTo(Double value) {
            addCriterion("acu10_50_uv_rr <>", value, "acu1050UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvRrGreaterThan(Double value) {
            addCriterion("acu10_50_uv_rr >", value, "acu1050UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvRrGreaterThanOrEqualTo(Double value) {
            addCriterion("acu10_50_uv_rr >=", value, "acu1050UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvRrLessThan(Double value) {
            addCriterion("acu10_50_uv_rr <", value, "acu1050UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvRrLessThanOrEqualTo(Double value) {
            addCriterion("acu10_50_uv_rr <=", value, "acu1050UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvRrIn(List<Double> values) {
            addCriterion("acu10_50_uv_rr in", values, "acu1050UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvRrNotIn(List<Double> values) {
            addCriterion("acu10_50_uv_rr not in", values, "acu1050UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvRrBetween(Double value1, Double value2) {
            addCriterion("acu10_50_uv_rr between", value1, value2, "acu1050UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvRrNotBetween(Double value1, Double value2) {
            addCriterion("acu10_50_uv_rr not between", value1, value2, "acu1050UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu10UvIsNull() {
            addCriterion("acu10_uv is null");
            return (Criteria) this;
        }

        public Criteria andAcu10UvIsNotNull() {
            addCriterion("acu10_uv is not null");
            return (Criteria) this;
        }

        public Criteria andAcu10UvEqualTo(Double value) {
            addCriterion("acu10_uv =", value, "acu10Uv");
            return (Criteria) this;
        }

        public Criteria andAcu10UvNotEqualTo(Double value) {
            addCriterion("acu10_uv <>", value, "acu10Uv");
            return (Criteria) this;
        }

        public Criteria andAcu10UvGreaterThan(Double value) {
            addCriterion("acu10_uv >", value, "acu10Uv");
            return (Criteria) this;
        }

        public Criteria andAcu10UvGreaterThanOrEqualTo(Double value) {
            addCriterion("acu10_uv >=", value, "acu10Uv");
            return (Criteria) this;
        }

        public Criteria andAcu10UvLessThan(Double value) {
            addCriterion("acu10_uv <", value, "acu10Uv");
            return (Criteria) this;
        }

        public Criteria andAcu10UvLessThanOrEqualTo(Double value) {
            addCriterion("acu10_uv <=", value, "acu10Uv");
            return (Criteria) this;
        }

        public Criteria andAcu10UvIn(List<Double> values) {
            addCriterion("acu10_uv in", values, "acu10Uv");
            return (Criteria) this;
        }

        public Criteria andAcu10UvNotIn(List<Double> values) {
            addCriterion("acu10_uv not in", values, "acu10Uv");
            return (Criteria) this;
        }

        public Criteria andAcu10UvBetween(Double value1, Double value2) {
            addCriterion("acu10_uv between", value1, value2, "acu10Uv");
            return (Criteria) this;
        }

        public Criteria andAcu10UvNotBetween(Double value1, Double value2) {
            addCriterion("acu10_uv not between", value1, value2, "acu10Uv");
            return (Criteria) this;
        }

        public Criteria andAcu10UvRrIsNull() {
            addCriterion("acu10_uv_rr is null");
            return (Criteria) this;
        }

        public Criteria andAcu10UvRrIsNotNull() {
            addCriterion("acu10_uv_rr is not null");
            return (Criteria) this;
        }

        public Criteria andAcu10UvRrEqualTo(Double value) {
            addCriterion("acu10_uv_rr =", value, "acu10UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu10UvRrNotEqualTo(Double value) {
            addCriterion("acu10_uv_rr <>", value, "acu10UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu10UvRrGreaterThan(Double value) {
            addCriterion("acu10_uv_rr >", value, "acu10UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu10UvRrGreaterThanOrEqualTo(Double value) {
            addCriterion("acu10_uv_rr >=", value, "acu10UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu10UvRrLessThan(Double value) {
            addCriterion("acu10_uv_rr <", value, "acu10UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu10UvRrLessThanOrEqualTo(Double value) {
            addCriterion("acu10_uv_rr <=", value, "acu10UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu10UvRrIn(List<Double> values) {
            addCriterion("acu10_uv_rr in", values, "acu10UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu10UvRrNotIn(List<Double> values) {
            addCriterion("acu10_uv_rr not in", values, "acu10UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu10UvRrBetween(Double value1, Double value2) {
            addCriterion("acu10_uv_rr between", value1, value2, "acu10UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu10UvRrNotBetween(Double value1, Double value2) {
            addCriterion("acu10_uv_rr not between", value1, value2, "acu10UvRr");
            return (Criteria) this;
        }

        public Criteria andBreakAUvIsNull() {
            addCriterion("break_a_uv is null");
            return (Criteria) this;
        }

        public Criteria andBreakAUvIsNotNull() {
            addCriterion("break_a_uv is not null");
            return (Criteria) this;
        }

        public Criteria andBreakAUvEqualTo(Long value) {
            addCriterion("break_a_uv =", value, "breakAUv");
            return (Criteria) this;
        }

        public Criteria andBreakAUvNotEqualTo(Long value) {
            addCriterion("break_a_uv <>", value, "breakAUv");
            return (Criteria) this;
        }

        public Criteria andBreakAUvGreaterThan(Long value) {
            addCriterion("break_a_uv >", value, "breakAUv");
            return (Criteria) this;
        }

        public Criteria andBreakAUvGreaterThanOrEqualTo(Long value) {
            addCriterion("break_a_uv >=", value, "breakAUv");
            return (Criteria) this;
        }

        public Criteria andBreakAUvLessThan(Long value) {
            addCriterion("break_a_uv <", value, "breakAUv");
            return (Criteria) this;
        }

        public Criteria andBreakAUvLessThanOrEqualTo(Long value) {
            addCriterion("break_a_uv <=", value, "breakAUv");
            return (Criteria) this;
        }

        public Criteria andBreakAUvIn(List<Long> values) {
            addCriterion("break_a_uv in", values, "breakAUv");
            return (Criteria) this;
        }

        public Criteria andBreakAUvNotIn(List<Long> values) {
            addCriterion("break_a_uv not in", values, "breakAUv");
            return (Criteria) this;
        }

        public Criteria andBreakAUvBetween(Long value1, Long value2) {
            addCriterion("break_a_uv between", value1, value2, "breakAUv");
            return (Criteria) this;
        }

        public Criteria andBreakAUvNotBetween(Long value1, Long value2) {
            addCriterion("break_a_uv not between", value1, value2, "breakAUv");
            return (Criteria) this;
        }

        public Criteria andBreakBUvIsNull() {
            addCriterion("break_b_uv is null");
            return (Criteria) this;
        }

        public Criteria andBreakBUvIsNotNull() {
            addCriterion("break_b_uv is not null");
            return (Criteria) this;
        }

        public Criteria andBreakBUvEqualTo(Long value) {
            addCriterion("break_b_uv =", value, "breakBUv");
            return (Criteria) this;
        }

        public Criteria andBreakBUvNotEqualTo(Long value) {
            addCriterion("break_b_uv <>", value, "breakBUv");
            return (Criteria) this;
        }

        public Criteria andBreakBUvGreaterThan(Long value) {
            addCriterion("break_b_uv >", value, "breakBUv");
            return (Criteria) this;
        }

        public Criteria andBreakBUvGreaterThanOrEqualTo(Long value) {
            addCriterion("break_b_uv >=", value, "breakBUv");
            return (Criteria) this;
        }

        public Criteria andBreakBUvLessThan(Long value) {
            addCriterion("break_b_uv <", value, "breakBUv");
            return (Criteria) this;
        }

        public Criteria andBreakBUvLessThanOrEqualTo(Long value) {
            addCriterion("break_b_uv <=", value, "breakBUv");
            return (Criteria) this;
        }

        public Criteria andBreakBUvIn(List<Long> values) {
            addCriterion("break_b_uv in", values, "breakBUv");
            return (Criteria) this;
        }

        public Criteria andBreakBUvNotIn(List<Long> values) {
            addCriterion("break_b_uv not in", values, "breakBUv");
            return (Criteria) this;
        }

        public Criteria andBreakBUvBetween(Long value1, Long value2) {
            addCriterion("break_b_uv between", value1, value2, "breakBUv");
            return (Criteria) this;
        }

        public Criteria andBreakBUvNotBetween(Long value1, Long value2) {
            addCriterion("break_b_uv not between", value1, value2, "breakBUv");
            return (Criteria) this;
        }

        public Criteria andBreakCUvIsNull() {
            addCriterion("break_c_uv is null");
            return (Criteria) this;
        }

        public Criteria andBreakCUvIsNotNull() {
            addCriterion("break_c_uv is not null");
            return (Criteria) this;
        }

        public Criteria andBreakCUvEqualTo(Long value) {
            addCriterion("break_c_uv =", value, "breakCUv");
            return (Criteria) this;
        }

        public Criteria andBreakCUvNotEqualTo(Long value) {
            addCriterion("break_c_uv <>", value, "breakCUv");
            return (Criteria) this;
        }

        public Criteria andBreakCUvGreaterThan(Long value) {
            addCriterion("break_c_uv >", value, "breakCUv");
            return (Criteria) this;
        }

        public Criteria andBreakCUvGreaterThanOrEqualTo(Long value) {
            addCriterion("break_c_uv >=", value, "breakCUv");
            return (Criteria) this;
        }

        public Criteria andBreakCUvLessThan(Long value) {
            addCriterion("break_c_uv <", value, "breakCUv");
            return (Criteria) this;
        }

        public Criteria andBreakCUvLessThanOrEqualTo(Long value) {
            addCriterion("break_c_uv <=", value, "breakCUv");
            return (Criteria) this;
        }

        public Criteria andBreakCUvIn(List<Long> values) {
            addCriterion("break_c_uv in", values, "breakCUv");
            return (Criteria) this;
        }

        public Criteria andBreakCUvNotIn(List<Long> values) {
            addCriterion("break_c_uv not in", values, "breakCUv");
            return (Criteria) this;
        }

        public Criteria andBreakCUvBetween(Long value1, Long value2) {
            addCriterion("break_c_uv between", value1, value2, "breakCUv");
            return (Criteria) this;
        }

        public Criteria andBreakCUvNotBetween(Long value1, Long value2) {
            addCriterion("break_c_uv not between", value1, value2, "breakCUv");
            return (Criteria) this;
        }

        public Criteria andBreakEUvIsNull() {
            addCriterion("break_e_uv is null");
            return (Criteria) this;
        }

        public Criteria andBreakEUvIsNotNull() {
            addCriterion("break_e_uv is not null");
            return (Criteria) this;
        }

        public Criteria andBreakEUvEqualTo(Long value) {
            addCriterion("break_e_uv =", value, "breakEUv");
            return (Criteria) this;
        }

        public Criteria andBreakEUvNotEqualTo(Long value) {
            addCriterion("break_e_uv <>", value, "breakEUv");
            return (Criteria) this;
        }

        public Criteria andBreakEUvGreaterThan(Long value) {
            addCriterion("break_e_uv >", value, "breakEUv");
            return (Criteria) this;
        }

        public Criteria andBreakEUvGreaterThanOrEqualTo(Long value) {
            addCriterion("break_e_uv >=", value, "breakEUv");
            return (Criteria) this;
        }

        public Criteria andBreakEUvLessThan(Long value) {
            addCriterion("break_e_uv <", value, "breakEUv");
            return (Criteria) this;
        }

        public Criteria andBreakEUvLessThanOrEqualTo(Long value) {
            addCriterion("break_e_uv <=", value, "breakEUv");
            return (Criteria) this;
        }

        public Criteria andBreakEUvIn(List<Long> values) {
            addCriterion("break_e_uv in", values, "breakEUv");
            return (Criteria) this;
        }

        public Criteria andBreakEUvNotIn(List<Long> values) {
            addCriterion("break_e_uv not in", values, "breakEUv");
            return (Criteria) this;
        }

        public Criteria andBreakEUvBetween(Long value1, Long value2) {
            addCriterion("break_e_uv between", value1, value2, "breakEUv");
            return (Criteria) this;
        }

        public Criteria andBreakEUvNotBetween(Long value1, Long value2) {
            addCriterion("break_e_uv not between", value1, value2, "breakEUv");
            return (Criteria) this;
        }

        public Criteria andLiveUvIsNull() {
            addCriterion("live_uv is null");
            return (Criteria) this;
        }

        public Criteria andLiveUvIsNotNull() {
            addCriterion("live_uv is not null");
            return (Criteria) this;
        }

        public Criteria andLiveUvEqualTo(Long value) {
            addCriterion("live_uv =", value, "liveUv");
            return (Criteria) this;
        }

        public Criteria andLiveUvNotEqualTo(Long value) {
            addCriterion("live_uv <>", value, "liveUv");
            return (Criteria) this;
        }

        public Criteria andLiveUvGreaterThan(Long value) {
            addCriterion("live_uv >", value, "liveUv");
            return (Criteria) this;
        }

        public Criteria andLiveUvGreaterThanOrEqualTo(Long value) {
            addCriterion("live_uv >=", value, "liveUv");
            return (Criteria) this;
        }

        public Criteria andLiveUvLessThan(Long value) {
            addCriterion("live_uv <", value, "liveUv");
            return (Criteria) this;
        }

        public Criteria andLiveUvLessThanOrEqualTo(Long value) {
            addCriterion("live_uv <=", value, "liveUv");
            return (Criteria) this;
        }

        public Criteria andLiveUvIn(List<Long> values) {
            addCriterion("live_uv in", values, "liveUv");
            return (Criteria) this;
        }

        public Criteria andLiveUvNotIn(List<Long> values) {
            addCriterion("live_uv not in", values, "liveUv");
            return (Criteria) this;
        }

        public Criteria andLiveUvBetween(Long value1, Long value2) {
            addCriterion("live_uv between", value1, value2, "liveUv");
            return (Criteria) this;
        }

        public Criteria andLiveUvNotBetween(Long value1, Long value2) {
            addCriterion("live_uv not between", value1, value2, "liveUv");
            return (Criteria) this;
        }

        public Criteria andOldBreakUvIsNull() {
            addCriterion("old_break_uv is null");
            return (Criteria) this;
        }

        public Criteria andOldBreakUvIsNotNull() {
            addCriterion("old_break_uv is not null");
            return (Criteria) this;
        }

        public Criteria andOldBreakUvEqualTo(Long value) {
            addCriterion("old_break_uv =", value, "oldBreakUv");
            return (Criteria) this;
        }

        public Criteria andOldBreakUvNotEqualTo(Long value) {
            addCriterion("old_break_uv <>", value, "oldBreakUv");
            return (Criteria) this;
        }

        public Criteria andOldBreakUvGreaterThan(Long value) {
            addCriterion("old_break_uv >", value, "oldBreakUv");
            return (Criteria) this;
        }

        public Criteria andOldBreakUvGreaterThanOrEqualTo(Long value) {
            addCriterion("old_break_uv >=", value, "oldBreakUv");
            return (Criteria) this;
        }

        public Criteria andOldBreakUvLessThan(Long value) {
            addCriterion("old_break_uv <", value, "oldBreakUv");
            return (Criteria) this;
        }

        public Criteria andOldBreakUvLessThanOrEqualTo(Long value) {
            addCriterion("old_break_uv <=", value, "oldBreakUv");
            return (Criteria) this;
        }

        public Criteria andOldBreakUvIn(List<Long> values) {
            addCriterion("old_break_uv in", values, "oldBreakUv");
            return (Criteria) this;
        }

        public Criteria andOldBreakUvNotIn(List<Long> values) {
            addCriterion("old_break_uv not in", values, "oldBreakUv");
            return (Criteria) this;
        }

        public Criteria andOldBreakUvBetween(Long value1, Long value2) {
            addCriterion("old_break_uv between", value1, value2, "oldBreakUv");
            return (Criteria) this;
        }

        public Criteria andOldBreakUvNotBetween(Long value1, Long value2) {
            addCriterion("old_break_uv not between", value1, value2, "oldBreakUv");
            return (Criteria) this;
        }

        public Criteria andBreakUvPpIsNull() {
            addCriterion("break_uv_pp is null");
            return (Criteria) this;
        }

        public Criteria andBreakUvPpIsNotNull() {
            addCriterion("break_uv_pp is not null");
            return (Criteria) this;
        }

        public Criteria andBreakUvPpEqualTo(Double value) {
            addCriterion("break_uv_pp =", value, "breakUvPp");
            return (Criteria) this;
        }

        public Criteria andBreakUvPpNotEqualTo(Double value) {
            addCriterion("break_uv_pp <>", value, "breakUvPp");
            return (Criteria) this;
        }

        public Criteria andBreakUvPpGreaterThan(Double value) {
            addCriterion("break_uv_pp >", value, "breakUvPp");
            return (Criteria) this;
        }

        public Criteria andBreakUvPpGreaterThanOrEqualTo(Double value) {
            addCriterion("break_uv_pp >=", value, "breakUvPp");
            return (Criteria) this;
        }

        public Criteria andBreakUvPpLessThan(Double value) {
            addCriterion("break_uv_pp <", value, "breakUvPp");
            return (Criteria) this;
        }

        public Criteria andBreakUvPpLessThanOrEqualTo(Double value) {
            addCriterion("break_uv_pp <=", value, "breakUvPp");
            return (Criteria) this;
        }

        public Criteria andBreakUvPpIn(List<Double> values) {
            addCriterion("break_uv_pp in", values, "breakUvPp");
            return (Criteria) this;
        }

        public Criteria andBreakUvPpNotIn(List<Double> values) {
            addCriterion("break_uv_pp not in", values, "breakUvPp");
            return (Criteria) this;
        }

        public Criteria andBreakUvPpBetween(Double value1, Double value2) {
            addCriterion("break_uv_pp between", value1, value2, "breakUvPp");
            return (Criteria) this;
        }

        public Criteria andBreakUvPpNotBetween(Double value1, Double value2) {
            addCriterion("break_uv_pp not between", value1, value2, "breakUvPp");
            return (Criteria) this;
        }

        public Criteria andBreakUvPpRrIsNull() {
            addCriterion("break_uv_pp_rr is null");
            return (Criteria) this;
        }

        public Criteria andBreakUvPpRrIsNotNull() {
            addCriterion("break_uv_pp_rr is not null");
            return (Criteria) this;
        }

        public Criteria andBreakUvPpRrEqualTo(Double value) {
            addCriterion("break_uv_pp_rr =", value, "breakUvPpRr");
            return (Criteria) this;
        }

        public Criteria andBreakUvPpRrNotEqualTo(Double value) {
            addCriterion("break_uv_pp_rr <>", value, "breakUvPpRr");
            return (Criteria) this;
        }

        public Criteria andBreakUvPpRrGreaterThan(Double value) {
            addCriterion("break_uv_pp_rr >", value, "breakUvPpRr");
            return (Criteria) this;
        }

        public Criteria andBreakUvPpRrGreaterThanOrEqualTo(Double value) {
            addCriterion("break_uv_pp_rr >=", value, "breakUvPpRr");
            return (Criteria) this;
        }

        public Criteria andBreakUvPpRrLessThan(Double value) {
            addCriterion("break_uv_pp_rr <", value, "breakUvPpRr");
            return (Criteria) this;
        }

        public Criteria andBreakUvPpRrLessThanOrEqualTo(Double value) {
            addCriterion("break_uv_pp_rr <=", value, "breakUvPpRr");
            return (Criteria) this;
        }

        public Criteria andBreakUvPpRrIn(List<Double> values) {
            addCriterion("break_uv_pp_rr in", values, "breakUvPpRr");
            return (Criteria) this;
        }

        public Criteria andBreakUvPpRrNotIn(List<Double> values) {
            addCriterion("break_uv_pp_rr not in", values, "breakUvPpRr");
            return (Criteria) this;
        }

        public Criteria andBreakUvPpRrBetween(Double value1, Double value2) {
            addCriterion("break_uv_pp_rr between", value1, value2, "breakUvPpRr");
            return (Criteria) this;
        }

        public Criteria andBreakUvPpRrNotBetween(Double value1, Double value2) {
            addCriterion("break_uv_pp_rr not between", value1, value2, "breakUvPpRr");
            return (Criteria) this;
        }

        public Criteria andPsSAllIsNull() {
            addCriterion("ps_s_all is null");
            return (Criteria) this;
        }

        public Criteria andPsSAllIsNotNull() {
            addCriterion("ps_s_all is not null");
            return (Criteria) this;
        }

        public Criteria andPsSAllEqualTo(Integer value) {
            addCriterion("ps_s_all =", value, "psSAll");
            return (Criteria) this;
        }

        public Criteria andPsSAllNotEqualTo(Integer value) {
            addCriterion("ps_s_all <>", value, "psSAll");
            return (Criteria) this;
        }

        public Criteria andPsSAllGreaterThan(Integer value) {
            addCriterion("ps_s_all >", value, "psSAll");
            return (Criteria) this;
        }

        public Criteria andPsSAllGreaterThanOrEqualTo(Integer value) {
            addCriterion("ps_s_all >=", value, "psSAll");
            return (Criteria) this;
        }

        public Criteria andPsSAllLessThan(Integer value) {
            addCriterion("ps_s_all <", value, "psSAll");
            return (Criteria) this;
        }

        public Criteria andPsSAllLessThanOrEqualTo(Integer value) {
            addCriterion("ps_s_all <=", value, "psSAll");
            return (Criteria) this;
        }

        public Criteria andPsSAllIn(List<Integer> values) {
            addCriterion("ps_s_all in", values, "psSAll");
            return (Criteria) this;
        }

        public Criteria andPsSAllNotIn(List<Integer> values) {
            addCriterion("ps_s_all not in", values, "psSAll");
            return (Criteria) this;
        }

        public Criteria andPsSAllBetween(Integer value1, Integer value2) {
            addCriterion("ps_s_all between", value1, value2, "psSAll");
            return (Criteria) this;
        }

        public Criteria andPsSAllNotBetween(Integer value1, Integer value2) {
            addCriterion("ps_s_all not between", value1, value2, "psSAll");
            return (Criteria) this;
        }

        public Criteria andPsSAllRrIsNull() {
            addCriterion("ps_s_all_rr is null");
            return (Criteria) this;
        }

        public Criteria andPsSAllRrIsNotNull() {
            addCriterion("ps_s_all_rr is not null");
            return (Criteria) this;
        }

        public Criteria andPsSAllRrEqualTo(Integer value) {
            addCriterion("ps_s_all_rr =", value, "psSAllRr");
            return (Criteria) this;
        }

        public Criteria andPsSAllRrNotEqualTo(Integer value) {
            addCriterion("ps_s_all_rr <>", value, "psSAllRr");
            return (Criteria) this;
        }

        public Criteria andPsSAllRrGreaterThan(Integer value) {
            addCriterion("ps_s_all_rr >", value, "psSAllRr");
            return (Criteria) this;
        }

        public Criteria andPsSAllRrGreaterThanOrEqualTo(Integer value) {
            addCriterion("ps_s_all_rr >=", value, "psSAllRr");
            return (Criteria) this;
        }

        public Criteria andPsSAllRrLessThan(Integer value) {
            addCriterion("ps_s_all_rr <", value, "psSAllRr");
            return (Criteria) this;
        }

        public Criteria andPsSAllRrLessThanOrEqualTo(Integer value) {
            addCriterion("ps_s_all_rr <=", value, "psSAllRr");
            return (Criteria) this;
        }

        public Criteria andPsSAllRrIn(List<Integer> values) {
            addCriterion("ps_s_all_rr in", values, "psSAllRr");
            return (Criteria) this;
        }

        public Criteria andPsSAllRrNotIn(List<Integer> values) {
            addCriterion("ps_s_all_rr not in", values, "psSAllRr");
            return (Criteria) this;
        }

        public Criteria andPsSAllRrBetween(Integer value1, Integer value2) {
            addCriterion("ps_s_all_rr between", value1, value2, "psSAllRr");
            return (Criteria) this;
        }

        public Criteria andPsSAllRrNotBetween(Integer value1, Integer value2) {
            addCriterion("ps_s_all_rr not between", value1, value2, "psSAllRr");
            return (Criteria) this;
        }

        public Criteria andPsSMonthDiamondIsNull() {
            addCriterion("ps_s_month_diamond is null");
            return (Criteria) this;
        }

        public Criteria andPsSMonthDiamondIsNotNull() {
            addCriterion("ps_s_month_diamond is not null");
            return (Criteria) this;
        }

        public Criteria andPsSMonthDiamondEqualTo(Integer value) {
            addCriterion("ps_s_month_diamond =", value, "psSMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andPsSMonthDiamondNotEqualTo(Integer value) {
            addCriterion("ps_s_month_diamond <>", value, "psSMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andPsSMonthDiamondGreaterThan(Integer value) {
            addCriterion("ps_s_month_diamond >", value, "psSMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andPsSMonthDiamondGreaterThanOrEqualTo(Integer value) {
            addCriterion("ps_s_month_diamond >=", value, "psSMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andPsSMonthDiamondLessThan(Integer value) {
            addCriterion("ps_s_month_diamond <", value, "psSMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andPsSMonthDiamondLessThanOrEqualTo(Integer value) {
            addCriterion("ps_s_month_diamond <=", value, "psSMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andPsSMonthDiamondIn(List<Integer> values) {
            addCriterion("ps_s_month_diamond in", values, "psSMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andPsSMonthDiamondNotIn(List<Integer> values) {
            addCriterion("ps_s_month_diamond not in", values, "psSMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andPsSMonthDiamondBetween(Integer value1, Integer value2) {
            addCriterion("ps_s_month_diamond between", value1, value2, "psSMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andPsSMonthDiamondNotBetween(Integer value1, Integer value2) {
            addCriterion("ps_s_month_diamond not between", value1, value2, "psSMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andSprMonthDiamondIsNull() {
            addCriterion("spr_month_diamond is null");
            return (Criteria) this;
        }

        public Criteria andSprMonthDiamondIsNotNull() {
            addCriterion("spr_month_diamond is not null");
            return (Criteria) this;
        }

        public Criteria andSprMonthDiamondEqualTo(Double value) {
            addCriterion("spr_month_diamond =", value, "sprMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andSprMonthDiamondNotEqualTo(Double value) {
            addCriterion("spr_month_diamond <>", value, "sprMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andSprMonthDiamondGreaterThan(Double value) {
            addCriterion("spr_month_diamond >", value, "sprMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andSprMonthDiamondGreaterThanOrEqualTo(Double value) {
            addCriterion("spr_month_diamond >=", value, "sprMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andSprMonthDiamondLessThan(Double value) {
            addCriterion("spr_month_diamond <", value, "sprMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andSprMonthDiamondLessThanOrEqualTo(Double value) {
            addCriterion("spr_month_diamond <=", value, "sprMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andSprMonthDiamondIn(List<Double> values) {
            addCriterion("spr_month_diamond in", values, "sprMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andSprMonthDiamondNotIn(List<Double> values) {
            addCriterion("spr_month_diamond not in", values, "sprMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andSprMonthDiamondBetween(Double value1, Double value2) {
            addCriterion("spr_month_diamond between", value1, value2, "sprMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andSprMonthDiamondNotBetween(Double value1, Double value2) {
            addCriterion("spr_month_diamond not between", value1, value2, "sprMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andPsSValidLiveUvIsNull() {
            addCriterion("ps_s_valid_live_uv is null");
            return (Criteria) this;
        }

        public Criteria andPsSValidLiveUvIsNotNull() {
            addCriterion("ps_s_valid_live_uv is not null");
            return (Criteria) this;
        }

        public Criteria andPsSValidLiveUvEqualTo(Integer value) {
            addCriterion("ps_s_valid_live_uv =", value, "psSValidLiveUv");
            return (Criteria) this;
        }

        public Criteria andPsSValidLiveUvNotEqualTo(Integer value) {
            addCriterion("ps_s_valid_live_uv <>", value, "psSValidLiveUv");
            return (Criteria) this;
        }

        public Criteria andPsSValidLiveUvGreaterThan(Integer value) {
            addCriterion("ps_s_valid_live_uv >", value, "psSValidLiveUv");
            return (Criteria) this;
        }

        public Criteria andPsSValidLiveUvGreaterThanOrEqualTo(Integer value) {
            addCriterion("ps_s_valid_live_uv >=", value, "psSValidLiveUv");
            return (Criteria) this;
        }

        public Criteria andPsSValidLiveUvLessThan(Integer value) {
            addCriterion("ps_s_valid_live_uv <", value, "psSValidLiveUv");
            return (Criteria) this;
        }

        public Criteria andPsSValidLiveUvLessThanOrEqualTo(Integer value) {
            addCriterion("ps_s_valid_live_uv <=", value, "psSValidLiveUv");
            return (Criteria) this;
        }

        public Criteria andPsSValidLiveUvIn(List<Integer> values) {
            addCriterion("ps_s_valid_live_uv in", values, "psSValidLiveUv");
            return (Criteria) this;
        }

        public Criteria andPsSValidLiveUvNotIn(List<Integer> values) {
            addCriterion("ps_s_valid_live_uv not in", values, "psSValidLiveUv");
            return (Criteria) this;
        }

        public Criteria andPsSValidLiveUvBetween(Integer value1, Integer value2) {
            addCriterion("ps_s_valid_live_uv between", value1, value2, "psSValidLiveUv");
            return (Criteria) this;
        }

        public Criteria andPsSValidLiveUvNotBetween(Integer value1, Integer value2) {
            addCriterion("ps_s_valid_live_uv not between", value1, value2, "psSValidLiveUv");
            return (Criteria) this;
        }

        public Criteria andSprValidLiveUvIsNull() {
            addCriterion("spr_valid_live_uv is null");
            return (Criteria) this;
        }

        public Criteria andSprValidLiveUvIsNotNull() {
            addCriterion("spr_valid_live_uv is not null");
            return (Criteria) this;
        }

        public Criteria andSprValidLiveUvEqualTo(Double value) {
            addCriterion("spr_valid_live_uv =", value, "sprValidLiveUv");
            return (Criteria) this;
        }

        public Criteria andSprValidLiveUvNotEqualTo(Double value) {
            addCriterion("spr_valid_live_uv <>", value, "sprValidLiveUv");
            return (Criteria) this;
        }

        public Criteria andSprValidLiveUvGreaterThan(Double value) {
            addCriterion("spr_valid_live_uv >", value, "sprValidLiveUv");
            return (Criteria) this;
        }

        public Criteria andSprValidLiveUvGreaterThanOrEqualTo(Double value) {
            addCriterion("spr_valid_live_uv >=", value, "sprValidLiveUv");
            return (Criteria) this;
        }

        public Criteria andSprValidLiveUvLessThan(Double value) {
            addCriterion("spr_valid_live_uv <", value, "sprValidLiveUv");
            return (Criteria) this;
        }

        public Criteria andSprValidLiveUvLessThanOrEqualTo(Double value) {
            addCriterion("spr_valid_live_uv <=", value, "sprValidLiveUv");
            return (Criteria) this;
        }

        public Criteria andSprValidLiveUvIn(List<Double> values) {
            addCriterion("spr_valid_live_uv in", values, "sprValidLiveUv");
            return (Criteria) this;
        }

        public Criteria andSprValidLiveUvNotIn(List<Double> values) {
            addCriterion("spr_valid_live_uv not in", values, "sprValidLiveUv");
            return (Criteria) this;
        }

        public Criteria andSprValidLiveUvBetween(Double value1, Double value2) {
            addCriterion("spr_valid_live_uv between", value1, value2, "sprValidLiveUv");
            return (Criteria) this;
        }

        public Criteria andSprValidLiveUvNotBetween(Double value1, Double value2) {
            addCriterion("spr_valid_live_uv not between", value1, value2, "sprValidLiveUv");
            return (Criteria) this;
        }

        public Criteria andPsAcuIsNull() {
            addCriterion("ps_acu is null");
            return (Criteria) this;
        }

        public Criteria andPsAcuIsNotNull() {
            addCriterion("ps_acu is not null");
            return (Criteria) this;
        }

        public Criteria andPsAcuEqualTo(Double value) {
            addCriterion("ps_acu =", value, "psAcu");
            return (Criteria) this;
        }

        public Criteria andPsAcuNotEqualTo(Double value) {
            addCriterion("ps_acu <>", value, "psAcu");
            return (Criteria) this;
        }

        public Criteria andPsAcuGreaterThan(Double value) {
            addCriterion("ps_acu >", value, "psAcu");
            return (Criteria) this;
        }

        public Criteria andPsAcuGreaterThanOrEqualTo(Double value) {
            addCriterion("ps_acu >=", value, "psAcu");
            return (Criteria) this;
        }

        public Criteria andPsAcuLessThan(Double value) {
            addCriterion("ps_acu <", value, "psAcu");
            return (Criteria) this;
        }

        public Criteria andPsAcuLessThanOrEqualTo(Double value) {
            addCriterion("ps_acu <=", value, "psAcu");
            return (Criteria) this;
        }

        public Criteria andPsAcuIn(List<Double> values) {
            addCriterion("ps_acu in", values, "psAcu");
            return (Criteria) this;
        }

        public Criteria andPsAcuNotIn(List<Double> values) {
            addCriterion("ps_acu not in", values, "psAcu");
            return (Criteria) this;
        }

        public Criteria andPsAcuBetween(Double value1, Double value2) {
            addCriterion("ps_acu between", value1, value2, "psAcu");
            return (Criteria) this;
        }

        public Criteria andPsAcuNotBetween(Double value1, Double value2) {
            addCriterion("ps_acu not between", value1, value2, "psAcu");
            return (Criteria) this;
        }

        public Criteria andPsSAcuIsNull() {
            addCriterion("ps_s_acu is null");
            return (Criteria) this;
        }

        public Criteria andPsSAcuIsNotNull() {
            addCriterion("ps_s_acu is not null");
            return (Criteria) this;
        }

        public Criteria andPsSAcuEqualTo(Integer value) {
            addCriterion("ps_s_acu =", value, "psSAcu");
            return (Criteria) this;
        }

        public Criteria andPsSAcuNotEqualTo(Integer value) {
            addCriterion("ps_s_acu <>", value, "psSAcu");
            return (Criteria) this;
        }

        public Criteria andPsSAcuGreaterThan(Integer value) {
            addCriterion("ps_s_acu >", value, "psSAcu");
            return (Criteria) this;
        }

        public Criteria andPsSAcuGreaterThanOrEqualTo(Integer value) {
            addCriterion("ps_s_acu >=", value, "psSAcu");
            return (Criteria) this;
        }

        public Criteria andPsSAcuLessThan(Integer value) {
            addCriterion("ps_s_acu <", value, "psSAcu");
            return (Criteria) this;
        }

        public Criteria andPsSAcuLessThanOrEqualTo(Integer value) {
            addCriterion("ps_s_acu <=", value, "psSAcu");
            return (Criteria) this;
        }

        public Criteria andPsSAcuIn(List<Integer> values) {
            addCriterion("ps_s_acu in", values, "psSAcu");
            return (Criteria) this;
        }

        public Criteria andPsSAcuNotIn(List<Integer> values) {
            addCriterion("ps_s_acu not in", values, "psSAcu");
            return (Criteria) this;
        }

        public Criteria andPsSAcuBetween(Integer value1, Integer value2) {
            addCriterion("ps_s_acu between", value1, value2, "psSAcu");
            return (Criteria) this;
        }

        public Criteria andPsSAcuNotBetween(Integer value1, Integer value2) {
            addCriterion("ps_s_acu not between", value1, value2, "psSAcu");
            return (Criteria) this;
        }

        public Criteria andSprAcuIsNull() {
            addCriterion("spr_acu is null");
            return (Criteria) this;
        }

        public Criteria andSprAcuIsNotNull() {
            addCriterion("spr_acu is not null");
            return (Criteria) this;
        }

        public Criteria andSprAcuEqualTo(Double value) {
            addCriterion("spr_acu =", value, "sprAcu");
            return (Criteria) this;
        }

        public Criteria andSprAcuNotEqualTo(Double value) {
            addCriterion("spr_acu <>", value, "sprAcu");
            return (Criteria) this;
        }

        public Criteria andSprAcuGreaterThan(Double value) {
            addCriterion("spr_acu >", value, "sprAcu");
            return (Criteria) this;
        }

        public Criteria andSprAcuGreaterThanOrEqualTo(Double value) {
            addCriterion("spr_acu >=", value, "sprAcu");
            return (Criteria) this;
        }

        public Criteria andSprAcuLessThan(Double value) {
            addCriterion("spr_acu <", value, "sprAcu");
            return (Criteria) this;
        }

        public Criteria andSprAcuLessThanOrEqualTo(Double value) {
            addCriterion("spr_acu <=", value, "sprAcu");
            return (Criteria) this;
        }

        public Criteria andSprAcuIn(List<Double> values) {
            addCriterion("spr_acu in", values, "sprAcu");
            return (Criteria) this;
        }

        public Criteria andSprAcuNotIn(List<Double> values) {
            addCriterion("spr_acu not in", values, "sprAcu");
            return (Criteria) this;
        }

        public Criteria andSprAcuBetween(Double value1, Double value2) {
            addCriterion("spr_acu between", value1, value2, "sprAcu");
            return (Criteria) this;
        }

        public Criteria andSprAcuNotBetween(Double value1, Double value2) {
            addCriterion("spr_acu not between", value1, value2, "sprAcu");
            return (Criteria) this;
        }

        public Criteria andPsSLiveUvIsNull() {
            addCriterion("ps_s_live_uv is null");
            return (Criteria) this;
        }

        public Criteria andPsSLiveUvIsNotNull() {
            addCriterion("ps_s_live_uv is not null");
            return (Criteria) this;
        }

        public Criteria andPsSLiveUvEqualTo(Integer value) {
            addCriterion("ps_s_live_uv =", value, "psSLiveUv");
            return (Criteria) this;
        }

        public Criteria andPsSLiveUvNotEqualTo(Integer value) {
            addCriterion("ps_s_live_uv <>", value, "psSLiveUv");
            return (Criteria) this;
        }

        public Criteria andPsSLiveUvGreaterThan(Integer value) {
            addCriterion("ps_s_live_uv >", value, "psSLiveUv");
            return (Criteria) this;
        }

        public Criteria andPsSLiveUvGreaterThanOrEqualTo(Integer value) {
            addCriterion("ps_s_live_uv >=", value, "psSLiveUv");
            return (Criteria) this;
        }

        public Criteria andPsSLiveUvLessThan(Integer value) {
            addCriterion("ps_s_live_uv <", value, "psSLiveUv");
            return (Criteria) this;
        }

        public Criteria andPsSLiveUvLessThanOrEqualTo(Integer value) {
            addCriterion("ps_s_live_uv <=", value, "psSLiveUv");
            return (Criteria) this;
        }

        public Criteria andPsSLiveUvIn(List<Integer> values) {
            addCriterion("ps_s_live_uv in", values, "psSLiveUv");
            return (Criteria) this;
        }

        public Criteria andPsSLiveUvNotIn(List<Integer> values) {
            addCriterion("ps_s_live_uv not in", values, "psSLiveUv");
            return (Criteria) this;
        }

        public Criteria andPsSLiveUvBetween(Integer value1, Integer value2) {
            addCriterion("ps_s_live_uv between", value1, value2, "psSLiveUv");
            return (Criteria) this;
        }

        public Criteria andPsSLiveUvNotBetween(Integer value1, Integer value2) {
            addCriterion("ps_s_live_uv not between", value1, value2, "psSLiveUv");
            return (Criteria) this;
        }

        public Criteria andPsSBreakUvPpIsNull() {
            addCriterion("ps_s_break_uv_pp is null");
            return (Criteria) this;
        }

        public Criteria andPsSBreakUvPpIsNotNull() {
            addCriterion("ps_s_break_uv_pp is not null");
            return (Criteria) this;
        }

        public Criteria andPsSBreakUvPpEqualTo(Integer value) {
            addCriterion("ps_s_break_uv_pp =", value, "psSBreakUvPp");
            return (Criteria) this;
        }

        public Criteria andPsSBreakUvPpNotEqualTo(Integer value) {
            addCriterion("ps_s_break_uv_pp <>", value, "psSBreakUvPp");
            return (Criteria) this;
        }

        public Criteria andPsSBreakUvPpGreaterThan(Integer value) {
            addCriterion("ps_s_break_uv_pp >", value, "psSBreakUvPp");
            return (Criteria) this;
        }

        public Criteria andPsSBreakUvPpGreaterThanOrEqualTo(Integer value) {
            addCriterion("ps_s_break_uv_pp >=", value, "psSBreakUvPp");
            return (Criteria) this;
        }

        public Criteria andPsSBreakUvPpLessThan(Integer value) {
            addCriterion("ps_s_break_uv_pp <", value, "psSBreakUvPp");
            return (Criteria) this;
        }

        public Criteria andPsSBreakUvPpLessThanOrEqualTo(Integer value) {
            addCriterion("ps_s_break_uv_pp <=", value, "psSBreakUvPp");
            return (Criteria) this;
        }

        public Criteria andPsSBreakUvPpIn(List<Integer> values) {
            addCriterion("ps_s_break_uv_pp in", values, "psSBreakUvPp");
            return (Criteria) this;
        }

        public Criteria andPsSBreakUvPpNotIn(List<Integer> values) {
            addCriterion("ps_s_break_uv_pp not in", values, "psSBreakUvPp");
            return (Criteria) this;
        }

        public Criteria andPsSBreakUvPpBetween(Integer value1, Integer value2) {
            addCriterion("ps_s_break_uv_pp between", value1, value2, "psSBreakUvPp");
            return (Criteria) this;
        }

        public Criteria andPsSBreakUvPpNotBetween(Integer value1, Integer value2) {
            addCriterion("ps_s_break_uv_pp not between", value1, value2, "psSBreakUvPp");
            return (Criteria) this;
        }

        public Criteria andSprBreakUvPpIsNull() {
            addCriterion("spr_break_uv_pp is null");
            return (Criteria) this;
        }

        public Criteria andSprBreakUvPpIsNotNull() {
            addCriterion("spr_break_uv_pp is not null");
            return (Criteria) this;
        }

        public Criteria andSprBreakUvPpEqualTo(Double value) {
            addCriterion("spr_break_uv_pp =", value, "sprBreakUvPp");
            return (Criteria) this;
        }

        public Criteria andSprBreakUvPpNotEqualTo(Double value) {
            addCriterion("spr_break_uv_pp <>", value, "sprBreakUvPp");
            return (Criteria) this;
        }

        public Criteria andSprBreakUvPpGreaterThan(Double value) {
            addCriterion("spr_break_uv_pp >", value, "sprBreakUvPp");
            return (Criteria) this;
        }

        public Criteria andSprBreakUvPpGreaterThanOrEqualTo(Double value) {
            addCriterion("spr_break_uv_pp >=", value, "sprBreakUvPp");
            return (Criteria) this;
        }

        public Criteria andSprBreakUvPpLessThan(Double value) {
            addCriterion("spr_break_uv_pp <", value, "sprBreakUvPp");
            return (Criteria) this;
        }

        public Criteria andSprBreakUvPpLessThanOrEqualTo(Double value) {
            addCriterion("spr_break_uv_pp <=", value, "sprBreakUvPp");
            return (Criteria) this;
        }

        public Criteria andSprBreakUvPpIn(List<Double> values) {
            addCriterion("spr_break_uv_pp in", values, "sprBreakUvPp");
            return (Criteria) this;
        }

        public Criteria andSprBreakUvPpNotIn(List<Double> values) {
            addCriterion("spr_break_uv_pp not in", values, "sprBreakUvPp");
            return (Criteria) this;
        }

        public Criteria andSprBreakUvPpBetween(Double value1, Double value2) {
            addCriterion("spr_break_uv_pp between", value1, value2, "sprBreakUvPp");
            return (Criteria) this;
        }

        public Criteria andSprBreakUvPpNotBetween(Double value1, Double value2) {
            addCriterion("spr_break_uv_pp not between", value1, value2, "sprBreakUvPp");
            return (Criteria) this;
        }

        public Criteria andDtIsNull() {
            addCriterion("dt is null");
            return (Criteria) this;
        }

        public Criteria andDtIsNotNull() {
            addCriterion("dt is not null");
            return (Criteria) this;
        }

        public Criteria andDtEqualTo(String value) {
            addCriterion("dt =", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotEqualTo(String value) {
            addCriterion("dt <>", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtGreaterThan(String value) {
            addCriterion("dt >", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtGreaterThanOrEqualTo(String value) {
            addCriterion("dt >=", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtLessThan(String value) {
            addCriterion("dt <", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtLessThanOrEqualTo(String value) {
            addCriterion("dt <=", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtLike(String value) {
            addCriterion("dt like", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotLike(String value) {
            addCriterion("dt not like", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtIn(List<String> values) {
            addCriterion("dt in", values, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotIn(List<String> values) {
            addCriterion("dt not in", values, "dt");
            return (Criteria) this;
        }

        public Criteria andDtBetween(String value1, String value2) {
            addCriterion("dt between", value1, value2, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotBetween(String value1, String value2) {
            addCriterion("dt not between", value1, value2, "dt");
            return (Criteria) this;
        }

        public Criteria andAvgHighAidNumIsNull() {
            addCriterion("avg_high_aid_num is null");
            return (Criteria) this;
        }

        public Criteria andAvgHighAidNumIsNotNull() {
            addCriterion("avg_high_aid_num is not null");
            return (Criteria) this;
        }

        public Criteria andAvgHighAidNumEqualTo(Double value) {
            addCriterion("avg_high_aid_num =", value, "avgHighAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgHighAidNumNotEqualTo(Double value) {
            addCriterion("avg_high_aid_num <>", value, "avgHighAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgHighAidNumGreaterThan(Double value) {
            addCriterion("avg_high_aid_num >", value, "avgHighAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgHighAidNumGreaterThanOrEqualTo(Double value) {
            addCriterion("avg_high_aid_num >=", value, "avgHighAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgHighAidNumLessThan(Double value) {
            addCriterion("avg_high_aid_num <", value, "avgHighAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgHighAidNumLessThanOrEqualTo(Double value) {
            addCriterion("avg_high_aid_num <=", value, "avgHighAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgHighAidNumIn(List<Double> values) {
            addCriterion("avg_high_aid_num in", values, "avgHighAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgHighAidNumNotIn(List<Double> values) {
            addCriterion("avg_high_aid_num not in", values, "avgHighAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgHighAidNumBetween(Double value1, Double value2) {
            addCriterion("avg_high_aid_num between", value1, value2, "avgHighAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgHighAidNumNotBetween(Double value1, Double value2) {
            addCriterion("avg_high_aid_num not between", value1, value2, "avgHighAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgHighAidNumRrIsNull() {
            addCriterion("avg_high_aid_num_rr is null");
            return (Criteria) this;
        }

        public Criteria andAvgHighAidNumRrIsNotNull() {
            addCriterion("avg_high_aid_num_rr is not null");
            return (Criteria) this;
        }

        public Criteria andAvgHighAidNumRrEqualTo(Double value) {
            addCriterion("avg_high_aid_num_rr =", value, "avgHighAidNumRr");
            return (Criteria) this;
        }

        public Criteria andAvgHighAidNumRrNotEqualTo(Double value) {
            addCriterion("avg_high_aid_num_rr <>", value, "avgHighAidNumRr");
            return (Criteria) this;
        }

        public Criteria andAvgHighAidNumRrGreaterThan(Double value) {
            addCriterion("avg_high_aid_num_rr >", value, "avgHighAidNumRr");
            return (Criteria) this;
        }

        public Criteria andAvgHighAidNumRrGreaterThanOrEqualTo(Double value) {
            addCriterion("avg_high_aid_num_rr >=", value, "avgHighAidNumRr");
            return (Criteria) this;
        }

        public Criteria andAvgHighAidNumRrLessThan(Double value) {
            addCriterion("avg_high_aid_num_rr <", value, "avgHighAidNumRr");
            return (Criteria) this;
        }

        public Criteria andAvgHighAidNumRrLessThanOrEqualTo(Double value) {
            addCriterion("avg_high_aid_num_rr <=", value, "avgHighAidNumRr");
            return (Criteria) this;
        }

        public Criteria andAvgHighAidNumRrIn(List<Double> values) {
            addCriterion("avg_high_aid_num_rr in", values, "avgHighAidNumRr");
            return (Criteria) this;
        }

        public Criteria andAvgHighAidNumRrNotIn(List<Double> values) {
            addCriterion("avg_high_aid_num_rr not in", values, "avgHighAidNumRr");
            return (Criteria) this;
        }

        public Criteria andAvgHighAidNumRrBetween(Double value1, Double value2) {
            addCriterion("avg_high_aid_num_rr between", value1, value2, "avgHighAidNumRr");
            return (Criteria) this;
        }

        public Criteria andAvgHighAidNumRrNotBetween(Double value1, Double value2) {
            addCriterion("avg_high_aid_num_rr not between", value1, value2, "avgHighAidNumRr");
            return (Criteria) this;
        }

        public Criteria andAvgWaistAidNumIsNull() {
            addCriterion("avg_waist_aid_num is null");
            return (Criteria) this;
        }

        public Criteria andAvgWaistAidNumIsNotNull() {
            addCriterion("avg_waist_aid_num is not null");
            return (Criteria) this;
        }

        public Criteria andAvgWaistAidNumEqualTo(Double value) {
            addCriterion("avg_waist_aid_num =", value, "avgWaistAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgWaistAidNumNotEqualTo(Double value) {
            addCriterion("avg_waist_aid_num <>", value, "avgWaistAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgWaistAidNumGreaterThan(Double value) {
            addCriterion("avg_waist_aid_num >", value, "avgWaistAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgWaistAidNumGreaterThanOrEqualTo(Double value) {
            addCriterion("avg_waist_aid_num >=", value, "avgWaistAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgWaistAidNumLessThan(Double value) {
            addCriterion("avg_waist_aid_num <", value, "avgWaistAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgWaistAidNumLessThanOrEqualTo(Double value) {
            addCriterion("avg_waist_aid_num <=", value, "avgWaistAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgWaistAidNumIn(List<Double> values) {
            addCriterion("avg_waist_aid_num in", values, "avgWaistAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgWaistAidNumNotIn(List<Double> values) {
            addCriterion("avg_waist_aid_num not in", values, "avgWaistAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgWaistAidNumBetween(Double value1, Double value2) {
            addCriterion("avg_waist_aid_num between", value1, value2, "avgWaistAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgWaistAidNumNotBetween(Double value1, Double value2) {
            addCriterion("avg_waist_aid_num not between", value1, value2, "avgWaistAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgWaistAidNumRrIsNull() {
            addCriterion("avg_waist_aid_num_rr is null");
            return (Criteria) this;
        }

        public Criteria andAvgWaistAidNumRrIsNotNull() {
            addCriterion("avg_waist_aid_num_rr is not null");
            return (Criteria) this;
        }

        public Criteria andAvgWaistAidNumRrEqualTo(Double value) {
            addCriterion("avg_waist_aid_num_rr =", value, "avgWaistAidNumRr");
            return (Criteria) this;
        }

        public Criteria andAvgWaistAidNumRrNotEqualTo(Double value) {
            addCriterion("avg_waist_aid_num_rr <>", value, "avgWaistAidNumRr");
            return (Criteria) this;
        }

        public Criteria andAvgWaistAidNumRrGreaterThan(Double value) {
            addCriterion("avg_waist_aid_num_rr >", value, "avgWaistAidNumRr");
            return (Criteria) this;
        }

        public Criteria andAvgWaistAidNumRrGreaterThanOrEqualTo(Double value) {
            addCriterion("avg_waist_aid_num_rr >=", value, "avgWaistAidNumRr");
            return (Criteria) this;
        }

        public Criteria andAvgWaistAidNumRrLessThan(Double value) {
            addCriterion("avg_waist_aid_num_rr <", value, "avgWaistAidNumRr");
            return (Criteria) this;
        }

        public Criteria andAvgWaistAidNumRrLessThanOrEqualTo(Double value) {
            addCriterion("avg_waist_aid_num_rr <=", value, "avgWaistAidNumRr");
            return (Criteria) this;
        }

        public Criteria andAvgWaistAidNumRrIn(List<Double> values) {
            addCriterion("avg_waist_aid_num_rr in", values, "avgWaistAidNumRr");
            return (Criteria) this;
        }

        public Criteria andAvgWaistAidNumRrNotIn(List<Double> values) {
            addCriterion("avg_waist_aid_num_rr not in", values, "avgWaistAidNumRr");
            return (Criteria) this;
        }

        public Criteria andAvgWaistAidNumRrBetween(Double value1, Double value2) {
            addCriterion("avg_waist_aid_num_rr between", value1, value2, "avgWaistAidNumRr");
            return (Criteria) this;
        }

        public Criteria andAvgWaistAidNumRrNotBetween(Double value1, Double value2) {
            addCriterion("avg_waist_aid_num_rr not between", value1, value2, "avgWaistAidNumRr");
            return (Criteria) this;
        }

        public Criteria andAvgTailAidNumIsNull() {
            addCriterion("avg_tail_aid_num is null");
            return (Criteria) this;
        }

        public Criteria andAvgTailAidNumIsNotNull() {
            addCriterion("avg_tail_aid_num is not null");
            return (Criteria) this;
        }

        public Criteria andAvgTailAidNumEqualTo(Double value) {
            addCriterion("avg_tail_aid_num =", value, "avgTailAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgTailAidNumNotEqualTo(Double value) {
            addCriterion("avg_tail_aid_num <>", value, "avgTailAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgTailAidNumGreaterThan(Double value) {
            addCriterion("avg_tail_aid_num >", value, "avgTailAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgTailAidNumGreaterThanOrEqualTo(Double value) {
            addCriterion("avg_tail_aid_num >=", value, "avgTailAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgTailAidNumLessThan(Double value) {
            addCriterion("avg_tail_aid_num <", value, "avgTailAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgTailAidNumLessThanOrEqualTo(Double value) {
            addCriterion("avg_tail_aid_num <=", value, "avgTailAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgTailAidNumIn(List<Double> values) {
            addCriterion("avg_tail_aid_num in", values, "avgTailAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgTailAidNumNotIn(List<Double> values) {
            addCriterion("avg_tail_aid_num not in", values, "avgTailAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgTailAidNumBetween(Double value1, Double value2) {
            addCriterion("avg_tail_aid_num between", value1, value2, "avgTailAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgTailAidNumNotBetween(Double value1, Double value2) {
            addCriterion("avg_tail_aid_num not between", value1, value2, "avgTailAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgTailAidNumRrIsNull() {
            addCriterion("avg_tail_aid_num_rr is null");
            return (Criteria) this;
        }

        public Criteria andAvgTailAidNumRrIsNotNull() {
            addCriterion("avg_tail_aid_num_rr is not null");
            return (Criteria) this;
        }

        public Criteria andAvgTailAidNumRrEqualTo(Double value) {
            addCriterion("avg_tail_aid_num_rr =", value, "avgTailAidNumRr");
            return (Criteria) this;
        }

        public Criteria andAvgTailAidNumRrNotEqualTo(Double value) {
            addCriterion("avg_tail_aid_num_rr <>", value, "avgTailAidNumRr");
            return (Criteria) this;
        }

        public Criteria andAvgTailAidNumRrGreaterThan(Double value) {
            addCriterion("avg_tail_aid_num_rr >", value, "avgTailAidNumRr");
            return (Criteria) this;
        }

        public Criteria andAvgTailAidNumRrGreaterThanOrEqualTo(Double value) {
            addCriterion("avg_tail_aid_num_rr >=", value, "avgTailAidNumRr");
            return (Criteria) this;
        }

        public Criteria andAvgTailAidNumRrLessThan(Double value) {
            addCriterion("avg_tail_aid_num_rr <", value, "avgTailAidNumRr");
            return (Criteria) this;
        }

        public Criteria andAvgTailAidNumRrLessThanOrEqualTo(Double value) {
            addCriterion("avg_tail_aid_num_rr <=", value, "avgTailAidNumRr");
            return (Criteria) this;
        }

        public Criteria andAvgTailAidNumRrIn(List<Double> values) {
            addCriterion("avg_tail_aid_num_rr in", values, "avgTailAidNumRr");
            return (Criteria) this;
        }

        public Criteria andAvgTailAidNumRrNotIn(List<Double> values) {
            addCriterion("avg_tail_aid_num_rr not in", values, "avgTailAidNumRr");
            return (Criteria) this;
        }

        public Criteria andAvgTailAidNumRrBetween(Double value1, Double value2) {
            addCriterion("avg_tail_aid_num_rr between", value1, value2, "avgTailAidNumRr");
            return (Criteria) this;
        }

        public Criteria andAvgTailAidNumRrNotBetween(Double value1, Double value2) {
            addCriterion("avg_tail_aid_num_rr not between", value1, value2, "avgTailAidNumRr");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreIsNull() {
            addCriterion("aid_value_score is null");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreIsNotNull() {
            addCriterion("aid_value_score is not null");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreEqualTo(Double value) {
            addCriterion("aid_value_score =", value, "aidValueScore");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreNotEqualTo(Double value) {
            addCriterion("aid_value_score <>", value, "aidValueScore");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreGreaterThan(Double value) {
            addCriterion("aid_value_score >", value, "aidValueScore");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreGreaterThanOrEqualTo(Double value) {
            addCriterion("aid_value_score >=", value, "aidValueScore");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreLessThan(Double value) {
            addCriterion("aid_value_score <", value, "aidValueScore");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreLessThanOrEqualTo(Double value) {
            addCriterion("aid_value_score <=", value, "aidValueScore");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreIn(List<Double> values) {
            addCriterion("aid_value_score in", values, "aidValueScore");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreNotIn(List<Double> values) {
            addCriterion("aid_value_score not in", values, "aidValueScore");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreBetween(Double value1, Double value2) {
            addCriterion("aid_value_score between", value1, value2, "aidValueScore");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreNotBetween(Double value1, Double value2) {
            addCriterion("aid_value_score not between", value1, value2, "aidValueScore");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreStarLvlIsNull() {
            addCriterion("aid_value_score_star_lvl is null");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreStarLvlIsNotNull() {
            addCriterion("aid_value_score_star_lvl is not null");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreStarLvlEqualTo(Integer value) {
            addCriterion("aid_value_score_star_lvl =", value, "aidValueScoreStarLvl");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreStarLvlNotEqualTo(Integer value) {
            addCriterion("aid_value_score_star_lvl <>", value, "aidValueScoreStarLvl");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreStarLvlGreaterThan(Integer value) {
            addCriterion("aid_value_score_star_lvl >", value, "aidValueScoreStarLvl");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreStarLvlGreaterThanOrEqualTo(Integer value) {
            addCriterion("aid_value_score_star_lvl >=", value, "aidValueScoreStarLvl");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreStarLvlLessThan(Integer value) {
            addCriterion("aid_value_score_star_lvl <", value, "aidValueScoreStarLvl");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreStarLvlLessThanOrEqualTo(Integer value) {
            addCriterion("aid_value_score_star_lvl <=", value, "aidValueScoreStarLvl");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreStarLvlIn(List<Integer> values) {
            addCriterion("aid_value_score_star_lvl in", values, "aidValueScoreStarLvl");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreStarLvlNotIn(List<Integer> values) {
            addCriterion("aid_value_score_star_lvl not in", values, "aidValueScoreStarLvl");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreStarLvlBetween(Integer value1, Integer value2) {
            addCriterion("aid_value_score_star_lvl between", value1, value2, "aidValueScoreStarLvl");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreStarLvlNotBetween(Integer value1, Integer value2) {
            addCriterion("aid_value_score_star_lvl not between", value1, value2, "aidValueScoreStarLvl");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreSprintValueIsNull() {
            addCriterion("aid_value_score_sprint_value is null");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreSprintValueIsNotNull() {
            addCriterion("aid_value_score_sprint_value is not null");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreSprintValueEqualTo(Double value) {
            addCriterion("aid_value_score_sprint_value =", value, "aidValueScoreSprintValue");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreSprintValueNotEqualTo(Double value) {
            addCriterion("aid_value_score_sprint_value <>", value, "aidValueScoreSprintValue");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreSprintValueGreaterThan(Double value) {
            addCriterion("aid_value_score_sprint_value >", value, "aidValueScoreSprintValue");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreSprintValueGreaterThanOrEqualTo(Double value) {
            addCriterion("aid_value_score_sprint_value >=", value, "aidValueScoreSprintValue");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreSprintValueLessThan(Double value) {
            addCriterion("aid_value_score_sprint_value <", value, "aidValueScoreSprintValue");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreSprintValueLessThanOrEqualTo(Double value) {
            addCriterion("aid_value_score_sprint_value <=", value, "aidValueScoreSprintValue");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreSprintValueIn(List<Double> values) {
            addCriterion("aid_value_score_sprint_value in", values, "aidValueScoreSprintValue");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreSprintValueNotIn(List<Double> values) {
            addCriterion("aid_value_score_sprint_value not in", values, "aidValueScoreSprintValue");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreSprintValueBetween(Double value1, Double value2) {
            addCriterion("aid_value_score_sprint_value between", value1, value2, "aidValueScoreSprintValue");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreSprintValueNotBetween(Double value1, Double value2) {
            addCriterion("aid_value_score_sprint_value not between", value1, value2, "aidValueScoreSprintValue");
            return (Criteria) this;
        }

        public Criteria andIsMarsGuildIsNull() {
            addCriterion("is_mars_guild is null");
            return (Criteria) this;
        }

        public Criteria andIsMarsGuildIsNotNull() {
            addCriterion("is_mars_guild is not null");
            return (Criteria) this;
        }

        public Criteria andIsMarsGuildEqualTo(Integer value) {
            addCriterion("is_mars_guild =", value, "isMarsGuild");
            return (Criteria) this;
        }

        public Criteria andIsMarsGuildNotEqualTo(Integer value) {
            addCriterion("is_mars_guild <>", value, "isMarsGuild");
            return (Criteria) this;
        }

        public Criteria andIsMarsGuildGreaterThan(Integer value) {
            addCriterion("is_mars_guild >", value, "isMarsGuild");
            return (Criteria) this;
        }

        public Criteria andIsMarsGuildGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_mars_guild >=", value, "isMarsGuild");
            return (Criteria) this;
        }

        public Criteria andIsMarsGuildLessThan(Integer value) {
            addCriterion("is_mars_guild <", value, "isMarsGuild");
            return (Criteria) this;
        }

        public Criteria andIsMarsGuildLessThanOrEqualTo(Integer value) {
            addCriterion("is_mars_guild <=", value, "isMarsGuild");
            return (Criteria) this;
        }

        public Criteria andIsMarsGuildIn(List<Integer> values) {
            addCriterion("is_mars_guild in", values, "isMarsGuild");
            return (Criteria) this;
        }

        public Criteria andIsMarsGuildNotIn(List<Integer> values) {
            addCriterion("is_mars_guild not in", values, "isMarsGuild");
            return (Criteria) this;
        }

        public Criteria andIsMarsGuildBetween(Integer value1, Integer value2) {
            addCriterion("is_mars_guild between", value1, value2, "isMarsGuild");
            return (Criteria) this;
        }

        public Criteria andIsMarsGuildNotBetween(Integer value1, Integer value2) {
            addCriterion("is_mars_guild not between", value1, value2, "isMarsGuild");
            return (Criteria) this;
        }

        public Criteria andMarsSidOwneridIsNull() {
            addCriterion("mars_sid_ownerid is null");
            return (Criteria) this;
        }

        public Criteria andMarsSidOwneridIsNotNull() {
            addCriterion("mars_sid_ownerid is not null");
            return (Criteria) this;
        }

        public Criteria andMarsSidOwneridEqualTo(Long value) {
            addCriterion("mars_sid_ownerid =", value, "marsSidOwnerid");
            return (Criteria) this;
        }

        public Criteria andMarsSidOwneridNotEqualTo(Long value) {
            addCriterion("mars_sid_ownerid <>", value, "marsSidOwnerid");
            return (Criteria) this;
        }

        public Criteria andMarsSidOwneridGreaterThan(Long value) {
            addCriterion("mars_sid_ownerid >", value, "marsSidOwnerid");
            return (Criteria) this;
        }

        public Criteria andMarsSidOwneridGreaterThanOrEqualTo(Long value) {
            addCriterion("mars_sid_ownerid >=", value, "marsSidOwnerid");
            return (Criteria) this;
        }

        public Criteria andMarsSidOwneridLessThan(Long value) {
            addCriterion("mars_sid_ownerid <", value, "marsSidOwnerid");
            return (Criteria) this;
        }

        public Criteria andMarsSidOwneridLessThanOrEqualTo(Long value) {
            addCriterion("mars_sid_ownerid <=", value, "marsSidOwnerid");
            return (Criteria) this;
        }

        public Criteria andMarsSidOwneridIn(List<Long> values) {
            addCriterion("mars_sid_ownerid in", values, "marsSidOwnerid");
            return (Criteria) this;
        }

        public Criteria andMarsSidOwneridNotIn(List<Long> values) {
            addCriterion("mars_sid_ownerid not in", values, "marsSidOwnerid");
            return (Criteria) this;
        }

        public Criteria andMarsSidOwneridBetween(Long value1, Long value2) {
            addCriterion("mars_sid_ownerid between", value1, value2, "marsSidOwnerid");
            return (Criteria) this;
        }

        public Criteria andMarsSidOwneridNotBetween(Long value1, Long value2) {
            addCriterion("mars_sid_ownerid not between", value1, value2, "marsSidOwnerid");
            return (Criteria) this;
        }
    }

    /**
     * dm_yy_guild_segment_score_month
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * dm_yy_guild_segment_score_month null
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}