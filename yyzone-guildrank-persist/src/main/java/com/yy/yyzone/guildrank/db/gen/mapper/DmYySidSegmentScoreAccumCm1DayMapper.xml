<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yy.yyzone.guildrank.db.gen.mapper.DmYySidSegmentScoreAccumCm1DayMapper" >
  <resultMap id="BaseResultMap" type="com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreAccumCm1Day" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="sid_ownerid" property="sidOwnerid" jdbcType="BIGINT" />
    <result column="ps_all" property="psAll" jdbcType="DOUBLE" />
    <result column="grade" property="grade" jdbcType="VARCHAR" />
    <result column="grade_rn" property="gradeRn" jdbcType="INTEGER" />
    <result column="dt" property="dt" jdbcType="VARCHAR" />
    <result column="sid_owyyid" property="sidOwyyid" jdbcType="BIGINT" />
    <result column="ps_all_weight" property="psAllWeight" jdbcType="DOUBLE" />
    <result column="weight_ab" property="weightAb" jdbcType="DOUBLE" />
    <result column="new_uv_cm" property="newUvCm" jdbcType="BIGINT" />
    <result column="weight_a" property="weightA" jdbcType="DOUBLE" />
    <result column="h_act_uv" property="hActUv" jdbcType="BIGINT" />
    <result column="weight_b" property="weightB" jdbcType="DOUBLE" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, sid_ownerid, ps_all, grade, grade_rn, dt, sid_owyyid, ps_all_weight, weight_ab, 
    new_uv_cm, weight_a, h_act_uv, weight_b
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreAccumCm1DayExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from dm_yy_sid_segment_score_accum_cm_1_day
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit > 0" >
      limit ${limit}
    </if>
    <if test="offset > 0" >
      offset ${offset}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from dm_yy_sid_segment_score_accum_cm_1_day
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from dm_yy_sid_segment_score_accum_cm_1_day
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreAccumCm1DayExample" >
    delete from dm_yy_sid_segment_score_accum_cm_1_day
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreAccumCm1Day" useGeneratedKeys="true" keyProperty="id" >
    insert into dm_yy_sid_segment_score_accum_cm_1_day (sid_ownerid, ps_all, grade, 
      grade_rn, dt, sid_owyyid, 
      ps_all_weight, weight_ab, new_uv_cm, 
      weight_a, h_act_uv, weight_b
      )
    values (#{sidOwnerid,jdbcType=BIGINT}, #{psAll,jdbcType=DOUBLE}, #{grade,jdbcType=VARCHAR}, 
      #{gradeRn,jdbcType=INTEGER}, #{dt,jdbcType=VARCHAR}, #{sidOwyyid,jdbcType=BIGINT}, 
      #{psAllWeight,jdbcType=DOUBLE}, #{weightAb,jdbcType=DOUBLE}, #{newUvCm,jdbcType=BIGINT}, 
      #{weightA,jdbcType=DOUBLE}, #{hActUv,jdbcType=BIGINT}, #{weightB,jdbcType=DOUBLE}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreAccumCm1Day" useGeneratedKeys="true" keyProperty="id" >
    insert into dm_yy_sid_segment_score_accum_cm_1_day
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="sidOwnerid != null" >
        sid_ownerid,
      </if>
      <if test="psAll != null" >
        ps_all,
      </if>
      <if test="grade != null" >
        grade,
      </if>
      <if test="gradeRn != null" >
        grade_rn,
      </if>
      <if test="dt != null" >
        dt,
      </if>
      <if test="sidOwyyid != null" >
        sid_owyyid,
      </if>
      <if test="psAllWeight != null" >
        ps_all_weight,
      </if>
      <if test="weightAb != null" >
        weight_ab,
      </if>
      <if test="newUvCm != null" >
        new_uv_cm,
      </if>
      <if test="weightA != null" >
        weight_a,
      </if>
      <if test="hActUv != null" >
        h_act_uv,
      </if>
      <if test="weightB != null" >
        weight_b,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="sidOwnerid != null" >
        #{sidOwnerid,jdbcType=BIGINT},
      </if>
      <if test="psAll != null" >
        #{psAll,jdbcType=DOUBLE},
      </if>
      <if test="grade != null" >
        #{grade,jdbcType=VARCHAR},
      </if>
      <if test="gradeRn != null" >
        #{gradeRn,jdbcType=INTEGER},
      </if>
      <if test="dt != null" >
        #{dt,jdbcType=VARCHAR},
      </if>
      <if test="sidOwyyid != null" >
        #{sidOwyyid,jdbcType=BIGINT},
      </if>
      <if test="psAllWeight != null" >
        #{psAllWeight,jdbcType=DOUBLE},
      </if>
      <if test="weightAb != null" >
        #{weightAb,jdbcType=DOUBLE},
      </if>
      <if test="newUvCm != null" >
        #{newUvCm,jdbcType=BIGINT},
      </if>
      <if test="weightA != null" >
        #{weightA,jdbcType=DOUBLE},
      </if>
      <if test="hActUv != null" >
        #{hActUv,jdbcType=BIGINT},
      </if>
      <if test="weightB != null" >
        #{weightB,jdbcType=DOUBLE},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreAccumCm1DayExample" resultType="java.lang.Integer" >
    select count(*) from dm_yy_sid_segment_score_accum_cm_1_day
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update dm_yy_sid_segment_score_accum_cm_1_day
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.sidOwnerid != null" >
        sid_ownerid = #{record.sidOwnerid,jdbcType=BIGINT},
      </if>
      <if test="record.psAll != null" >
        ps_all = #{record.psAll,jdbcType=DOUBLE},
      </if>
      <if test="record.grade != null" >
        grade = #{record.grade,jdbcType=VARCHAR},
      </if>
      <if test="record.gradeRn != null" >
        grade_rn = #{record.gradeRn,jdbcType=INTEGER},
      </if>
      <if test="record.dt != null" >
        dt = #{record.dt,jdbcType=VARCHAR},
      </if>
      <if test="record.sidOwyyid != null" >
        sid_owyyid = #{record.sidOwyyid,jdbcType=BIGINT},
      </if>
      <if test="record.psAllWeight != null" >
        ps_all_weight = #{record.psAllWeight,jdbcType=DOUBLE},
      </if>
      <if test="record.weightAb != null" >
        weight_ab = #{record.weightAb,jdbcType=DOUBLE},
      </if>
      <if test="record.newUvCm != null" >
        new_uv_cm = #{record.newUvCm,jdbcType=BIGINT},
      </if>
      <if test="record.weightA != null" >
        weight_a = #{record.weightA,jdbcType=DOUBLE},
      </if>
      <if test="record.hActUv != null" >
        h_act_uv = #{record.hActUv,jdbcType=BIGINT},
      </if>
      <if test="record.weightB != null" >
        weight_b = #{record.weightB,jdbcType=DOUBLE},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update dm_yy_sid_segment_score_accum_cm_1_day
    set id = #{record.id,jdbcType=BIGINT},
      sid_ownerid = #{record.sidOwnerid,jdbcType=BIGINT},
      ps_all = #{record.psAll,jdbcType=DOUBLE},
      grade = #{record.grade,jdbcType=VARCHAR},
      grade_rn = #{record.gradeRn,jdbcType=INTEGER},
      dt = #{record.dt,jdbcType=VARCHAR},
      sid_owyyid = #{record.sidOwyyid,jdbcType=BIGINT},
      ps_all_weight = #{record.psAllWeight,jdbcType=DOUBLE},
      weight_ab = #{record.weightAb,jdbcType=DOUBLE},
      new_uv_cm = #{record.newUvCm,jdbcType=BIGINT},
      weight_a = #{record.weightA,jdbcType=DOUBLE},
      h_act_uv = #{record.hActUv,jdbcType=BIGINT},
      weight_b = #{record.weightB,jdbcType=DOUBLE}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreAccumCm1Day" >
    update dm_yy_sid_segment_score_accum_cm_1_day
    <set >
      <if test="sidOwnerid != null" >
        sid_ownerid = #{sidOwnerid,jdbcType=BIGINT},
      </if>
      <if test="psAll != null" >
        ps_all = #{psAll,jdbcType=DOUBLE},
      </if>
      <if test="grade != null" >
        grade = #{grade,jdbcType=VARCHAR},
      </if>
      <if test="gradeRn != null" >
        grade_rn = #{gradeRn,jdbcType=INTEGER},
      </if>
      <if test="dt != null" >
        dt = #{dt,jdbcType=VARCHAR},
      </if>
      <if test="sidOwyyid != null" >
        sid_owyyid = #{sidOwyyid,jdbcType=BIGINT},
      </if>
      <if test="psAllWeight != null" >
        ps_all_weight = #{psAllWeight,jdbcType=DOUBLE},
      </if>
      <if test="weightAb != null" >
        weight_ab = #{weightAb,jdbcType=DOUBLE},
      </if>
      <if test="newUvCm != null" >
        new_uv_cm = #{newUvCm,jdbcType=BIGINT},
      </if>
      <if test="weightA != null" >
        weight_a = #{weightA,jdbcType=DOUBLE},
      </if>
      <if test="hActUv != null" >
        h_act_uv = #{hActUv,jdbcType=BIGINT},
      </if>
      <if test="weightB != null" >
        weight_b = #{weightB,jdbcType=DOUBLE},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreAccumCm1Day" >
    update dm_yy_sid_segment_score_accum_cm_1_day
    set sid_ownerid = #{sidOwnerid,jdbcType=BIGINT},
      ps_all = #{psAll,jdbcType=DOUBLE},
      grade = #{grade,jdbcType=VARCHAR},
      grade_rn = #{gradeRn,jdbcType=INTEGER},
      dt = #{dt,jdbcType=VARCHAR},
      sid_owyyid = #{sidOwyyid,jdbcType=BIGINT},
      ps_all_weight = #{psAllWeight,jdbcType=DOUBLE},
      weight_ab = #{weightAb,jdbcType=DOUBLE},
      new_uv_cm = #{newUvCm,jdbcType=BIGINT},
      weight_a = #{weightA,jdbcType=DOUBLE},
      h_act_uv = #{hActUv,jdbcType=BIGINT},
      weight_b = #{weightB,jdbcType=DOUBLE}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>