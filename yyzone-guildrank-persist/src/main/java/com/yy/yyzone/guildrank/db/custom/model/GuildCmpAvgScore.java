package com.yy.yyzone.guildrank.db.custom.model;

import java.math.BigDecimal;

public class GuildCmpAvgScore {
    /**
     * 招募
     */
    private BigDecimal recruScore;

    /**
     * 孵化
     */
    private BigDecimal hatchScore;

    /**
     * 留存
     */
    private BigDecimal retScore;

    /**
     * 营收
     */
    private BigDecimal rvnuScore;

    /**
     * 综合
     */
    private BigDecimal comScore;

    /**
     * 等级
     */
    private Integer level;

    public BigDecimal getRecruScore() {
        return this.recruScore;
    }

    public void setRecruScore(final BigDecimal recruScore) {
        this.recruScore = recruScore;
    }

    public BigDecimal getHatchScore() {
        return this.hatchScore;
    }

    public void setHatchScore(final BigDecimal hatchScore) {
        this.hatchScore = hatchScore;
    }

    public BigDecimal getRetScore() {
        return this.retScore;
    }

    public void setRetScore(final BigDecimal retScore) {
        this.retScore = retScore;
    }

    public BigDecimal getRvnuScore() {
        return this.rvnuScore;
    }

    public void setRvnuScore(final BigDecimal rvnuScore) {
        this.rvnuScore = rvnuScore;
    }

    public BigDecimal getComScore() {
        return this.comScore;
    }

    public void setComScore(final BigDecimal comScore) {
        this.comScore = comScore;
    }

    public Integer getLevel() {
        return this.level;
    }

    public void setLevel(final Integer level) {
        this.level = level;
    }

    @Override
    public String toString() {
        return "GuildCmpAvgScore{" +
                "recruScore=" + recruScore +
                ", hatchScore=" + hatchScore +
                ", retScore=" + retScore +
                ", rvnuScore=" + rvnuScore +
                ", comScore=" + comScore +
                ", level=" + level +
                '}';
    }
}