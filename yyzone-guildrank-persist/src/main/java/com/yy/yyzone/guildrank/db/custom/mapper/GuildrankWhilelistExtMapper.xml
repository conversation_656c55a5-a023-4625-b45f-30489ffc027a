<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yy.yyzone.guildrank.db.custom.mapper.GuildrankWhilelistExtMapper" >
    <!-- 批量插入 -->
    <insert id="insertBatch">
        insert into guildrank_whilelist(guild_uid, `rank`, oprator, remark, monthStr, create_time) values
        <foreach collection="list" item="i" separator=",">
            (#{i.guildUid},#{i.rank},#{i.oprator},#{i.remark},#{i.monthstr},#{i.createTime})
        </foreach>
    </insert>

    <select id="selectRank" resultType="com.yy.yyzone.guildrank.db.custom.model.KV">
        select guild_uid K,`rank` V from guildrank_whilelist where monthStr=#{month,jdbcType=VARCHAR};
    </select>
</mapper>