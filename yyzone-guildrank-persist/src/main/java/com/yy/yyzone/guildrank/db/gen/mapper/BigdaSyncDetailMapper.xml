<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yy.yyzone.guildrank.db.gen.mapper.BigdaSyncDetailMapper" >
  <resultMap id="BaseResultMap" type="com.yy.yyzone.guildrank.db.gen.model.BigdaSyncDetail" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="dt" property="dt" jdbcType="VARCHAR" />
    <result column="table_name" property="tableName" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="sync_time" property="syncTime" jdbcType="TIMESTAMP" />
    <result column="data_time" property="dataTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, dt, table_name, status, sync_time, data_time, update_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.yy.yyzone.guildrank.db.gen.model.BigdaSyncDetailExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from bigda_sync_detail
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit > 0" >
      limit ${limit}
    </if>
    <if test="offset > 0" >
      offset ${offset}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from bigda_sync_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from bigda_sync_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.yy.yyzone.guildrank.db.gen.model.BigdaSyncDetailExample" >
    delete from bigda_sync_detail
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.yy.yyzone.guildrank.db.gen.model.BigdaSyncDetail" useGeneratedKeys="true" keyProperty="id" >
    insert into bigda_sync_detail (dt, table_name, status, 
      sync_time, data_time, update_time
      )
    values (#{dt,jdbcType=VARCHAR}, #{tableName,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{syncTime,jdbcType=TIMESTAMP}, #{dataTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.yy.yyzone.guildrank.db.gen.model.BigdaSyncDetail" useGeneratedKeys="true" keyProperty="id" >
    insert into bigda_sync_detail
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="dt != null" >
        dt,
      </if>
      <if test="tableName != null" >
        table_name,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="syncTime != null" >
        sync_time,
      </if>
      <if test="dataTime != null" >
        data_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="dt != null" >
        #{dt,jdbcType=VARCHAR},
      </if>
      <if test="tableName != null" >
        #{tableName,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="syncTime != null" >
        #{syncTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dataTime != null" >
        #{dataTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.yy.yyzone.guildrank.db.gen.model.BigdaSyncDetailExample" resultType="java.lang.Integer" >
    select count(*) from bigda_sync_detail
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update bigda_sync_detail
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.dt != null" >
        dt = #{record.dt,jdbcType=VARCHAR},
      </if>
      <if test="record.tableName != null" >
        table_name = #{record.tableName,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null" >
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.syncTime != null" >
        sync_time = #{record.syncTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.dataTime != null" >
        data_time = #{record.dataTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update bigda_sync_detail
    set id = #{record.id,jdbcType=BIGINT},
      dt = #{record.dt,jdbcType=VARCHAR},
      table_name = #{record.tableName,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      sync_time = #{record.syncTime,jdbcType=TIMESTAMP},
      data_time = #{record.dataTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.yyzone.guildrank.db.gen.model.BigdaSyncDetail" >
    update bigda_sync_detail
    <set >
      <if test="dt != null" >
        dt = #{dt,jdbcType=VARCHAR},
      </if>
      <if test="tableName != null" >
        table_name = #{tableName,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="syncTime != null" >
        sync_time = #{syncTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dataTime != null" >
        data_time = #{dataTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.yyzone.guildrank.db.gen.model.BigdaSyncDetail" >
    update bigda_sync_detail
    set dt = #{dt,jdbcType=VARCHAR},
      table_name = #{tableName,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      sync_time = #{syncTime,jdbcType=TIMESTAMP},
      data_time = #{dataTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>