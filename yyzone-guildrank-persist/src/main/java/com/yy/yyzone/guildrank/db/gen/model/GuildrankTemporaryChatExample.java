package com.yy.yyzone.guildrank.db.gen.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.annotation.Generated;

@Generated("guildrank_temporary_chat")
public class GuildrankTemporaryChatExample {
    /**
     * guildrank_temporary_chat
     */
    protected String orderByClause;

    /**
     * guildrank_temporary_chat
     */
    protected boolean distinct;

    /**
     * guildrank_temporary_chat
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public GuildrankTemporaryChatExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    /**
     * guildrank_temporary_chat null
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andServiceyyIsNull() {
            addCriterion("serviceYY is null");
            return (Criteria) this;
        }

        public Criteria andServiceyyIsNotNull() {
            addCriterion("serviceYY is not null");
            return (Criteria) this;
        }

        public Criteria andServiceyyEqualTo(Long value) {
            addCriterion("serviceYY =", value, "serviceyy");
            return (Criteria) this;
        }

        public Criteria andServiceyyNotEqualTo(Long value) {
            addCriterion("serviceYY <>", value, "serviceyy");
            return (Criteria) this;
        }

        public Criteria andServiceyyGreaterThan(Long value) {
            addCriterion("serviceYY >", value, "serviceyy");
            return (Criteria) this;
        }

        public Criteria andServiceyyGreaterThanOrEqualTo(Long value) {
            addCriterion("serviceYY >=", value, "serviceyy");
            return (Criteria) this;
        }

        public Criteria andServiceyyLessThan(Long value) {
            addCriterion("serviceYY <", value, "serviceyy");
            return (Criteria) this;
        }

        public Criteria andServiceyyLessThanOrEqualTo(Long value) {
            addCriterion("serviceYY <=", value, "serviceyy");
            return (Criteria) this;
        }

        public Criteria andServiceyyIn(List<Long> values) {
            addCriterion("serviceYY in", values, "serviceyy");
            return (Criteria) this;
        }

        public Criteria andServiceyyNotIn(List<Long> values) {
            addCriterion("serviceYY not in", values, "serviceyy");
            return (Criteria) this;
        }

        public Criteria andServiceyyBetween(Long value1, Long value2) {
            addCriterion("serviceYY between", value1, value2, "serviceyy");
            return (Criteria) this;
        }

        public Criteria andServiceyyNotBetween(Long value1, Long value2) {
            addCriterion("serviceYY not between", value1, value2, "serviceyy");
            return (Criteria) this;
        }

        public Criteria andGuilduidIsNull() {
            addCriterion("guildUid is null");
            return (Criteria) this;
        }

        public Criteria andGuilduidIsNotNull() {
            addCriterion("guildUid is not null");
            return (Criteria) this;
        }

        public Criteria andGuilduidEqualTo(Long value) {
            addCriterion("guildUid =", value, "guilduid");
            return (Criteria) this;
        }

        public Criteria andGuilduidNotEqualTo(Long value) {
            addCriterion("guildUid <>", value, "guilduid");
            return (Criteria) this;
        }

        public Criteria andGuilduidGreaterThan(Long value) {
            addCriterion("guildUid >", value, "guilduid");
            return (Criteria) this;
        }

        public Criteria andGuilduidGreaterThanOrEqualTo(Long value) {
            addCriterion("guildUid >=", value, "guilduid");
            return (Criteria) this;
        }

        public Criteria andGuilduidLessThan(Long value) {
            addCriterion("guildUid <", value, "guilduid");
            return (Criteria) this;
        }

        public Criteria andGuilduidLessThanOrEqualTo(Long value) {
            addCriterion("guildUid <=", value, "guilduid");
            return (Criteria) this;
        }

        public Criteria andGuilduidIn(List<Long> values) {
            addCriterion("guildUid in", values, "guilduid");
            return (Criteria) this;
        }

        public Criteria andGuilduidNotIn(List<Long> values) {
            addCriterion("guildUid not in", values, "guilduid");
            return (Criteria) this;
        }

        public Criteria andGuilduidBetween(Long value1, Long value2) {
            addCriterion("guildUid between", value1, value2, "guilduid");
            return (Criteria) this;
        }

        public Criteria andGuilduidNotBetween(Long value1, Long value2) {
            addCriterion("guildUid not between", value1, value2, "guilduid");
            return (Criteria) this;
        }

        public Criteria andCreatetimeIsNull() {
            addCriterion("createTime is null");
            return (Criteria) this;
        }

        public Criteria andCreatetimeIsNotNull() {
            addCriterion("createTime is not null");
            return (Criteria) this;
        }

        public Criteria andCreatetimeEqualTo(Date value) {
            addCriterion("createTime =", value, "createtime");
            return (Criteria) this;
        }

        public Criteria andCreatetimeNotEqualTo(Date value) {
            addCriterion("createTime <>", value, "createtime");
            return (Criteria) this;
        }

        public Criteria andCreatetimeGreaterThan(Date value) {
            addCriterion("createTime >", value, "createtime");
            return (Criteria) this;
        }

        public Criteria andCreatetimeGreaterThanOrEqualTo(Date value) {
            addCriterion("createTime >=", value, "createtime");
            return (Criteria) this;
        }

        public Criteria andCreatetimeLessThan(Date value) {
            addCriterion("createTime <", value, "createtime");
            return (Criteria) this;
        }

        public Criteria andCreatetimeLessThanOrEqualTo(Date value) {
            addCriterion("createTime <=", value, "createtime");
            return (Criteria) this;
        }

        public Criteria andCreatetimeIn(List<Date> values) {
            addCriterion("createTime in", values, "createtime");
            return (Criteria) this;
        }

        public Criteria andCreatetimeNotIn(List<Date> values) {
            addCriterion("createTime not in", values, "createtime");
            return (Criteria) this;
        }

        public Criteria andCreatetimeBetween(Date value1, Date value2) {
            addCriterion("createTime between", value1, value2, "createtime");
            return (Criteria) this;
        }

        public Criteria andCreatetimeNotBetween(Date value1, Date value2) {
            addCriterion("createTime not between", value1, value2, "createtime");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeIsNull() {
            addCriterion("updateTime is null");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeIsNotNull() {
            addCriterion("updateTime is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeEqualTo(Date value) {
            addCriterion("updateTime =", value, "updatetime");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeNotEqualTo(Date value) {
            addCriterion("updateTime <>", value, "updatetime");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeGreaterThan(Date value) {
            addCriterion("updateTime >", value, "updatetime");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeGreaterThanOrEqualTo(Date value) {
            addCriterion("updateTime >=", value, "updatetime");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeLessThan(Date value) {
            addCriterion("updateTime <", value, "updatetime");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeLessThanOrEqualTo(Date value) {
            addCriterion("updateTime <=", value, "updatetime");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeIn(List<Date> values) {
            addCriterion("updateTime in", values, "updatetime");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeNotIn(List<Date> values) {
            addCriterion("updateTime not in", values, "updatetime");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeBetween(Date value1, Date value2) {
            addCriterion("updateTime between", value1, value2, "updatetime");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeNotBetween(Date value1, Date value2) {
            addCriterion("updateTime not between", value1, value2, "updatetime");
            return (Criteria) this;
        }

        public Criteria andAddbyIsNull() {
            addCriterion("addBy is null");
            return (Criteria) this;
        }

        public Criteria andAddbyIsNotNull() {
            addCriterion("addBy is not null");
            return (Criteria) this;
        }

        public Criteria andAddbyEqualTo(String value) {
            addCriterion("addBy =", value, "addby");
            return (Criteria) this;
        }

        public Criteria andAddbyNotEqualTo(String value) {
            addCriterion("addBy <>", value, "addby");
            return (Criteria) this;
        }

        public Criteria andAddbyGreaterThan(String value) {
            addCriterion("addBy >", value, "addby");
            return (Criteria) this;
        }

        public Criteria andAddbyGreaterThanOrEqualTo(String value) {
            addCriterion("addBy >=", value, "addby");
            return (Criteria) this;
        }

        public Criteria andAddbyLessThan(String value) {
            addCriterion("addBy <", value, "addby");
            return (Criteria) this;
        }

        public Criteria andAddbyLessThanOrEqualTo(String value) {
            addCriterion("addBy <=", value, "addby");
            return (Criteria) this;
        }

        public Criteria andAddbyLike(String value) {
            addCriterion("addBy like", value, "addby");
            return (Criteria) this;
        }

        public Criteria andAddbyNotLike(String value) {
            addCriterion("addBy not like", value, "addby");
            return (Criteria) this;
        }

        public Criteria andAddbyIn(List<String> values) {
            addCriterion("addBy in", values, "addby");
            return (Criteria) this;
        }

        public Criteria andAddbyNotIn(List<String> values) {
            addCriterion("addBy not in", values, "addby");
            return (Criteria) this;
        }

        public Criteria andAddbyBetween(String value1, String value2) {
            addCriterion("addBy between", value1, value2, "addby");
            return (Criteria) this;
        }

        public Criteria andAddbyNotBetween(String value1, String value2) {
            addCriterion("addBy not between", value1, value2, "addby");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andInsertnumIsNull() {
            addCriterion("insertNum is null");
            return (Criteria) this;
        }

        public Criteria andInsertnumIsNotNull() {
            addCriterion("insertNum is not null");
            return (Criteria) this;
        }

        public Criteria andInsertnumEqualTo(Integer value) {
            addCriterion("insertNum =", value, "insertnum");
            return (Criteria) this;
        }

        public Criteria andInsertnumNotEqualTo(Integer value) {
            addCriterion("insertNum <>", value, "insertnum");
            return (Criteria) this;
        }

        public Criteria andInsertnumGreaterThan(Integer value) {
            addCriterion("insertNum >", value, "insertnum");
            return (Criteria) this;
        }

        public Criteria andInsertnumGreaterThanOrEqualTo(Integer value) {
            addCriterion("insertNum >=", value, "insertnum");
            return (Criteria) this;
        }

        public Criteria andInsertnumLessThan(Integer value) {
            addCriterion("insertNum <", value, "insertnum");
            return (Criteria) this;
        }

        public Criteria andInsertnumLessThanOrEqualTo(Integer value) {
            addCriterion("insertNum <=", value, "insertnum");
            return (Criteria) this;
        }

        public Criteria andInsertnumIn(List<Integer> values) {
            addCriterion("insertNum in", values, "insertnum");
            return (Criteria) this;
        }

        public Criteria andInsertnumNotIn(List<Integer> values) {
            addCriterion("insertNum not in", values, "insertnum");
            return (Criteria) this;
        }

        public Criteria andInsertnumBetween(Integer value1, Integer value2) {
            addCriterion("insertNum between", value1, value2, "insertnum");
            return (Criteria) this;
        }

        public Criteria andInsertnumNotBetween(Integer value1, Integer value2) {
            addCriterion("insertNum not between", value1, value2, "insertnum");
            return (Criteria) this;
        }

        public Criteria andBiztypeIsNull() {
            addCriterion("bizType is null");
            return (Criteria) this;
        }

        public Criteria andBiztypeIsNotNull() {
            addCriterion("bizType is not null");
            return (Criteria) this;
        }

        public Criteria andBiztypeEqualTo(Integer value) {
            addCriterion("bizType =", value, "biztype");
            return (Criteria) this;
        }

        public Criteria andBiztypeNotEqualTo(Integer value) {
            addCriterion("bizType <>", value, "biztype");
            return (Criteria) this;
        }

        public Criteria andBiztypeGreaterThan(Integer value) {
            addCriterion("bizType >", value, "biztype");
            return (Criteria) this;
        }

        public Criteria andBiztypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("bizType >=", value, "biztype");
            return (Criteria) this;
        }

        public Criteria andBiztypeLessThan(Integer value) {
            addCriterion("bizType <", value, "biztype");
            return (Criteria) this;
        }

        public Criteria andBiztypeLessThanOrEqualTo(Integer value) {
            addCriterion("bizType <=", value, "biztype");
            return (Criteria) this;
        }

        public Criteria andBiztypeIn(List<Integer> values) {
            addCriterion("bizType in", values, "biztype");
            return (Criteria) this;
        }

        public Criteria andBiztypeNotIn(List<Integer> values) {
            addCriterion("bizType not in", values, "biztype");
            return (Criteria) this;
        }

        public Criteria andBiztypeBetween(Integer value1, Integer value2) {
            addCriterion("bizType between", value1, value2, "biztype");
            return (Criteria) this;
        }

        public Criteria andBiztypeNotBetween(Integer value1, Integer value2) {
            addCriterion("bizType not between", value1, value2, "biztype");
            return (Criteria) this;
        }

        public Criteria andServicenickIsNull() {
            addCriterion("serviceNick is null");
            return (Criteria) this;
        }

        public Criteria andServicenickIsNotNull() {
            addCriterion("serviceNick is not null");
            return (Criteria) this;
        }

        public Criteria andServicenickEqualTo(String value) {
            addCriterion("serviceNick =", value, "servicenick");
            return (Criteria) this;
        }

        public Criteria andServicenickNotEqualTo(String value) {
            addCriterion("serviceNick <>", value, "servicenick");
            return (Criteria) this;
        }

        public Criteria andServicenickGreaterThan(String value) {
            addCriterion("serviceNick >", value, "servicenick");
            return (Criteria) this;
        }

        public Criteria andServicenickGreaterThanOrEqualTo(String value) {
            addCriterion("serviceNick >=", value, "servicenick");
            return (Criteria) this;
        }

        public Criteria andServicenickLessThan(String value) {
            addCriterion("serviceNick <", value, "servicenick");
            return (Criteria) this;
        }

        public Criteria andServicenickLessThanOrEqualTo(String value) {
            addCriterion("serviceNick <=", value, "servicenick");
            return (Criteria) this;
        }

        public Criteria andServicenickLike(String value) {
            addCriterion("serviceNick like", value, "servicenick");
            return (Criteria) this;
        }

        public Criteria andServicenickNotLike(String value) {
            addCriterion("serviceNick not like", value, "servicenick");
            return (Criteria) this;
        }

        public Criteria andServicenickIn(List<String> values) {
            addCriterion("serviceNick in", values, "servicenick");
            return (Criteria) this;
        }

        public Criteria andServicenickNotIn(List<String> values) {
            addCriterion("serviceNick not in", values, "servicenick");
            return (Criteria) this;
        }

        public Criteria andServicenickBetween(String value1, String value2) {
            addCriterion("serviceNick between", value1, value2, "servicenick");
            return (Criteria) this;
        }

        public Criteria andServicenickNotBetween(String value1, String value2) {
            addCriterion("serviceNick not between", value1, value2, "servicenick");
            return (Criteria) this;
        }
    }

    /**
     * guildrank_temporary_chat
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * guildrank_temporary_chat null
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}