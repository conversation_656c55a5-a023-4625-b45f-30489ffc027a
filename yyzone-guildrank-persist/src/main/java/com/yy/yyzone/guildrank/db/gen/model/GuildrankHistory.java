package com.yy.yyzone.guildrank.db.gen.model;

import java.util.Date;

public class GuildrankHistory {
    /**
     * id
     */
    private Long id;

    /**
     * 月份 1号日期
     */
    private Date month;

    /**
     * 主体uid
     */
    private Long mainGuildUid;

    /**
     * 公会uid
     */
    private Long guildUid;

    /**
     * 等级
     */
    private Integer level;

    /**
     * 类型 0BI数据 1无段位默认为一星
     */
    private Integer type;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 获取id
     * @return id id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置id
     * @param id id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取月份 1号日期
     * @return month 月份 1号日期
     */
    public Date getMonth() {
        return month;
    }

    /**
     * 设置月份 1号日期
     * @param month 月份 1号日期
     */
    public void setMonth(Date month) {
        this.month = month;
    }

    /**
     * 获取主体uid
     * @return main_guild_uid 主体uid
     */
    public Long getMainGuildUid() {
        return mainGuildUid;
    }

    /**
     * 设置主体uid
     * @param mainGuildUid 主体uid
     */
    public void setMainGuildUid(Long mainGuildUid) {
        this.mainGuildUid = mainGuildUid;
    }

    /**
     * 获取公会uid
     * @return guild_uid 公会uid
     */
    public Long getGuildUid() {
        return guildUid;
    }

    /**
     * 设置公会uid
     * @param guildUid 公会uid
     */
    public void setGuildUid(Long guildUid) {
        this.guildUid = guildUid;
    }

    /**
     * 获取等级
     * @return level 等级
     */
    public Integer getLevel() {
        return level;
    }

    /**
     * 设置等级
     * @param level 等级
     */
    public void setLevel(Integer level) {
        this.level = level;
    }

    /**
     * 获取类型 0BI数据 1无段位默认为一星
     * @return type 类型 0BI数据 1无段位默认为一星
     */
    public Integer getType() {
        return type;
    }

    /**
     * 设置类型 0BI数据 1无段位默认为一星
     * @param type 类型 0BI数据 1无段位默认为一星
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * 获取更新时间
     * @return update_time 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", month=").append(month);
        sb.append(", mainGuildUid=").append(mainGuildUid);
        sb.append(", guildUid=").append(guildUid);
        sb.append(", level=").append(level);
        sb.append(", type=").append(type);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }
}