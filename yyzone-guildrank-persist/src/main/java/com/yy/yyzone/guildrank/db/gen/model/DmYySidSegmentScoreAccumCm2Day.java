package com.yy.yyzone.guildrank.db.gen.model;

public class DmYySidSegmentScoreAccumCm2Day {
    /**
     * 
     */
    private Long id;

    /**
     * 公会owuid
     */
    private Long sidOwnerid;

    /**
     * 指标归一分
     */
    private Double psN;

    /**
     * 指标归一分排名
     */
    private Integer psNAllRn;

    /**
     * 单项指标名称：新主播运营能力、活跃主播、公会营收能力
     */
    private String kpiName;

    /**
     * 日期
     */
    private String dt;

    /**
     * 上月段位
     */
    private String grade;

    /**
     * 获取
     * @return id 
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置
     * @param id 
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取公会owuid
     * @return sid_ownerid 公会owuid
     */
    public Long getSidOwnerid() {
        return sidOwnerid;
    }

    /**
     * 设置公会owuid
     * @param sidOwnerid 公会owuid
     */
    public void setSidOwnerid(Long sidOwnerid) {
        this.sidOwnerid = sidOwnerid;
    }

    /**
     * 获取指标归一分
     * @return ps_n 指标归一分
     */
    public Double getPsN() {
        return psN;
    }

    /**
     * 设置指标归一分
     * @param psN 指标归一分
     */
    public void setPsN(Double psN) {
        this.psN = psN;
    }

    /**
     * 获取指标归一分排名
     * @return ps_n_all_rn 指标归一分排名
     */
    public Integer getPsNAllRn() {
        return psNAllRn;
    }

    /**
     * 设置指标归一分排名
     * @param psNAllRn 指标归一分排名
     */
    public void setPsNAllRn(Integer psNAllRn) {
        this.psNAllRn = psNAllRn;
    }

    /**
     * 获取单项指标名称：新主播运营能力、活跃主播、公会营收能力
     * @return kpi_name 单项指标名称：新主播运营能力、活跃主播、公会营收能力
     */
    public String getKpiName() {
        return kpiName;
    }

    /**
     * 设置单项指标名称：新主播运营能力、活跃主播、公会营收能力
     * @param kpiName 单项指标名称：新主播运营能力、活跃主播、公会营收能力
     */
    public void setKpiName(String kpiName) {
        this.kpiName = kpiName;
    }

    /**
     * 获取日期
     * @return dt 日期
     */
    public String getDt() {
        return dt;
    }

    /**
     * 设置日期
     * @param dt 日期
     */
    public void setDt(String dt) {
        this.dt = dt;
    }

    /**
     * 获取上月段位
     * @return grade 上月段位
     */
    public String getGrade() {
        return grade;
    }

    /**
     * 设置上月段位
     * @param grade 上月段位
     */
    public void setGrade(String grade) {
        this.grade = grade;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", sidOwnerid=").append(sidOwnerid);
        sb.append(", psN=").append(psN);
        sb.append(", psNAllRn=").append(psNAllRn);
        sb.append(", kpiName=").append(kpiName);
        sb.append(", dt=").append(dt);
        sb.append(", grade=").append(grade);
        sb.append("]");
        return sb.toString();
    }
}