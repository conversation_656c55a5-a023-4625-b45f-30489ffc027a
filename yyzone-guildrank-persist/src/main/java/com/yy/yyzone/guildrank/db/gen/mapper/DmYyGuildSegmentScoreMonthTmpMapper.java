package com.yy.yyzone.guildrank.db.gen.mapper;

import com.yy.yyzone.guildrank.db.gen.model.DmYyGuildSegmentScoreMonthTmp;
import com.yy.yyzone.guildrank.db.gen.model.DmYyGuildSegmentScoreMonthTmpExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DmYyGuildSegmentScoreMonthTmpMapper {
    int countByExample(DmYyGuildSegmentScoreMonthTmpExample example);

    int deleteByExample(DmYyGuildSegmentScoreMonthTmpExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DmYyGuildSegmentScoreMonthTmp record);

    int insertSelective(DmYyGuildSegmentScoreMonthTmp record);

    List<DmYyGuildSegmentScoreMonthTmp> selectByExample(DmYyGuildSegmentScoreMonthTmpExample example);

    DmYyGuildSegmentScoreMonthTmp selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DmYyGuildSegmentScoreMonthTmp record, @Param("example") DmYyGuildSegmentScoreMonthTmpExample example);

    int updateByExample(@Param("record") DmYyGuildSegmentScoreMonthTmp record, @Param("example") DmYyGuildSegmentScoreMonthTmpExample example);

    int updateByPrimaryKeySelective(DmYyGuildSegmentScoreMonthTmp record);

    int updateByPrimaryKey(DmYyGuildSegmentScoreMonthTmp record);
}