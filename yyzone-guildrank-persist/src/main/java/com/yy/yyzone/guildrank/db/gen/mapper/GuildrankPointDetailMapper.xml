<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yy.yyzone.guildrank.db.gen.mapper.GuildrankPointDetailMapper" >
  <resultMap id="BaseResultMap" type="com.yy.yyzone.guildrank.db.gen.model.GuildrankPointDetail" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="uid" property="uid" jdbcType="BIGINT" />
    <result column="point" property="point" jdbcType="INTEGER" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="type_serial" property="typeSerial" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="due_dt" property="dueDt" jdbcType="DATE" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="dt" property="dt" jdbcType="DATE" />
    <result column="grant_time" property="grantTime" jdbcType="TIMESTAMP" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, uid, point, type, type_serial, status, due_dt, remark, dt, grant_time, create_time, 
    update_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankPointDetailExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from guildrank_point_detail
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit > 0" >
      limit ${limit}
    </if>
    <if test="offset > 0" >
      offset ${offset}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from guildrank_point_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from guildrank_point_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankPointDetailExample" >
    delete from guildrank_point_detail
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankPointDetail" useGeneratedKeys="true" keyProperty="id" >
    insert into guildrank_point_detail (uid, point, type, 
      type_serial, status, due_dt, 
      remark, dt, grant_time, 
      create_time, update_time)
    values (#{uid,jdbcType=BIGINT}, #{point,jdbcType=INTEGER}, #{type,jdbcType=INTEGER}, 
      #{typeSerial,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{dueDt,jdbcType=DATE}, 
      #{remark,jdbcType=VARCHAR}, #{dt,jdbcType=DATE}, #{grantTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankPointDetail" useGeneratedKeys="true" keyProperty="id" >
    insert into guildrank_point_detail
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="uid != null" >
        uid,
      </if>
      <if test="point != null" >
        point,
      </if>
      <if test="type != null" >
        type,
      </if>
      <if test="typeSerial != null" >
        type_serial,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="dueDt != null" >
        due_dt,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="dt != null" >
        dt,
      </if>
      <if test="grantTime != null" >
        grant_time,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="uid != null" >
        #{uid,jdbcType=BIGINT},
      </if>
      <if test="point != null" >
        #{point,jdbcType=INTEGER},
      </if>
      <if test="type != null" >
        #{type,jdbcType=INTEGER},
      </if>
      <if test="typeSerial != null" >
        #{typeSerial,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="dueDt != null" >
        #{dueDt,jdbcType=DATE},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="dt != null" >
        #{dt,jdbcType=DATE},
      </if>
      <if test="grantTime != null" >
        #{grantTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankPointDetailExample" resultType="java.lang.Integer" >
    select count(*) from guildrank_point_detail
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update guildrank_point_detail
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.uid != null" >
        uid = #{record.uid,jdbcType=BIGINT},
      </if>
      <if test="record.point != null" >
        point = #{record.point,jdbcType=INTEGER},
      </if>
      <if test="record.type != null" >
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.typeSerial != null" >
        type_serial = #{record.typeSerial,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null" >
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.dueDt != null" >
        due_dt = #{record.dueDt,jdbcType=DATE},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.dt != null" >
        dt = #{record.dt,jdbcType=DATE},
      </if>
      <if test="record.grantTime != null" >
        grant_time = #{record.grantTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update guildrank_point_detail
    set id = #{record.id,jdbcType=BIGINT},
      uid = #{record.uid,jdbcType=BIGINT},
      point = #{record.point,jdbcType=INTEGER},
      type = #{record.type,jdbcType=INTEGER},
      type_serial = #{record.typeSerial,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      due_dt = #{record.dueDt,jdbcType=DATE},
      remark = #{record.remark,jdbcType=VARCHAR},
      dt = #{record.dt,jdbcType=DATE},
      grant_time = #{record.grantTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankPointDetail" >
    update guildrank_point_detail
    <set >
      <if test="uid != null" >
        uid = #{uid,jdbcType=BIGINT},
      </if>
      <if test="point != null" >
        point = #{point,jdbcType=INTEGER},
      </if>
      <if test="type != null" >
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="typeSerial != null" >
        type_serial = #{typeSerial,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="dueDt != null" >
        due_dt = #{dueDt,jdbcType=DATE},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="dt != null" >
        dt = #{dt,jdbcType=DATE},
      </if>
      <if test="grantTime != null" >
        grant_time = #{grantTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankPointDetail" >
    update guildrank_point_detail
    set uid = #{uid,jdbcType=BIGINT},
      point = #{point,jdbcType=INTEGER},
      type = #{type,jdbcType=INTEGER},
      type_serial = #{typeSerial,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      due_dt = #{dueDt,jdbcType=DATE},
      remark = #{remark,jdbcType=VARCHAR},
      dt = #{dt,jdbcType=DATE},
      grant_time = #{grantTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>