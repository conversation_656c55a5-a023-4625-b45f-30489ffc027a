package com.yy.yyzone.guildrank.db.custom.mapper;

import com.yy.yyzone.guildrank.db.custom.model.GuildCmpAvgScore;
import com.yy.yyzone.guildrank.db.custom.model.KV;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankHistory;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankPointDetail;
import com.yy.yyzone.guildrank.db.gen.model.YyDmEntityGuildCmpHealthAnalysisDiTmp;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface BiExtMapper {
    List<KV<Long, Integer>> selectDataCmpLevel(@Param("dt") Date dt);

    List<KV<Long, Integer>> selectCmpStarLevel(@Param("dt") Date dt);

    List<KV<Long, Integer>> selectLevelFromHisotry(@Param("guildUids") List<Long> guildUids, @Param("month") Date month);

    int insertBatch(@Param("list") List<YyDmEntityGuildCmpHealthAnalysisDiTmp> list);

    int insertPoints(@Param("list") List<GuildrankPointDetail> list);

    int insertOrUpdateBatchHistory(@Param("list") List<GuildrankHistory> list);

    List<Date> selectDt(@Param("tbl") String tbl);

    int deleteByDt(@Param("tbl") String tbl, @Param("dt") Date dt, @Param("limit") int limit);

    int insertSyncDetail();

    List<KV<Long, Integer>> selectLevelCount(@Param("tbl") String tbl,@Param("dt") Date dt);

    GuildCmpAvgScore selectGuildCmpAvgScoreByUid(@Param("dt") Date dt, @Param("uid") Long uid);

    GuildCmpAvgScore selectGuildCmpAvgScoreByLvl(@Param("dt") Date dt, @Param("lvl") Integer lvl);
}
