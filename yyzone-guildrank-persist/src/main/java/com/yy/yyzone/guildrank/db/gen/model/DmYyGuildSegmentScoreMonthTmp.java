package com.yy.yyzone.guildrank.db.gen.model;

public class DmYyGuildSegmentScoreMonthTmp {
    /**
     * id
     */
    private Long id;

    /**
     * 公会uid
     */
    private Long sidOwnerid;

    /**
     * 公会yy
     */
    private Long sidOwyyid;

    /**
     * 旗下主播蓝钻收入
     */
    private Double monthDiamond;

    /**
     * 旗下主播累计蓝钻收入环比上月增长数
     */
    private Double monthDiamondRr;

    /**
     * 日均有效开播主播数
     */
    private Double validLiveUv;

    /**
     * 日均有效开播主播数环比上月增长数
     */
    private Double validLiveUvRr;

    /**
     * 超高人气有效开播主播数（acu[300,）
     */
    private Double acu300Uv;

    /**
     * 超高人气有效开播主播数环比上月增长数
     */
    private Double acu300UvRr;

    /**
     * 高人气有效开播主播数（acu[50,300)）
     */
    private Double acu50300Uv;

    /**
     * @@高人气有效开播主播数环比上月增长数
     */
    private Double acu50300UvRr;

    /**
     * 中人气有效开播主播数（acu[10,50)）
     */
    private Double acu1050Uv;

    /**
     * 中人气有效开播主播数环比上月增长数
     */
    private Double acu1050UvRr;

    /**
     * 非人气有效开播主播数（ACU≥10）
     */
    private Double acu10Uv;

    /**
     * 非人气有效开播主播数环比上月增长数
     */
    private Double acu10UvRr;

    /**
     * A类违规主播数
     */
    private Long breakAUv;

    /**
     * B类违规主播数
     */
    private Long breakBUv;

    /**
     * C类违规主播数
     */
    private Long breakCUv;

    /**
     * E类违规主播数
     */
    private Long breakEUv;

    /**
     * 开播主播数
     */
    private Long liveUv;

    /**
     * 历史约当月违规主播数
     */
    private Long oldBreakUv;

    /**
     * 违规主播数占比
     */
    private Double breakUvPp;

    /**
     * 违规主播数占比环比上月增长数
     */
    private Double breakUvPpRr;

    /**
     * 最终星级
     */
    private Integer psSAll;

    /**
     * 环比上月星级涨跌,若当月才有星级，上月无星级，则为null值
     */
    private Integer psSAllRr;

    /**
     * 旗下主播蓝钻收入星级
     */
    private Integer psSMonthDiamond;

    /**
     * 旗下主播累计蓝钻收入冲刺值
     */
    private Double sprMonthDiamond;

    /**
     * 日均有效开播主播数星级
     */
    private Integer psSValidLiveUv;

    /**
     * 日均有效开播主播数冲刺值
     */
    private Double sprValidLiveUv;

    /**
     * 人气主播总得分
     */
    private Double psAcu;

    /**
     * 人气主播总得分星级
     */
    private Integer psSAcu;

    /**
     * 人气主播总得分冲刺值
     */
    private Double sprAcu;

    /**
     * 主播开播管理星级
     */
    private Integer psSLiveUv;

    /**
     * 违规主播数占比星级
     */
    private Integer psSBreakUvPp;

    /**
     * 违规主播数占比冲刺值
     */
    private Double sprBreakUvPp;

    /**
     * 月份
     */
    private String dt;

    /**
     * 日均高优有效开播主播数，从202308开始有效
     */
    private Double avgHighAidNum;

    /**
     * 日均高优有效开播主播数环比上月增长数，从202308开始有效
     */
    private Double avgHighAidNumRr;

    /**
     * 日均腰部有效开播主播数，从202308开始有效
     */
    private Double avgWaistAidNum;

    /**
     * 日均腰部有效开播主播数环比上月增长数，从202308开始有效
     */
    private Double avgWaistAidNumRr;

    /**
     * 日均潜力有效开播主播数，从202308开始有效
     */
    private Double avgTailAidNum;

    /**
     * 日均潜力有效开播主播数环比上月增长数，从202308开始有效
     */
    private Double avgTailAidNumRr;

    /**
     * 等级主播总得分，从202308开始有效
     */
    private Double aidValueScore;

    /**
     * 等级主播总得分星级，从202308开始有效
     */
    private Integer aidValueScoreStarLvl;

    /**
     * 等级主播总得分冲刺值，从202308开始有效
     */
    private Double aidValueScoreSprintValue;

    /**
     * 是否小火星公会
     */
    private Integer isMarsGuild;

    /**
     * 关联的SDK小火星公会UID
     */
    private Long marsSidOwnerid;

    /**
     * 获取id
     * @return id id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置id
     * @param id id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取公会uid
     * @return sid_ownerid 公会uid
     */
    public Long getSidOwnerid() {
        return sidOwnerid;
    }

    /**
     * 设置公会uid
     * @param sidOwnerid 公会uid
     */
    public void setSidOwnerid(Long sidOwnerid) {
        this.sidOwnerid = sidOwnerid;
    }

    /**
     * 获取公会yy
     * @return sid_owyyid 公会yy
     */
    public Long getSidOwyyid() {
        return sidOwyyid;
    }

    /**
     * 设置公会yy
     * @param sidOwyyid 公会yy
     */
    public void setSidOwyyid(Long sidOwyyid) {
        this.sidOwyyid = sidOwyyid;
    }

    /**
     * 获取旗下主播蓝钻收入
     * @return month_diamond 旗下主播蓝钻收入
     */
    public Double getMonthDiamond() {
        return monthDiamond;
    }

    /**
     * 设置旗下主播蓝钻收入
     * @param monthDiamond 旗下主播蓝钻收入
     */
    public void setMonthDiamond(Double monthDiamond) {
        this.monthDiamond = monthDiamond;
    }

    /**
     * 获取旗下主播累计蓝钻收入环比上月增长数
     * @return month_diamond_rr 旗下主播累计蓝钻收入环比上月增长数
     */
    public Double getMonthDiamondRr() {
        return monthDiamondRr;
    }

    /**
     * 设置旗下主播累计蓝钻收入环比上月增长数
     * @param monthDiamondRr 旗下主播累计蓝钻收入环比上月增长数
     */
    public void setMonthDiamondRr(Double monthDiamondRr) {
        this.monthDiamondRr = monthDiamondRr;
    }

    /**
     * 获取日均有效开播主播数
     * @return valid_live_uv 日均有效开播主播数
     */
    public Double getValidLiveUv() {
        return validLiveUv;
    }

    /**
     * 设置日均有效开播主播数
     * @param validLiveUv 日均有效开播主播数
     */
    public void setValidLiveUv(Double validLiveUv) {
        this.validLiveUv = validLiveUv;
    }

    /**
     * 获取日均有效开播主播数环比上月增长数
     * @return valid_live_uv_rr 日均有效开播主播数环比上月增长数
     */
    public Double getValidLiveUvRr() {
        return validLiveUvRr;
    }

    /**
     * 设置日均有效开播主播数环比上月增长数
     * @param validLiveUvRr 日均有效开播主播数环比上月增长数
     */
    public void setValidLiveUvRr(Double validLiveUvRr) {
        this.validLiveUvRr = validLiveUvRr;
    }

    /**
     * 获取超高人气有效开播主播数（acu[300,）
     * @return acu300_uv 超高人气有效开播主播数（acu[300,）
     */
    public Double getAcu300Uv() {
        return acu300Uv;
    }

    /**
     * 设置超高人气有效开播主播数（acu[300,）
     * @param acu300Uv 超高人气有效开播主播数（acu[300,）
     */
    public void setAcu300Uv(Double acu300Uv) {
        this.acu300Uv = acu300Uv;
    }

    /**
     * 获取超高人气有效开播主播数环比上月增长数
     * @return acu300_uv_rr 超高人气有效开播主播数环比上月增长数
     */
    public Double getAcu300UvRr() {
        return acu300UvRr;
    }

    /**
     * 设置超高人气有效开播主播数环比上月增长数
     * @param acu300UvRr 超高人气有效开播主播数环比上月增长数
     */
    public void setAcu300UvRr(Double acu300UvRr) {
        this.acu300UvRr = acu300UvRr;
    }

    /**
     * 获取高人气有效开播主播数（acu[50,300)）
     * @return acu50_300_uv 高人气有效开播主播数（acu[50,300)）
     */
    public Double getAcu50300Uv() {
        return acu50300Uv;
    }

    /**
     * 设置高人气有效开播主播数（acu[50,300)）
     * @param acu50300Uv 高人气有效开播主播数（acu[50,300)）
     */
    public void setAcu50300Uv(Double acu50300Uv) {
        this.acu50300Uv = acu50300Uv;
    }

    /**
     * 获取@@高人气有效开播主播数环比上月增长数
     * @return acu50_300_uv_rr @@高人气有效开播主播数环比上月增长数
     */
    public Double getAcu50300UvRr() {
        return acu50300UvRr;
    }

    /**
     * 设置@@高人气有效开播主播数环比上月增长数
     * @param acu50300UvRr @@高人气有效开播主播数环比上月增长数
     */
    public void setAcu50300UvRr(Double acu50300UvRr) {
        this.acu50300UvRr = acu50300UvRr;
    }

    /**
     * 获取中人气有效开播主播数（acu[10,50)）
     * @return acu10_50_uv 中人气有效开播主播数（acu[10,50)）
     */
    public Double getAcu1050Uv() {
        return acu1050Uv;
    }

    /**
     * 设置中人气有效开播主播数（acu[10,50)）
     * @param acu1050Uv 中人气有效开播主播数（acu[10,50)）
     */
    public void setAcu1050Uv(Double acu1050Uv) {
        this.acu1050Uv = acu1050Uv;
    }

    /**
     * 获取中人气有效开播主播数环比上月增长数
     * @return acu10_50_uv_rr 中人气有效开播主播数环比上月增长数
     */
    public Double getAcu1050UvRr() {
        return acu1050UvRr;
    }

    /**
     * 设置中人气有效开播主播数环比上月增长数
     * @param acu1050UvRr 中人气有效开播主播数环比上月增长数
     */
    public void setAcu1050UvRr(Double acu1050UvRr) {
        this.acu1050UvRr = acu1050UvRr;
    }

    /**
     * 获取非人气有效开播主播数（ACU≥10）
     * @return acu10_uv 非人气有效开播主播数（ACU≥10）
     */
    public Double getAcu10Uv() {
        return acu10Uv;
    }

    /**
     * 设置非人气有效开播主播数（ACU≥10）
     * @param acu10Uv 非人气有效开播主播数（ACU≥10）
     */
    public void setAcu10Uv(Double acu10Uv) {
        this.acu10Uv = acu10Uv;
    }

    /**
     * 获取非人气有效开播主播数环比上月增长数
     * @return acu10_uv_rr 非人气有效开播主播数环比上月增长数
     */
    public Double getAcu10UvRr() {
        return acu10UvRr;
    }

    /**
     * 设置非人气有效开播主播数环比上月增长数
     * @param acu10UvRr 非人气有效开播主播数环比上月增长数
     */
    public void setAcu10UvRr(Double acu10UvRr) {
        this.acu10UvRr = acu10UvRr;
    }

    /**
     * 获取A类违规主播数
     * @return break_a_uv A类违规主播数
     */
    public Long getBreakAUv() {
        return breakAUv;
    }

    /**
     * 设置A类违规主播数
     * @param breakAUv A类违规主播数
     */
    public void setBreakAUv(Long breakAUv) {
        this.breakAUv = breakAUv;
    }

    /**
     * 获取B类违规主播数
     * @return break_b_uv B类违规主播数
     */
    public Long getBreakBUv() {
        return breakBUv;
    }

    /**
     * 设置B类违规主播数
     * @param breakBUv B类违规主播数
     */
    public void setBreakBUv(Long breakBUv) {
        this.breakBUv = breakBUv;
    }

    /**
     * 获取C类违规主播数
     * @return break_c_uv C类违规主播数
     */
    public Long getBreakCUv() {
        return breakCUv;
    }

    /**
     * 设置C类违规主播数
     * @param breakCUv C类违规主播数
     */
    public void setBreakCUv(Long breakCUv) {
        this.breakCUv = breakCUv;
    }

    /**
     * 获取E类违规主播数
     * @return break_e_uv E类违规主播数
     */
    public Long getBreakEUv() {
        return breakEUv;
    }

    /**
     * 设置E类违规主播数
     * @param breakEUv E类违规主播数
     */
    public void setBreakEUv(Long breakEUv) {
        this.breakEUv = breakEUv;
    }

    /**
     * 获取开播主播数
     * @return live_uv 开播主播数
     */
    public Long getLiveUv() {
        return liveUv;
    }

    /**
     * 设置开播主播数
     * @param liveUv 开播主播数
     */
    public void setLiveUv(Long liveUv) {
        this.liveUv = liveUv;
    }

    /**
     * 获取历史约当月违规主播数
     * @return old_break_uv 历史约当月违规主播数
     */
    public Long getOldBreakUv() {
        return oldBreakUv;
    }

    /**
     * 设置历史约当月违规主播数
     * @param oldBreakUv 历史约当月违规主播数
     */
    public void setOldBreakUv(Long oldBreakUv) {
        this.oldBreakUv = oldBreakUv;
    }

    /**
     * 获取违规主播数占比
     * @return break_uv_pp 违规主播数占比
     */
    public Double getBreakUvPp() {
        return breakUvPp;
    }

    /**
     * 设置违规主播数占比
     * @param breakUvPp 违规主播数占比
     */
    public void setBreakUvPp(Double breakUvPp) {
        this.breakUvPp = breakUvPp;
    }

    /**
     * 获取违规主播数占比环比上月增长数
     * @return break_uv_pp_rr 违规主播数占比环比上月增长数
     */
    public Double getBreakUvPpRr() {
        return breakUvPpRr;
    }

    /**
     * 设置违规主播数占比环比上月增长数
     * @param breakUvPpRr 违规主播数占比环比上月增长数
     */
    public void setBreakUvPpRr(Double breakUvPpRr) {
        this.breakUvPpRr = breakUvPpRr;
    }

    /**
     * 获取最终星级
     * @return ps_s_all 最终星级
     */
    public Integer getPsSAll() {
        return psSAll;
    }

    /**
     * 设置最终星级
     * @param psSAll 最终星级
     */
    public void setPsSAll(Integer psSAll) {
        this.psSAll = psSAll;
    }

    /**
     * 获取环比上月星级涨跌,若当月才有星级，上月无星级，则为null值
     * @return ps_s_all_rr 环比上月星级涨跌,若当月才有星级，上月无星级，则为null值
     */
    public Integer getPsSAllRr() {
        return psSAllRr;
    }

    /**
     * 设置环比上月星级涨跌,若当月才有星级，上月无星级，则为null值
     * @param psSAllRr 环比上月星级涨跌,若当月才有星级，上月无星级，则为null值
     */
    public void setPsSAllRr(Integer psSAllRr) {
        this.psSAllRr = psSAllRr;
    }

    /**
     * 获取旗下主播蓝钻收入星级
     * @return ps_s_month_diamond 旗下主播蓝钻收入星级
     */
    public Integer getPsSMonthDiamond() {
        return psSMonthDiamond;
    }

    /**
     * 设置旗下主播蓝钻收入星级
     * @param psSMonthDiamond 旗下主播蓝钻收入星级
     */
    public void setPsSMonthDiamond(Integer psSMonthDiamond) {
        this.psSMonthDiamond = psSMonthDiamond;
    }

    /**
     * 获取旗下主播累计蓝钻收入冲刺值
     * @return spr_month_diamond 旗下主播累计蓝钻收入冲刺值
     */
    public Double getSprMonthDiamond() {
        return sprMonthDiamond;
    }

    /**
     * 设置旗下主播累计蓝钻收入冲刺值
     * @param sprMonthDiamond 旗下主播累计蓝钻收入冲刺值
     */
    public void setSprMonthDiamond(Double sprMonthDiamond) {
        this.sprMonthDiamond = sprMonthDiamond;
    }

    /**
     * 获取日均有效开播主播数星级
     * @return ps_s_valid_live_uv 日均有效开播主播数星级
     */
    public Integer getPsSValidLiveUv() {
        return psSValidLiveUv;
    }

    /**
     * 设置日均有效开播主播数星级
     * @param psSValidLiveUv 日均有效开播主播数星级
     */
    public void setPsSValidLiveUv(Integer psSValidLiveUv) {
        this.psSValidLiveUv = psSValidLiveUv;
    }

    /**
     * 获取日均有效开播主播数冲刺值
     * @return spr_valid_live_uv 日均有效开播主播数冲刺值
     */
    public Double getSprValidLiveUv() {
        return sprValidLiveUv;
    }

    /**
     * 设置日均有效开播主播数冲刺值
     * @param sprValidLiveUv 日均有效开播主播数冲刺值
     */
    public void setSprValidLiveUv(Double sprValidLiveUv) {
        this.sprValidLiveUv = sprValidLiveUv;
    }

    /**
     * 获取人气主播总得分
     * @return ps_acu 人气主播总得分
     */
    public Double getPsAcu() {
        return psAcu;
    }

    /**
     * 设置人气主播总得分
     * @param psAcu 人气主播总得分
     */
    public void setPsAcu(Double psAcu) {
        this.psAcu = psAcu;
    }

    /**
     * 获取人气主播总得分星级
     * @return ps_s_acu 人气主播总得分星级
     */
    public Integer getPsSAcu() {
        return psSAcu;
    }

    /**
     * 设置人气主播总得分星级
     * @param psSAcu 人气主播总得分星级
     */
    public void setPsSAcu(Integer psSAcu) {
        this.psSAcu = psSAcu;
    }

    /**
     * 获取人气主播总得分冲刺值
     * @return spr_acu 人气主播总得分冲刺值
     */
    public Double getSprAcu() {
        return sprAcu;
    }

    /**
     * 设置人气主播总得分冲刺值
     * @param sprAcu 人气主播总得分冲刺值
     */
    public void setSprAcu(Double sprAcu) {
        this.sprAcu = sprAcu;
    }

    /**
     * 获取主播开播管理星级
     * @return ps_s_live_uv 主播开播管理星级
     */
    public Integer getPsSLiveUv() {
        return psSLiveUv;
    }

    /**
     * 设置主播开播管理星级
     * @param psSLiveUv 主播开播管理星级
     */
    public void setPsSLiveUv(Integer psSLiveUv) {
        this.psSLiveUv = psSLiveUv;
    }

    /**
     * 获取违规主播数占比星级
     * @return ps_s_break_uv_pp 违规主播数占比星级
     */
    public Integer getPsSBreakUvPp() {
        return psSBreakUvPp;
    }

    /**
     * 设置违规主播数占比星级
     * @param psSBreakUvPp 违规主播数占比星级
     */
    public void setPsSBreakUvPp(Integer psSBreakUvPp) {
        this.psSBreakUvPp = psSBreakUvPp;
    }

    /**
     * 获取违规主播数占比冲刺值
     * @return spr_break_uv_pp 违规主播数占比冲刺值
     */
    public Double getSprBreakUvPp() {
        return sprBreakUvPp;
    }

    /**
     * 设置违规主播数占比冲刺值
     * @param sprBreakUvPp 违规主播数占比冲刺值
     */
    public void setSprBreakUvPp(Double sprBreakUvPp) {
        this.sprBreakUvPp = sprBreakUvPp;
    }

    /**
     * 获取月份
     * @return dt 月份
     */
    public String getDt() {
        return dt;
    }

    /**
     * 设置月份
     * @param dt 月份
     */
    public void setDt(String dt) {
        this.dt = dt;
    }

    /**
     * 获取日均高优有效开播主播数，从202308开始有效
     * @return avg_high_aid_num 日均高优有效开播主播数，从202308开始有效
     */
    public Double getAvgHighAidNum() {
        return avgHighAidNum;
    }

    /**
     * 设置日均高优有效开播主播数，从202308开始有效
     * @param avgHighAidNum 日均高优有效开播主播数，从202308开始有效
     */
    public void setAvgHighAidNum(Double avgHighAidNum) {
        this.avgHighAidNum = avgHighAidNum;
    }

    /**
     * 获取日均高优有效开播主播数环比上月增长数，从202308开始有效
     * @return avg_high_aid_num_rr 日均高优有效开播主播数环比上月增长数，从202308开始有效
     */
    public Double getAvgHighAidNumRr() {
        return avgHighAidNumRr;
    }

    /**
     * 设置日均高优有效开播主播数环比上月增长数，从202308开始有效
     * @param avgHighAidNumRr 日均高优有效开播主播数环比上月增长数，从202308开始有效
     */
    public void setAvgHighAidNumRr(Double avgHighAidNumRr) {
        this.avgHighAidNumRr = avgHighAidNumRr;
    }

    /**
     * 获取日均腰部有效开播主播数，从202308开始有效
     * @return avg_waist_aid_num 日均腰部有效开播主播数，从202308开始有效
     */
    public Double getAvgWaistAidNum() {
        return avgWaistAidNum;
    }

    /**
     * 设置日均腰部有效开播主播数，从202308开始有效
     * @param avgWaistAidNum 日均腰部有效开播主播数，从202308开始有效
     */
    public void setAvgWaistAidNum(Double avgWaistAidNum) {
        this.avgWaistAidNum = avgWaistAidNum;
    }

    /**
     * 获取日均腰部有效开播主播数环比上月增长数，从202308开始有效
     * @return avg_waist_aid_num_rr 日均腰部有效开播主播数环比上月增长数，从202308开始有效
     */
    public Double getAvgWaistAidNumRr() {
        return avgWaistAidNumRr;
    }

    /**
     * 设置日均腰部有效开播主播数环比上月增长数，从202308开始有效
     * @param avgWaistAidNumRr 日均腰部有效开播主播数环比上月增长数，从202308开始有效
     */
    public void setAvgWaistAidNumRr(Double avgWaistAidNumRr) {
        this.avgWaistAidNumRr = avgWaistAidNumRr;
    }

    /**
     * 获取日均潜力有效开播主播数，从202308开始有效
     * @return avg_tail_aid_num 日均潜力有效开播主播数，从202308开始有效
     */
    public Double getAvgTailAidNum() {
        return avgTailAidNum;
    }

    /**
     * 设置日均潜力有效开播主播数，从202308开始有效
     * @param avgTailAidNum 日均潜力有效开播主播数，从202308开始有效
     */
    public void setAvgTailAidNum(Double avgTailAidNum) {
        this.avgTailAidNum = avgTailAidNum;
    }

    /**
     * 获取日均潜力有效开播主播数环比上月增长数，从202308开始有效
     * @return avg_tail_aid_num_rr 日均潜力有效开播主播数环比上月增长数，从202308开始有效
     */
    public Double getAvgTailAidNumRr() {
        return avgTailAidNumRr;
    }

    /**
     * 设置日均潜力有效开播主播数环比上月增长数，从202308开始有效
     * @param avgTailAidNumRr 日均潜力有效开播主播数环比上月增长数，从202308开始有效
     */
    public void setAvgTailAidNumRr(Double avgTailAidNumRr) {
        this.avgTailAidNumRr = avgTailAidNumRr;
    }

    /**
     * 获取等级主播总得分，从202308开始有效
     * @return aid_value_score 等级主播总得分，从202308开始有效
     */
    public Double getAidValueScore() {
        return aidValueScore;
    }

    /**
     * 设置等级主播总得分，从202308开始有效
     * @param aidValueScore 等级主播总得分，从202308开始有效
     */
    public void setAidValueScore(Double aidValueScore) {
        this.aidValueScore = aidValueScore;
    }

    /**
     * 获取等级主播总得分星级，从202308开始有效
     * @return aid_value_score_star_lvl 等级主播总得分星级，从202308开始有效
     */
    public Integer getAidValueScoreStarLvl() {
        return aidValueScoreStarLvl;
    }

    /**
     * 设置等级主播总得分星级，从202308开始有效
     * @param aidValueScoreStarLvl 等级主播总得分星级，从202308开始有效
     */
    public void setAidValueScoreStarLvl(Integer aidValueScoreStarLvl) {
        this.aidValueScoreStarLvl = aidValueScoreStarLvl;
    }

    /**
     * 获取等级主播总得分冲刺值，从202308开始有效
     * @return aid_value_score_sprint_value 等级主播总得分冲刺值，从202308开始有效
     */
    public Double getAidValueScoreSprintValue() {
        return aidValueScoreSprintValue;
    }

    /**
     * 设置等级主播总得分冲刺值，从202308开始有效
     * @param aidValueScoreSprintValue 等级主播总得分冲刺值，从202308开始有效
     */
    public void setAidValueScoreSprintValue(Double aidValueScoreSprintValue) {
        this.aidValueScoreSprintValue = aidValueScoreSprintValue;
    }

    /**
     * 获取是否小火星公会
     * @return is_mars_guild 是否小火星公会
     */
    public Integer getIsMarsGuild() {
        return isMarsGuild;
    }

    /**
     * 设置是否小火星公会
     * @param isMarsGuild 是否小火星公会
     */
    public void setIsMarsGuild(Integer isMarsGuild) {
        this.isMarsGuild = isMarsGuild;
    }

    /**
     * 获取关联的SDK小火星公会UID
     * @return mars_sid_ownerid 关联的SDK小火星公会UID
     */
    public Long getMarsSidOwnerid() {
        return marsSidOwnerid;
    }

    /**
     * 设置关联的SDK小火星公会UID
     * @param marsSidOwnerid 关联的SDK小火星公会UID
     */
    public void setMarsSidOwnerid(Long marsSidOwnerid) {
        this.marsSidOwnerid = marsSidOwnerid;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", sidOwnerid=").append(sidOwnerid);
        sb.append(", sidOwyyid=").append(sidOwyyid);
        sb.append(", monthDiamond=").append(monthDiamond);
        sb.append(", monthDiamondRr=").append(monthDiamondRr);
        sb.append(", validLiveUv=").append(validLiveUv);
        sb.append(", validLiveUvRr=").append(validLiveUvRr);
        sb.append(", acu300Uv=").append(acu300Uv);
        sb.append(", acu300UvRr=").append(acu300UvRr);
        sb.append(", acu50300Uv=").append(acu50300Uv);
        sb.append(", acu50300UvRr=").append(acu50300UvRr);
        sb.append(", acu1050Uv=").append(acu1050Uv);
        sb.append(", acu1050UvRr=").append(acu1050UvRr);
        sb.append(", acu10Uv=").append(acu10Uv);
        sb.append(", acu10UvRr=").append(acu10UvRr);
        sb.append(", breakAUv=").append(breakAUv);
        sb.append(", breakBUv=").append(breakBUv);
        sb.append(", breakCUv=").append(breakCUv);
        sb.append(", breakEUv=").append(breakEUv);
        sb.append(", liveUv=").append(liveUv);
        sb.append(", oldBreakUv=").append(oldBreakUv);
        sb.append(", breakUvPp=").append(breakUvPp);
        sb.append(", breakUvPpRr=").append(breakUvPpRr);
        sb.append(", psSAll=").append(psSAll);
        sb.append(", psSAllRr=").append(psSAllRr);
        sb.append(", psSMonthDiamond=").append(psSMonthDiamond);
        sb.append(", sprMonthDiamond=").append(sprMonthDiamond);
        sb.append(", psSValidLiveUv=").append(psSValidLiveUv);
        sb.append(", sprValidLiveUv=").append(sprValidLiveUv);
        sb.append(", psAcu=").append(psAcu);
        sb.append(", psSAcu=").append(psSAcu);
        sb.append(", sprAcu=").append(sprAcu);
        sb.append(", psSLiveUv=").append(psSLiveUv);
        sb.append(", psSBreakUvPp=").append(psSBreakUvPp);
        sb.append(", sprBreakUvPp=").append(sprBreakUvPp);
        sb.append(", dt=").append(dt);
        sb.append(", avgHighAidNum=").append(avgHighAidNum);
        sb.append(", avgHighAidNumRr=").append(avgHighAidNumRr);
        sb.append(", avgWaistAidNum=").append(avgWaistAidNum);
        sb.append(", avgWaistAidNumRr=").append(avgWaistAidNumRr);
        sb.append(", avgTailAidNum=").append(avgTailAidNum);
        sb.append(", avgTailAidNumRr=").append(avgTailAidNumRr);
        sb.append(", aidValueScore=").append(aidValueScore);
        sb.append(", aidValueScoreStarLvl=").append(aidValueScoreStarLvl);
        sb.append(", aidValueScoreSprintValue=").append(aidValueScoreSprintValue);
        sb.append(", isMarsGuild=").append(isMarsGuild);
        sb.append(", marsSidOwnerid=").append(marsSidOwnerid);
        sb.append("]");
        return sb.toString();
    }
}