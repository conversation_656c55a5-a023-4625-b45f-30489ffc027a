<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yy.yyzone.guildrank.db.gen.mapper.GuildrankConfigLogMapper" >
  <resultMap id="BaseResultMap" type="com.yy.yyzone.guildrank.db.gen.model.GuildrankConfigLog" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="config_id" property="configId" jdbcType="BIGINT" />
    <result column="opt_passport" property="optPassport" jdbcType="VARCHAR" />
    <result column="opt_time" property="optTime" jdbcType="TIMESTAMP" />
    <result column="type" property="type" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.yy.yyzone.guildrank.db.gen.model.GuildrankConfigLog" extends="BaseResultMap" >
    <result column="old_data" property="oldData" jdbcType="LONGVARCHAR" />
    <result column="new_data" property="newData" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, config_id, opt_passport, opt_time, type
  </sql>
  <sql id="Blob_Column_List" >
    old_data, new_data
  </sql>
  <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankConfigLogExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from guildrank_config_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankConfigLogExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from guildrank_config_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit > 0" >
      limit ${limit}
    </if>
    <if test="offset > 0" >
      offset ${offset}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from guildrank_config_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from guildrank_config_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankConfigLogExample" >
    delete from guildrank_config_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankConfigLog" useGeneratedKeys="true" keyProperty="id" >
    insert into guildrank_config_log (config_id, opt_passport, opt_time, 
      type, old_data, new_data
      )
    values (#{configId,jdbcType=BIGINT}, #{optPassport,jdbcType=VARCHAR}, #{optTime,jdbcType=TIMESTAMP}, 
      #{type,jdbcType=VARCHAR}, #{oldData,jdbcType=LONGVARCHAR}, #{newData,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankConfigLog" useGeneratedKeys="true" keyProperty="id" >
    insert into guildrank_config_log
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="configId != null" >
        config_id,
      </if>
      <if test="optPassport != null" >
        opt_passport,
      </if>
      <if test="optTime != null" >
        opt_time,
      </if>
      <if test="type != null" >
        type,
      </if>
      <if test="oldData != null" >
        old_data,
      </if>
      <if test="newData != null" >
        new_data,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="configId != null" >
        #{configId,jdbcType=BIGINT},
      </if>
      <if test="optPassport != null" >
        #{optPassport,jdbcType=VARCHAR},
      </if>
      <if test="optTime != null" >
        #{optTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null" >
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="oldData != null" >
        #{oldData,jdbcType=LONGVARCHAR},
      </if>
      <if test="newData != null" >
        #{newData,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankConfigLogExample" resultType="java.lang.Integer" >
    select count(*) from guildrank_config_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update guildrank_config_log
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.configId != null" >
        config_id = #{record.configId,jdbcType=BIGINT},
      </if>
      <if test="record.optPassport != null" >
        opt_passport = #{record.optPassport,jdbcType=VARCHAR},
      </if>
      <if test="record.optTime != null" >
        opt_time = #{record.optTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.type != null" >
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.oldData != null" >
        old_data = #{record.oldData,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.newData != null" >
        new_data = #{record.newData,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map" >
    update guildrank_config_log
    set id = #{record.id,jdbcType=BIGINT},
      config_id = #{record.configId,jdbcType=BIGINT},
      opt_passport = #{record.optPassport,jdbcType=VARCHAR},
      opt_time = #{record.optTime,jdbcType=TIMESTAMP},
      type = #{record.type,jdbcType=VARCHAR},
      old_data = #{record.oldData,jdbcType=LONGVARCHAR},
      new_data = #{record.newData,jdbcType=LONGVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update guildrank_config_log
    set id = #{record.id,jdbcType=BIGINT},
      config_id = #{record.configId,jdbcType=BIGINT},
      opt_passport = #{record.optPassport,jdbcType=VARCHAR},
      opt_time = #{record.optTime,jdbcType=TIMESTAMP},
      type = #{record.type,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankConfigLog" >
    update guildrank_config_log
    <set >
      <if test="configId != null" >
        config_id = #{configId,jdbcType=BIGINT},
      </if>
      <if test="optPassport != null" >
        opt_passport = #{optPassport,jdbcType=VARCHAR},
      </if>
      <if test="optTime != null" >
        opt_time = #{optTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null" >
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="oldData != null" >
        old_data = #{oldData,jdbcType=LONGVARCHAR},
      </if>
      <if test="newData != null" >
        new_data = #{newData,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankConfigLog" >
    update guildrank_config_log
    set config_id = #{configId,jdbcType=BIGINT},
      opt_passport = #{optPassport,jdbcType=VARCHAR},
      opt_time = #{optTime,jdbcType=TIMESTAMP},
      type = #{type,jdbcType=VARCHAR},
      old_data = #{oldData,jdbcType=LONGVARCHAR},
      new_data = #{newData,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankConfigLog" >
    update guildrank_config_log
    set config_id = #{configId,jdbcType=BIGINT},
      opt_passport = #{optPassport,jdbcType=VARCHAR},
      opt_time = #{optTime,jdbcType=TIMESTAMP},
      type = #{type,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>