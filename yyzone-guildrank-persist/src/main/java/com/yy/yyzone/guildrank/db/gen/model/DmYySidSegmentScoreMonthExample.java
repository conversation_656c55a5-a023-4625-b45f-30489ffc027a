package com.yy.yyzone.guildrank.db.gen.model;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated("dm_yy_sid_segment_score_month")
public class DmYySidSegmentScoreMonthExample {
    /**
     * dm_yy_sid_segment_score_month
     */
    protected String orderByClause;

    /**
     * dm_yy_sid_segment_score_month
     */
    protected boolean distinct;

    /**
     * dm_yy_sid_segment_score_month
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public DmYySidSegmentScoreMonthExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    /**
     * dm_yy_sid_segment_score_month null
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSidOwneridIsNull() {
            addCriterion("sid_ownerid is null");
            return (Criteria) this;
        }

        public Criteria andSidOwneridIsNotNull() {
            addCriterion("sid_ownerid is not null");
            return (Criteria) this;
        }

        public Criteria andSidOwneridEqualTo(Long value) {
            addCriterion("sid_ownerid =", value, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridNotEqualTo(Long value) {
            addCriterion("sid_ownerid <>", value, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridGreaterThan(Long value) {
            addCriterion("sid_ownerid >", value, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridGreaterThanOrEqualTo(Long value) {
            addCriterion("sid_ownerid >=", value, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridLessThan(Long value) {
            addCriterion("sid_ownerid <", value, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridLessThanOrEqualTo(Long value) {
            addCriterion("sid_ownerid <=", value, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridIn(List<Long> values) {
            addCriterion("sid_ownerid in", values, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridNotIn(List<Long> values) {
            addCriterion("sid_ownerid not in", values, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridBetween(Long value1, Long value2) {
            addCriterion("sid_ownerid between", value1, value2, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridNotBetween(Long value1, Long value2) {
            addCriterion("sid_ownerid not between", value1, value2, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidIsNull() {
            addCriterion("sid_owyyid is null");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidIsNotNull() {
            addCriterion("sid_owyyid is not null");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidEqualTo(Long value) {
            addCriterion("sid_owyyid =", value, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidNotEqualTo(Long value) {
            addCriterion("sid_owyyid <>", value, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidGreaterThan(Long value) {
            addCriterion("sid_owyyid >", value, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidGreaterThanOrEqualTo(Long value) {
            addCriterion("sid_owyyid >=", value, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidLessThan(Long value) {
            addCriterion("sid_owyyid <", value, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidLessThanOrEqualTo(Long value) {
            addCriterion("sid_owyyid <=", value, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidIn(List<Long> values) {
            addCriterion("sid_owyyid in", values, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidNotIn(List<Long> values) {
            addCriterion("sid_owyyid not in", values, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidBetween(Long value1, Long value2) {
            addCriterion("sid_owyyid between", value1, value2, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidNotBetween(Long value1, Long value2) {
            addCriterion("sid_owyyid not between", value1, value2, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andNewUvIsNull() {
            addCriterion("new_uv is null");
            return (Criteria) this;
        }

        public Criteria andNewUvIsNotNull() {
            addCriterion("new_uv is not null");
            return (Criteria) this;
        }

        public Criteria andNewUvEqualTo(Long value) {
            addCriterion("new_uv =", value, "newUv");
            return (Criteria) this;
        }

        public Criteria andNewUvNotEqualTo(Long value) {
            addCriterion("new_uv <>", value, "newUv");
            return (Criteria) this;
        }

        public Criteria andNewUvGreaterThan(Long value) {
            addCriterion("new_uv >", value, "newUv");
            return (Criteria) this;
        }

        public Criteria andNewUvGreaterThanOrEqualTo(Long value) {
            addCriterion("new_uv >=", value, "newUv");
            return (Criteria) this;
        }

        public Criteria andNewUvLessThan(Long value) {
            addCriterion("new_uv <", value, "newUv");
            return (Criteria) this;
        }

        public Criteria andNewUvLessThanOrEqualTo(Long value) {
            addCriterion("new_uv <=", value, "newUv");
            return (Criteria) this;
        }

        public Criteria andNewUvIn(List<Long> values) {
            addCriterion("new_uv in", values, "newUv");
            return (Criteria) this;
        }

        public Criteria andNewUvNotIn(List<Long> values) {
            addCriterion("new_uv not in", values, "newUv");
            return (Criteria) this;
        }

        public Criteria andNewUvBetween(Long value1, Long value2) {
            addCriterion("new_uv between", value1, value2, "newUv");
            return (Criteria) this;
        }

        public Criteria andNewUvNotBetween(Long value1, Long value2) {
            addCriterion("new_uv not between", value1, value2, "newUv");
            return (Criteria) this;
        }

        public Criteria andNewUvRrIsNull() {
            addCriterion("new_uv_rr is null");
            return (Criteria) this;
        }

        public Criteria andNewUvRrIsNotNull() {
            addCriterion("new_uv_rr is not null");
            return (Criteria) this;
        }

        public Criteria andNewUvRrEqualTo(Long value) {
            addCriterion("new_uv_rr =", value, "newUvRr");
            return (Criteria) this;
        }

        public Criteria andNewUvRrNotEqualTo(Long value) {
            addCriterion("new_uv_rr <>", value, "newUvRr");
            return (Criteria) this;
        }

        public Criteria andNewUvRrGreaterThan(Long value) {
            addCriterion("new_uv_rr >", value, "newUvRr");
            return (Criteria) this;
        }

        public Criteria andNewUvRrGreaterThanOrEqualTo(Long value) {
            addCriterion("new_uv_rr >=", value, "newUvRr");
            return (Criteria) this;
        }

        public Criteria andNewUvRrLessThan(Long value) {
            addCriterion("new_uv_rr <", value, "newUvRr");
            return (Criteria) this;
        }

        public Criteria andNewUvRrLessThanOrEqualTo(Long value) {
            addCriterion("new_uv_rr <=", value, "newUvRr");
            return (Criteria) this;
        }

        public Criteria andNewUvRrIn(List<Long> values) {
            addCriterion("new_uv_rr in", values, "newUvRr");
            return (Criteria) this;
        }

        public Criteria andNewUvRrNotIn(List<Long> values) {
            addCriterion("new_uv_rr not in", values, "newUvRr");
            return (Criteria) this;
        }

        public Criteria andNewUvRrBetween(Long value1, Long value2) {
            addCriterion("new_uv_rr between", value1, value2, "newUvRr");
            return (Criteria) this;
        }

        public Criteria andNewUvRrNotBetween(Long value1, Long value2) {
            addCriterion("new_uv_rr not between", value1, value2, "newUvRr");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewUvIsNull() {
            addCriterion("valid_live_new_uv is null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewUvIsNotNull() {
            addCriterion("valid_live_new_uv is not null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewUvEqualTo(Long value) {
            addCriterion("valid_live_new_uv =", value, "validLiveNewUv");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewUvNotEqualTo(Long value) {
            addCriterion("valid_live_new_uv <>", value, "validLiveNewUv");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewUvGreaterThan(Long value) {
            addCriterion("valid_live_new_uv >", value, "validLiveNewUv");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewUvGreaterThanOrEqualTo(Long value) {
            addCriterion("valid_live_new_uv >=", value, "validLiveNewUv");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewUvLessThan(Long value) {
            addCriterion("valid_live_new_uv <", value, "validLiveNewUv");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewUvLessThanOrEqualTo(Long value) {
            addCriterion("valid_live_new_uv <=", value, "validLiveNewUv");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewUvIn(List<Long> values) {
            addCriterion("valid_live_new_uv in", values, "validLiveNewUv");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewUvNotIn(List<Long> values) {
            addCriterion("valid_live_new_uv not in", values, "validLiveNewUv");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewUvBetween(Long value1, Long value2) {
            addCriterion("valid_live_new_uv between", value1, value2, "validLiveNewUv");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewUvNotBetween(Long value1, Long value2) {
            addCriterion("valid_live_new_uv not between", value1, value2, "validLiveNewUv");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewUvRrIsNull() {
            addCriterion("valid_live_new_uv_rr is null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewUvRrIsNotNull() {
            addCriterion("valid_live_new_uv_rr is not null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewUvRrEqualTo(Long value) {
            addCriterion("valid_live_new_uv_rr =", value, "validLiveNewUvRr");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewUvRrNotEqualTo(Long value) {
            addCriterion("valid_live_new_uv_rr <>", value, "validLiveNewUvRr");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewUvRrGreaterThan(Long value) {
            addCriterion("valid_live_new_uv_rr >", value, "validLiveNewUvRr");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewUvRrGreaterThanOrEqualTo(Long value) {
            addCriterion("valid_live_new_uv_rr >=", value, "validLiveNewUvRr");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewUvRrLessThan(Long value) {
            addCriterion("valid_live_new_uv_rr <", value, "validLiveNewUvRr");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewUvRrLessThanOrEqualTo(Long value) {
            addCriterion("valid_live_new_uv_rr <=", value, "validLiveNewUvRr");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewUvRrIn(List<Long> values) {
            addCriterion("valid_live_new_uv_rr in", values, "validLiveNewUvRr");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewUvRrNotIn(List<Long> values) {
            addCriterion("valid_live_new_uv_rr not in", values, "validLiveNewUvRr");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewUvRrBetween(Long value1, Long value2) {
            addCriterion("valid_live_new_uv_rr between", value1, value2, "validLiveNewUvRr");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewUvRrNotBetween(Long value1, Long value2) {
            addCriterion("valid_live_new_uv_rr not between", value1, value2, "validLiveNewUvRr");
            return (Criteria) this;
        }

        public Criteria andPerNewUvIsNull() {
            addCriterion("per_new_uv is null");
            return (Criteria) this;
        }

        public Criteria andPerNewUvIsNotNull() {
            addCriterion("per_new_uv is not null");
            return (Criteria) this;
        }

        public Criteria andPerNewUvEqualTo(Double value) {
            addCriterion("per_new_uv =", value, "perNewUv");
            return (Criteria) this;
        }

        public Criteria andPerNewUvNotEqualTo(Double value) {
            addCriterion("per_new_uv <>", value, "perNewUv");
            return (Criteria) this;
        }

        public Criteria andPerNewUvGreaterThan(Double value) {
            addCriterion("per_new_uv >", value, "perNewUv");
            return (Criteria) this;
        }

        public Criteria andPerNewUvGreaterThanOrEqualTo(Double value) {
            addCriterion("per_new_uv >=", value, "perNewUv");
            return (Criteria) this;
        }

        public Criteria andPerNewUvLessThan(Double value) {
            addCriterion("per_new_uv <", value, "perNewUv");
            return (Criteria) this;
        }

        public Criteria andPerNewUvLessThanOrEqualTo(Double value) {
            addCriterion("per_new_uv <=", value, "perNewUv");
            return (Criteria) this;
        }

        public Criteria andPerNewUvIn(List<Double> values) {
            addCriterion("per_new_uv in", values, "perNewUv");
            return (Criteria) this;
        }

        public Criteria andPerNewUvNotIn(List<Double> values) {
            addCriterion("per_new_uv not in", values, "perNewUv");
            return (Criteria) this;
        }

        public Criteria andPerNewUvBetween(Double value1, Double value2) {
            addCriterion("per_new_uv between", value1, value2, "perNewUv");
            return (Criteria) this;
        }

        public Criteria andPerNewUvNotBetween(Double value1, Double value2) {
            addCriterion("per_new_uv not between", value1, value2, "perNewUv");
            return (Criteria) this;
        }

        public Criteria andPerValidLiveNewUvIsNull() {
            addCriterion("per_valid_live_new_uv is null");
            return (Criteria) this;
        }

        public Criteria andPerValidLiveNewUvIsNotNull() {
            addCriterion("per_valid_live_new_uv is not null");
            return (Criteria) this;
        }

        public Criteria andPerValidLiveNewUvEqualTo(Double value) {
            addCriterion("per_valid_live_new_uv =", value, "perValidLiveNewUv");
            return (Criteria) this;
        }

        public Criteria andPerValidLiveNewUvNotEqualTo(Double value) {
            addCriterion("per_valid_live_new_uv <>", value, "perValidLiveNewUv");
            return (Criteria) this;
        }

        public Criteria andPerValidLiveNewUvGreaterThan(Double value) {
            addCriterion("per_valid_live_new_uv >", value, "perValidLiveNewUv");
            return (Criteria) this;
        }

        public Criteria andPerValidLiveNewUvGreaterThanOrEqualTo(Double value) {
            addCriterion("per_valid_live_new_uv >=", value, "perValidLiveNewUv");
            return (Criteria) this;
        }

        public Criteria andPerValidLiveNewUvLessThan(Double value) {
            addCriterion("per_valid_live_new_uv <", value, "perValidLiveNewUv");
            return (Criteria) this;
        }

        public Criteria andPerValidLiveNewUvLessThanOrEqualTo(Double value) {
            addCriterion("per_valid_live_new_uv <=", value, "perValidLiveNewUv");
            return (Criteria) this;
        }

        public Criteria andPerValidLiveNewUvIn(List<Double> values) {
            addCriterion("per_valid_live_new_uv in", values, "perValidLiveNewUv");
            return (Criteria) this;
        }

        public Criteria andPerValidLiveNewUvNotIn(List<Double> values) {
            addCriterion("per_valid_live_new_uv not in", values, "perValidLiveNewUv");
            return (Criteria) this;
        }

        public Criteria andPerValidLiveNewUvBetween(Double value1, Double value2) {
            addCriterion("per_valid_live_new_uv between", value1, value2, "perValidLiveNewUv");
            return (Criteria) this;
        }

        public Criteria andPerValidLiveNewUvNotBetween(Double value1, Double value2) {
            addCriterion("per_valid_live_new_uv not between", value1, value2, "perValidLiveNewUv");
            return (Criteria) this;
        }

        public Criteria andNewUvRtIsNull() {
            addCriterion("new_uv_rt is null");
            return (Criteria) this;
        }

        public Criteria andNewUvRtIsNotNull() {
            addCriterion("new_uv_rt is not null");
            return (Criteria) this;
        }

        public Criteria andNewUvRtEqualTo(Double value) {
            addCriterion("new_uv_rt =", value, "newUvRt");
            return (Criteria) this;
        }

        public Criteria andNewUvRtNotEqualTo(Double value) {
            addCriterion("new_uv_rt <>", value, "newUvRt");
            return (Criteria) this;
        }

        public Criteria andNewUvRtGreaterThan(Double value) {
            addCriterion("new_uv_rt >", value, "newUvRt");
            return (Criteria) this;
        }

        public Criteria andNewUvRtGreaterThanOrEqualTo(Double value) {
            addCriterion("new_uv_rt >=", value, "newUvRt");
            return (Criteria) this;
        }

        public Criteria andNewUvRtLessThan(Double value) {
            addCriterion("new_uv_rt <", value, "newUvRt");
            return (Criteria) this;
        }

        public Criteria andNewUvRtLessThanOrEqualTo(Double value) {
            addCriterion("new_uv_rt <=", value, "newUvRt");
            return (Criteria) this;
        }

        public Criteria andNewUvRtIn(List<Double> values) {
            addCriterion("new_uv_rt in", values, "newUvRt");
            return (Criteria) this;
        }

        public Criteria andNewUvRtNotIn(List<Double> values) {
            addCriterion("new_uv_rt not in", values, "newUvRt");
            return (Criteria) this;
        }

        public Criteria andNewUvRtBetween(Double value1, Double value2) {
            addCriterion("new_uv_rt between", value1, value2, "newUvRt");
            return (Criteria) this;
        }

        public Criteria andNewUvRtNotBetween(Double value1, Double value2) {
            addCriterion("new_uv_rt not between", value1, value2, "newUvRt");
            return (Criteria) this;
        }

        public Criteria andAcu10UvIsNull() {
            addCriterion("acu10_uv is null");
            return (Criteria) this;
        }

        public Criteria andAcu10UvIsNotNull() {
            addCriterion("acu10_uv is not null");
            return (Criteria) this;
        }

        public Criteria andAcu10UvEqualTo(Long value) {
            addCriterion("acu10_uv =", value, "acu10Uv");
            return (Criteria) this;
        }

        public Criteria andAcu10UvNotEqualTo(Long value) {
            addCriterion("acu10_uv <>", value, "acu10Uv");
            return (Criteria) this;
        }

        public Criteria andAcu10UvGreaterThan(Long value) {
            addCriterion("acu10_uv >", value, "acu10Uv");
            return (Criteria) this;
        }

        public Criteria andAcu10UvGreaterThanOrEqualTo(Long value) {
            addCriterion("acu10_uv >=", value, "acu10Uv");
            return (Criteria) this;
        }

        public Criteria andAcu10UvLessThan(Long value) {
            addCriterion("acu10_uv <", value, "acu10Uv");
            return (Criteria) this;
        }

        public Criteria andAcu10UvLessThanOrEqualTo(Long value) {
            addCriterion("acu10_uv <=", value, "acu10Uv");
            return (Criteria) this;
        }

        public Criteria andAcu10UvIn(List<Long> values) {
            addCriterion("acu10_uv in", values, "acu10Uv");
            return (Criteria) this;
        }

        public Criteria andAcu10UvNotIn(List<Long> values) {
            addCriterion("acu10_uv not in", values, "acu10Uv");
            return (Criteria) this;
        }

        public Criteria andAcu10UvBetween(Long value1, Long value2) {
            addCriterion("acu10_uv between", value1, value2, "acu10Uv");
            return (Criteria) this;
        }

        public Criteria andAcu10UvNotBetween(Long value1, Long value2) {
            addCriterion("acu10_uv not between", value1, value2, "acu10Uv");
            return (Criteria) this;
        }

        public Criteria andAcu10UvRrIsNull() {
            addCriterion("acu10_uv_rr is null");
            return (Criteria) this;
        }

        public Criteria andAcu10UvRrIsNotNull() {
            addCriterion("acu10_uv_rr is not null");
            return (Criteria) this;
        }

        public Criteria andAcu10UvRrEqualTo(Long value) {
            addCriterion("acu10_uv_rr =", value, "acu10UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu10UvRrNotEqualTo(Long value) {
            addCriterion("acu10_uv_rr <>", value, "acu10UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu10UvRrGreaterThan(Long value) {
            addCriterion("acu10_uv_rr >", value, "acu10UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu10UvRrGreaterThanOrEqualTo(Long value) {
            addCriterion("acu10_uv_rr >=", value, "acu10UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu10UvRrLessThan(Long value) {
            addCriterion("acu10_uv_rr <", value, "acu10UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu10UvRrLessThanOrEqualTo(Long value) {
            addCriterion("acu10_uv_rr <=", value, "acu10UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu10UvRrIn(List<Long> values) {
            addCriterion("acu10_uv_rr in", values, "acu10UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu10UvRrNotIn(List<Long> values) {
            addCriterion("acu10_uv_rr not in", values, "acu10UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu10UvRrBetween(Long value1, Long value2) {
            addCriterion("acu10_uv_rr between", value1, value2, "acu10UvRr");
            return (Criteria) this;
        }

        public Criteria andAcu10UvRrNotBetween(Long value1, Long value2) {
            addCriterion("acu10_uv_rr not between", value1, value2, "acu10UvRr");
            return (Criteria) this;
        }

        public Criteria andPerAcu10UvIsNull() {
            addCriterion("per_acu10_uv is null");
            return (Criteria) this;
        }

        public Criteria andPerAcu10UvIsNotNull() {
            addCriterion("per_acu10_uv is not null");
            return (Criteria) this;
        }

        public Criteria andPerAcu10UvEqualTo(Double value) {
            addCriterion("per_acu10_uv =", value, "perAcu10Uv");
            return (Criteria) this;
        }

        public Criteria andPerAcu10UvNotEqualTo(Double value) {
            addCriterion("per_acu10_uv <>", value, "perAcu10Uv");
            return (Criteria) this;
        }

        public Criteria andPerAcu10UvGreaterThan(Double value) {
            addCriterion("per_acu10_uv >", value, "perAcu10Uv");
            return (Criteria) this;
        }

        public Criteria andPerAcu10UvGreaterThanOrEqualTo(Double value) {
            addCriterion("per_acu10_uv >=", value, "perAcu10Uv");
            return (Criteria) this;
        }

        public Criteria andPerAcu10UvLessThan(Double value) {
            addCriterion("per_acu10_uv <", value, "perAcu10Uv");
            return (Criteria) this;
        }

        public Criteria andPerAcu10UvLessThanOrEqualTo(Double value) {
            addCriterion("per_acu10_uv <=", value, "perAcu10Uv");
            return (Criteria) this;
        }

        public Criteria andPerAcu10UvIn(List<Double> values) {
            addCriterion("per_acu10_uv in", values, "perAcu10Uv");
            return (Criteria) this;
        }

        public Criteria andPerAcu10UvNotIn(List<Double> values) {
            addCriterion("per_acu10_uv not in", values, "perAcu10Uv");
            return (Criteria) this;
        }

        public Criteria andPerAcu10UvBetween(Double value1, Double value2) {
            addCriterion("per_acu10_uv between", value1, value2, "perAcu10Uv");
            return (Criteria) this;
        }

        public Criteria andPerAcu10UvNotBetween(Double value1, Double value2) {
            addCriterion("per_acu10_uv not between", value1, value2, "perAcu10Uv");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvIsNull() {
            addCriterion("acu10_50_uv is null");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvIsNotNull() {
            addCriterion("acu10_50_uv is not null");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvEqualTo(Long value) {
            addCriterion("acu10_50_uv =", value, "acu1050Uv");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvNotEqualTo(Long value) {
            addCriterion("acu10_50_uv <>", value, "acu1050Uv");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvGreaterThan(Long value) {
            addCriterion("acu10_50_uv >", value, "acu1050Uv");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvGreaterThanOrEqualTo(Long value) {
            addCriterion("acu10_50_uv >=", value, "acu1050Uv");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvLessThan(Long value) {
            addCriterion("acu10_50_uv <", value, "acu1050Uv");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvLessThanOrEqualTo(Long value) {
            addCriterion("acu10_50_uv <=", value, "acu1050Uv");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvIn(List<Long> values) {
            addCriterion("acu10_50_uv in", values, "acu1050Uv");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvNotIn(List<Long> values) {
            addCriterion("acu10_50_uv not in", values, "acu1050Uv");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvBetween(Long value1, Long value2) {
            addCriterion("acu10_50_uv between", value1, value2, "acu1050Uv");
            return (Criteria) this;
        }

        public Criteria andAcu1050UvNotBetween(Long value1, Long value2) {
            addCriterion("acu10_50_uv not between", value1, value2, "acu1050Uv");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvIsNull() {
            addCriterion("acu50_300_uv is null");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvIsNotNull() {
            addCriterion("acu50_300_uv is not null");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvEqualTo(Long value) {
            addCriterion("acu50_300_uv =", value, "acu50300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvNotEqualTo(Long value) {
            addCriterion("acu50_300_uv <>", value, "acu50300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvGreaterThan(Long value) {
            addCriterion("acu50_300_uv >", value, "acu50300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvGreaterThanOrEqualTo(Long value) {
            addCriterion("acu50_300_uv >=", value, "acu50300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvLessThan(Long value) {
            addCriterion("acu50_300_uv <", value, "acu50300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvLessThanOrEqualTo(Long value) {
            addCriterion("acu50_300_uv <=", value, "acu50300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvIn(List<Long> values) {
            addCriterion("acu50_300_uv in", values, "acu50300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvNotIn(List<Long> values) {
            addCriterion("acu50_300_uv not in", values, "acu50300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvBetween(Long value1, Long value2) {
            addCriterion("acu50_300_uv between", value1, value2, "acu50300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu50300UvNotBetween(Long value1, Long value2) {
            addCriterion("acu50_300_uv not between", value1, value2, "acu50300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu300UvIsNull() {
            addCriterion("acu300_uv is null");
            return (Criteria) this;
        }

        public Criteria andAcu300UvIsNotNull() {
            addCriterion("acu300_uv is not null");
            return (Criteria) this;
        }

        public Criteria andAcu300UvEqualTo(Long value) {
            addCriterion("acu300_uv =", value, "acu300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu300UvNotEqualTo(Long value) {
            addCriterion("acu300_uv <>", value, "acu300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu300UvGreaterThan(Long value) {
            addCriterion("acu300_uv >", value, "acu300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu300UvGreaterThanOrEqualTo(Long value) {
            addCriterion("acu300_uv >=", value, "acu300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu300UvLessThan(Long value) {
            addCriterion("acu300_uv <", value, "acu300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu300UvLessThanOrEqualTo(Long value) {
            addCriterion("acu300_uv <=", value, "acu300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu300UvIn(List<Long> values) {
            addCriterion("acu300_uv in", values, "acu300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu300UvNotIn(List<Long> values) {
            addCriterion("acu300_uv not in", values, "acu300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu300UvBetween(Long value1, Long value2) {
            addCriterion("acu300_uv between", value1, value2, "acu300Uv");
            return (Criteria) this;
        }

        public Criteria andAcu300UvNotBetween(Long value1, Long value2) {
            addCriterion("acu300_uv not between", value1, value2, "acu300Uv");
            return (Criteria) this;
        }

        public Criteria andHActUvIsNull() {
            addCriterion("h_act_uv is null");
            return (Criteria) this;
        }

        public Criteria andHActUvIsNotNull() {
            addCriterion("h_act_uv is not null");
            return (Criteria) this;
        }

        public Criteria andHActUvEqualTo(Long value) {
            addCriterion("h_act_uv =", value, "hActUv");
            return (Criteria) this;
        }

        public Criteria andHActUvNotEqualTo(Long value) {
            addCriterion("h_act_uv <>", value, "hActUv");
            return (Criteria) this;
        }

        public Criteria andHActUvGreaterThan(Long value) {
            addCriterion("h_act_uv >", value, "hActUv");
            return (Criteria) this;
        }

        public Criteria andHActUvGreaterThanOrEqualTo(Long value) {
            addCriterion("h_act_uv >=", value, "hActUv");
            return (Criteria) this;
        }

        public Criteria andHActUvLessThan(Long value) {
            addCriterion("h_act_uv <", value, "hActUv");
            return (Criteria) this;
        }

        public Criteria andHActUvLessThanOrEqualTo(Long value) {
            addCriterion("h_act_uv <=", value, "hActUv");
            return (Criteria) this;
        }

        public Criteria andHActUvIn(List<Long> values) {
            addCriterion("h_act_uv in", values, "hActUv");
            return (Criteria) this;
        }

        public Criteria andHActUvNotIn(List<Long> values) {
            addCriterion("h_act_uv not in", values, "hActUv");
            return (Criteria) this;
        }

        public Criteria andHActUvBetween(Long value1, Long value2) {
            addCriterion("h_act_uv between", value1, value2, "hActUv");
            return (Criteria) this;
        }

        public Criteria andHActUvNotBetween(Long value1, Long value2) {
            addCriterion("h_act_uv not between", value1, value2, "hActUv");
            return (Criteria) this;
        }

        public Criteria andHActUvRrIsNull() {
            addCriterion("h_act_uv_rr is null");
            return (Criteria) this;
        }

        public Criteria andHActUvRrIsNotNull() {
            addCriterion("h_act_uv_rr is not null");
            return (Criteria) this;
        }

        public Criteria andHActUvRrEqualTo(Long value) {
            addCriterion("h_act_uv_rr =", value, "hActUvRr");
            return (Criteria) this;
        }

        public Criteria andHActUvRrNotEqualTo(Long value) {
            addCriterion("h_act_uv_rr <>", value, "hActUvRr");
            return (Criteria) this;
        }

        public Criteria andHActUvRrGreaterThan(Long value) {
            addCriterion("h_act_uv_rr >", value, "hActUvRr");
            return (Criteria) this;
        }

        public Criteria andHActUvRrGreaterThanOrEqualTo(Long value) {
            addCriterion("h_act_uv_rr >=", value, "hActUvRr");
            return (Criteria) this;
        }

        public Criteria andHActUvRrLessThan(Long value) {
            addCriterion("h_act_uv_rr <", value, "hActUvRr");
            return (Criteria) this;
        }

        public Criteria andHActUvRrLessThanOrEqualTo(Long value) {
            addCriterion("h_act_uv_rr <=", value, "hActUvRr");
            return (Criteria) this;
        }

        public Criteria andHActUvRrIn(List<Long> values) {
            addCriterion("h_act_uv_rr in", values, "hActUvRr");
            return (Criteria) this;
        }

        public Criteria andHActUvRrNotIn(List<Long> values) {
            addCriterion("h_act_uv_rr not in", values, "hActUvRr");
            return (Criteria) this;
        }

        public Criteria andHActUvRrBetween(Long value1, Long value2) {
            addCriterion("h_act_uv_rr between", value1, value2, "hActUvRr");
            return (Criteria) this;
        }

        public Criteria andHActUvRrNotBetween(Long value1, Long value2) {
            addCriterion("h_act_uv_rr not between", value1, value2, "hActUvRr");
            return (Criteria) this;
        }

        public Criteria andPerHActUvIsNull() {
            addCriterion("per_h_act_uv is null");
            return (Criteria) this;
        }

        public Criteria andPerHActUvIsNotNull() {
            addCriterion("per_h_act_uv is not null");
            return (Criteria) this;
        }

        public Criteria andPerHActUvEqualTo(Double value) {
            addCriterion("per_h_act_uv =", value, "perHActUv");
            return (Criteria) this;
        }

        public Criteria andPerHActUvNotEqualTo(Double value) {
            addCriterion("per_h_act_uv <>", value, "perHActUv");
            return (Criteria) this;
        }

        public Criteria andPerHActUvGreaterThan(Double value) {
            addCriterion("per_h_act_uv >", value, "perHActUv");
            return (Criteria) this;
        }

        public Criteria andPerHActUvGreaterThanOrEqualTo(Double value) {
            addCriterion("per_h_act_uv >=", value, "perHActUv");
            return (Criteria) this;
        }

        public Criteria andPerHActUvLessThan(Double value) {
            addCriterion("per_h_act_uv <", value, "perHActUv");
            return (Criteria) this;
        }

        public Criteria andPerHActUvLessThanOrEqualTo(Double value) {
            addCriterion("per_h_act_uv <=", value, "perHActUv");
            return (Criteria) this;
        }

        public Criteria andPerHActUvIn(List<Double> values) {
            addCriterion("per_h_act_uv in", values, "perHActUv");
            return (Criteria) this;
        }

        public Criteria andPerHActUvNotIn(List<Double> values) {
            addCriterion("per_h_act_uv not in", values, "perHActUv");
            return (Criteria) this;
        }

        public Criteria andPerHActUvBetween(Double value1, Double value2) {
            addCriterion("per_h_act_uv between", value1, value2, "perHActUv");
            return (Criteria) this;
        }

        public Criteria andPerHActUvNotBetween(Double value1, Double value2) {
            addCriterion("per_h_act_uv not between", value1, value2, "perHActUv");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondIsNull() {
            addCriterion("month_diamond is null");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondIsNotNull() {
            addCriterion("month_diamond is not null");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondEqualTo(Double value) {
            addCriterion("month_diamond =", value, "monthDiamond");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondNotEqualTo(Double value) {
            addCriterion("month_diamond <>", value, "monthDiamond");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondGreaterThan(Double value) {
            addCriterion("month_diamond >", value, "monthDiamond");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondGreaterThanOrEqualTo(Double value) {
            addCriterion("month_diamond >=", value, "monthDiamond");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondLessThan(Double value) {
            addCriterion("month_diamond <", value, "monthDiamond");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondLessThanOrEqualTo(Double value) {
            addCriterion("month_diamond <=", value, "monthDiamond");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondIn(List<Double> values) {
            addCriterion("month_diamond in", values, "monthDiamond");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondNotIn(List<Double> values) {
            addCriterion("month_diamond not in", values, "monthDiamond");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondBetween(Double value1, Double value2) {
            addCriterion("month_diamond between", value1, value2, "monthDiamond");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondNotBetween(Double value1, Double value2) {
            addCriterion("month_diamond not between", value1, value2, "monthDiamond");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondRrIsNull() {
            addCriterion("month_diamond_rr is null");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondRrIsNotNull() {
            addCriterion("month_diamond_rr is not null");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondRrEqualTo(Double value) {
            addCriterion("month_diamond_rr =", value, "monthDiamondRr");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondRrNotEqualTo(Double value) {
            addCriterion("month_diamond_rr <>", value, "monthDiamondRr");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondRrGreaterThan(Double value) {
            addCriterion("month_diamond_rr >", value, "monthDiamondRr");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondRrGreaterThanOrEqualTo(Double value) {
            addCriterion("month_diamond_rr >=", value, "monthDiamondRr");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondRrLessThan(Double value) {
            addCriterion("month_diamond_rr <", value, "monthDiamondRr");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondRrLessThanOrEqualTo(Double value) {
            addCriterion("month_diamond_rr <=", value, "monthDiamondRr");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondRrIn(List<Double> values) {
            addCriterion("month_diamond_rr in", values, "monthDiamondRr");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondRrNotIn(List<Double> values) {
            addCriterion("month_diamond_rr not in", values, "monthDiamondRr");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondRrBetween(Double value1, Double value2) {
            addCriterion("month_diamond_rr between", value1, value2, "monthDiamondRr");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondRrNotBetween(Double value1, Double value2) {
            addCriterion("month_diamond_rr not between", value1, value2, "monthDiamondRr");
            return (Criteria) this;
        }

        public Criteria andPerMonthDiamondIsNull() {
            addCriterion("per_month_diamond is null");
            return (Criteria) this;
        }

        public Criteria andPerMonthDiamondIsNotNull() {
            addCriterion("per_month_diamond is not null");
            return (Criteria) this;
        }

        public Criteria andPerMonthDiamondEqualTo(Double value) {
            addCriterion("per_month_diamond =", value, "perMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andPerMonthDiamondNotEqualTo(Double value) {
            addCriterion("per_month_diamond <>", value, "perMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andPerMonthDiamondGreaterThan(Double value) {
            addCriterion("per_month_diamond >", value, "perMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andPerMonthDiamondGreaterThanOrEqualTo(Double value) {
            addCriterion("per_month_diamond >=", value, "perMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andPerMonthDiamondLessThan(Double value) {
            addCriterion("per_month_diamond <", value, "perMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andPerMonthDiamondLessThanOrEqualTo(Double value) {
            addCriterion("per_month_diamond <=", value, "perMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andPerMonthDiamondIn(List<Double> values) {
            addCriterion("per_month_diamond in", values, "perMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andPerMonthDiamondNotIn(List<Double> values) {
            addCriterion("per_month_diamond not in", values, "perMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andPerMonthDiamondBetween(Double value1, Double value2) {
            addCriterion("per_month_diamond between", value1, value2, "perMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andPerMonthDiamondNotBetween(Double value1, Double value2) {
            addCriterion("per_month_diamond not between", value1, value2, "perMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andBreakANumIsNull() {
            addCriterion("break_a_num is null");
            return (Criteria) this;
        }

        public Criteria andBreakANumIsNotNull() {
            addCriterion("break_a_num is not null");
            return (Criteria) this;
        }

        public Criteria andBreakANumEqualTo(Long value) {
            addCriterion("break_a_num =", value, "breakANum");
            return (Criteria) this;
        }

        public Criteria andBreakANumNotEqualTo(Long value) {
            addCriterion("break_a_num <>", value, "breakANum");
            return (Criteria) this;
        }

        public Criteria andBreakANumGreaterThan(Long value) {
            addCriterion("break_a_num >", value, "breakANum");
            return (Criteria) this;
        }

        public Criteria andBreakANumGreaterThanOrEqualTo(Long value) {
            addCriterion("break_a_num >=", value, "breakANum");
            return (Criteria) this;
        }

        public Criteria andBreakANumLessThan(Long value) {
            addCriterion("break_a_num <", value, "breakANum");
            return (Criteria) this;
        }

        public Criteria andBreakANumLessThanOrEqualTo(Long value) {
            addCriterion("break_a_num <=", value, "breakANum");
            return (Criteria) this;
        }

        public Criteria andBreakANumIn(List<Long> values) {
            addCriterion("break_a_num in", values, "breakANum");
            return (Criteria) this;
        }

        public Criteria andBreakANumNotIn(List<Long> values) {
            addCriterion("break_a_num not in", values, "breakANum");
            return (Criteria) this;
        }

        public Criteria andBreakANumBetween(Long value1, Long value2) {
            addCriterion("break_a_num between", value1, value2, "breakANum");
            return (Criteria) this;
        }

        public Criteria andBreakANumNotBetween(Long value1, Long value2) {
            addCriterion("break_a_num not between", value1, value2, "breakANum");
            return (Criteria) this;
        }

        public Criteria andBreakBNumIsNull() {
            addCriterion("break_b_num is null");
            return (Criteria) this;
        }

        public Criteria andBreakBNumIsNotNull() {
            addCriterion("break_b_num is not null");
            return (Criteria) this;
        }

        public Criteria andBreakBNumEqualTo(Long value) {
            addCriterion("break_b_num =", value, "breakBNum");
            return (Criteria) this;
        }

        public Criteria andBreakBNumNotEqualTo(Long value) {
            addCriterion("break_b_num <>", value, "breakBNum");
            return (Criteria) this;
        }

        public Criteria andBreakBNumGreaterThan(Long value) {
            addCriterion("break_b_num >", value, "breakBNum");
            return (Criteria) this;
        }

        public Criteria andBreakBNumGreaterThanOrEqualTo(Long value) {
            addCriterion("break_b_num >=", value, "breakBNum");
            return (Criteria) this;
        }

        public Criteria andBreakBNumLessThan(Long value) {
            addCriterion("break_b_num <", value, "breakBNum");
            return (Criteria) this;
        }

        public Criteria andBreakBNumLessThanOrEqualTo(Long value) {
            addCriterion("break_b_num <=", value, "breakBNum");
            return (Criteria) this;
        }

        public Criteria andBreakBNumIn(List<Long> values) {
            addCriterion("break_b_num in", values, "breakBNum");
            return (Criteria) this;
        }

        public Criteria andBreakBNumNotIn(List<Long> values) {
            addCriterion("break_b_num not in", values, "breakBNum");
            return (Criteria) this;
        }

        public Criteria andBreakBNumBetween(Long value1, Long value2) {
            addCriterion("break_b_num between", value1, value2, "breakBNum");
            return (Criteria) this;
        }

        public Criteria andBreakBNumNotBetween(Long value1, Long value2) {
            addCriterion("break_b_num not between", value1, value2, "breakBNum");
            return (Criteria) this;
        }

        public Criteria andBreakCNumIsNull() {
            addCriterion("break_c_num is null");
            return (Criteria) this;
        }

        public Criteria andBreakCNumIsNotNull() {
            addCriterion("break_c_num is not null");
            return (Criteria) this;
        }

        public Criteria andBreakCNumEqualTo(Long value) {
            addCriterion("break_c_num =", value, "breakCNum");
            return (Criteria) this;
        }

        public Criteria andBreakCNumNotEqualTo(Long value) {
            addCriterion("break_c_num <>", value, "breakCNum");
            return (Criteria) this;
        }

        public Criteria andBreakCNumGreaterThan(Long value) {
            addCriterion("break_c_num >", value, "breakCNum");
            return (Criteria) this;
        }

        public Criteria andBreakCNumGreaterThanOrEqualTo(Long value) {
            addCriterion("break_c_num >=", value, "breakCNum");
            return (Criteria) this;
        }

        public Criteria andBreakCNumLessThan(Long value) {
            addCriterion("break_c_num <", value, "breakCNum");
            return (Criteria) this;
        }

        public Criteria andBreakCNumLessThanOrEqualTo(Long value) {
            addCriterion("break_c_num <=", value, "breakCNum");
            return (Criteria) this;
        }

        public Criteria andBreakCNumIn(List<Long> values) {
            addCriterion("break_c_num in", values, "breakCNum");
            return (Criteria) this;
        }

        public Criteria andBreakCNumNotIn(List<Long> values) {
            addCriterion("break_c_num not in", values, "breakCNum");
            return (Criteria) this;
        }

        public Criteria andBreakCNumBetween(Long value1, Long value2) {
            addCriterion("break_c_num between", value1, value2, "breakCNum");
            return (Criteria) this;
        }

        public Criteria andBreakCNumNotBetween(Long value1, Long value2) {
            addCriterion("break_c_num not between", value1, value2, "breakCNum");
            return (Criteria) this;
        }

        public Criteria andBreakENumIsNull() {
            addCriterion("break_e_num is null");
            return (Criteria) this;
        }

        public Criteria andBreakENumIsNotNull() {
            addCriterion("break_e_num is not null");
            return (Criteria) this;
        }

        public Criteria andBreakENumEqualTo(Long value) {
            addCriterion("break_e_num =", value, "breakENum");
            return (Criteria) this;
        }

        public Criteria andBreakENumNotEqualTo(Long value) {
            addCriterion("break_e_num <>", value, "breakENum");
            return (Criteria) this;
        }

        public Criteria andBreakENumGreaterThan(Long value) {
            addCriterion("break_e_num >", value, "breakENum");
            return (Criteria) this;
        }

        public Criteria andBreakENumGreaterThanOrEqualTo(Long value) {
            addCriterion("break_e_num >=", value, "breakENum");
            return (Criteria) this;
        }

        public Criteria andBreakENumLessThan(Long value) {
            addCriterion("break_e_num <", value, "breakENum");
            return (Criteria) this;
        }

        public Criteria andBreakENumLessThanOrEqualTo(Long value) {
            addCriterion("break_e_num <=", value, "breakENum");
            return (Criteria) this;
        }

        public Criteria andBreakENumIn(List<Long> values) {
            addCriterion("break_e_num in", values, "breakENum");
            return (Criteria) this;
        }

        public Criteria andBreakENumNotIn(List<Long> values) {
            addCriterion("break_e_num not in", values, "breakENum");
            return (Criteria) this;
        }

        public Criteria andBreakENumBetween(Long value1, Long value2) {
            addCriterion("break_e_num between", value1, value2, "breakENum");
            return (Criteria) this;
        }

        public Criteria andBreakENumNotBetween(Long value1, Long value2) {
            addCriterion("break_e_num not between", value1, value2, "breakENum");
            return (Criteria) this;
        }

        public Criteria andLiveUvIsNull() {
            addCriterion("live_uv is null");
            return (Criteria) this;
        }

        public Criteria andLiveUvIsNotNull() {
            addCriterion("live_uv is not null");
            return (Criteria) this;
        }

        public Criteria andLiveUvEqualTo(Long value) {
            addCriterion("live_uv =", value, "liveUv");
            return (Criteria) this;
        }

        public Criteria andLiveUvNotEqualTo(Long value) {
            addCriterion("live_uv <>", value, "liveUv");
            return (Criteria) this;
        }

        public Criteria andLiveUvGreaterThan(Long value) {
            addCriterion("live_uv >", value, "liveUv");
            return (Criteria) this;
        }

        public Criteria andLiveUvGreaterThanOrEqualTo(Long value) {
            addCriterion("live_uv >=", value, "liveUv");
            return (Criteria) this;
        }

        public Criteria andLiveUvLessThan(Long value) {
            addCriterion("live_uv <", value, "liveUv");
            return (Criteria) this;
        }

        public Criteria andLiveUvLessThanOrEqualTo(Long value) {
            addCriterion("live_uv <=", value, "liveUv");
            return (Criteria) this;
        }

        public Criteria andLiveUvIn(List<Long> values) {
            addCriterion("live_uv in", values, "liveUv");
            return (Criteria) this;
        }

        public Criteria andLiveUvNotIn(List<Long> values) {
            addCriterion("live_uv not in", values, "liveUv");
            return (Criteria) this;
        }

        public Criteria andLiveUvBetween(Long value1, Long value2) {
            addCriterion("live_uv between", value1, value2, "liveUv");
            return (Criteria) this;
        }

        public Criteria andLiveUvNotBetween(Long value1, Long value2) {
            addCriterion("live_uv not between", value1, value2, "liveUv");
            return (Criteria) this;
        }

        public Criteria andBreakRtIsNull() {
            addCriterion("break_rt is null");
            return (Criteria) this;
        }

        public Criteria andBreakRtIsNotNull() {
            addCriterion("break_rt is not null");
            return (Criteria) this;
        }

        public Criteria andBreakRtEqualTo(Double value) {
            addCriterion("break_rt =", value, "breakRt");
            return (Criteria) this;
        }

        public Criteria andBreakRtNotEqualTo(Double value) {
            addCriterion("break_rt <>", value, "breakRt");
            return (Criteria) this;
        }

        public Criteria andBreakRtGreaterThan(Double value) {
            addCriterion("break_rt >", value, "breakRt");
            return (Criteria) this;
        }

        public Criteria andBreakRtGreaterThanOrEqualTo(Double value) {
            addCriterion("break_rt >=", value, "breakRt");
            return (Criteria) this;
        }

        public Criteria andBreakRtLessThan(Double value) {
            addCriterion("break_rt <", value, "breakRt");
            return (Criteria) this;
        }

        public Criteria andBreakRtLessThanOrEqualTo(Double value) {
            addCriterion("break_rt <=", value, "breakRt");
            return (Criteria) this;
        }

        public Criteria andBreakRtIn(List<Double> values) {
            addCriterion("break_rt in", values, "breakRt");
            return (Criteria) this;
        }

        public Criteria andBreakRtNotIn(List<Double> values) {
            addCriterion("break_rt not in", values, "breakRt");
            return (Criteria) this;
        }

        public Criteria andBreakRtBetween(Double value1, Double value2) {
            addCriterion("break_rt between", value1, value2, "breakRt");
            return (Criteria) this;
        }

        public Criteria andBreakRtNotBetween(Double value1, Double value2) {
            addCriterion("break_rt not between", value1, value2, "breakRt");
            return (Criteria) this;
        }

        public Criteria andBreakRrIsNull() {
            addCriterion("break_rr is null");
            return (Criteria) this;
        }

        public Criteria andBreakRrIsNotNull() {
            addCriterion("break_rr is not null");
            return (Criteria) this;
        }

        public Criteria andBreakRrEqualTo(Double value) {
            addCriterion("break_rr =", value, "breakRr");
            return (Criteria) this;
        }

        public Criteria andBreakRrNotEqualTo(Double value) {
            addCriterion("break_rr <>", value, "breakRr");
            return (Criteria) this;
        }

        public Criteria andBreakRrGreaterThan(Double value) {
            addCriterion("break_rr >", value, "breakRr");
            return (Criteria) this;
        }

        public Criteria andBreakRrGreaterThanOrEqualTo(Double value) {
            addCriterion("break_rr >=", value, "breakRr");
            return (Criteria) this;
        }

        public Criteria andBreakRrLessThan(Double value) {
            addCriterion("break_rr <", value, "breakRr");
            return (Criteria) this;
        }

        public Criteria andBreakRrLessThanOrEqualTo(Double value) {
            addCriterion("break_rr <=", value, "breakRr");
            return (Criteria) this;
        }

        public Criteria andBreakRrIn(List<Double> values) {
            addCriterion("break_rr in", values, "breakRr");
            return (Criteria) this;
        }

        public Criteria andBreakRrNotIn(List<Double> values) {
            addCriterion("break_rr not in", values, "breakRr");
            return (Criteria) this;
        }

        public Criteria andBreakRrBetween(Double value1, Double value2) {
            addCriterion("break_rr between", value1, value2, "breakRr");
            return (Criteria) this;
        }

        public Criteria andBreakRrNotBetween(Double value1, Double value2) {
            addCriterion("break_rr not between", value1, value2, "breakRr");
            return (Criteria) this;
        }

        public Criteria andPerBreakIsNull() {
            addCriterion("per_break is null");
            return (Criteria) this;
        }

        public Criteria andPerBreakIsNotNull() {
            addCriterion("per_break is not null");
            return (Criteria) this;
        }

        public Criteria andPerBreakEqualTo(Double value) {
            addCriterion("per_break =", value, "perBreak");
            return (Criteria) this;
        }

        public Criteria andPerBreakNotEqualTo(Double value) {
            addCriterion("per_break <>", value, "perBreak");
            return (Criteria) this;
        }

        public Criteria andPerBreakGreaterThan(Double value) {
            addCriterion("per_break >", value, "perBreak");
            return (Criteria) this;
        }

        public Criteria andPerBreakGreaterThanOrEqualTo(Double value) {
            addCriterion("per_break >=", value, "perBreak");
            return (Criteria) this;
        }

        public Criteria andPerBreakLessThan(Double value) {
            addCriterion("per_break <", value, "perBreak");
            return (Criteria) this;
        }

        public Criteria andPerBreakLessThanOrEqualTo(Double value) {
            addCriterion("per_break <=", value, "perBreak");
            return (Criteria) this;
        }

        public Criteria andPerBreakIn(List<Double> values) {
            addCriterion("per_break in", values, "perBreak");
            return (Criteria) this;
        }

        public Criteria andPerBreakNotIn(List<Double> values) {
            addCriterion("per_break not in", values, "perBreak");
            return (Criteria) this;
        }

        public Criteria andPerBreakBetween(Double value1, Double value2) {
            addCriterion("per_break between", value1, value2, "perBreak");
            return (Criteria) this;
        }

        public Criteria andPerBreakNotBetween(Double value1, Double value2) {
            addCriterion("per_break not between", value1, value2, "perBreak");
            return (Criteria) this;
        }

        public Criteria andP1IsNull() {
            addCriterion("p1 is null");
            return (Criteria) this;
        }

        public Criteria andP1IsNotNull() {
            addCriterion("p1 is not null");
            return (Criteria) this;
        }

        public Criteria andP1EqualTo(Double value) {
            addCriterion("p1 =", value, "p1");
            return (Criteria) this;
        }

        public Criteria andP1NotEqualTo(Double value) {
            addCriterion("p1 <>", value, "p1");
            return (Criteria) this;
        }

        public Criteria andP1GreaterThan(Double value) {
            addCriterion("p1 >", value, "p1");
            return (Criteria) this;
        }

        public Criteria andP1GreaterThanOrEqualTo(Double value) {
            addCriterion("p1 >=", value, "p1");
            return (Criteria) this;
        }

        public Criteria andP1LessThan(Double value) {
            addCriterion("p1 <", value, "p1");
            return (Criteria) this;
        }

        public Criteria andP1LessThanOrEqualTo(Double value) {
            addCriterion("p1 <=", value, "p1");
            return (Criteria) this;
        }

        public Criteria andP1In(List<Double> values) {
            addCriterion("p1 in", values, "p1");
            return (Criteria) this;
        }

        public Criteria andP1NotIn(List<Double> values) {
            addCriterion("p1 not in", values, "p1");
            return (Criteria) this;
        }

        public Criteria andP1Between(Double value1, Double value2) {
            addCriterion("p1 between", value1, value2, "p1");
            return (Criteria) this;
        }

        public Criteria andP1NotBetween(Double value1, Double value2) {
            addCriterion("p1 not between", value1, value2, "p1");
            return (Criteria) this;
        }

        public Criteria andPs1IsNull() {
            addCriterion("ps_1 is null");
            return (Criteria) this;
        }

        public Criteria andPs1IsNotNull() {
            addCriterion("ps_1 is not null");
            return (Criteria) this;
        }

        public Criteria andPs1EqualTo(Double value) {
            addCriterion("ps_1 =", value, "ps1");
            return (Criteria) this;
        }

        public Criteria andPs1NotEqualTo(Double value) {
            addCriterion("ps_1 <>", value, "ps1");
            return (Criteria) this;
        }

        public Criteria andPs1GreaterThan(Double value) {
            addCriterion("ps_1 >", value, "ps1");
            return (Criteria) this;
        }

        public Criteria andPs1GreaterThanOrEqualTo(Double value) {
            addCriterion("ps_1 >=", value, "ps1");
            return (Criteria) this;
        }

        public Criteria andPs1LessThan(Double value) {
            addCriterion("ps_1 <", value, "ps1");
            return (Criteria) this;
        }

        public Criteria andPs1LessThanOrEqualTo(Double value) {
            addCriterion("ps_1 <=", value, "ps1");
            return (Criteria) this;
        }

        public Criteria andPs1In(List<Double> values) {
            addCriterion("ps_1 in", values, "ps1");
            return (Criteria) this;
        }

        public Criteria andPs1NotIn(List<Double> values) {
            addCriterion("ps_1 not in", values, "ps1");
            return (Criteria) this;
        }

        public Criteria andPs1Between(Double value1, Double value2) {
            addCriterion("ps_1 between", value1, value2, "ps1");
            return (Criteria) this;
        }

        public Criteria andPs1NotBetween(Double value1, Double value2) {
            addCriterion("ps_1 not between", value1, value2, "ps1");
            return (Criteria) this;
        }

        public Criteria andPsS1IsNull() {
            addCriterion("ps_s_1 is null");
            return (Criteria) this;
        }

        public Criteria andPsS1IsNotNull() {
            addCriterion("ps_s_1 is not null");
            return (Criteria) this;
        }

        public Criteria andPsS1EqualTo(Long value) {
            addCriterion("ps_s_1 =", value, "psS1");
            return (Criteria) this;
        }

        public Criteria andPsS1NotEqualTo(Long value) {
            addCriterion("ps_s_1 <>", value, "psS1");
            return (Criteria) this;
        }

        public Criteria andPsS1GreaterThan(Long value) {
            addCriterion("ps_s_1 >", value, "psS1");
            return (Criteria) this;
        }

        public Criteria andPsS1GreaterThanOrEqualTo(Long value) {
            addCriterion("ps_s_1 >=", value, "psS1");
            return (Criteria) this;
        }

        public Criteria andPsS1LessThan(Long value) {
            addCriterion("ps_s_1 <", value, "psS1");
            return (Criteria) this;
        }

        public Criteria andPsS1LessThanOrEqualTo(Long value) {
            addCriterion("ps_s_1 <=", value, "psS1");
            return (Criteria) this;
        }

        public Criteria andPsS1In(List<Long> values) {
            addCriterion("ps_s_1 in", values, "psS1");
            return (Criteria) this;
        }

        public Criteria andPsS1NotIn(List<Long> values) {
            addCriterion("ps_s_1 not in", values, "psS1");
            return (Criteria) this;
        }

        public Criteria andPsS1Between(Long value1, Long value2) {
            addCriterion("ps_s_1 between", value1, value2, "psS1");
            return (Criteria) this;
        }

        public Criteria andPsS1NotBetween(Long value1, Long value2) {
            addCriterion("ps_s_1 not between", value1, value2, "psS1");
            return (Criteria) this;
        }

        public Criteria andPsS1ShIsNull() {
            addCriterion("ps_s_1_sh is null");
            return (Criteria) this;
        }

        public Criteria andPsS1ShIsNotNull() {
            addCriterion("ps_s_1_sh is not null");
            return (Criteria) this;
        }

        public Criteria andPsS1ShEqualTo(String value) {
            addCriterion("ps_s_1_sh =", value, "psS1Sh");
            return (Criteria) this;
        }

        public Criteria andPsS1ShNotEqualTo(String value) {
            addCriterion("ps_s_1_sh <>", value, "psS1Sh");
            return (Criteria) this;
        }

        public Criteria andPsS1ShGreaterThan(String value) {
            addCriterion("ps_s_1_sh >", value, "psS1Sh");
            return (Criteria) this;
        }

        public Criteria andPsS1ShGreaterThanOrEqualTo(String value) {
            addCriterion("ps_s_1_sh >=", value, "psS1Sh");
            return (Criteria) this;
        }

        public Criteria andPsS1ShLessThan(String value) {
            addCriterion("ps_s_1_sh <", value, "psS1Sh");
            return (Criteria) this;
        }

        public Criteria andPsS1ShLessThanOrEqualTo(String value) {
            addCriterion("ps_s_1_sh <=", value, "psS1Sh");
            return (Criteria) this;
        }

        public Criteria andPsS1ShLike(String value) {
            addCriterion("ps_s_1_sh like", value, "psS1Sh");
            return (Criteria) this;
        }

        public Criteria andPsS1ShNotLike(String value) {
            addCriterion("ps_s_1_sh not like", value, "psS1Sh");
            return (Criteria) this;
        }

        public Criteria andPsS1ShIn(List<String> values) {
            addCriterion("ps_s_1_sh in", values, "psS1Sh");
            return (Criteria) this;
        }

        public Criteria andPsS1ShNotIn(List<String> values) {
            addCriterion("ps_s_1_sh not in", values, "psS1Sh");
            return (Criteria) this;
        }

        public Criteria andPsS1ShBetween(String value1, String value2) {
            addCriterion("ps_s_1_sh between", value1, value2, "psS1Sh");
            return (Criteria) this;
        }

        public Criteria andPsS1ShNotBetween(String value1, String value2) {
            addCriterion("ps_s_1_sh not between", value1, value2, "psS1Sh");
            return (Criteria) this;
        }

        public Criteria andPsS1RnIsNull() {
            addCriterion("ps_s_1_rn is null");
            return (Criteria) this;
        }

        public Criteria andPsS1RnIsNotNull() {
            addCriterion("ps_s_1_rn is not null");
            return (Criteria) this;
        }

        public Criteria andPsS1RnEqualTo(Long value) {
            addCriterion("ps_s_1_rn =", value, "psS1Rn");
            return (Criteria) this;
        }

        public Criteria andPsS1RnNotEqualTo(Long value) {
            addCriterion("ps_s_1_rn <>", value, "psS1Rn");
            return (Criteria) this;
        }

        public Criteria andPsS1RnGreaterThan(Long value) {
            addCriterion("ps_s_1_rn >", value, "psS1Rn");
            return (Criteria) this;
        }

        public Criteria andPsS1RnGreaterThanOrEqualTo(Long value) {
            addCriterion("ps_s_1_rn >=", value, "psS1Rn");
            return (Criteria) this;
        }

        public Criteria andPsS1RnLessThan(Long value) {
            addCriterion("ps_s_1_rn <", value, "psS1Rn");
            return (Criteria) this;
        }

        public Criteria andPsS1RnLessThanOrEqualTo(Long value) {
            addCriterion("ps_s_1_rn <=", value, "psS1Rn");
            return (Criteria) this;
        }

        public Criteria andPsS1RnIn(List<Long> values) {
            addCriterion("ps_s_1_rn in", values, "psS1Rn");
            return (Criteria) this;
        }

        public Criteria andPsS1RnNotIn(List<Long> values) {
            addCriterion("ps_s_1_rn not in", values, "psS1Rn");
            return (Criteria) this;
        }

        public Criteria andPsS1RnBetween(Long value1, Long value2) {
            addCriterion("ps_s_1_rn between", value1, value2, "psS1Rn");
            return (Criteria) this;
        }

        public Criteria andPsS1RnNotBetween(Long value1, Long value2) {
            addCriterion("ps_s_1_rn not between", value1, value2, "psS1Rn");
            return (Criteria) this;
        }

        public Criteria andP2IsNull() {
            addCriterion("p2 is null");
            return (Criteria) this;
        }

        public Criteria andP2IsNotNull() {
            addCriterion("p2 is not null");
            return (Criteria) this;
        }

        public Criteria andP2EqualTo(Double value) {
            addCriterion("p2 =", value, "p2");
            return (Criteria) this;
        }

        public Criteria andP2NotEqualTo(Double value) {
            addCriterion("p2 <>", value, "p2");
            return (Criteria) this;
        }

        public Criteria andP2GreaterThan(Double value) {
            addCriterion("p2 >", value, "p2");
            return (Criteria) this;
        }

        public Criteria andP2GreaterThanOrEqualTo(Double value) {
            addCriterion("p2 >=", value, "p2");
            return (Criteria) this;
        }

        public Criteria andP2LessThan(Double value) {
            addCriterion("p2 <", value, "p2");
            return (Criteria) this;
        }

        public Criteria andP2LessThanOrEqualTo(Double value) {
            addCriterion("p2 <=", value, "p2");
            return (Criteria) this;
        }

        public Criteria andP2In(List<Double> values) {
            addCriterion("p2 in", values, "p2");
            return (Criteria) this;
        }

        public Criteria andP2NotIn(List<Double> values) {
            addCriterion("p2 not in", values, "p2");
            return (Criteria) this;
        }

        public Criteria andP2Between(Double value1, Double value2) {
            addCriterion("p2 between", value1, value2, "p2");
            return (Criteria) this;
        }

        public Criteria andP2NotBetween(Double value1, Double value2) {
            addCriterion("p2 not between", value1, value2, "p2");
            return (Criteria) this;
        }

        public Criteria andPs2IsNull() {
            addCriterion("ps_2 is null");
            return (Criteria) this;
        }

        public Criteria andPs2IsNotNull() {
            addCriterion("ps_2 is not null");
            return (Criteria) this;
        }

        public Criteria andPs2EqualTo(Double value) {
            addCriterion("ps_2 =", value, "ps2");
            return (Criteria) this;
        }

        public Criteria andPs2NotEqualTo(Double value) {
            addCriterion("ps_2 <>", value, "ps2");
            return (Criteria) this;
        }

        public Criteria andPs2GreaterThan(Double value) {
            addCriterion("ps_2 >", value, "ps2");
            return (Criteria) this;
        }

        public Criteria andPs2GreaterThanOrEqualTo(Double value) {
            addCriterion("ps_2 >=", value, "ps2");
            return (Criteria) this;
        }

        public Criteria andPs2LessThan(Double value) {
            addCriterion("ps_2 <", value, "ps2");
            return (Criteria) this;
        }

        public Criteria andPs2LessThanOrEqualTo(Double value) {
            addCriterion("ps_2 <=", value, "ps2");
            return (Criteria) this;
        }

        public Criteria andPs2In(List<Double> values) {
            addCriterion("ps_2 in", values, "ps2");
            return (Criteria) this;
        }

        public Criteria andPs2NotIn(List<Double> values) {
            addCriterion("ps_2 not in", values, "ps2");
            return (Criteria) this;
        }

        public Criteria andPs2Between(Double value1, Double value2) {
            addCriterion("ps_2 between", value1, value2, "ps2");
            return (Criteria) this;
        }

        public Criteria andPs2NotBetween(Double value1, Double value2) {
            addCriterion("ps_2 not between", value1, value2, "ps2");
            return (Criteria) this;
        }

        public Criteria andPsS2IsNull() {
            addCriterion("ps_s_2 is null");
            return (Criteria) this;
        }

        public Criteria andPsS2IsNotNull() {
            addCriterion("ps_s_2 is not null");
            return (Criteria) this;
        }

        public Criteria andPsS2EqualTo(Long value) {
            addCriterion("ps_s_2 =", value, "psS2");
            return (Criteria) this;
        }

        public Criteria andPsS2NotEqualTo(Long value) {
            addCriterion("ps_s_2 <>", value, "psS2");
            return (Criteria) this;
        }

        public Criteria andPsS2GreaterThan(Long value) {
            addCriterion("ps_s_2 >", value, "psS2");
            return (Criteria) this;
        }

        public Criteria andPsS2GreaterThanOrEqualTo(Long value) {
            addCriterion("ps_s_2 >=", value, "psS2");
            return (Criteria) this;
        }

        public Criteria andPsS2LessThan(Long value) {
            addCriterion("ps_s_2 <", value, "psS2");
            return (Criteria) this;
        }

        public Criteria andPsS2LessThanOrEqualTo(Long value) {
            addCriterion("ps_s_2 <=", value, "psS2");
            return (Criteria) this;
        }

        public Criteria andPsS2In(List<Long> values) {
            addCriterion("ps_s_2 in", values, "psS2");
            return (Criteria) this;
        }

        public Criteria andPsS2NotIn(List<Long> values) {
            addCriterion("ps_s_2 not in", values, "psS2");
            return (Criteria) this;
        }

        public Criteria andPsS2Between(Long value1, Long value2) {
            addCriterion("ps_s_2 between", value1, value2, "psS2");
            return (Criteria) this;
        }

        public Criteria andPsS2NotBetween(Long value1, Long value2) {
            addCriterion("ps_s_2 not between", value1, value2, "psS2");
            return (Criteria) this;
        }

        public Criteria andPsS2ShIsNull() {
            addCriterion("ps_s_2_sh is null");
            return (Criteria) this;
        }

        public Criteria andPsS2ShIsNotNull() {
            addCriterion("ps_s_2_sh is not null");
            return (Criteria) this;
        }

        public Criteria andPsS2ShEqualTo(String value) {
            addCriterion("ps_s_2_sh =", value, "psS2Sh");
            return (Criteria) this;
        }

        public Criteria andPsS2ShNotEqualTo(String value) {
            addCriterion("ps_s_2_sh <>", value, "psS2Sh");
            return (Criteria) this;
        }

        public Criteria andPsS2ShGreaterThan(String value) {
            addCriterion("ps_s_2_sh >", value, "psS2Sh");
            return (Criteria) this;
        }

        public Criteria andPsS2ShGreaterThanOrEqualTo(String value) {
            addCriterion("ps_s_2_sh >=", value, "psS2Sh");
            return (Criteria) this;
        }

        public Criteria andPsS2ShLessThan(String value) {
            addCriterion("ps_s_2_sh <", value, "psS2Sh");
            return (Criteria) this;
        }

        public Criteria andPsS2ShLessThanOrEqualTo(String value) {
            addCriterion("ps_s_2_sh <=", value, "psS2Sh");
            return (Criteria) this;
        }

        public Criteria andPsS2ShLike(String value) {
            addCriterion("ps_s_2_sh like", value, "psS2Sh");
            return (Criteria) this;
        }

        public Criteria andPsS2ShNotLike(String value) {
            addCriterion("ps_s_2_sh not like", value, "psS2Sh");
            return (Criteria) this;
        }

        public Criteria andPsS2ShIn(List<String> values) {
            addCriterion("ps_s_2_sh in", values, "psS2Sh");
            return (Criteria) this;
        }

        public Criteria andPsS2ShNotIn(List<String> values) {
            addCriterion("ps_s_2_sh not in", values, "psS2Sh");
            return (Criteria) this;
        }

        public Criteria andPsS2ShBetween(String value1, String value2) {
            addCriterion("ps_s_2_sh between", value1, value2, "psS2Sh");
            return (Criteria) this;
        }

        public Criteria andPsS2ShNotBetween(String value1, String value2) {
            addCriterion("ps_s_2_sh not between", value1, value2, "psS2Sh");
            return (Criteria) this;
        }

        public Criteria andPsS2RnIsNull() {
            addCriterion("ps_s_2_rn is null");
            return (Criteria) this;
        }

        public Criteria andPsS2RnIsNotNull() {
            addCriterion("ps_s_2_rn is not null");
            return (Criteria) this;
        }

        public Criteria andPsS2RnEqualTo(Long value) {
            addCriterion("ps_s_2_rn =", value, "psS2Rn");
            return (Criteria) this;
        }

        public Criteria andPsS2RnNotEqualTo(Long value) {
            addCriterion("ps_s_2_rn <>", value, "psS2Rn");
            return (Criteria) this;
        }

        public Criteria andPsS2RnGreaterThan(Long value) {
            addCriterion("ps_s_2_rn >", value, "psS2Rn");
            return (Criteria) this;
        }

        public Criteria andPsS2RnGreaterThanOrEqualTo(Long value) {
            addCriterion("ps_s_2_rn >=", value, "psS2Rn");
            return (Criteria) this;
        }

        public Criteria andPsS2RnLessThan(Long value) {
            addCriterion("ps_s_2_rn <", value, "psS2Rn");
            return (Criteria) this;
        }

        public Criteria andPsS2RnLessThanOrEqualTo(Long value) {
            addCriterion("ps_s_2_rn <=", value, "psS2Rn");
            return (Criteria) this;
        }

        public Criteria andPsS2RnIn(List<Long> values) {
            addCriterion("ps_s_2_rn in", values, "psS2Rn");
            return (Criteria) this;
        }

        public Criteria andPsS2RnNotIn(List<Long> values) {
            addCriterion("ps_s_2_rn not in", values, "psS2Rn");
            return (Criteria) this;
        }

        public Criteria andPsS2RnBetween(Long value1, Long value2) {
            addCriterion("ps_s_2_rn between", value1, value2, "psS2Rn");
            return (Criteria) this;
        }

        public Criteria andPsS2RnNotBetween(Long value1, Long value2) {
            addCriterion("ps_s_2_rn not between", value1, value2, "psS2Rn");
            return (Criteria) this;
        }

        public Criteria andP3IsNull() {
            addCriterion("p3 is null");
            return (Criteria) this;
        }

        public Criteria andP3IsNotNull() {
            addCriterion("p3 is not null");
            return (Criteria) this;
        }

        public Criteria andP3EqualTo(Double value) {
            addCriterion("p3 =", value, "p3");
            return (Criteria) this;
        }

        public Criteria andP3NotEqualTo(Double value) {
            addCriterion("p3 <>", value, "p3");
            return (Criteria) this;
        }

        public Criteria andP3GreaterThan(Double value) {
            addCriterion("p3 >", value, "p3");
            return (Criteria) this;
        }

        public Criteria andP3GreaterThanOrEqualTo(Double value) {
            addCriterion("p3 >=", value, "p3");
            return (Criteria) this;
        }

        public Criteria andP3LessThan(Double value) {
            addCriterion("p3 <", value, "p3");
            return (Criteria) this;
        }

        public Criteria andP3LessThanOrEqualTo(Double value) {
            addCriterion("p3 <=", value, "p3");
            return (Criteria) this;
        }

        public Criteria andP3In(List<Double> values) {
            addCriterion("p3 in", values, "p3");
            return (Criteria) this;
        }

        public Criteria andP3NotIn(List<Double> values) {
            addCriterion("p3 not in", values, "p3");
            return (Criteria) this;
        }

        public Criteria andP3Between(Double value1, Double value2) {
            addCriterion("p3 between", value1, value2, "p3");
            return (Criteria) this;
        }

        public Criteria andP3NotBetween(Double value1, Double value2) {
            addCriterion("p3 not between", value1, value2, "p3");
            return (Criteria) this;
        }

        public Criteria andPs3IsNull() {
            addCriterion("ps_3 is null");
            return (Criteria) this;
        }

        public Criteria andPs3IsNotNull() {
            addCriterion("ps_3 is not null");
            return (Criteria) this;
        }

        public Criteria andPs3EqualTo(Double value) {
            addCriterion("ps_3 =", value, "ps3");
            return (Criteria) this;
        }

        public Criteria andPs3NotEqualTo(Double value) {
            addCriterion("ps_3 <>", value, "ps3");
            return (Criteria) this;
        }

        public Criteria andPs3GreaterThan(Double value) {
            addCriterion("ps_3 >", value, "ps3");
            return (Criteria) this;
        }

        public Criteria andPs3GreaterThanOrEqualTo(Double value) {
            addCriterion("ps_3 >=", value, "ps3");
            return (Criteria) this;
        }

        public Criteria andPs3LessThan(Double value) {
            addCriterion("ps_3 <", value, "ps3");
            return (Criteria) this;
        }

        public Criteria andPs3LessThanOrEqualTo(Double value) {
            addCriterion("ps_3 <=", value, "ps3");
            return (Criteria) this;
        }

        public Criteria andPs3In(List<Double> values) {
            addCriterion("ps_3 in", values, "ps3");
            return (Criteria) this;
        }

        public Criteria andPs3NotIn(List<Double> values) {
            addCriterion("ps_3 not in", values, "ps3");
            return (Criteria) this;
        }

        public Criteria andPs3Between(Double value1, Double value2) {
            addCriterion("ps_3 between", value1, value2, "ps3");
            return (Criteria) this;
        }

        public Criteria andPs3NotBetween(Double value1, Double value2) {
            addCriterion("ps_3 not between", value1, value2, "ps3");
            return (Criteria) this;
        }

        public Criteria andPsS3IsNull() {
            addCriterion("ps_s_3 is null");
            return (Criteria) this;
        }

        public Criteria andPsS3IsNotNull() {
            addCriterion("ps_s_3 is not null");
            return (Criteria) this;
        }

        public Criteria andPsS3EqualTo(Long value) {
            addCriterion("ps_s_3 =", value, "psS3");
            return (Criteria) this;
        }

        public Criteria andPsS3NotEqualTo(Long value) {
            addCriterion("ps_s_3 <>", value, "psS3");
            return (Criteria) this;
        }

        public Criteria andPsS3GreaterThan(Long value) {
            addCriterion("ps_s_3 >", value, "psS3");
            return (Criteria) this;
        }

        public Criteria andPsS3GreaterThanOrEqualTo(Long value) {
            addCriterion("ps_s_3 >=", value, "psS3");
            return (Criteria) this;
        }

        public Criteria andPsS3LessThan(Long value) {
            addCriterion("ps_s_3 <", value, "psS3");
            return (Criteria) this;
        }

        public Criteria andPsS3LessThanOrEqualTo(Long value) {
            addCriterion("ps_s_3 <=", value, "psS3");
            return (Criteria) this;
        }

        public Criteria andPsS3In(List<Long> values) {
            addCriterion("ps_s_3 in", values, "psS3");
            return (Criteria) this;
        }

        public Criteria andPsS3NotIn(List<Long> values) {
            addCriterion("ps_s_3 not in", values, "psS3");
            return (Criteria) this;
        }

        public Criteria andPsS3Between(Long value1, Long value2) {
            addCriterion("ps_s_3 between", value1, value2, "psS3");
            return (Criteria) this;
        }

        public Criteria andPsS3NotBetween(Long value1, Long value2) {
            addCriterion("ps_s_3 not between", value1, value2, "psS3");
            return (Criteria) this;
        }

        public Criteria andPsS3ShIsNull() {
            addCriterion("ps_s_3_sh is null");
            return (Criteria) this;
        }

        public Criteria andPsS3ShIsNotNull() {
            addCriterion("ps_s_3_sh is not null");
            return (Criteria) this;
        }

        public Criteria andPsS3ShEqualTo(String value) {
            addCriterion("ps_s_3_sh =", value, "psS3Sh");
            return (Criteria) this;
        }

        public Criteria andPsS3ShNotEqualTo(String value) {
            addCriterion("ps_s_3_sh <>", value, "psS3Sh");
            return (Criteria) this;
        }

        public Criteria andPsS3ShGreaterThan(String value) {
            addCriterion("ps_s_3_sh >", value, "psS3Sh");
            return (Criteria) this;
        }

        public Criteria andPsS3ShGreaterThanOrEqualTo(String value) {
            addCriterion("ps_s_3_sh >=", value, "psS3Sh");
            return (Criteria) this;
        }

        public Criteria andPsS3ShLessThan(String value) {
            addCriterion("ps_s_3_sh <", value, "psS3Sh");
            return (Criteria) this;
        }

        public Criteria andPsS3ShLessThanOrEqualTo(String value) {
            addCriterion("ps_s_3_sh <=", value, "psS3Sh");
            return (Criteria) this;
        }

        public Criteria andPsS3ShLike(String value) {
            addCriterion("ps_s_3_sh like", value, "psS3Sh");
            return (Criteria) this;
        }

        public Criteria andPsS3ShNotLike(String value) {
            addCriterion("ps_s_3_sh not like", value, "psS3Sh");
            return (Criteria) this;
        }

        public Criteria andPsS3ShIn(List<String> values) {
            addCriterion("ps_s_3_sh in", values, "psS3Sh");
            return (Criteria) this;
        }

        public Criteria andPsS3ShNotIn(List<String> values) {
            addCriterion("ps_s_3_sh not in", values, "psS3Sh");
            return (Criteria) this;
        }

        public Criteria andPsS3ShBetween(String value1, String value2) {
            addCriterion("ps_s_3_sh between", value1, value2, "psS3Sh");
            return (Criteria) this;
        }

        public Criteria andPsS3ShNotBetween(String value1, String value2) {
            addCriterion("ps_s_3_sh not between", value1, value2, "psS3Sh");
            return (Criteria) this;
        }

        public Criteria andPsS3RnIsNull() {
            addCriterion("ps_s_3_rn is null");
            return (Criteria) this;
        }

        public Criteria andPsS3RnIsNotNull() {
            addCriterion("ps_s_3_rn is not null");
            return (Criteria) this;
        }

        public Criteria andPsS3RnEqualTo(Long value) {
            addCriterion("ps_s_3_rn =", value, "psS3Rn");
            return (Criteria) this;
        }

        public Criteria andPsS3RnNotEqualTo(Long value) {
            addCriterion("ps_s_3_rn <>", value, "psS3Rn");
            return (Criteria) this;
        }

        public Criteria andPsS3RnGreaterThan(Long value) {
            addCriterion("ps_s_3_rn >", value, "psS3Rn");
            return (Criteria) this;
        }

        public Criteria andPsS3RnGreaterThanOrEqualTo(Long value) {
            addCriterion("ps_s_3_rn >=", value, "psS3Rn");
            return (Criteria) this;
        }

        public Criteria andPsS3RnLessThan(Long value) {
            addCriterion("ps_s_3_rn <", value, "psS3Rn");
            return (Criteria) this;
        }

        public Criteria andPsS3RnLessThanOrEqualTo(Long value) {
            addCriterion("ps_s_3_rn <=", value, "psS3Rn");
            return (Criteria) this;
        }

        public Criteria andPsS3RnIn(List<Long> values) {
            addCriterion("ps_s_3_rn in", values, "psS3Rn");
            return (Criteria) this;
        }

        public Criteria andPsS3RnNotIn(List<Long> values) {
            addCriterion("ps_s_3_rn not in", values, "psS3Rn");
            return (Criteria) this;
        }

        public Criteria andPsS3RnBetween(Long value1, Long value2) {
            addCriterion("ps_s_3_rn between", value1, value2, "psS3Rn");
            return (Criteria) this;
        }

        public Criteria andPsS3RnNotBetween(Long value1, Long value2) {
            addCriterion("ps_s_3_rn not between", value1, value2, "psS3Rn");
            return (Criteria) this;
        }

        public Criteria andP4IsNull() {
            addCriterion("p4 is null");
            return (Criteria) this;
        }

        public Criteria andP4IsNotNull() {
            addCriterion("p4 is not null");
            return (Criteria) this;
        }

        public Criteria andP4EqualTo(Double value) {
            addCriterion("p4 =", value, "p4");
            return (Criteria) this;
        }

        public Criteria andP4NotEqualTo(Double value) {
            addCriterion("p4 <>", value, "p4");
            return (Criteria) this;
        }

        public Criteria andP4GreaterThan(Double value) {
            addCriterion("p4 >", value, "p4");
            return (Criteria) this;
        }

        public Criteria andP4GreaterThanOrEqualTo(Double value) {
            addCriterion("p4 >=", value, "p4");
            return (Criteria) this;
        }

        public Criteria andP4LessThan(Double value) {
            addCriterion("p4 <", value, "p4");
            return (Criteria) this;
        }

        public Criteria andP4LessThanOrEqualTo(Double value) {
            addCriterion("p4 <=", value, "p4");
            return (Criteria) this;
        }

        public Criteria andP4In(List<Double> values) {
            addCriterion("p4 in", values, "p4");
            return (Criteria) this;
        }

        public Criteria andP4NotIn(List<Double> values) {
            addCriterion("p4 not in", values, "p4");
            return (Criteria) this;
        }

        public Criteria andP4Between(Double value1, Double value2) {
            addCriterion("p4 between", value1, value2, "p4");
            return (Criteria) this;
        }

        public Criteria andP4NotBetween(Double value1, Double value2) {
            addCriterion("p4 not between", value1, value2, "p4");
            return (Criteria) this;
        }

        public Criteria andPs4IsNull() {
            addCriterion("ps_4 is null");
            return (Criteria) this;
        }

        public Criteria andPs4IsNotNull() {
            addCriterion("ps_4 is not null");
            return (Criteria) this;
        }

        public Criteria andPs4EqualTo(Double value) {
            addCriterion("ps_4 =", value, "ps4");
            return (Criteria) this;
        }

        public Criteria andPs4NotEqualTo(Double value) {
            addCriterion("ps_4 <>", value, "ps4");
            return (Criteria) this;
        }

        public Criteria andPs4GreaterThan(Double value) {
            addCriterion("ps_4 >", value, "ps4");
            return (Criteria) this;
        }

        public Criteria andPs4GreaterThanOrEqualTo(Double value) {
            addCriterion("ps_4 >=", value, "ps4");
            return (Criteria) this;
        }

        public Criteria andPs4LessThan(Double value) {
            addCriterion("ps_4 <", value, "ps4");
            return (Criteria) this;
        }

        public Criteria andPs4LessThanOrEqualTo(Double value) {
            addCriterion("ps_4 <=", value, "ps4");
            return (Criteria) this;
        }

        public Criteria andPs4In(List<Double> values) {
            addCriterion("ps_4 in", values, "ps4");
            return (Criteria) this;
        }

        public Criteria andPs4NotIn(List<Double> values) {
            addCriterion("ps_4 not in", values, "ps4");
            return (Criteria) this;
        }

        public Criteria andPs4Between(Double value1, Double value2) {
            addCriterion("ps_4 between", value1, value2, "ps4");
            return (Criteria) this;
        }

        public Criteria andPs4NotBetween(Double value1, Double value2) {
            addCriterion("ps_4 not between", value1, value2, "ps4");
            return (Criteria) this;
        }

        public Criteria andPsS4IsNull() {
            addCriterion("ps_s_4 is null");
            return (Criteria) this;
        }

        public Criteria andPsS4IsNotNull() {
            addCriterion("ps_s_4 is not null");
            return (Criteria) this;
        }

        public Criteria andPsS4EqualTo(Long value) {
            addCriterion("ps_s_4 =", value, "psS4");
            return (Criteria) this;
        }

        public Criteria andPsS4NotEqualTo(Long value) {
            addCriterion("ps_s_4 <>", value, "psS4");
            return (Criteria) this;
        }

        public Criteria andPsS4GreaterThan(Long value) {
            addCriterion("ps_s_4 >", value, "psS4");
            return (Criteria) this;
        }

        public Criteria andPsS4GreaterThanOrEqualTo(Long value) {
            addCriterion("ps_s_4 >=", value, "psS4");
            return (Criteria) this;
        }

        public Criteria andPsS4LessThan(Long value) {
            addCriterion("ps_s_4 <", value, "psS4");
            return (Criteria) this;
        }

        public Criteria andPsS4LessThanOrEqualTo(Long value) {
            addCriterion("ps_s_4 <=", value, "psS4");
            return (Criteria) this;
        }

        public Criteria andPsS4In(List<Long> values) {
            addCriterion("ps_s_4 in", values, "psS4");
            return (Criteria) this;
        }

        public Criteria andPsS4NotIn(List<Long> values) {
            addCriterion("ps_s_4 not in", values, "psS4");
            return (Criteria) this;
        }

        public Criteria andPsS4Between(Long value1, Long value2) {
            addCriterion("ps_s_4 between", value1, value2, "psS4");
            return (Criteria) this;
        }

        public Criteria andPsS4NotBetween(Long value1, Long value2) {
            addCriterion("ps_s_4 not between", value1, value2, "psS4");
            return (Criteria) this;
        }

        public Criteria andPsS4ShIsNull() {
            addCriterion("ps_s_4_sh is null");
            return (Criteria) this;
        }

        public Criteria andPsS4ShIsNotNull() {
            addCriterion("ps_s_4_sh is not null");
            return (Criteria) this;
        }

        public Criteria andPsS4ShEqualTo(String value) {
            addCriterion("ps_s_4_sh =", value, "psS4Sh");
            return (Criteria) this;
        }

        public Criteria andPsS4ShNotEqualTo(String value) {
            addCriterion("ps_s_4_sh <>", value, "psS4Sh");
            return (Criteria) this;
        }

        public Criteria andPsS4ShGreaterThan(String value) {
            addCriterion("ps_s_4_sh >", value, "psS4Sh");
            return (Criteria) this;
        }

        public Criteria andPsS4ShGreaterThanOrEqualTo(String value) {
            addCriterion("ps_s_4_sh >=", value, "psS4Sh");
            return (Criteria) this;
        }

        public Criteria andPsS4ShLessThan(String value) {
            addCriterion("ps_s_4_sh <", value, "psS4Sh");
            return (Criteria) this;
        }

        public Criteria andPsS4ShLessThanOrEqualTo(String value) {
            addCriterion("ps_s_4_sh <=", value, "psS4Sh");
            return (Criteria) this;
        }

        public Criteria andPsS4ShLike(String value) {
            addCriterion("ps_s_4_sh like", value, "psS4Sh");
            return (Criteria) this;
        }

        public Criteria andPsS4ShNotLike(String value) {
            addCriterion("ps_s_4_sh not like", value, "psS4Sh");
            return (Criteria) this;
        }

        public Criteria andPsS4ShIn(List<String> values) {
            addCriterion("ps_s_4_sh in", values, "psS4Sh");
            return (Criteria) this;
        }

        public Criteria andPsS4ShNotIn(List<String> values) {
            addCriterion("ps_s_4_sh not in", values, "psS4Sh");
            return (Criteria) this;
        }

        public Criteria andPsS4ShBetween(String value1, String value2) {
            addCriterion("ps_s_4_sh between", value1, value2, "psS4Sh");
            return (Criteria) this;
        }

        public Criteria andPsS4ShNotBetween(String value1, String value2) {
            addCriterion("ps_s_4_sh not between", value1, value2, "psS4Sh");
            return (Criteria) this;
        }

        public Criteria andPsS4RnIsNull() {
            addCriterion("ps_s_4_rn is null");
            return (Criteria) this;
        }

        public Criteria andPsS4RnIsNotNull() {
            addCriterion("ps_s_4_rn is not null");
            return (Criteria) this;
        }

        public Criteria andPsS4RnEqualTo(Long value) {
            addCriterion("ps_s_4_rn =", value, "psS4Rn");
            return (Criteria) this;
        }

        public Criteria andPsS4RnNotEqualTo(Long value) {
            addCriterion("ps_s_4_rn <>", value, "psS4Rn");
            return (Criteria) this;
        }

        public Criteria andPsS4RnGreaterThan(Long value) {
            addCriterion("ps_s_4_rn >", value, "psS4Rn");
            return (Criteria) this;
        }

        public Criteria andPsS4RnGreaterThanOrEqualTo(Long value) {
            addCriterion("ps_s_4_rn >=", value, "psS4Rn");
            return (Criteria) this;
        }

        public Criteria andPsS4RnLessThan(Long value) {
            addCriterion("ps_s_4_rn <", value, "psS4Rn");
            return (Criteria) this;
        }

        public Criteria andPsS4RnLessThanOrEqualTo(Long value) {
            addCriterion("ps_s_4_rn <=", value, "psS4Rn");
            return (Criteria) this;
        }

        public Criteria andPsS4RnIn(List<Long> values) {
            addCriterion("ps_s_4_rn in", values, "psS4Rn");
            return (Criteria) this;
        }

        public Criteria andPsS4RnNotIn(List<Long> values) {
            addCriterion("ps_s_4_rn not in", values, "psS4Rn");
            return (Criteria) this;
        }

        public Criteria andPsS4RnBetween(Long value1, Long value2) {
            addCriterion("ps_s_4_rn between", value1, value2, "psS4Rn");
            return (Criteria) this;
        }

        public Criteria andPsS4RnNotBetween(Long value1, Long value2) {
            addCriterion("ps_s_4_rn not between", value1, value2, "psS4Rn");
            return (Criteria) this;
        }

        public Criteria andP5IsNull() {
            addCriterion("p5 is null");
            return (Criteria) this;
        }

        public Criteria andP5IsNotNull() {
            addCriterion("p5 is not null");
            return (Criteria) this;
        }

        public Criteria andP5EqualTo(Double value) {
            addCriterion("p5 =", value, "p5");
            return (Criteria) this;
        }

        public Criteria andP5NotEqualTo(Double value) {
            addCriterion("p5 <>", value, "p5");
            return (Criteria) this;
        }

        public Criteria andP5GreaterThan(Double value) {
            addCriterion("p5 >", value, "p5");
            return (Criteria) this;
        }

        public Criteria andP5GreaterThanOrEqualTo(Double value) {
            addCriterion("p5 >=", value, "p5");
            return (Criteria) this;
        }

        public Criteria andP5LessThan(Double value) {
            addCriterion("p5 <", value, "p5");
            return (Criteria) this;
        }

        public Criteria andP5LessThanOrEqualTo(Double value) {
            addCriterion("p5 <=", value, "p5");
            return (Criteria) this;
        }

        public Criteria andP5In(List<Double> values) {
            addCriterion("p5 in", values, "p5");
            return (Criteria) this;
        }

        public Criteria andP5NotIn(List<Double> values) {
            addCriterion("p5 not in", values, "p5");
            return (Criteria) this;
        }

        public Criteria andP5Between(Double value1, Double value2) {
            addCriterion("p5 between", value1, value2, "p5");
            return (Criteria) this;
        }

        public Criteria andP5NotBetween(Double value1, Double value2) {
            addCriterion("p5 not between", value1, value2, "p5");
            return (Criteria) this;
        }

        public Criteria andPs5IsNull() {
            addCriterion("ps_5 is null");
            return (Criteria) this;
        }

        public Criteria andPs5IsNotNull() {
            addCriterion("ps_5 is not null");
            return (Criteria) this;
        }

        public Criteria andPs5EqualTo(Double value) {
            addCriterion("ps_5 =", value, "ps5");
            return (Criteria) this;
        }

        public Criteria andPs5NotEqualTo(Double value) {
            addCriterion("ps_5 <>", value, "ps5");
            return (Criteria) this;
        }

        public Criteria andPs5GreaterThan(Double value) {
            addCriterion("ps_5 >", value, "ps5");
            return (Criteria) this;
        }

        public Criteria andPs5GreaterThanOrEqualTo(Double value) {
            addCriterion("ps_5 >=", value, "ps5");
            return (Criteria) this;
        }

        public Criteria andPs5LessThan(Double value) {
            addCriterion("ps_5 <", value, "ps5");
            return (Criteria) this;
        }

        public Criteria andPs5LessThanOrEqualTo(Double value) {
            addCriterion("ps_5 <=", value, "ps5");
            return (Criteria) this;
        }

        public Criteria andPs5In(List<Double> values) {
            addCriterion("ps_5 in", values, "ps5");
            return (Criteria) this;
        }

        public Criteria andPs5NotIn(List<Double> values) {
            addCriterion("ps_5 not in", values, "ps5");
            return (Criteria) this;
        }

        public Criteria andPs5Between(Double value1, Double value2) {
            addCriterion("ps_5 between", value1, value2, "ps5");
            return (Criteria) this;
        }

        public Criteria andPs5NotBetween(Double value1, Double value2) {
            addCriterion("ps_5 not between", value1, value2, "ps5");
            return (Criteria) this;
        }

        public Criteria andPsS5IsNull() {
            addCriterion("ps_s_5 is null");
            return (Criteria) this;
        }

        public Criteria andPsS5IsNotNull() {
            addCriterion("ps_s_5 is not null");
            return (Criteria) this;
        }

        public Criteria andPsS5EqualTo(Long value) {
            addCriterion("ps_s_5 =", value, "psS5");
            return (Criteria) this;
        }

        public Criteria andPsS5NotEqualTo(Long value) {
            addCriterion("ps_s_5 <>", value, "psS5");
            return (Criteria) this;
        }

        public Criteria andPsS5GreaterThan(Long value) {
            addCriterion("ps_s_5 >", value, "psS5");
            return (Criteria) this;
        }

        public Criteria andPsS5GreaterThanOrEqualTo(Long value) {
            addCriterion("ps_s_5 >=", value, "psS5");
            return (Criteria) this;
        }

        public Criteria andPsS5LessThan(Long value) {
            addCriterion("ps_s_5 <", value, "psS5");
            return (Criteria) this;
        }

        public Criteria andPsS5LessThanOrEqualTo(Long value) {
            addCriterion("ps_s_5 <=", value, "psS5");
            return (Criteria) this;
        }

        public Criteria andPsS5In(List<Long> values) {
            addCriterion("ps_s_5 in", values, "psS5");
            return (Criteria) this;
        }

        public Criteria andPsS5NotIn(List<Long> values) {
            addCriterion("ps_s_5 not in", values, "psS5");
            return (Criteria) this;
        }

        public Criteria andPsS5Between(Long value1, Long value2) {
            addCriterion("ps_s_5 between", value1, value2, "psS5");
            return (Criteria) this;
        }

        public Criteria andPsS5NotBetween(Long value1, Long value2) {
            addCriterion("ps_s_5 not between", value1, value2, "psS5");
            return (Criteria) this;
        }

        public Criteria andPsS5ShIsNull() {
            addCriterion("ps_s_5_sh is null");
            return (Criteria) this;
        }

        public Criteria andPsS5ShIsNotNull() {
            addCriterion("ps_s_5_sh is not null");
            return (Criteria) this;
        }

        public Criteria andPsS5ShEqualTo(String value) {
            addCriterion("ps_s_5_sh =", value, "psS5Sh");
            return (Criteria) this;
        }

        public Criteria andPsS5ShNotEqualTo(String value) {
            addCriterion("ps_s_5_sh <>", value, "psS5Sh");
            return (Criteria) this;
        }

        public Criteria andPsS5ShGreaterThan(String value) {
            addCriterion("ps_s_5_sh >", value, "psS5Sh");
            return (Criteria) this;
        }

        public Criteria andPsS5ShGreaterThanOrEqualTo(String value) {
            addCriterion("ps_s_5_sh >=", value, "psS5Sh");
            return (Criteria) this;
        }

        public Criteria andPsS5ShLessThan(String value) {
            addCriterion("ps_s_5_sh <", value, "psS5Sh");
            return (Criteria) this;
        }

        public Criteria andPsS5ShLessThanOrEqualTo(String value) {
            addCriterion("ps_s_5_sh <=", value, "psS5Sh");
            return (Criteria) this;
        }

        public Criteria andPsS5ShLike(String value) {
            addCriterion("ps_s_5_sh like", value, "psS5Sh");
            return (Criteria) this;
        }

        public Criteria andPsS5ShNotLike(String value) {
            addCriterion("ps_s_5_sh not like", value, "psS5Sh");
            return (Criteria) this;
        }

        public Criteria andPsS5ShIn(List<String> values) {
            addCriterion("ps_s_5_sh in", values, "psS5Sh");
            return (Criteria) this;
        }

        public Criteria andPsS5ShNotIn(List<String> values) {
            addCriterion("ps_s_5_sh not in", values, "psS5Sh");
            return (Criteria) this;
        }

        public Criteria andPsS5ShBetween(String value1, String value2) {
            addCriterion("ps_s_5_sh between", value1, value2, "psS5Sh");
            return (Criteria) this;
        }

        public Criteria andPsS5ShNotBetween(String value1, String value2) {
            addCriterion("ps_s_5_sh not between", value1, value2, "psS5Sh");
            return (Criteria) this;
        }

        public Criteria andPsS5RnIsNull() {
            addCriterion("ps_s_5_rn is null");
            return (Criteria) this;
        }

        public Criteria andPsS5RnIsNotNull() {
            addCriterion("ps_s_5_rn is not null");
            return (Criteria) this;
        }

        public Criteria andPsS5RnEqualTo(Long value) {
            addCriterion("ps_s_5_rn =", value, "psS5Rn");
            return (Criteria) this;
        }

        public Criteria andPsS5RnNotEqualTo(Long value) {
            addCriterion("ps_s_5_rn <>", value, "psS5Rn");
            return (Criteria) this;
        }

        public Criteria andPsS5RnGreaterThan(Long value) {
            addCriterion("ps_s_5_rn >", value, "psS5Rn");
            return (Criteria) this;
        }

        public Criteria andPsS5RnGreaterThanOrEqualTo(Long value) {
            addCriterion("ps_s_5_rn >=", value, "psS5Rn");
            return (Criteria) this;
        }

        public Criteria andPsS5RnLessThan(Long value) {
            addCriterion("ps_s_5_rn <", value, "psS5Rn");
            return (Criteria) this;
        }

        public Criteria andPsS5RnLessThanOrEqualTo(Long value) {
            addCriterion("ps_s_5_rn <=", value, "psS5Rn");
            return (Criteria) this;
        }

        public Criteria andPsS5RnIn(List<Long> values) {
            addCriterion("ps_s_5_rn in", values, "psS5Rn");
            return (Criteria) this;
        }

        public Criteria andPsS5RnNotIn(List<Long> values) {
            addCriterion("ps_s_5_rn not in", values, "psS5Rn");
            return (Criteria) this;
        }

        public Criteria andPsS5RnBetween(Long value1, Long value2) {
            addCriterion("ps_s_5_rn between", value1, value2, "psS5Rn");
            return (Criteria) this;
        }

        public Criteria andPsS5RnNotBetween(Long value1, Long value2) {
            addCriterion("ps_s_5_rn not between", value1, value2, "psS5Rn");
            return (Criteria) this;
        }

        public Criteria andPsAllIsNull() {
            addCriterion("ps_all is null");
            return (Criteria) this;
        }

        public Criteria andPsAllIsNotNull() {
            addCriterion("ps_all is not null");
            return (Criteria) this;
        }

        public Criteria andPsAllEqualTo(Double value) {
            addCriterion("ps_all =", value, "psAll");
            return (Criteria) this;
        }

        public Criteria andPsAllNotEqualTo(Double value) {
            addCriterion("ps_all <>", value, "psAll");
            return (Criteria) this;
        }

        public Criteria andPsAllGreaterThan(Double value) {
            addCriterion("ps_all >", value, "psAll");
            return (Criteria) this;
        }

        public Criteria andPsAllGreaterThanOrEqualTo(Double value) {
            addCriterion("ps_all >=", value, "psAll");
            return (Criteria) this;
        }

        public Criteria andPsAllLessThan(Double value) {
            addCriterion("ps_all <", value, "psAll");
            return (Criteria) this;
        }

        public Criteria andPsAllLessThanOrEqualTo(Double value) {
            addCriterion("ps_all <=", value, "psAll");
            return (Criteria) this;
        }

        public Criteria andPsAllIn(List<Double> values) {
            addCriterion("ps_all in", values, "psAll");
            return (Criteria) this;
        }

        public Criteria andPsAllNotIn(List<Double> values) {
            addCriterion("ps_all not in", values, "psAll");
            return (Criteria) this;
        }

        public Criteria andPsAllBetween(Double value1, Double value2) {
            addCriterion("ps_all between", value1, value2, "psAll");
            return (Criteria) this;
        }

        public Criteria andPsAllNotBetween(Double value1, Double value2) {
            addCriterion("ps_all not between", value1, value2, "psAll");
            return (Criteria) this;
        }

        public Criteria andPsSAllCorIsNull() {
            addCriterion("ps_s_all_cor is null");
            return (Criteria) this;
        }

        public Criteria andPsSAllCorIsNotNull() {
            addCriterion("ps_s_all_cor is not null");
            return (Criteria) this;
        }

        public Criteria andPsSAllCorEqualTo(Long value) {
            addCriterion("ps_s_all_cor =", value, "psSAllCor");
            return (Criteria) this;
        }

        public Criteria andPsSAllCorNotEqualTo(Long value) {
            addCriterion("ps_s_all_cor <>", value, "psSAllCor");
            return (Criteria) this;
        }

        public Criteria andPsSAllCorGreaterThan(Long value) {
            addCriterion("ps_s_all_cor >", value, "psSAllCor");
            return (Criteria) this;
        }

        public Criteria andPsSAllCorGreaterThanOrEqualTo(Long value) {
            addCriterion("ps_s_all_cor >=", value, "psSAllCor");
            return (Criteria) this;
        }

        public Criteria andPsSAllCorLessThan(Long value) {
            addCriterion("ps_s_all_cor <", value, "psSAllCor");
            return (Criteria) this;
        }

        public Criteria andPsSAllCorLessThanOrEqualTo(Long value) {
            addCriterion("ps_s_all_cor <=", value, "psSAllCor");
            return (Criteria) this;
        }

        public Criteria andPsSAllCorIn(List<Long> values) {
            addCriterion("ps_s_all_cor in", values, "psSAllCor");
            return (Criteria) this;
        }

        public Criteria andPsSAllCorNotIn(List<Long> values) {
            addCriterion("ps_s_all_cor not in", values, "psSAllCor");
            return (Criteria) this;
        }

        public Criteria andPsSAllCorBetween(Long value1, Long value2) {
            addCriterion("ps_s_all_cor between", value1, value2, "psSAllCor");
            return (Criteria) this;
        }

        public Criteria andPsSAllCorNotBetween(Long value1, Long value2) {
            addCriterion("ps_s_all_cor not between", value1, value2, "psSAllCor");
            return (Criteria) this;
        }

        public Criteria andPsSAllCorRrIsNull() {
            addCriterion("ps_s_all_cor_rr is null");
            return (Criteria) this;
        }

        public Criteria andPsSAllCorRrIsNotNull() {
            addCriterion("ps_s_all_cor_rr is not null");
            return (Criteria) this;
        }

        public Criteria andPsSAllCorRrEqualTo(String value) {
            addCriterion("ps_s_all_cor_rr =", value, "psSAllCorRr");
            return (Criteria) this;
        }

        public Criteria andPsSAllCorRrNotEqualTo(String value) {
            addCriterion("ps_s_all_cor_rr <>", value, "psSAllCorRr");
            return (Criteria) this;
        }

        public Criteria andPsSAllCorRrGreaterThan(String value) {
            addCriterion("ps_s_all_cor_rr >", value, "psSAllCorRr");
            return (Criteria) this;
        }

        public Criteria andPsSAllCorRrGreaterThanOrEqualTo(String value) {
            addCriterion("ps_s_all_cor_rr >=", value, "psSAllCorRr");
            return (Criteria) this;
        }

        public Criteria andPsSAllCorRrLessThan(String value) {
            addCriterion("ps_s_all_cor_rr <", value, "psSAllCorRr");
            return (Criteria) this;
        }

        public Criteria andPsSAllCorRrLessThanOrEqualTo(String value) {
            addCriterion("ps_s_all_cor_rr <=", value, "psSAllCorRr");
            return (Criteria) this;
        }

        public Criteria andPsSAllCorRrLike(String value) {
            addCriterion("ps_s_all_cor_rr like", value, "psSAllCorRr");
            return (Criteria) this;
        }

        public Criteria andPsSAllCorRrNotLike(String value) {
            addCriterion("ps_s_all_cor_rr not like", value, "psSAllCorRr");
            return (Criteria) this;
        }

        public Criteria andPsSAllCorRrIn(List<String> values) {
            addCriterion("ps_s_all_cor_rr in", values, "psSAllCorRr");
            return (Criteria) this;
        }

        public Criteria andPsSAllCorRrNotIn(List<String> values) {
            addCriterion("ps_s_all_cor_rr not in", values, "psSAllCorRr");
            return (Criteria) this;
        }

        public Criteria andPsSAllCorRrBetween(String value1, String value2) {
            addCriterion("ps_s_all_cor_rr between", value1, value2, "psSAllCorRr");
            return (Criteria) this;
        }

        public Criteria andPsSAllCorRrNotBetween(String value1, String value2) {
            addCriterion("ps_s_all_cor_rr not between", value1, value2, "psSAllCorRr");
            return (Criteria) this;
        }

        public Criteria andPsSAllRnCorIsNull() {
            addCriterion("ps_s_all_rn_cor is null");
            return (Criteria) this;
        }

        public Criteria andPsSAllRnCorIsNotNull() {
            addCriterion("ps_s_all_rn_cor is not null");
            return (Criteria) this;
        }

        public Criteria andPsSAllRnCorEqualTo(Long value) {
            addCriterion("ps_s_all_rn_cor =", value, "psSAllRnCor");
            return (Criteria) this;
        }

        public Criteria andPsSAllRnCorNotEqualTo(Long value) {
            addCriterion("ps_s_all_rn_cor <>", value, "psSAllRnCor");
            return (Criteria) this;
        }

        public Criteria andPsSAllRnCorGreaterThan(Long value) {
            addCriterion("ps_s_all_rn_cor >", value, "psSAllRnCor");
            return (Criteria) this;
        }

        public Criteria andPsSAllRnCorGreaterThanOrEqualTo(Long value) {
            addCriterion("ps_s_all_rn_cor >=", value, "psSAllRnCor");
            return (Criteria) this;
        }

        public Criteria andPsSAllRnCorLessThan(Long value) {
            addCriterion("ps_s_all_rn_cor <", value, "psSAllRnCor");
            return (Criteria) this;
        }

        public Criteria andPsSAllRnCorLessThanOrEqualTo(Long value) {
            addCriterion("ps_s_all_rn_cor <=", value, "psSAllRnCor");
            return (Criteria) this;
        }

        public Criteria andPsSAllRnCorIn(List<Long> values) {
            addCriterion("ps_s_all_rn_cor in", values, "psSAllRnCor");
            return (Criteria) this;
        }

        public Criteria andPsSAllRnCorNotIn(List<Long> values) {
            addCriterion("ps_s_all_rn_cor not in", values, "psSAllRnCor");
            return (Criteria) this;
        }

        public Criteria andPsSAllRnCorBetween(Long value1, Long value2) {
            addCriterion("ps_s_all_rn_cor between", value1, value2, "psSAllRnCor");
            return (Criteria) this;
        }

        public Criteria andPsSAllRnCorNotBetween(Long value1, Long value2) {
            addCriterion("ps_s_all_rn_cor not between", value1, value2, "psSAllRnCor");
            return (Criteria) this;
        }

        public Criteria andPsSAllRnCorRrIsNull() {
            addCriterion("ps_s_all_rn_cor_rr is null");
            return (Criteria) this;
        }

        public Criteria andPsSAllRnCorRrIsNotNull() {
            addCriterion("ps_s_all_rn_cor_rr is not null");
            return (Criteria) this;
        }

        public Criteria andPsSAllRnCorRrEqualTo(String value) {
            addCriterion("ps_s_all_rn_cor_rr =", value, "psSAllRnCorRr");
            return (Criteria) this;
        }

        public Criteria andPsSAllRnCorRrNotEqualTo(String value) {
            addCriterion("ps_s_all_rn_cor_rr <>", value, "psSAllRnCorRr");
            return (Criteria) this;
        }

        public Criteria andPsSAllRnCorRrGreaterThan(String value) {
            addCriterion("ps_s_all_rn_cor_rr >", value, "psSAllRnCorRr");
            return (Criteria) this;
        }

        public Criteria andPsSAllRnCorRrGreaterThanOrEqualTo(String value) {
            addCriterion("ps_s_all_rn_cor_rr >=", value, "psSAllRnCorRr");
            return (Criteria) this;
        }

        public Criteria andPsSAllRnCorRrLessThan(String value) {
            addCriterion("ps_s_all_rn_cor_rr <", value, "psSAllRnCorRr");
            return (Criteria) this;
        }

        public Criteria andPsSAllRnCorRrLessThanOrEqualTo(String value) {
            addCriterion("ps_s_all_rn_cor_rr <=", value, "psSAllRnCorRr");
            return (Criteria) this;
        }

        public Criteria andPsSAllRnCorRrLike(String value) {
            addCriterion("ps_s_all_rn_cor_rr like", value, "psSAllRnCorRr");
            return (Criteria) this;
        }

        public Criteria andPsSAllRnCorRrNotLike(String value) {
            addCriterion("ps_s_all_rn_cor_rr not like", value, "psSAllRnCorRr");
            return (Criteria) this;
        }

        public Criteria andPsSAllRnCorRrIn(List<String> values) {
            addCriterion("ps_s_all_rn_cor_rr in", values, "psSAllRnCorRr");
            return (Criteria) this;
        }

        public Criteria andPsSAllRnCorRrNotIn(List<String> values) {
            addCriterion("ps_s_all_rn_cor_rr not in", values, "psSAllRnCorRr");
            return (Criteria) this;
        }

        public Criteria andPsSAllRnCorRrBetween(String value1, String value2) {
            addCriterion("ps_s_all_rn_cor_rr between", value1, value2, "psSAllRnCorRr");
            return (Criteria) this;
        }

        public Criteria andPsSAllRnCorRrNotBetween(String value1, String value2) {
            addCriterion("ps_s_all_rn_cor_rr not between", value1, value2, "psSAllRnCorRr");
            return (Criteria) this;
        }

        public Criteria andSprNewUvIsNull() {
            addCriterion("spr_new_uv is null");
            return (Criteria) this;
        }

        public Criteria andSprNewUvIsNotNull() {
            addCriterion("spr_new_uv is not null");
            return (Criteria) this;
        }

        public Criteria andSprNewUvEqualTo(Double value) {
            addCriterion("spr_new_uv =", value, "sprNewUv");
            return (Criteria) this;
        }

        public Criteria andSprNewUvNotEqualTo(Double value) {
            addCriterion("spr_new_uv <>", value, "sprNewUv");
            return (Criteria) this;
        }

        public Criteria andSprNewUvGreaterThan(Double value) {
            addCriterion("spr_new_uv >", value, "sprNewUv");
            return (Criteria) this;
        }

        public Criteria andSprNewUvGreaterThanOrEqualTo(Double value) {
            addCriterion("spr_new_uv >=", value, "sprNewUv");
            return (Criteria) this;
        }

        public Criteria andSprNewUvLessThan(Double value) {
            addCriterion("spr_new_uv <", value, "sprNewUv");
            return (Criteria) this;
        }

        public Criteria andSprNewUvLessThanOrEqualTo(Double value) {
            addCriterion("spr_new_uv <=", value, "sprNewUv");
            return (Criteria) this;
        }

        public Criteria andSprNewUvIn(List<Double> values) {
            addCriterion("spr_new_uv in", values, "sprNewUv");
            return (Criteria) this;
        }

        public Criteria andSprNewUvNotIn(List<Double> values) {
            addCriterion("spr_new_uv not in", values, "sprNewUv");
            return (Criteria) this;
        }

        public Criteria andSprNewUvBetween(Double value1, Double value2) {
            addCriterion("spr_new_uv between", value1, value2, "sprNewUv");
            return (Criteria) this;
        }

        public Criteria andSprNewUvNotBetween(Double value1, Double value2) {
            addCriterion("spr_new_uv not between", value1, value2, "sprNewUv");
            return (Criteria) this;
        }

        public Criteria andSprValidLiveNewUvIsNull() {
            addCriterion("spr_valid_live_new_uv is null");
            return (Criteria) this;
        }

        public Criteria andSprValidLiveNewUvIsNotNull() {
            addCriterion("spr_valid_live_new_uv is not null");
            return (Criteria) this;
        }

        public Criteria andSprValidLiveNewUvEqualTo(Double value) {
            addCriterion("spr_valid_live_new_uv =", value, "sprValidLiveNewUv");
            return (Criteria) this;
        }

        public Criteria andSprValidLiveNewUvNotEqualTo(Double value) {
            addCriterion("spr_valid_live_new_uv <>", value, "sprValidLiveNewUv");
            return (Criteria) this;
        }

        public Criteria andSprValidLiveNewUvGreaterThan(Double value) {
            addCriterion("spr_valid_live_new_uv >", value, "sprValidLiveNewUv");
            return (Criteria) this;
        }

        public Criteria andSprValidLiveNewUvGreaterThanOrEqualTo(Double value) {
            addCriterion("spr_valid_live_new_uv >=", value, "sprValidLiveNewUv");
            return (Criteria) this;
        }

        public Criteria andSprValidLiveNewUvLessThan(Double value) {
            addCriterion("spr_valid_live_new_uv <", value, "sprValidLiveNewUv");
            return (Criteria) this;
        }

        public Criteria andSprValidLiveNewUvLessThanOrEqualTo(Double value) {
            addCriterion("spr_valid_live_new_uv <=", value, "sprValidLiveNewUv");
            return (Criteria) this;
        }

        public Criteria andSprValidLiveNewUvIn(List<Double> values) {
            addCriterion("spr_valid_live_new_uv in", values, "sprValidLiveNewUv");
            return (Criteria) this;
        }

        public Criteria andSprValidLiveNewUvNotIn(List<Double> values) {
            addCriterion("spr_valid_live_new_uv not in", values, "sprValidLiveNewUv");
            return (Criteria) this;
        }

        public Criteria andSprValidLiveNewUvBetween(Double value1, Double value2) {
            addCriterion("spr_valid_live_new_uv between", value1, value2, "sprValidLiveNewUv");
            return (Criteria) this;
        }

        public Criteria andSprValidLiveNewUvNotBetween(Double value1, Double value2) {
            addCriterion("spr_valid_live_new_uv not between", value1, value2, "sprValidLiveNewUv");
            return (Criteria) this;
        }

        public Criteria andSprAcu10UvIsNull() {
            addCriterion("spr_acu10_uv is null");
            return (Criteria) this;
        }

        public Criteria andSprAcu10UvIsNotNull() {
            addCriterion("spr_acu10_uv is not null");
            return (Criteria) this;
        }

        public Criteria andSprAcu10UvEqualTo(Double value) {
            addCriterion("spr_acu10_uv =", value, "sprAcu10Uv");
            return (Criteria) this;
        }

        public Criteria andSprAcu10UvNotEqualTo(Double value) {
            addCriterion("spr_acu10_uv <>", value, "sprAcu10Uv");
            return (Criteria) this;
        }

        public Criteria andSprAcu10UvGreaterThan(Double value) {
            addCriterion("spr_acu10_uv >", value, "sprAcu10Uv");
            return (Criteria) this;
        }

        public Criteria andSprAcu10UvGreaterThanOrEqualTo(Double value) {
            addCriterion("spr_acu10_uv >=", value, "sprAcu10Uv");
            return (Criteria) this;
        }

        public Criteria andSprAcu10UvLessThan(Double value) {
            addCriterion("spr_acu10_uv <", value, "sprAcu10Uv");
            return (Criteria) this;
        }

        public Criteria andSprAcu10UvLessThanOrEqualTo(Double value) {
            addCriterion("spr_acu10_uv <=", value, "sprAcu10Uv");
            return (Criteria) this;
        }

        public Criteria andSprAcu10UvIn(List<Double> values) {
            addCriterion("spr_acu10_uv in", values, "sprAcu10Uv");
            return (Criteria) this;
        }

        public Criteria andSprAcu10UvNotIn(List<Double> values) {
            addCriterion("spr_acu10_uv not in", values, "sprAcu10Uv");
            return (Criteria) this;
        }

        public Criteria andSprAcu10UvBetween(Double value1, Double value2) {
            addCriterion("spr_acu10_uv between", value1, value2, "sprAcu10Uv");
            return (Criteria) this;
        }

        public Criteria andSprAcu10UvNotBetween(Double value1, Double value2) {
            addCriterion("spr_acu10_uv not between", value1, value2, "sprAcu10Uv");
            return (Criteria) this;
        }

        public Criteria andSprHActUvIsNull() {
            addCriterion("spr_h_act_uv is null");
            return (Criteria) this;
        }

        public Criteria andSprHActUvIsNotNull() {
            addCriterion("spr_h_act_uv is not null");
            return (Criteria) this;
        }

        public Criteria andSprHActUvEqualTo(Double value) {
            addCriterion("spr_h_act_uv =", value, "sprHActUv");
            return (Criteria) this;
        }

        public Criteria andSprHActUvNotEqualTo(Double value) {
            addCriterion("spr_h_act_uv <>", value, "sprHActUv");
            return (Criteria) this;
        }

        public Criteria andSprHActUvGreaterThan(Double value) {
            addCriterion("spr_h_act_uv >", value, "sprHActUv");
            return (Criteria) this;
        }

        public Criteria andSprHActUvGreaterThanOrEqualTo(Double value) {
            addCriterion("spr_h_act_uv >=", value, "sprHActUv");
            return (Criteria) this;
        }

        public Criteria andSprHActUvLessThan(Double value) {
            addCriterion("spr_h_act_uv <", value, "sprHActUv");
            return (Criteria) this;
        }

        public Criteria andSprHActUvLessThanOrEqualTo(Double value) {
            addCriterion("spr_h_act_uv <=", value, "sprHActUv");
            return (Criteria) this;
        }

        public Criteria andSprHActUvIn(List<Double> values) {
            addCriterion("spr_h_act_uv in", values, "sprHActUv");
            return (Criteria) this;
        }

        public Criteria andSprHActUvNotIn(List<Double> values) {
            addCriterion("spr_h_act_uv not in", values, "sprHActUv");
            return (Criteria) this;
        }

        public Criteria andSprHActUvBetween(Double value1, Double value2) {
            addCriterion("spr_h_act_uv between", value1, value2, "sprHActUv");
            return (Criteria) this;
        }

        public Criteria andSprHActUvNotBetween(Double value1, Double value2) {
            addCriterion("spr_h_act_uv not between", value1, value2, "sprHActUv");
            return (Criteria) this;
        }

        public Criteria andSprMonthDiamondIsNull() {
            addCriterion("spr_month_diamond is null");
            return (Criteria) this;
        }

        public Criteria andSprMonthDiamondIsNotNull() {
            addCriterion("spr_month_diamond is not null");
            return (Criteria) this;
        }

        public Criteria andSprMonthDiamondEqualTo(Double value) {
            addCriterion("spr_month_diamond =", value, "sprMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andSprMonthDiamondNotEqualTo(Double value) {
            addCriterion("spr_month_diamond <>", value, "sprMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andSprMonthDiamondGreaterThan(Double value) {
            addCriterion("spr_month_diamond >", value, "sprMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andSprMonthDiamondGreaterThanOrEqualTo(Double value) {
            addCriterion("spr_month_diamond >=", value, "sprMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andSprMonthDiamondLessThan(Double value) {
            addCriterion("spr_month_diamond <", value, "sprMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andSprMonthDiamondLessThanOrEqualTo(Double value) {
            addCriterion("spr_month_diamond <=", value, "sprMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andSprMonthDiamondIn(List<Double> values) {
            addCriterion("spr_month_diamond in", values, "sprMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andSprMonthDiamondNotIn(List<Double> values) {
            addCriterion("spr_month_diamond not in", values, "sprMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andSprMonthDiamondBetween(Double value1, Double value2) {
            addCriterion("spr_month_diamond between", value1, value2, "sprMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andSprMonthDiamondNotBetween(Double value1, Double value2) {
            addCriterion("spr_month_diamond not between", value1, value2, "sprMonthDiamond");
            return (Criteria) this;
        }

        public Criteria andSprP5IsNull() {
            addCriterion("spr_p5 is null");
            return (Criteria) this;
        }

        public Criteria andSprP5IsNotNull() {
            addCriterion("spr_p5 is not null");
            return (Criteria) this;
        }

        public Criteria andSprP5EqualTo(Double value) {
            addCriterion("spr_p5 =", value, "sprP5");
            return (Criteria) this;
        }

        public Criteria andSprP5NotEqualTo(Double value) {
            addCriterion("spr_p5 <>", value, "sprP5");
            return (Criteria) this;
        }

        public Criteria andSprP5GreaterThan(Double value) {
            addCriterion("spr_p5 >", value, "sprP5");
            return (Criteria) this;
        }

        public Criteria andSprP5GreaterThanOrEqualTo(Double value) {
            addCriterion("spr_p5 >=", value, "sprP5");
            return (Criteria) this;
        }

        public Criteria andSprP5LessThan(Double value) {
            addCriterion("spr_p5 <", value, "sprP5");
            return (Criteria) this;
        }

        public Criteria andSprP5LessThanOrEqualTo(Double value) {
            addCriterion("spr_p5 <=", value, "sprP5");
            return (Criteria) this;
        }

        public Criteria andSprP5In(List<Double> values) {
            addCriterion("spr_p5 in", values, "sprP5");
            return (Criteria) this;
        }

        public Criteria andSprP5NotIn(List<Double> values) {
            addCriterion("spr_p5 not in", values, "sprP5");
            return (Criteria) this;
        }

        public Criteria andSprP5Between(Double value1, Double value2) {
            addCriterion("spr_p5 between", value1, value2, "sprP5");
            return (Criteria) this;
        }

        public Criteria andSprP5NotBetween(Double value1, Double value2) {
            addCriterion("spr_p5 not between", value1, value2, "sprP5");
            return (Criteria) this;
        }

        public Criteria andDtIsNull() {
            addCriterion("dt is null");
            return (Criteria) this;
        }

        public Criteria andDtIsNotNull() {
            addCriterion("dt is not null");
            return (Criteria) this;
        }

        public Criteria andDtEqualTo(String value) {
            addCriterion("dt =", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotEqualTo(String value) {
            addCriterion("dt <>", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtGreaterThan(String value) {
            addCriterion("dt >", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtGreaterThanOrEqualTo(String value) {
            addCriterion("dt >=", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtLessThan(String value) {
            addCriterion("dt <", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtLessThanOrEqualTo(String value) {
            addCriterion("dt <=", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtLike(String value) {
            addCriterion("dt like", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotLike(String value) {
            addCriterion("dt not like", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtIn(List<String> values) {
            addCriterion("dt in", values, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotIn(List<String> values) {
            addCriterion("dt not in", values, "dt");
            return (Criteria) this;
        }

        public Criteria andDtBetween(String value1, String value2) {
            addCriterion("dt between", value1, value2, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotBetween(String value1, String value2) {
            addCriterion("dt not between", value1, value2, "dt");
            return (Criteria) this;
        }
    }

    /**
     * dm_yy_sid_segment_score_month
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * dm_yy_sid_segment_score_month null
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}