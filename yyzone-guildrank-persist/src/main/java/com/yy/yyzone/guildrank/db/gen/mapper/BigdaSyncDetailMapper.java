package com.yy.yyzone.guildrank.db.gen.mapper;

import com.yy.yyzone.guildrank.db.gen.model.BigdaSyncDetail;
import com.yy.yyzone.guildrank.db.gen.model.BigdaSyncDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BigdaSyncDetailMapper {
    int countByExample(BigdaSyncDetailExample example);

    int deleteByExample(BigdaSyncDetailExample example);

    int deleteByPrimaryKey(Long id);

    int insert(BigdaSyncDetail record);

    int insertSelective(BigdaSyncDetail record);

    List<BigdaSyncDetail> selectByExample(BigdaSyncDetailExample example);

    BigdaSyncDetail selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") BigdaSyncDetail record, @Param("example") BigdaSyncDetailExample example);

    int updateByExample(@Param("record") BigdaSyncDetail record, @Param("example") BigdaSyncDetailExample example);

    int updateByPrimaryKeySelective(BigdaSyncDetail record);

    int updateByPrimaryKey(BigdaSyncDetail record);
}