package com.yy.yyzone.guildrank.db.gen.model;

public class DmYySidSegmentOriginStarDay {
    /**
     * id，自增主键
     */
    private Long id;

    /**
     * 公会uid
     */
    private String sidOwnerid;

    /**
     * 公会yy号
     */
    private String sidOwyyid;

    /**
     * 公会营收
     */
    private Double monthDiamond;

    /**
     * 往上一星冲刺营收目标
     */
    private Double monthDiamondSprintValue;

    /**
     * 公会营收距离目标差值
     */
    private Double monthDiamondDiff;

    /**
     * 公会日有效开播主播数
     */
    private Double avgValidLiveUv;

    /**
     * 往上一星冲刺日有效目标
     */
    private Double avgValidLiveUvSprintValue;

    /**
     * 公会日有效开播主播数距离目标差值
     */
    private Double avgValidLiveUvDiff;

    /**
     * 公会人气主播总得分
     */
    private Double anchorTotalScore;

    /**
     * 往上一星冲刺人气主播得分目标
     */
    private Double anchorTotalScoreSprintValue;

    /**
     * 公会人气主播总得分距离目标差值
     */
    private Double anchorTotalScoreDiff;

    /**
     * 公会违规主播数占比
     */
    private Double breakUvRate;

    /**
     * 往上一星冲刺违规主播数占比目标
     */
    private Double breakUvRateSprintValue;

    /**
     * 公会违规主播数占比距离目标差值
     */
    private Double breakUvRateDiff;

    /**
     * 日期
     */
    private String dt;

    /**
     * 等级主播总得分，从202308开始有效
     */
    private Double aidValueScore;

    /**
     * 往上一星冲刺等级主播得分目标，从202308开始有效
     */
    private Double aidValueScoreSprintValue;

    /**
     * 等级主播总得分距离目标差值，从202308开始有效
     */
    private Double aidValueScoreDiff;

    /**
     * 是否小火星公会
     */
    private Integer isMarsGuild;

    /**
     * 关联的SDK小火星公会UID
     */
    private Long marsSidOwnerid;

    /**
     * 获取id，自增主键
     * @return id id，自增主键
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置id，自增主键
     * @param id id，自增主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取公会uid
     * @return sid_ownerid 公会uid
     */
    public String getSidOwnerid() {
        return sidOwnerid;
    }

    /**
     * 设置公会uid
     * @param sidOwnerid 公会uid
     */
    public void setSidOwnerid(String sidOwnerid) {
        this.sidOwnerid = sidOwnerid;
    }

    /**
     * 获取公会yy号
     * @return sid_owyyid 公会yy号
     */
    public String getSidOwyyid() {
        return sidOwyyid;
    }

    /**
     * 设置公会yy号
     * @param sidOwyyid 公会yy号
     */
    public void setSidOwyyid(String sidOwyyid) {
        this.sidOwyyid = sidOwyyid;
    }

    /**
     * 获取公会营收
     * @return month_diamond 公会营收
     */
    public Double getMonthDiamond() {
        return monthDiamond;
    }

    /**
     * 设置公会营收
     * @param monthDiamond 公会营收
     */
    public void setMonthDiamond(Double monthDiamond) {
        this.monthDiamond = monthDiamond;
    }

    /**
     * 获取往上一星冲刺营收目标
     * @return month_diamond_sprint_value 往上一星冲刺营收目标
     */
    public Double getMonthDiamondSprintValue() {
        return monthDiamondSprintValue;
    }

    /**
     * 设置往上一星冲刺营收目标
     * @param monthDiamondSprintValue 往上一星冲刺营收目标
     */
    public void setMonthDiamondSprintValue(Double monthDiamondSprintValue) {
        this.monthDiamondSprintValue = monthDiamondSprintValue;
    }

    /**
     * 获取公会营收距离目标差值
     * @return month_diamond_diff 公会营收距离目标差值
     */
    public Double getMonthDiamondDiff() {
        return monthDiamondDiff;
    }

    /**
     * 设置公会营收距离目标差值
     * @param monthDiamondDiff 公会营收距离目标差值
     */
    public void setMonthDiamondDiff(Double monthDiamondDiff) {
        this.monthDiamondDiff = monthDiamondDiff;
    }

    /**
     * 获取公会日有效开播主播数
     * @return avg_valid_live_uv 公会日有效开播主播数
     */
    public Double getAvgValidLiveUv() {
        return avgValidLiveUv;
    }

    /**
     * 设置公会日有效开播主播数
     * @param avgValidLiveUv 公会日有效开播主播数
     */
    public void setAvgValidLiveUv(Double avgValidLiveUv) {
        this.avgValidLiveUv = avgValidLiveUv;
    }

    /**
     * 获取往上一星冲刺日有效目标
     * @return avg_valid_live_uv_sprint_value 往上一星冲刺日有效目标
     */
    public Double getAvgValidLiveUvSprintValue() {
        return avgValidLiveUvSprintValue;
    }

    /**
     * 设置往上一星冲刺日有效目标
     * @param avgValidLiveUvSprintValue 往上一星冲刺日有效目标
     */
    public void setAvgValidLiveUvSprintValue(Double avgValidLiveUvSprintValue) {
        this.avgValidLiveUvSprintValue = avgValidLiveUvSprintValue;
    }

    /**
     * 获取公会日有效开播主播数距离目标差值
     * @return avg_valid_live_uv_diff 公会日有效开播主播数距离目标差值
     */
    public Double getAvgValidLiveUvDiff() {
        return avgValidLiveUvDiff;
    }

    /**
     * 设置公会日有效开播主播数距离目标差值
     * @param avgValidLiveUvDiff 公会日有效开播主播数距离目标差值
     */
    public void setAvgValidLiveUvDiff(Double avgValidLiveUvDiff) {
        this.avgValidLiveUvDiff = avgValidLiveUvDiff;
    }

    /**
     * 获取公会人气主播总得分
     * @return anchor_total_score 公会人气主播总得分
     */
    public Double getAnchorTotalScore() {
        return anchorTotalScore;
    }

    /**
     * 设置公会人气主播总得分
     * @param anchorTotalScore 公会人气主播总得分
     */
    public void setAnchorTotalScore(Double anchorTotalScore) {
        this.anchorTotalScore = anchorTotalScore;
    }

    /**
     * 获取往上一星冲刺人气主播得分目标
     * @return anchor_total_score_sprint_value 往上一星冲刺人气主播得分目标
     */
    public Double getAnchorTotalScoreSprintValue() {
        return anchorTotalScoreSprintValue;
    }

    /**
     * 设置往上一星冲刺人气主播得分目标
     * @param anchorTotalScoreSprintValue 往上一星冲刺人气主播得分目标
     */
    public void setAnchorTotalScoreSprintValue(Double anchorTotalScoreSprintValue) {
        this.anchorTotalScoreSprintValue = anchorTotalScoreSprintValue;
    }

    /**
     * 获取公会人气主播总得分距离目标差值
     * @return anchor_total_score_diff 公会人气主播总得分距离目标差值
     */
    public Double getAnchorTotalScoreDiff() {
        return anchorTotalScoreDiff;
    }

    /**
     * 设置公会人气主播总得分距离目标差值
     * @param anchorTotalScoreDiff 公会人气主播总得分距离目标差值
     */
    public void setAnchorTotalScoreDiff(Double anchorTotalScoreDiff) {
        this.anchorTotalScoreDiff = anchorTotalScoreDiff;
    }

    /**
     * 获取公会违规主播数占比
     * @return break_uv_rate 公会违规主播数占比
     */
    public Double getBreakUvRate() {
        return breakUvRate;
    }

    /**
     * 设置公会违规主播数占比
     * @param breakUvRate 公会违规主播数占比
     */
    public void setBreakUvRate(Double breakUvRate) {
        this.breakUvRate = breakUvRate;
    }

    /**
     * 获取往上一星冲刺违规主播数占比目标
     * @return break_uv_rate_sprint_value 往上一星冲刺违规主播数占比目标
     */
    public Double getBreakUvRateSprintValue() {
        return breakUvRateSprintValue;
    }

    /**
     * 设置往上一星冲刺违规主播数占比目标
     * @param breakUvRateSprintValue 往上一星冲刺违规主播数占比目标
     */
    public void setBreakUvRateSprintValue(Double breakUvRateSprintValue) {
        this.breakUvRateSprintValue = breakUvRateSprintValue;
    }

    /**
     * 获取公会违规主播数占比距离目标差值
     * @return break_uv_rate_diff 公会违规主播数占比距离目标差值
     */
    public Double getBreakUvRateDiff() {
        return breakUvRateDiff;
    }

    /**
     * 设置公会违规主播数占比距离目标差值
     * @param breakUvRateDiff 公会违规主播数占比距离目标差值
     */
    public void setBreakUvRateDiff(Double breakUvRateDiff) {
        this.breakUvRateDiff = breakUvRateDiff;
    }

    /**
     * 获取日期
     * @return dt 日期
     */
    public String getDt() {
        return dt;
    }

    /**
     * 设置日期
     * @param dt 日期
     */
    public void setDt(String dt) {
        this.dt = dt;
    }

    /**
     * 获取等级主播总得分，从202308开始有效
     * @return aid_value_score 等级主播总得分，从202308开始有效
     */
    public Double getAidValueScore() {
        return aidValueScore;
    }

    /**
     * 设置等级主播总得分，从202308开始有效
     * @param aidValueScore 等级主播总得分，从202308开始有效
     */
    public void setAidValueScore(Double aidValueScore) {
        this.aidValueScore = aidValueScore;
    }

    /**
     * 获取往上一星冲刺等级主播得分目标，从202308开始有效
     * @return aid_value_score_sprint_value 往上一星冲刺等级主播得分目标，从202308开始有效
     */
    public Double getAidValueScoreSprintValue() {
        return aidValueScoreSprintValue;
    }

    /**
     * 设置往上一星冲刺等级主播得分目标，从202308开始有效
     * @param aidValueScoreSprintValue 往上一星冲刺等级主播得分目标，从202308开始有效
     */
    public void setAidValueScoreSprintValue(Double aidValueScoreSprintValue) {
        this.aidValueScoreSprintValue = aidValueScoreSprintValue;
    }

    /**
     * 获取等级主播总得分距离目标差值，从202308开始有效
     * @return aid_value_score_diff 等级主播总得分距离目标差值，从202308开始有效
     */
    public Double getAidValueScoreDiff() {
        return aidValueScoreDiff;
    }

    /**
     * 设置等级主播总得分距离目标差值，从202308开始有效
     * @param aidValueScoreDiff 等级主播总得分距离目标差值，从202308开始有效
     */
    public void setAidValueScoreDiff(Double aidValueScoreDiff) {
        this.aidValueScoreDiff = aidValueScoreDiff;
    }

    /**
     * 获取是否小火星公会
     * @return is_mars_guild 是否小火星公会
     */
    public Integer getIsMarsGuild() {
        return isMarsGuild;
    }

    /**
     * 设置是否小火星公会
     * @param isMarsGuild 是否小火星公会
     */
    public void setIsMarsGuild(Integer isMarsGuild) {
        this.isMarsGuild = isMarsGuild;
    }

    /**
     * 获取关联的SDK小火星公会UID
     * @return mars_sid_ownerid 关联的SDK小火星公会UID
     */
    public Long getMarsSidOwnerid() {
        return marsSidOwnerid;
    }

    /**
     * 设置关联的SDK小火星公会UID
     * @param marsSidOwnerid 关联的SDK小火星公会UID
     */
    public void setMarsSidOwnerid(Long marsSidOwnerid) {
        this.marsSidOwnerid = marsSidOwnerid;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", sidOwnerid=").append(sidOwnerid);
        sb.append(", sidOwyyid=").append(sidOwyyid);
        sb.append(", monthDiamond=").append(monthDiamond);
        sb.append(", monthDiamondSprintValue=").append(monthDiamondSprintValue);
        sb.append(", monthDiamondDiff=").append(monthDiamondDiff);
        sb.append(", avgValidLiveUv=").append(avgValidLiveUv);
        sb.append(", avgValidLiveUvSprintValue=").append(avgValidLiveUvSprintValue);
        sb.append(", avgValidLiveUvDiff=").append(avgValidLiveUvDiff);
        sb.append(", anchorTotalScore=").append(anchorTotalScore);
        sb.append(", anchorTotalScoreSprintValue=").append(anchorTotalScoreSprintValue);
        sb.append(", anchorTotalScoreDiff=").append(anchorTotalScoreDiff);
        sb.append(", breakUvRate=").append(breakUvRate);
        sb.append(", breakUvRateSprintValue=").append(breakUvRateSprintValue);
        sb.append(", breakUvRateDiff=").append(breakUvRateDiff);
        sb.append(", dt=").append(dt);
        sb.append(", aidValueScore=").append(aidValueScore);
        sb.append(", aidValueScoreSprintValue=").append(aidValueScoreSprintValue);
        sb.append(", aidValueScoreDiff=").append(aidValueScoreDiff);
        sb.append(", isMarsGuild=").append(isMarsGuild);
        sb.append(", marsSidOwnerid=").append(marsSidOwnerid);
        sb.append("]");
        return sb.toString();
    }
}