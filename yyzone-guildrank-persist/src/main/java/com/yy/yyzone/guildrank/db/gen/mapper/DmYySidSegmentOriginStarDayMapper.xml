<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yy.yyzone.guildrank.db.gen.mapper.DmYySidSegmentOriginStarDayMapper" >
  <resultMap id="BaseResultMap" type="com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentOriginStarDay" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="sid_ownerid" property="sidOwnerid" jdbcType="VARCHAR" />
    <result column="sid_owyyid" property="sidOwyyid" jdbcType="VARCHAR" />
    <result column="month_diamond" property="monthDiamond" jdbcType="DOUBLE" />
    <result column="month_diamond_sprint_value" property="monthDiamondSprintValue" jdbcType="DOUBLE" />
    <result column="month_diamond_diff" property="monthDiamondDiff" jdbcType="DOUBLE" />
    <result column="avg_valid_live_uv" property="avgValidLiveUv" jdbcType="DOUBLE" />
    <result column="avg_valid_live_uv_sprint_value" property="avgValidLiveUvSprintValue" jdbcType="DOUBLE" />
    <result column="avg_valid_live_uv_diff" property="avgValidLiveUvDiff" jdbcType="DOUBLE" />
    <result column="anchor_total_score" property="anchorTotalScore" jdbcType="DOUBLE" />
    <result column="anchor_total_score_sprint_value" property="anchorTotalScoreSprintValue" jdbcType="DOUBLE" />
    <result column="anchor_total_score_diff" property="anchorTotalScoreDiff" jdbcType="DOUBLE" />
    <result column="break_uv_rate" property="breakUvRate" jdbcType="DOUBLE" />
    <result column="break_uv_rate_sprint_value" property="breakUvRateSprintValue" jdbcType="DOUBLE" />
    <result column="break_uv_rate_diff" property="breakUvRateDiff" jdbcType="DOUBLE" />
    <result column="dt" property="dt" jdbcType="VARCHAR" />
    <result column="aid_value_score" property="aidValueScore" jdbcType="DOUBLE" />
    <result column="aid_value_score_sprint_value" property="aidValueScoreSprintValue" jdbcType="DOUBLE" />
    <result column="aid_value_score_diff" property="aidValueScoreDiff" jdbcType="DOUBLE" />
    <result column="is_mars_guild" property="isMarsGuild" jdbcType="INTEGER" />
    <result column="mars_sid_ownerid" property="marsSidOwnerid" jdbcType="BIGINT" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, sid_ownerid, sid_owyyid, month_diamond, month_diamond_sprint_value, month_diamond_diff, 
    avg_valid_live_uv, avg_valid_live_uv_sprint_value, avg_valid_live_uv_diff, anchor_total_score, 
    anchor_total_score_sprint_value, anchor_total_score_diff, break_uv_rate, break_uv_rate_sprint_value, 
    break_uv_rate_diff, dt, aid_value_score, aid_value_score_sprint_value, aid_value_score_diff, 
    is_mars_guild, mars_sid_ownerid
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentOriginStarDayExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from dm_yy_sid_segment_origin_star_day
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit > 0" >
      limit ${limit}
    </if>
    <if test="offset > 0" >
      offset ${offset}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from dm_yy_sid_segment_origin_star_day
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from dm_yy_sid_segment_origin_star_day
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentOriginStarDayExample" >
    delete from dm_yy_sid_segment_origin_star_day
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentOriginStarDay" useGeneratedKeys="true" keyProperty="id" >
    insert into dm_yy_sid_segment_origin_star_day (sid_ownerid, sid_owyyid, month_diamond, 
      month_diamond_sprint_value, month_diamond_diff, 
      avg_valid_live_uv, avg_valid_live_uv_sprint_value, 
      avg_valid_live_uv_diff, anchor_total_score, anchor_total_score_sprint_value, 
      anchor_total_score_diff, break_uv_rate, break_uv_rate_sprint_value, 
      break_uv_rate_diff, dt, aid_value_score, 
      aid_value_score_sprint_value, aid_value_score_diff, 
      is_mars_guild, mars_sid_ownerid)
    values (#{sidOwnerid,jdbcType=VARCHAR}, #{sidOwyyid,jdbcType=VARCHAR}, #{monthDiamond,jdbcType=DOUBLE}, 
      #{monthDiamondSprintValue,jdbcType=DOUBLE}, #{monthDiamondDiff,jdbcType=DOUBLE}, 
      #{avgValidLiveUv,jdbcType=DOUBLE}, #{avgValidLiveUvSprintValue,jdbcType=DOUBLE}, 
      #{avgValidLiveUvDiff,jdbcType=DOUBLE}, #{anchorTotalScore,jdbcType=DOUBLE}, #{anchorTotalScoreSprintValue,jdbcType=DOUBLE}, 
      #{anchorTotalScoreDiff,jdbcType=DOUBLE}, #{breakUvRate,jdbcType=DOUBLE}, #{breakUvRateSprintValue,jdbcType=DOUBLE}, 
      #{breakUvRateDiff,jdbcType=DOUBLE}, #{dt,jdbcType=VARCHAR}, #{aidValueScore,jdbcType=DOUBLE}, 
      #{aidValueScoreSprintValue,jdbcType=DOUBLE}, #{aidValueScoreDiff,jdbcType=DOUBLE}, 
      #{isMarsGuild,jdbcType=INTEGER}, #{marsSidOwnerid,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentOriginStarDay" useGeneratedKeys="true" keyProperty="id" >
    insert into dm_yy_sid_segment_origin_star_day
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="sidOwnerid != null" >
        sid_ownerid,
      </if>
      <if test="sidOwyyid != null" >
        sid_owyyid,
      </if>
      <if test="monthDiamond != null" >
        month_diamond,
      </if>
      <if test="monthDiamondSprintValue != null" >
        month_diamond_sprint_value,
      </if>
      <if test="monthDiamondDiff != null" >
        month_diamond_diff,
      </if>
      <if test="avgValidLiveUv != null" >
        avg_valid_live_uv,
      </if>
      <if test="avgValidLiveUvSprintValue != null" >
        avg_valid_live_uv_sprint_value,
      </if>
      <if test="avgValidLiveUvDiff != null" >
        avg_valid_live_uv_diff,
      </if>
      <if test="anchorTotalScore != null" >
        anchor_total_score,
      </if>
      <if test="anchorTotalScoreSprintValue != null" >
        anchor_total_score_sprint_value,
      </if>
      <if test="anchorTotalScoreDiff != null" >
        anchor_total_score_diff,
      </if>
      <if test="breakUvRate != null" >
        break_uv_rate,
      </if>
      <if test="breakUvRateSprintValue != null" >
        break_uv_rate_sprint_value,
      </if>
      <if test="breakUvRateDiff != null" >
        break_uv_rate_diff,
      </if>
      <if test="dt != null" >
        dt,
      </if>
      <if test="aidValueScore != null" >
        aid_value_score,
      </if>
      <if test="aidValueScoreSprintValue != null" >
        aid_value_score_sprint_value,
      </if>
      <if test="aidValueScoreDiff != null" >
        aid_value_score_diff,
      </if>
      <if test="isMarsGuild != null" >
        is_mars_guild,
      </if>
      <if test="marsSidOwnerid != null" >
        mars_sid_ownerid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="sidOwnerid != null" >
        #{sidOwnerid,jdbcType=VARCHAR},
      </if>
      <if test="sidOwyyid != null" >
        #{sidOwyyid,jdbcType=VARCHAR},
      </if>
      <if test="monthDiamond != null" >
        #{monthDiamond,jdbcType=DOUBLE},
      </if>
      <if test="monthDiamondSprintValue != null" >
        #{monthDiamondSprintValue,jdbcType=DOUBLE},
      </if>
      <if test="monthDiamondDiff != null" >
        #{monthDiamondDiff,jdbcType=DOUBLE},
      </if>
      <if test="avgValidLiveUv != null" >
        #{avgValidLiveUv,jdbcType=DOUBLE},
      </if>
      <if test="avgValidLiveUvSprintValue != null" >
        #{avgValidLiveUvSprintValue,jdbcType=DOUBLE},
      </if>
      <if test="avgValidLiveUvDiff != null" >
        #{avgValidLiveUvDiff,jdbcType=DOUBLE},
      </if>
      <if test="anchorTotalScore != null" >
        #{anchorTotalScore,jdbcType=DOUBLE},
      </if>
      <if test="anchorTotalScoreSprintValue != null" >
        #{anchorTotalScoreSprintValue,jdbcType=DOUBLE},
      </if>
      <if test="anchorTotalScoreDiff != null" >
        #{anchorTotalScoreDiff,jdbcType=DOUBLE},
      </if>
      <if test="breakUvRate != null" >
        #{breakUvRate,jdbcType=DOUBLE},
      </if>
      <if test="breakUvRateSprintValue != null" >
        #{breakUvRateSprintValue,jdbcType=DOUBLE},
      </if>
      <if test="breakUvRateDiff != null" >
        #{breakUvRateDiff,jdbcType=DOUBLE},
      </if>
      <if test="dt != null" >
        #{dt,jdbcType=VARCHAR},
      </if>
      <if test="aidValueScore != null" >
        #{aidValueScore,jdbcType=DOUBLE},
      </if>
      <if test="aidValueScoreSprintValue != null" >
        #{aidValueScoreSprintValue,jdbcType=DOUBLE},
      </if>
      <if test="aidValueScoreDiff != null" >
        #{aidValueScoreDiff,jdbcType=DOUBLE},
      </if>
      <if test="isMarsGuild != null" >
        #{isMarsGuild,jdbcType=INTEGER},
      </if>
      <if test="marsSidOwnerid != null" >
        #{marsSidOwnerid,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentOriginStarDayExample" resultType="java.lang.Integer" >
    select count(*) from dm_yy_sid_segment_origin_star_day
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update dm_yy_sid_segment_origin_star_day
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.sidOwnerid != null" >
        sid_ownerid = #{record.sidOwnerid,jdbcType=VARCHAR},
      </if>
      <if test="record.sidOwyyid != null" >
        sid_owyyid = #{record.sidOwyyid,jdbcType=VARCHAR},
      </if>
      <if test="record.monthDiamond != null" >
        month_diamond = #{record.monthDiamond,jdbcType=DOUBLE},
      </if>
      <if test="record.monthDiamondSprintValue != null" >
        month_diamond_sprint_value = #{record.monthDiamondSprintValue,jdbcType=DOUBLE},
      </if>
      <if test="record.monthDiamondDiff != null" >
        month_diamond_diff = #{record.monthDiamondDiff,jdbcType=DOUBLE},
      </if>
      <if test="record.avgValidLiveUv != null" >
        avg_valid_live_uv = #{record.avgValidLiveUv,jdbcType=DOUBLE},
      </if>
      <if test="record.avgValidLiveUvSprintValue != null" >
        avg_valid_live_uv_sprint_value = #{record.avgValidLiveUvSprintValue,jdbcType=DOUBLE},
      </if>
      <if test="record.avgValidLiveUvDiff != null" >
        avg_valid_live_uv_diff = #{record.avgValidLiveUvDiff,jdbcType=DOUBLE},
      </if>
      <if test="record.anchorTotalScore != null" >
        anchor_total_score = #{record.anchorTotalScore,jdbcType=DOUBLE},
      </if>
      <if test="record.anchorTotalScoreSprintValue != null" >
        anchor_total_score_sprint_value = #{record.anchorTotalScoreSprintValue,jdbcType=DOUBLE},
      </if>
      <if test="record.anchorTotalScoreDiff != null" >
        anchor_total_score_diff = #{record.anchorTotalScoreDiff,jdbcType=DOUBLE},
      </if>
      <if test="record.breakUvRate != null" >
        break_uv_rate = #{record.breakUvRate,jdbcType=DOUBLE},
      </if>
      <if test="record.breakUvRateSprintValue != null" >
        break_uv_rate_sprint_value = #{record.breakUvRateSprintValue,jdbcType=DOUBLE},
      </if>
      <if test="record.breakUvRateDiff != null" >
        break_uv_rate_diff = #{record.breakUvRateDiff,jdbcType=DOUBLE},
      </if>
      <if test="record.dt != null" >
        dt = #{record.dt,jdbcType=VARCHAR},
      </if>
      <if test="record.aidValueScore != null" >
        aid_value_score = #{record.aidValueScore,jdbcType=DOUBLE},
      </if>
      <if test="record.aidValueScoreSprintValue != null" >
        aid_value_score_sprint_value = #{record.aidValueScoreSprintValue,jdbcType=DOUBLE},
      </if>
      <if test="record.aidValueScoreDiff != null" >
        aid_value_score_diff = #{record.aidValueScoreDiff,jdbcType=DOUBLE},
      </if>
      <if test="record.isMarsGuild != null" >
        is_mars_guild = #{record.isMarsGuild,jdbcType=INTEGER},
      </if>
      <if test="record.marsSidOwnerid != null" >
        mars_sid_ownerid = #{record.marsSidOwnerid,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update dm_yy_sid_segment_origin_star_day
    set id = #{record.id,jdbcType=BIGINT},
      sid_ownerid = #{record.sidOwnerid,jdbcType=VARCHAR},
      sid_owyyid = #{record.sidOwyyid,jdbcType=VARCHAR},
      month_diamond = #{record.monthDiamond,jdbcType=DOUBLE},
      month_diamond_sprint_value = #{record.monthDiamondSprintValue,jdbcType=DOUBLE},
      month_diamond_diff = #{record.monthDiamondDiff,jdbcType=DOUBLE},
      avg_valid_live_uv = #{record.avgValidLiveUv,jdbcType=DOUBLE},
      avg_valid_live_uv_sprint_value = #{record.avgValidLiveUvSprintValue,jdbcType=DOUBLE},
      avg_valid_live_uv_diff = #{record.avgValidLiveUvDiff,jdbcType=DOUBLE},
      anchor_total_score = #{record.anchorTotalScore,jdbcType=DOUBLE},
      anchor_total_score_sprint_value = #{record.anchorTotalScoreSprintValue,jdbcType=DOUBLE},
      anchor_total_score_diff = #{record.anchorTotalScoreDiff,jdbcType=DOUBLE},
      break_uv_rate = #{record.breakUvRate,jdbcType=DOUBLE},
      break_uv_rate_sprint_value = #{record.breakUvRateSprintValue,jdbcType=DOUBLE},
      break_uv_rate_diff = #{record.breakUvRateDiff,jdbcType=DOUBLE},
      dt = #{record.dt,jdbcType=VARCHAR},
      aid_value_score = #{record.aidValueScore,jdbcType=DOUBLE},
      aid_value_score_sprint_value = #{record.aidValueScoreSprintValue,jdbcType=DOUBLE},
      aid_value_score_diff = #{record.aidValueScoreDiff,jdbcType=DOUBLE},
      is_mars_guild = #{record.isMarsGuild,jdbcType=INTEGER},
      mars_sid_ownerid = #{record.marsSidOwnerid,jdbcType=BIGINT}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentOriginStarDay" >
    update dm_yy_sid_segment_origin_star_day
    <set >
      <if test="sidOwnerid != null" >
        sid_ownerid = #{sidOwnerid,jdbcType=VARCHAR},
      </if>
      <if test="sidOwyyid != null" >
        sid_owyyid = #{sidOwyyid,jdbcType=VARCHAR},
      </if>
      <if test="monthDiamond != null" >
        month_diamond = #{monthDiamond,jdbcType=DOUBLE},
      </if>
      <if test="monthDiamondSprintValue != null" >
        month_diamond_sprint_value = #{monthDiamondSprintValue,jdbcType=DOUBLE},
      </if>
      <if test="monthDiamondDiff != null" >
        month_diamond_diff = #{monthDiamondDiff,jdbcType=DOUBLE},
      </if>
      <if test="avgValidLiveUv != null" >
        avg_valid_live_uv = #{avgValidLiveUv,jdbcType=DOUBLE},
      </if>
      <if test="avgValidLiveUvSprintValue != null" >
        avg_valid_live_uv_sprint_value = #{avgValidLiveUvSprintValue,jdbcType=DOUBLE},
      </if>
      <if test="avgValidLiveUvDiff != null" >
        avg_valid_live_uv_diff = #{avgValidLiveUvDiff,jdbcType=DOUBLE},
      </if>
      <if test="anchorTotalScore != null" >
        anchor_total_score = #{anchorTotalScore,jdbcType=DOUBLE},
      </if>
      <if test="anchorTotalScoreSprintValue != null" >
        anchor_total_score_sprint_value = #{anchorTotalScoreSprintValue,jdbcType=DOUBLE},
      </if>
      <if test="anchorTotalScoreDiff != null" >
        anchor_total_score_diff = #{anchorTotalScoreDiff,jdbcType=DOUBLE},
      </if>
      <if test="breakUvRate != null" >
        break_uv_rate = #{breakUvRate,jdbcType=DOUBLE},
      </if>
      <if test="breakUvRateSprintValue != null" >
        break_uv_rate_sprint_value = #{breakUvRateSprintValue,jdbcType=DOUBLE},
      </if>
      <if test="breakUvRateDiff != null" >
        break_uv_rate_diff = #{breakUvRateDiff,jdbcType=DOUBLE},
      </if>
      <if test="dt != null" >
        dt = #{dt,jdbcType=VARCHAR},
      </if>
      <if test="aidValueScore != null" >
        aid_value_score = #{aidValueScore,jdbcType=DOUBLE},
      </if>
      <if test="aidValueScoreSprintValue != null" >
        aid_value_score_sprint_value = #{aidValueScoreSprintValue,jdbcType=DOUBLE},
      </if>
      <if test="aidValueScoreDiff != null" >
        aid_value_score_diff = #{aidValueScoreDiff,jdbcType=DOUBLE},
      </if>
      <if test="isMarsGuild != null" >
        is_mars_guild = #{isMarsGuild,jdbcType=INTEGER},
      </if>
      <if test="marsSidOwnerid != null" >
        mars_sid_ownerid = #{marsSidOwnerid,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentOriginStarDay" >
    update dm_yy_sid_segment_origin_star_day
    set sid_ownerid = #{sidOwnerid,jdbcType=VARCHAR},
      sid_owyyid = #{sidOwyyid,jdbcType=VARCHAR},
      month_diamond = #{monthDiamond,jdbcType=DOUBLE},
      month_diamond_sprint_value = #{monthDiamondSprintValue,jdbcType=DOUBLE},
      month_diamond_diff = #{monthDiamondDiff,jdbcType=DOUBLE},
      avg_valid_live_uv = #{avgValidLiveUv,jdbcType=DOUBLE},
      avg_valid_live_uv_sprint_value = #{avgValidLiveUvSprintValue,jdbcType=DOUBLE},
      avg_valid_live_uv_diff = #{avgValidLiveUvDiff,jdbcType=DOUBLE},
      anchor_total_score = #{anchorTotalScore,jdbcType=DOUBLE},
      anchor_total_score_sprint_value = #{anchorTotalScoreSprintValue,jdbcType=DOUBLE},
      anchor_total_score_diff = #{anchorTotalScoreDiff,jdbcType=DOUBLE},
      break_uv_rate = #{breakUvRate,jdbcType=DOUBLE},
      break_uv_rate_sprint_value = #{breakUvRateSprintValue,jdbcType=DOUBLE},
      break_uv_rate_diff = #{breakUvRateDiff,jdbcType=DOUBLE},
      dt = #{dt,jdbcType=VARCHAR},
      aid_value_score = #{aidValueScore,jdbcType=DOUBLE},
      aid_value_score_sprint_value = #{aidValueScoreSprintValue,jdbcType=DOUBLE},
      aid_value_score_diff = #{aidValueScoreDiff,jdbcType=DOUBLE},
      is_mars_guild = #{isMarsGuild,jdbcType=INTEGER},
      mars_sid_ownerid = #{marsSidOwnerid,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>