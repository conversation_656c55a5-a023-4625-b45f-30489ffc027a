package com.yy.yyzone.guildrank.db.gen.model;

import java.util.Date;

public class GuildrankPointDetail {
    /**
     * id
     */
    private Long id;

    /**
     * uid
     */
    private Long uid;

    /**
     * 积分
     */
    private Integer point;

    /**
     * 类型 0每月段位更新
     */
    private Integer type;

    /**
     * 类型下唯一号
     */
    private String typeSerial;

    /**
     * 状态 0待处理 1已处理 2处理中 3处理失败
     */
    private Integer status;

    /**
     * 积分过期日期
     */
    private Date dueDt;

    /**
     * 备注
     */
    private String remark;

    /**
     * 数据日期
     */
    private Date dt;

    /**
     * 发放时间
     */
    private Date grantTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 获取id
     * @return id id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置id
     * @param id id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取uid
     * @return uid uid
     */
    public Long getUid() {
        return uid;
    }

    /**
     * 设置uid
     * @param uid uid
     */
    public void setUid(Long uid) {
        this.uid = uid;
    }

    /**
     * 获取积分
     * @return point 积分
     */
    public Integer getPoint() {
        return point;
    }

    /**
     * 设置积分
     * @param point 积分
     */
    public void setPoint(Integer point) {
        this.point = point;
    }

    /**
     * 获取类型 0每月段位更新
     * @return type 类型 0每月段位更新
     */
    public Integer getType() {
        return type;
    }

    /**
     * 设置类型 0每月段位更新
     * @param type 类型 0每月段位更新
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * 获取类型下唯一号
     * @return type_serial 类型下唯一号
     */
    public String getTypeSerial() {
        return typeSerial;
    }

    /**
     * 设置类型下唯一号
     * @param typeSerial 类型下唯一号
     */
    public void setTypeSerial(String typeSerial) {
        this.typeSerial = typeSerial;
    }

    /**
     * 获取状态 0待处理 1已处理 2处理中 3处理失败
     * @return status 状态 0待处理 1已处理 2处理中 3处理失败
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 设置状态 0待处理 1已处理 2处理中 3处理失败
     * @param status 状态 0待处理 1已处理 2处理中 3处理失败
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 获取积分过期日期
     * @return due_dt 积分过期日期
     */
    public Date getDueDt() {
        return dueDt;
    }

    /**
     * 设置积分过期日期
     * @param dueDt 积分过期日期
     */
    public void setDueDt(Date dueDt) {
        this.dueDt = dueDt;
    }

    /**
     * 获取备注
     * @return remark 备注
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 设置备注
     * @param remark 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 获取数据日期
     * @return dt 数据日期
     */
    public Date getDt() {
        return dt;
    }

    /**
     * 设置数据日期
     * @param dt 数据日期
     */
    public void setDt(Date dt) {
        this.dt = dt;
    }

    /**
     * 获取发放时间
     * @return grant_time 发放时间
     */
    public Date getGrantTime() {
        return grantTime;
    }

    /**
     * 设置发放时间
     * @param grantTime 发放时间
     */
    public void setGrantTime(Date grantTime) {
        this.grantTime = grantTime;
    }

    /**
     * 获取创建时间
     * @return create_time 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     * @return update_time 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", uid=").append(uid);
        sb.append(", point=").append(point);
        sb.append(", type=").append(type);
        sb.append(", typeSerial=").append(typeSerial);
        sb.append(", status=").append(status);
        sb.append(", dueDt=").append(dueDt);
        sb.append(", remark=").append(remark);
        sb.append(", dt=").append(dt);
        sb.append(", grantTime=").append(grantTime);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }
}