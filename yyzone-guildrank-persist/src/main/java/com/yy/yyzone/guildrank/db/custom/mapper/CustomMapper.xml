<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yy.yyzone.guildrank.db.custom.mapper.CustomMapper">
    <select id="getMaxinsertNum" resultType="int">
    select max(insertNum) from guildrank_temporary_chat
  </select>


    <insert id="batchAddTemporaryChat" parameterType="java.util.List">
        INSERT INTO guildrank_temporary_chat
        (serviceYY,guildUid,createTime,updateTime,addBy,status,insertNum,bizType,serviceNick)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.serviceyy},#{item.guilduid},#{item.createtime},#{item.updatetime},#{item.addby},#{item.status},#{item.insertnum},#{item.biztype},#{item.servicenick})
        </foreach>
    </insert>


    <resultMap id="BaseResultMap" type="com.yy.yyzone.guildrank.db.gen.model.GuildrankTemporaryChat">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="serviceYY" property="serviceyy" jdbcType="BIGINT"/>
        <result column="guildUid" property="guilduid" jdbcType="BIGINT"/>
        <result column="createTime" property="createtime" jdbcType="TIMESTAMP"/>
        <result column="updateTime" property="updatetime" jdbcType="TIMESTAMP"/>
        <result column="addBy" property="addby" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="insertNum" property="insertnum" jdbcType="INTEGER"/>
        <result column="bizType" property="biztype" jdbcType="INTEGER"/>
        <result column="serviceNick" property="servicenick" jdbcType="VARCHAR"/>
    </resultMap>
    <select id="listTemporaryChat" resultMap="BaseResultMap">
      select id, serviceYY, guildUid, createTime, updateTime, addBy, status, insertNum, bizType,serviceNick from guildrank_temporary_chat GROUP BY (insertNum) ORDER BY status,createTime DESC limit #{offset}, #{limit}
  </select>

    <insert id="insertBatchRenewArtistCount">
        insert into guildrank_renew_artist_count(data_month, main_guild_uid, renew_count)
        values
        <foreach collection="list" item="i" separator=",">
            (#{i.dataMonth},#{i.mainGuildUid},#{i.renewCount})
        </foreach>
    </insert>
</mapper>