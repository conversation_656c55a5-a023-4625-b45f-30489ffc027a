<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yy.yyzone.guildrank.db.gen.mapper.GuildRankPointOpsManualMapper" >
  <resultMap id="BaseResultMap" type="com.yy.yyzone.guildrank.db.gen.model.GuildRankPointOpsManual" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="target_month" property="targetMonth" jdbcType="TIMESTAMP" />
    <result column="entity_uid" property="entityUid" jdbcType="BIGINT" />
    <result column="modify_type" property="modifyType" jdbcType="TINYINT" />
    <result column="modify_num" property="modifyNum" jdbcType="INTEGER" />
    <result column="modify_reason" property="modifyReason" jdbcType="VARCHAR" />
    <result column="passport" property="passport" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, target_month, entity_uid, modify_type, modify_num, modify_reason, passport, create_time, 
    update_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildRankPointOpsManualExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from guild_rank_point_ops_manual
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit > 0" >
      limit ${limit}
    </if>
    <if test="offset > 0" >
      offset ${offset}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from guild_rank_point_ops_manual
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from guild_rank_point_ops_manual
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildRankPointOpsManualExample" >
    delete from guild_rank_point_ops_manual
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildRankPointOpsManual" useGeneratedKeys="true" keyProperty="id" >
    insert into guild_rank_point_ops_manual (target_month, entity_uid, modify_type, 
      modify_num, modify_reason, passport, 
      create_time, update_time)
    values (#{targetMonth,jdbcType=TIMESTAMP}, #{entityUid,jdbcType=BIGINT}, #{modifyType,jdbcType=TINYINT}, 
      #{modifyNum,jdbcType=INTEGER}, #{modifyReason,jdbcType=VARCHAR}, #{passport,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildRankPointOpsManual" useGeneratedKeys="true" keyProperty="id" >
    insert into guild_rank_point_ops_manual
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="targetMonth != null" >
        target_month,
      </if>
      <if test="entityUid != null" >
        entity_uid,
      </if>
      <if test="modifyType != null" >
        modify_type,
      </if>
      <if test="modifyNum != null" >
        modify_num,
      </if>
      <if test="modifyReason != null" >
        modify_reason,
      </if>
      <if test="passport != null" >
        passport,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="targetMonth != null" >
        #{targetMonth,jdbcType=TIMESTAMP},
      </if>
      <if test="entityUid != null" >
        #{entityUid,jdbcType=BIGINT},
      </if>
      <if test="modifyType != null" >
        #{modifyType,jdbcType=TINYINT},
      </if>
      <if test="modifyNum != null" >
        #{modifyNum,jdbcType=INTEGER},
      </if>
      <if test="modifyReason != null" >
        #{modifyReason,jdbcType=VARCHAR},
      </if>
      <if test="passport != null" >
        #{passport,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildRankPointOpsManualExample" resultType="java.lang.Integer" >
    select count(*) from guild_rank_point_ops_manual
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update guild_rank_point_ops_manual
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.targetMonth != null" >
        target_month = #{record.targetMonth,jdbcType=TIMESTAMP},
      </if>
      <if test="record.entityUid != null" >
        entity_uid = #{record.entityUid,jdbcType=BIGINT},
      </if>
      <if test="record.modifyType != null" >
        modify_type = #{record.modifyType,jdbcType=TINYINT},
      </if>
      <if test="record.modifyNum != null" >
        modify_num = #{record.modifyNum,jdbcType=INTEGER},
      </if>
      <if test="record.modifyReason != null" >
        modify_reason = #{record.modifyReason,jdbcType=VARCHAR},
      </if>
      <if test="record.passport != null" >
        passport = #{record.passport,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update guild_rank_point_ops_manual
    set id = #{record.id,jdbcType=BIGINT},
      target_month = #{record.targetMonth,jdbcType=TIMESTAMP},
      entity_uid = #{record.entityUid,jdbcType=BIGINT},
      modify_type = #{record.modifyType,jdbcType=TINYINT},
      modify_num = #{record.modifyNum,jdbcType=INTEGER},
      modify_reason = #{record.modifyReason,jdbcType=VARCHAR},
      passport = #{record.passport,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildRankPointOpsManual" >
    update guild_rank_point_ops_manual
    <set >
      <if test="targetMonth != null" >
        target_month = #{targetMonth,jdbcType=TIMESTAMP},
      </if>
      <if test="entityUid != null" >
        entity_uid = #{entityUid,jdbcType=BIGINT},
      </if>
      <if test="modifyType != null" >
        modify_type = #{modifyType,jdbcType=TINYINT},
      </if>
      <if test="modifyNum != null" >
        modify_num = #{modifyNum,jdbcType=INTEGER},
      </if>
      <if test="modifyReason != null" >
        modify_reason = #{modifyReason,jdbcType=VARCHAR},
      </if>
      <if test="passport != null" >
        passport = #{passport,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildRankPointOpsManual" >
    update guild_rank_point_ops_manual
    set target_month = #{targetMonth,jdbcType=TIMESTAMP},
      entity_uid = #{entityUid,jdbcType=BIGINT},
      modify_type = #{modifyType,jdbcType=TINYINT},
      modify_num = #{modifyNum,jdbcType=INTEGER},
      modify_reason = #{modifyReason,jdbcType=VARCHAR},
      passport = #{passport,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>