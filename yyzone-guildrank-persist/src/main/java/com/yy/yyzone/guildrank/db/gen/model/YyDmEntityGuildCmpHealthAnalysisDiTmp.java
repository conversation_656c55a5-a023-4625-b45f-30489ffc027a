package com.yy.yyzone.guildrank.db.gen.model;

import java.math.BigDecimal;
import java.util.Date;

public class YyDmEntityGuildCmpHealthAnalysisDiTmp {
    /**
     * id
     */
    private Long id;

    /**
     * 主体uid
     */
    private Long guildCmpOwnrId;

    /**
     * 3&4星主播签约数量
     */
    private Long newAidRat34AidNum;

    /**
     * 3&4星主播签约数量排名
     */
    private Long newAidRat34AidNumRn;

    /**
     * 3&4星主播签约数量diff
     */
    private Long newAidRat34AidNumDiff;

    /**
     * 头部主播数
     */
    private Long upgrdHighAidNum;

    /**
     * 头部主播数diff
     */
    private Long upgrdHighAidNumDiff;

    /**
     * 腰部主播数
     */
    private Long upgrdWaistAidNum;

    /**
     * 腰部主播数diff
     */
    private Long upgrdWaistAidNumDiff;

    /**
     * 月日均头部有效开播主播数
     */
    private Double avgHighValidLiveAidNum;

    /**
     * 月日均头部有效开播主播数排名
     */
    private Long avgHighValidLiveAidNumRn;

    /**
     * 月日均头部有效开播主播数diff
     */
    private Double avgHighValidLiveAidNumDiff;

    /**
     * 月日均腰部有效开播主播数
     */
    private Double avgWaistValidLiveAidNum;

    /**
     * 月日均腰部有效开播主播数排名
     */
    private Long avgWaistValidLiveAidNumRn;

    /**
     * 月日均腰部有效开播主播数diff
     */
    private Double avgWaistValidLiveAidNumDiff;

    /**
     * 新转存开播率
     */
    private Double avgNtoValidLiveAidRate;

    /**
     * 新转存开播率排名
     */
    private Long avgNtoValidLiveAidRateRn;

    /**
     * 存量主播开播率
     */
    private Double avgStockValidLiveAidRate;

    /**
     * 存量主播开播率排名
     */
    private Long avgStockValidLiveAidRateRn;

    /**
     * 新授权金牌艺人
     */
    private Long newAuthGoldenAidNum;

    /**
     * 新授权金牌艺人排名
     */
    private Long newAuthGoldenAidNumRn;

    /**
     * 续约重点金牌艺人数（续约1-6级金牌艺人）
     */
    private Long extGoldenAidNum;

    /**
     * 月累积活动礼物流水
     */
    private Double actvProdPayAmt;

    /**
     * 月累积流水
     */
    private Double prodPayAmt;

    /**
     * 活动礼物流水占比总流水比例
     */
    private Double actvProdPayAmtRate;

    /**
     * 新主播流水
     */
    private Double validLiveNewAidProdPayAmt;

    /**
     * 新主播流水排名
     */
    private Long validLiveNewAidProdPayAmtRn;

    /**
     * 新主播流水diff
     */
    private Double validLiveNewAidProdPayAmtDiff;

    /**
     * 存量主播流水
     */
    private Double unvalidLiveNewAidProdPayAmt;

    /**
     * 存量主播流水排名
     */
    private Long unvalidLiveNewAidProdPayAmtRn;

    /**
     * 存量主播流水diff
     */
    private Double unvalidLiveNewAidProdPayAmtDiff;

    /**
     * 存量主播流水占比
     */
    private Double unvalidLiveNewAidProdPayAmtRate;

    /**
     * 大盘存量流水占比
     */
    private Double totalUnvalidLiveNewAidProdPayAmtRate;

    /**
     * 存量主播流水占比对比大盘存量流水占比差值
     */
    private Double unvalidLiveNewAidProdPayAmtRateDiff;

    /**
     * 月累积活动蓝钻收入
     */
    private Double actvIncmAmt;

    /**
     * 月累积蓝钻收入
     */
    private Double incmAmt;

    /**
     * 活动蓝钻收入占比总蓝钻收入比例
     */
    private Double actvIncmAmtRate;

    /**
     * 新主播蓝钻
     */
    private Double validLiveNewAidIncmAmt;

    /**
     * 新主播蓝钻排名
     */
    private Long validLiveNewAidIncmAmtRn;

    /**
     * 新主播蓝钻diff
     */
    private Double validLiveNewAidIncmAmtDiff;

    /**
     * 存量主播蓝钻
     */
    private Double unvalidLiveNewAidIncmAmt;

    /**
     * 存量主播蓝钻排名
     */
    private Long unvalidLiveNewAidIncmAmtRn;

    /**
     * 存量主播蓝钻diff
     */
    private Double unvalidLiveNewAidIncmAmtDiff;

    /**
     * 存量主播蓝钻占比
     */
    private Double unvalidLiveNewAidIncmAmtRate;

    /**
     * 大盘存量蓝钻占比
     */
    private Double totalUnvalidLiveNewAidIncmAmtRate;

    /**
     * 存量主播蓝钻占比对比大盘存量蓝钻占比差值
     */
    private Double unvalidLiveNewAidIncmAmtRateDiff;

    /**
     * 公会健康分
     */
    private Double guildHealthPoint;

    /**
     * 3&4星主播签约数量排名分值
     */
    private Integer newAidRat34AidNumRnScore;

    /**
     * 3&4星主播签约数量diff分值
     */
    private Integer newAidRat34AidNumDiffScore;

    /**
     * 头部主播数diff分值
     */
    private Integer upgrdHighAidNumDiffScore;

    /**
     * 腰部主播数diff分值
     */
    private Integer upgrdWaistAidNumDiffScore;

    /**
     * 月日均头部有效开播主播数排名分值
     */
    private Integer avgHighValidLiveAidNumRnScore;

    /**
     * 月日均腰部有效开播主播数排名分值
     */
    private Integer avgWaistValidLiveAidNumRnScore;

    /**
     * 月日均头部有效开播主播数diff分值
     */
    private Integer avgHighValidLiveAidNumDiffScore;

    /**
     * 月日均腰部有效开播主播数diff分值
     */
    private Integer avgWaistValidLiveAidNumDiffScore;

    /**
     * 新转存开播率排名分值
     */
    private Integer avgNtoValidLiveAidRateRnScore;

    /**
     * 存量主播开播率排名分值
     */
    private Integer avgStockValidLiveAidRateRnScore;

    /**
     * 新授权金牌艺人排名分值
     */
    private Integer newAuthGoldenAidNumRnScore;

    /**
     * 新主播流水排名分值
     */
    private Integer validLiveNewAidProdPayAmtRnScore;

    /**
     * 存量主播流水排名分值
     */
    private Integer unvalidLiveNewAidProdPayAmtRnScore;

    /**
     * 新主播流水diff分值
     */
    private Integer validLiveNewAidProdPayAmtDiffScore;

    /**
     * 存量主播流水diff分值
     */
    private Integer unvalidLiveNewAidProdPayAmtDiffScore;

    /**
     * 存量主播流水占比对比大盘存量流水占比差值分值
     */
    private Integer unvalidLiveNewAidProdPayAmtRateDiffScore;

    /**
     * 新主播蓝钻排名分值
     */
    private Integer validLiveNewAidIncmAmtRnScore;

    /**
     * 存量主播蓝钻排名分值
     */
    private Integer unvalidLiveNewAidIncmAmtRnScore;

    /**
     * 新主播蓝钻diff分值
     */
    private Integer validLiveNewAidIncmAmtDiffScore;

    /**
     * 存量主播蓝钻diff分值
     */
    private Integer unvalidLiveNewAidIncmAmtDiffScore;

    /**
     * 存量主播蓝钻占比对比大盘存量蓝钻占比差值分值
     */
    private Integer unvalidLiveNewAidIncmAmtRateDiffScore;

    /**
     * 公会健康分分值
     */
    private Integer guildHealthPointScore;

    /**
     * 招募(15分)
     */
    private Integer recruScore;

    /**
     * 孵化(30分)
     */
    private Integer hatchScore;

    /**
     * 留存(20分)
     */
    private Integer retScore;

    /**
     * 营收(30分)
     */
    private Integer rvnuScore;

    /**
     * 综合(5分)
     */
    private Integer comScore;

    /**
     * 数据指标总得分
     */
    private Integer dataTotalScore;

    /**
     * 数据指标主体星级	
     */
    private Integer dataCmpStarLvl;

    /**
     * 主体星级
     */
    private Integer cmpStarLvl;

    /**
     * 新签3&4星主播数得分
     */
    private Integer newAidRat34AidNumScore;

    /**
     * 新增头部主播数得分
     */
    private Integer upgrdHighAidNumScore;

    /**
     * 新增腰部主播数得分
     */
    private Integer upgrdWaistAidNumScore;

    /**
     * 续约重点金牌艺人（续约1-6级金牌艺人）得分
     */
    private Integer extGoldenAidNumScore;

    /**
     * 礼物流水占比大于70%得分
     */
    private Integer actvProdPayAmtRateScore;

    /**
     * 活动蓝钻收入占比大于70%得分
     */
    private Integer actvIncmAmtRateScore;

    /**
     * 任务指标总得分
     */
    private Integer taskTotalScore;

    /**
     * 新签3&4星主播数积分
     */
    private Integer newAidRat34AidNumItg;

    /**
     * 新增头部主播数积分
     */
    private Integer upgrdHighAidNumItg;

    /**
     * 新增腰部主播数积分
     */
    private Integer upgrdWaistAidNumItg;

    /**
     * 续约重点金牌艺人（续约1-6级金牌艺人）积分
     */
    private Integer extGoldenAidNumItg;

    /**
     * 礼物流水占比大于70%积分
     */
    private Integer actvProdPayAmtRateItg;

    /**
     * 活动蓝钻收入占比大于70%积分
     */
    private Integer actvIncmAmtRateItg;

    /**
     * 任务指标总积分
     */
    private Integer taskTotalItg;

    /**
     * 总得分
     */
    private Integer totalScore;

    /**
     * 日期
     */
    private Date dt;

    /**
     * 公会近3个自然月月均新签3&4星主播数，保留两位小数后四舍五入取整
     */
    private BigDecimal avgNewAidRat34AidNum3m;

    /**
     * 新签3星主播数，当月3星主播签约数量
     */
    private Long newAidRat3AidNum;

    /**
     * 新签4星主播数，当月4星主播签约数量
     */
    private Long newAidRat4AidNum;

    /**
     * 线上绑定3&4星主播任务要求
     */
    private Integer avgNewAidRat34AidNum3mAim;

    /**
     * 线上绑定3&4星主播任务得分
     */
    private Integer avgNewAidRat34AidNum3mScore;

    /**
     * 公会近3个自然月月均新增头&腰部主播数，保留两位小数后四舍五入取整
     */
    private BigDecimal avgUpgrdHighWaistAidNum3m;

    /**
     * 新增头&腰部主播任务要求
     */
    private Integer avgUpgrdHighWaistAidNum3mAim;

    /**
     * 新增头&腰部主播任务得分
     */
    private Integer avgUpgrdHighWaistAidNum3mScore;

    /**
     * 自然月新主播蓝钻任务完成率，保留整数
     */
    private BigDecimal newAidIncmComRate;

    /**
     * 新主播蓝钻任务完成率要求
     */
    private BigDecimal newAidIncmComRateAim;

    /**
     * 新主播蓝钻任务得分
     */
    private Integer newAidIncmComRateScore;

    /**
     * 公会蓝钻任务完成档位
     */
    private Integer guildTaskIncmLvl;

    /**
     * 公会蓝钻任务目标档位
     */
    private Integer guildTaskIncmLvlAim;

    /**
     * 公会蓝钻任务得分
     */
    private Integer guildTaskIncmLvlScore;

    /**
     * 自然月月累计蓝钻diff
     */
    private Long accumIncmAmtDiff;

    /**
     * 自然月月累计蓝钻任务要求
     */
    private Long accumIncmAmtAim;

    /**
     * 自然月月累计蓝钻任务得分
     */
    private Integer accumIncmAmtSocre;

    /**
     * 公司名
     */
    private String guildCmpName;

    /**
     * 获取id
     * @return id id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置id
     * @param id id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取主体uid
     * @return guild_cmp_ownr_id 主体uid
     */
    public Long getGuildCmpOwnrId() {
        return guildCmpOwnrId;
    }

    /**
     * 设置主体uid
     * @param guildCmpOwnrId 主体uid
     */
    public void setGuildCmpOwnrId(Long guildCmpOwnrId) {
        this.guildCmpOwnrId = guildCmpOwnrId;
    }

    /**
     * 获取3&4星主播签约数量
     * @return new_aid_rat_3_4_aid_num 3&4星主播签约数量
     */
    public Long getNewAidRat34AidNum() {
        return newAidRat34AidNum;
    }

    /**
     * 设置3&4星主播签约数量
     * @param newAidRat34AidNum 3&4星主播签约数量
     */
    public void setNewAidRat34AidNum(Long newAidRat34AidNum) {
        this.newAidRat34AidNum = newAidRat34AidNum;
    }

    /**
     * 获取3&4星主播签约数量排名
     * @return new_aid_rat_3_4_aid_num_rn 3&4星主播签约数量排名
     */
    public Long getNewAidRat34AidNumRn() {
        return newAidRat34AidNumRn;
    }

    /**
     * 设置3&4星主播签约数量排名
     * @param newAidRat34AidNumRn 3&4星主播签约数量排名
     */
    public void setNewAidRat34AidNumRn(Long newAidRat34AidNumRn) {
        this.newAidRat34AidNumRn = newAidRat34AidNumRn;
    }

    /**
     * 获取3&4星主播签约数量diff
     * @return new_aid_rat_3_4_aid_num_diff 3&4星主播签约数量diff
     */
    public Long getNewAidRat34AidNumDiff() {
        return newAidRat34AidNumDiff;
    }

    /**
     * 设置3&4星主播签约数量diff
     * @param newAidRat34AidNumDiff 3&4星主播签约数量diff
     */
    public void setNewAidRat34AidNumDiff(Long newAidRat34AidNumDiff) {
        this.newAidRat34AidNumDiff = newAidRat34AidNumDiff;
    }

    /**
     * 获取头部主播数
     * @return upgrd_high_aid_num 头部主播数
     */
    public Long getUpgrdHighAidNum() {
        return upgrdHighAidNum;
    }

    /**
     * 设置头部主播数
     * @param upgrdHighAidNum 头部主播数
     */
    public void setUpgrdHighAidNum(Long upgrdHighAidNum) {
        this.upgrdHighAidNum = upgrdHighAidNum;
    }

    /**
     * 获取头部主播数diff
     * @return upgrd_high_aid_num_diff 头部主播数diff
     */
    public Long getUpgrdHighAidNumDiff() {
        return upgrdHighAidNumDiff;
    }

    /**
     * 设置头部主播数diff
     * @param upgrdHighAidNumDiff 头部主播数diff
     */
    public void setUpgrdHighAidNumDiff(Long upgrdHighAidNumDiff) {
        this.upgrdHighAidNumDiff = upgrdHighAidNumDiff;
    }

    /**
     * 获取腰部主播数
     * @return upgrd_waist_aid_num 腰部主播数
     */
    public Long getUpgrdWaistAidNum() {
        return upgrdWaistAidNum;
    }

    /**
     * 设置腰部主播数
     * @param upgrdWaistAidNum 腰部主播数
     */
    public void setUpgrdWaistAidNum(Long upgrdWaistAidNum) {
        this.upgrdWaistAidNum = upgrdWaistAidNum;
    }

    /**
     * 获取腰部主播数diff
     * @return upgrd_waist_aid_num_diff 腰部主播数diff
     */
    public Long getUpgrdWaistAidNumDiff() {
        return upgrdWaistAidNumDiff;
    }

    /**
     * 设置腰部主播数diff
     * @param upgrdWaistAidNumDiff 腰部主播数diff
     */
    public void setUpgrdWaistAidNumDiff(Long upgrdWaistAidNumDiff) {
        this.upgrdWaistAidNumDiff = upgrdWaistAidNumDiff;
    }

    /**
     * 获取月日均头部有效开播主播数
     * @return avg_high_valid_live_aid_num 月日均头部有效开播主播数
     */
    public Double getAvgHighValidLiveAidNum() {
        return avgHighValidLiveAidNum;
    }

    /**
     * 设置月日均头部有效开播主播数
     * @param avgHighValidLiveAidNum 月日均头部有效开播主播数
     */
    public void setAvgHighValidLiveAidNum(Double avgHighValidLiveAidNum) {
        this.avgHighValidLiveAidNum = avgHighValidLiveAidNum;
    }

    /**
     * 获取月日均头部有效开播主播数排名
     * @return avg_high_valid_live_aid_num_rn 月日均头部有效开播主播数排名
     */
    public Long getAvgHighValidLiveAidNumRn() {
        return avgHighValidLiveAidNumRn;
    }

    /**
     * 设置月日均头部有效开播主播数排名
     * @param avgHighValidLiveAidNumRn 月日均头部有效开播主播数排名
     */
    public void setAvgHighValidLiveAidNumRn(Long avgHighValidLiveAidNumRn) {
        this.avgHighValidLiveAidNumRn = avgHighValidLiveAidNumRn;
    }

    /**
     * 获取月日均头部有效开播主播数diff
     * @return avg_high_valid_live_aid_num_diff 月日均头部有效开播主播数diff
     */
    public Double getAvgHighValidLiveAidNumDiff() {
        return avgHighValidLiveAidNumDiff;
    }

    /**
     * 设置月日均头部有效开播主播数diff
     * @param avgHighValidLiveAidNumDiff 月日均头部有效开播主播数diff
     */
    public void setAvgHighValidLiveAidNumDiff(Double avgHighValidLiveAidNumDiff) {
        this.avgHighValidLiveAidNumDiff = avgHighValidLiveAidNumDiff;
    }

    /**
     * 获取月日均腰部有效开播主播数
     * @return avg_waist_valid_live_aid_num 月日均腰部有效开播主播数
     */
    public Double getAvgWaistValidLiveAidNum() {
        return avgWaistValidLiveAidNum;
    }

    /**
     * 设置月日均腰部有效开播主播数
     * @param avgWaistValidLiveAidNum 月日均腰部有效开播主播数
     */
    public void setAvgWaistValidLiveAidNum(Double avgWaistValidLiveAidNum) {
        this.avgWaistValidLiveAidNum = avgWaistValidLiveAidNum;
    }

    /**
     * 获取月日均腰部有效开播主播数排名
     * @return avg_waist_valid_live_aid_num_rn 月日均腰部有效开播主播数排名
     */
    public Long getAvgWaistValidLiveAidNumRn() {
        return avgWaistValidLiveAidNumRn;
    }

    /**
     * 设置月日均腰部有效开播主播数排名
     * @param avgWaistValidLiveAidNumRn 月日均腰部有效开播主播数排名
     */
    public void setAvgWaistValidLiveAidNumRn(Long avgWaistValidLiveAidNumRn) {
        this.avgWaistValidLiveAidNumRn = avgWaistValidLiveAidNumRn;
    }

    /**
     * 获取月日均腰部有效开播主播数diff
     * @return avg_waist_valid_live_aid_num_diff 月日均腰部有效开播主播数diff
     */
    public Double getAvgWaistValidLiveAidNumDiff() {
        return avgWaistValidLiveAidNumDiff;
    }

    /**
     * 设置月日均腰部有效开播主播数diff
     * @param avgWaistValidLiveAidNumDiff 月日均腰部有效开播主播数diff
     */
    public void setAvgWaistValidLiveAidNumDiff(Double avgWaistValidLiveAidNumDiff) {
        this.avgWaistValidLiveAidNumDiff = avgWaistValidLiveAidNumDiff;
    }

    /**
     * 获取新转存开播率
     * @return avg_nto_valid_live_aid_rate 新转存开播率
     */
    public Double getAvgNtoValidLiveAidRate() {
        return avgNtoValidLiveAidRate;
    }

    /**
     * 设置新转存开播率
     * @param avgNtoValidLiveAidRate 新转存开播率
     */
    public void setAvgNtoValidLiveAidRate(Double avgNtoValidLiveAidRate) {
        this.avgNtoValidLiveAidRate = avgNtoValidLiveAidRate;
    }

    /**
     * 获取新转存开播率排名
     * @return avg_nto_valid_live_aid_rate_rn 新转存开播率排名
     */
    public Long getAvgNtoValidLiveAidRateRn() {
        return avgNtoValidLiveAidRateRn;
    }

    /**
     * 设置新转存开播率排名
     * @param avgNtoValidLiveAidRateRn 新转存开播率排名
     */
    public void setAvgNtoValidLiveAidRateRn(Long avgNtoValidLiveAidRateRn) {
        this.avgNtoValidLiveAidRateRn = avgNtoValidLiveAidRateRn;
    }

    /**
     * 获取存量主播开播率
     * @return avg_stock_valid_live_aid_rate 存量主播开播率
     */
    public Double getAvgStockValidLiveAidRate() {
        return avgStockValidLiveAidRate;
    }

    /**
     * 设置存量主播开播率
     * @param avgStockValidLiveAidRate 存量主播开播率
     */
    public void setAvgStockValidLiveAidRate(Double avgStockValidLiveAidRate) {
        this.avgStockValidLiveAidRate = avgStockValidLiveAidRate;
    }

    /**
     * 获取存量主播开播率排名
     * @return avg_stock_valid_live_aid_rate_rn 存量主播开播率排名
     */
    public Long getAvgStockValidLiveAidRateRn() {
        return avgStockValidLiveAidRateRn;
    }

    /**
     * 设置存量主播开播率排名
     * @param avgStockValidLiveAidRateRn 存量主播开播率排名
     */
    public void setAvgStockValidLiveAidRateRn(Long avgStockValidLiveAidRateRn) {
        this.avgStockValidLiveAidRateRn = avgStockValidLiveAidRateRn;
    }

    /**
     * 获取新授权金牌艺人
     * @return new_auth_golden_aid_num 新授权金牌艺人
     */
    public Long getNewAuthGoldenAidNum() {
        return newAuthGoldenAidNum;
    }

    /**
     * 设置新授权金牌艺人
     * @param newAuthGoldenAidNum 新授权金牌艺人
     */
    public void setNewAuthGoldenAidNum(Long newAuthGoldenAidNum) {
        this.newAuthGoldenAidNum = newAuthGoldenAidNum;
    }

    /**
     * 获取新授权金牌艺人排名
     * @return new_auth_golden_aid_num_rn 新授权金牌艺人排名
     */
    public Long getNewAuthGoldenAidNumRn() {
        return newAuthGoldenAidNumRn;
    }

    /**
     * 设置新授权金牌艺人排名
     * @param newAuthGoldenAidNumRn 新授权金牌艺人排名
     */
    public void setNewAuthGoldenAidNumRn(Long newAuthGoldenAidNumRn) {
        this.newAuthGoldenAidNumRn = newAuthGoldenAidNumRn;
    }

    /**
     * 获取续约重点金牌艺人数（续约1-6级金牌艺人）
     * @return ext_golden_aid_num 续约重点金牌艺人数（续约1-6级金牌艺人）
     */
    public Long getExtGoldenAidNum() {
        return extGoldenAidNum;
    }

    /**
     * 设置续约重点金牌艺人数（续约1-6级金牌艺人）
     * @param extGoldenAidNum 续约重点金牌艺人数（续约1-6级金牌艺人）
     */
    public void setExtGoldenAidNum(Long extGoldenAidNum) {
        this.extGoldenAidNum = extGoldenAidNum;
    }

    /**
     * 获取月累积活动礼物流水
     * @return actv_prod_pay_amt 月累积活动礼物流水
     */
    public Double getActvProdPayAmt() {
        return actvProdPayAmt;
    }

    /**
     * 设置月累积活动礼物流水
     * @param actvProdPayAmt 月累积活动礼物流水
     */
    public void setActvProdPayAmt(Double actvProdPayAmt) {
        this.actvProdPayAmt = actvProdPayAmt;
    }

    /**
     * 获取月累积流水
     * @return prod_pay_amt 月累积流水
     */
    public Double getProdPayAmt() {
        return prodPayAmt;
    }

    /**
     * 设置月累积流水
     * @param prodPayAmt 月累积流水
     */
    public void setProdPayAmt(Double prodPayAmt) {
        this.prodPayAmt = prodPayAmt;
    }

    /**
     * 获取活动礼物流水占比总流水比例
     * @return actv_prod_pay_amt_rate 活动礼物流水占比总流水比例
     */
    public Double getActvProdPayAmtRate() {
        return actvProdPayAmtRate;
    }

    /**
     * 设置活动礼物流水占比总流水比例
     * @param actvProdPayAmtRate 活动礼物流水占比总流水比例
     */
    public void setActvProdPayAmtRate(Double actvProdPayAmtRate) {
        this.actvProdPayAmtRate = actvProdPayAmtRate;
    }

    /**
     * 获取新主播流水
     * @return valid_live_new_aid_prod_pay_amt 新主播流水
     */
    public Double getValidLiveNewAidProdPayAmt() {
        return validLiveNewAidProdPayAmt;
    }

    /**
     * 设置新主播流水
     * @param validLiveNewAidProdPayAmt 新主播流水
     */
    public void setValidLiveNewAidProdPayAmt(Double validLiveNewAidProdPayAmt) {
        this.validLiveNewAidProdPayAmt = validLiveNewAidProdPayAmt;
    }

    /**
     * 获取新主播流水排名
     * @return valid_live_new_aid_prod_pay_amt_rn 新主播流水排名
     */
    public Long getValidLiveNewAidProdPayAmtRn() {
        return validLiveNewAidProdPayAmtRn;
    }

    /**
     * 设置新主播流水排名
     * @param validLiveNewAidProdPayAmtRn 新主播流水排名
     */
    public void setValidLiveNewAidProdPayAmtRn(Long validLiveNewAidProdPayAmtRn) {
        this.validLiveNewAidProdPayAmtRn = validLiveNewAidProdPayAmtRn;
    }

    /**
     * 获取新主播流水diff
     * @return valid_live_new_aid_prod_pay_amt_diff 新主播流水diff
     */
    public Double getValidLiveNewAidProdPayAmtDiff() {
        return validLiveNewAidProdPayAmtDiff;
    }

    /**
     * 设置新主播流水diff
     * @param validLiveNewAidProdPayAmtDiff 新主播流水diff
     */
    public void setValidLiveNewAidProdPayAmtDiff(Double validLiveNewAidProdPayAmtDiff) {
        this.validLiveNewAidProdPayAmtDiff = validLiveNewAidProdPayAmtDiff;
    }

    /**
     * 获取存量主播流水
     * @return unvalid_live_new_aid_prod_pay_amt 存量主播流水
     */
    public Double getUnvalidLiveNewAidProdPayAmt() {
        return unvalidLiveNewAidProdPayAmt;
    }

    /**
     * 设置存量主播流水
     * @param unvalidLiveNewAidProdPayAmt 存量主播流水
     */
    public void setUnvalidLiveNewAidProdPayAmt(Double unvalidLiveNewAidProdPayAmt) {
        this.unvalidLiveNewAidProdPayAmt = unvalidLiveNewAidProdPayAmt;
    }

    /**
     * 获取存量主播流水排名
     * @return unvalid_live_new_aid_prod_pay_amt_rn 存量主播流水排名
     */
    public Long getUnvalidLiveNewAidProdPayAmtRn() {
        return unvalidLiveNewAidProdPayAmtRn;
    }

    /**
     * 设置存量主播流水排名
     * @param unvalidLiveNewAidProdPayAmtRn 存量主播流水排名
     */
    public void setUnvalidLiveNewAidProdPayAmtRn(Long unvalidLiveNewAidProdPayAmtRn) {
        this.unvalidLiveNewAidProdPayAmtRn = unvalidLiveNewAidProdPayAmtRn;
    }

    /**
     * 获取存量主播流水diff
     * @return unvalid_live_new_aid_prod_pay_amt_diff 存量主播流水diff
     */
    public Double getUnvalidLiveNewAidProdPayAmtDiff() {
        return unvalidLiveNewAidProdPayAmtDiff;
    }

    /**
     * 设置存量主播流水diff
     * @param unvalidLiveNewAidProdPayAmtDiff 存量主播流水diff
     */
    public void setUnvalidLiveNewAidProdPayAmtDiff(Double unvalidLiveNewAidProdPayAmtDiff) {
        this.unvalidLiveNewAidProdPayAmtDiff = unvalidLiveNewAidProdPayAmtDiff;
    }

    /**
     * 获取存量主播流水占比
     * @return unvalid_live_new_aid_prod_pay_amt_rate 存量主播流水占比
     */
    public Double getUnvalidLiveNewAidProdPayAmtRate() {
        return unvalidLiveNewAidProdPayAmtRate;
    }

    /**
     * 设置存量主播流水占比
     * @param unvalidLiveNewAidProdPayAmtRate 存量主播流水占比
     */
    public void setUnvalidLiveNewAidProdPayAmtRate(Double unvalidLiveNewAidProdPayAmtRate) {
        this.unvalidLiveNewAidProdPayAmtRate = unvalidLiveNewAidProdPayAmtRate;
    }

    /**
     * 获取大盘存量流水占比
     * @return total_unvalid_live_new_aid_prod_pay_amt_rate 大盘存量流水占比
     */
    public Double getTotalUnvalidLiveNewAidProdPayAmtRate() {
        return totalUnvalidLiveNewAidProdPayAmtRate;
    }

    /**
     * 设置大盘存量流水占比
     * @param totalUnvalidLiveNewAidProdPayAmtRate 大盘存量流水占比
     */
    public void setTotalUnvalidLiveNewAidProdPayAmtRate(Double totalUnvalidLiveNewAidProdPayAmtRate) {
        this.totalUnvalidLiveNewAidProdPayAmtRate = totalUnvalidLiveNewAidProdPayAmtRate;
    }

    /**
     * 获取存量主播流水占比对比大盘存量流水占比差值
     * @return unvalid_live_new_aid_prod_pay_amt_rate_diff 存量主播流水占比对比大盘存量流水占比差值
     */
    public Double getUnvalidLiveNewAidProdPayAmtRateDiff() {
        return unvalidLiveNewAidProdPayAmtRateDiff;
    }

    /**
     * 设置存量主播流水占比对比大盘存量流水占比差值
     * @param unvalidLiveNewAidProdPayAmtRateDiff 存量主播流水占比对比大盘存量流水占比差值
     */
    public void setUnvalidLiveNewAidProdPayAmtRateDiff(Double unvalidLiveNewAidProdPayAmtRateDiff) {
        this.unvalidLiveNewAidProdPayAmtRateDiff = unvalidLiveNewAidProdPayAmtRateDiff;
    }

    /**
     * 获取月累积活动蓝钻收入
     * @return actv_incm_amt 月累积活动蓝钻收入
     */
    public Double getActvIncmAmt() {
        return actvIncmAmt;
    }

    /**
     * 设置月累积活动蓝钻收入
     * @param actvIncmAmt 月累积活动蓝钻收入
     */
    public void setActvIncmAmt(Double actvIncmAmt) {
        this.actvIncmAmt = actvIncmAmt;
    }

    /**
     * 获取月累积蓝钻收入
     * @return incm_amt 月累积蓝钻收入
     */
    public Double getIncmAmt() {
        return incmAmt;
    }

    /**
     * 设置月累积蓝钻收入
     * @param incmAmt 月累积蓝钻收入
     */
    public void setIncmAmt(Double incmAmt) {
        this.incmAmt = incmAmt;
    }

    /**
     * 获取活动蓝钻收入占比总蓝钻收入比例
     * @return actv_incm_amt_rate 活动蓝钻收入占比总蓝钻收入比例
     */
    public Double getActvIncmAmtRate() {
        return actvIncmAmtRate;
    }

    /**
     * 设置活动蓝钻收入占比总蓝钻收入比例
     * @param actvIncmAmtRate 活动蓝钻收入占比总蓝钻收入比例
     */
    public void setActvIncmAmtRate(Double actvIncmAmtRate) {
        this.actvIncmAmtRate = actvIncmAmtRate;
    }

    /**
     * 获取新主播蓝钻
     * @return valid_live_new_aid_incm_amt 新主播蓝钻
     */
    public Double getValidLiveNewAidIncmAmt() {
        return validLiveNewAidIncmAmt;
    }

    /**
     * 设置新主播蓝钻
     * @param validLiveNewAidIncmAmt 新主播蓝钻
     */
    public void setValidLiveNewAidIncmAmt(Double validLiveNewAidIncmAmt) {
        this.validLiveNewAidIncmAmt = validLiveNewAidIncmAmt;
    }

    /**
     * 获取新主播蓝钻排名
     * @return valid_live_new_aid_incm_amt_rn 新主播蓝钻排名
     */
    public Long getValidLiveNewAidIncmAmtRn() {
        return validLiveNewAidIncmAmtRn;
    }

    /**
     * 设置新主播蓝钻排名
     * @param validLiveNewAidIncmAmtRn 新主播蓝钻排名
     */
    public void setValidLiveNewAidIncmAmtRn(Long validLiveNewAidIncmAmtRn) {
        this.validLiveNewAidIncmAmtRn = validLiveNewAidIncmAmtRn;
    }

    /**
     * 获取新主播蓝钻diff
     * @return valid_live_new_aid_incm_amt_diff 新主播蓝钻diff
     */
    public Double getValidLiveNewAidIncmAmtDiff() {
        return validLiveNewAidIncmAmtDiff;
    }

    /**
     * 设置新主播蓝钻diff
     * @param validLiveNewAidIncmAmtDiff 新主播蓝钻diff
     */
    public void setValidLiveNewAidIncmAmtDiff(Double validLiveNewAidIncmAmtDiff) {
        this.validLiveNewAidIncmAmtDiff = validLiveNewAidIncmAmtDiff;
    }

    /**
     * 获取存量主播蓝钻
     * @return unvalid_live_new_aid_incm_amt 存量主播蓝钻
     */
    public Double getUnvalidLiveNewAidIncmAmt() {
        return unvalidLiveNewAidIncmAmt;
    }

    /**
     * 设置存量主播蓝钻
     * @param unvalidLiveNewAidIncmAmt 存量主播蓝钻
     */
    public void setUnvalidLiveNewAidIncmAmt(Double unvalidLiveNewAidIncmAmt) {
        this.unvalidLiveNewAidIncmAmt = unvalidLiveNewAidIncmAmt;
    }

    /**
     * 获取存量主播蓝钻排名
     * @return unvalid_live_new_aid_incm_amt_rn 存量主播蓝钻排名
     */
    public Long getUnvalidLiveNewAidIncmAmtRn() {
        return unvalidLiveNewAidIncmAmtRn;
    }

    /**
     * 设置存量主播蓝钻排名
     * @param unvalidLiveNewAidIncmAmtRn 存量主播蓝钻排名
     */
    public void setUnvalidLiveNewAidIncmAmtRn(Long unvalidLiveNewAidIncmAmtRn) {
        this.unvalidLiveNewAidIncmAmtRn = unvalidLiveNewAidIncmAmtRn;
    }

    /**
     * 获取存量主播蓝钻diff
     * @return unvalid_live_new_aid_incm_amt_diff 存量主播蓝钻diff
     */
    public Double getUnvalidLiveNewAidIncmAmtDiff() {
        return unvalidLiveNewAidIncmAmtDiff;
    }

    /**
     * 设置存量主播蓝钻diff
     * @param unvalidLiveNewAidIncmAmtDiff 存量主播蓝钻diff
     */
    public void setUnvalidLiveNewAidIncmAmtDiff(Double unvalidLiveNewAidIncmAmtDiff) {
        this.unvalidLiveNewAidIncmAmtDiff = unvalidLiveNewAidIncmAmtDiff;
    }

    /**
     * 获取存量主播蓝钻占比
     * @return unvalid_live_new_aid_incm_amt_rate 存量主播蓝钻占比
     */
    public Double getUnvalidLiveNewAidIncmAmtRate() {
        return unvalidLiveNewAidIncmAmtRate;
    }

    /**
     * 设置存量主播蓝钻占比
     * @param unvalidLiveNewAidIncmAmtRate 存量主播蓝钻占比
     */
    public void setUnvalidLiveNewAidIncmAmtRate(Double unvalidLiveNewAidIncmAmtRate) {
        this.unvalidLiveNewAidIncmAmtRate = unvalidLiveNewAidIncmAmtRate;
    }

    /**
     * 获取大盘存量蓝钻占比
     * @return total_unvalid_live_new_aid_incm_amt_rate 大盘存量蓝钻占比
     */
    public Double getTotalUnvalidLiveNewAidIncmAmtRate() {
        return totalUnvalidLiveNewAidIncmAmtRate;
    }

    /**
     * 设置大盘存量蓝钻占比
     * @param totalUnvalidLiveNewAidIncmAmtRate 大盘存量蓝钻占比
     */
    public void setTotalUnvalidLiveNewAidIncmAmtRate(Double totalUnvalidLiveNewAidIncmAmtRate) {
        this.totalUnvalidLiveNewAidIncmAmtRate = totalUnvalidLiveNewAidIncmAmtRate;
    }

    /**
     * 获取存量主播蓝钻占比对比大盘存量蓝钻占比差值
     * @return unvalid_live_new_aid_incm_amt_rate_diff 存量主播蓝钻占比对比大盘存量蓝钻占比差值
     */
    public Double getUnvalidLiveNewAidIncmAmtRateDiff() {
        return unvalidLiveNewAidIncmAmtRateDiff;
    }

    /**
     * 设置存量主播蓝钻占比对比大盘存量蓝钻占比差值
     * @param unvalidLiveNewAidIncmAmtRateDiff 存量主播蓝钻占比对比大盘存量蓝钻占比差值
     */
    public void setUnvalidLiveNewAidIncmAmtRateDiff(Double unvalidLiveNewAidIncmAmtRateDiff) {
        this.unvalidLiveNewAidIncmAmtRateDiff = unvalidLiveNewAidIncmAmtRateDiff;
    }

    /**
     * 获取公会健康分
     * @return guild_health_point 公会健康分
     */
    public Double getGuildHealthPoint() {
        return guildHealthPoint;
    }

    /**
     * 设置公会健康分
     * @param guildHealthPoint 公会健康分
     */
    public void setGuildHealthPoint(Double guildHealthPoint) {
        this.guildHealthPoint = guildHealthPoint;
    }

    /**
     * 获取3&4星主播签约数量排名分值
     * @return new_aid_rat_3_4_aid_num_rn_score 3&4星主播签约数量排名分值
     */
    public Integer getNewAidRat34AidNumRnScore() {
        return newAidRat34AidNumRnScore;
    }

    /**
     * 设置3&4星主播签约数量排名分值
     * @param newAidRat34AidNumRnScore 3&4星主播签约数量排名分值
     */
    public void setNewAidRat34AidNumRnScore(Integer newAidRat34AidNumRnScore) {
        this.newAidRat34AidNumRnScore = newAidRat34AidNumRnScore;
    }

    /**
     * 获取3&4星主播签约数量diff分值
     * @return new_aid_rat_3_4_aid_num_diff_score 3&4星主播签约数量diff分值
     */
    public Integer getNewAidRat34AidNumDiffScore() {
        return newAidRat34AidNumDiffScore;
    }

    /**
     * 设置3&4星主播签约数量diff分值
     * @param newAidRat34AidNumDiffScore 3&4星主播签约数量diff分值
     */
    public void setNewAidRat34AidNumDiffScore(Integer newAidRat34AidNumDiffScore) {
        this.newAidRat34AidNumDiffScore = newAidRat34AidNumDiffScore;
    }

    /**
     * 获取头部主播数diff分值
     * @return upgrd_high_aid_num_diff_score 头部主播数diff分值
     */
    public Integer getUpgrdHighAidNumDiffScore() {
        return upgrdHighAidNumDiffScore;
    }

    /**
     * 设置头部主播数diff分值
     * @param upgrdHighAidNumDiffScore 头部主播数diff分值
     */
    public void setUpgrdHighAidNumDiffScore(Integer upgrdHighAidNumDiffScore) {
        this.upgrdHighAidNumDiffScore = upgrdHighAidNumDiffScore;
    }

    /**
     * 获取腰部主播数diff分值
     * @return upgrd_waist_aid_num_diff_score 腰部主播数diff分值
     */
    public Integer getUpgrdWaistAidNumDiffScore() {
        return upgrdWaistAidNumDiffScore;
    }

    /**
     * 设置腰部主播数diff分值
     * @param upgrdWaistAidNumDiffScore 腰部主播数diff分值
     */
    public void setUpgrdWaistAidNumDiffScore(Integer upgrdWaistAidNumDiffScore) {
        this.upgrdWaistAidNumDiffScore = upgrdWaistAidNumDiffScore;
    }

    /**
     * 获取月日均头部有效开播主播数排名分值
     * @return avg_high_valid_live_aid_num_rn_score 月日均头部有效开播主播数排名分值
     */
    public Integer getAvgHighValidLiveAidNumRnScore() {
        return avgHighValidLiveAidNumRnScore;
    }

    /**
     * 设置月日均头部有效开播主播数排名分值
     * @param avgHighValidLiveAidNumRnScore 月日均头部有效开播主播数排名分值
     */
    public void setAvgHighValidLiveAidNumRnScore(Integer avgHighValidLiveAidNumRnScore) {
        this.avgHighValidLiveAidNumRnScore = avgHighValidLiveAidNumRnScore;
    }

    /**
     * 获取月日均腰部有效开播主播数排名分值
     * @return avg_waist_valid_live_aid_num_rn_score 月日均腰部有效开播主播数排名分值
     */
    public Integer getAvgWaistValidLiveAidNumRnScore() {
        return avgWaistValidLiveAidNumRnScore;
    }

    /**
     * 设置月日均腰部有效开播主播数排名分值
     * @param avgWaistValidLiveAidNumRnScore 月日均腰部有效开播主播数排名分值
     */
    public void setAvgWaistValidLiveAidNumRnScore(Integer avgWaistValidLiveAidNumRnScore) {
        this.avgWaistValidLiveAidNumRnScore = avgWaistValidLiveAidNumRnScore;
    }

    /**
     * 获取月日均头部有效开播主播数diff分值
     * @return avg_high_valid_live_aid_num_diff_score 月日均头部有效开播主播数diff分值
     */
    public Integer getAvgHighValidLiveAidNumDiffScore() {
        return avgHighValidLiveAidNumDiffScore;
    }

    /**
     * 设置月日均头部有效开播主播数diff分值
     * @param avgHighValidLiveAidNumDiffScore 月日均头部有效开播主播数diff分值
     */
    public void setAvgHighValidLiveAidNumDiffScore(Integer avgHighValidLiveAidNumDiffScore) {
        this.avgHighValidLiveAidNumDiffScore = avgHighValidLiveAidNumDiffScore;
    }

    /**
     * 获取月日均腰部有效开播主播数diff分值
     * @return avg_waist_valid_live_aid_num_diff_score 月日均腰部有效开播主播数diff分值
     */
    public Integer getAvgWaistValidLiveAidNumDiffScore() {
        return avgWaistValidLiveAidNumDiffScore;
    }

    /**
     * 设置月日均腰部有效开播主播数diff分值
     * @param avgWaistValidLiveAidNumDiffScore 月日均腰部有效开播主播数diff分值
     */
    public void setAvgWaistValidLiveAidNumDiffScore(Integer avgWaistValidLiveAidNumDiffScore) {
        this.avgWaistValidLiveAidNumDiffScore = avgWaistValidLiveAidNumDiffScore;
    }

    /**
     * 获取新转存开播率排名分值
     * @return avg_nto_valid_live_aid_rate_rn_score 新转存开播率排名分值
     */
    public Integer getAvgNtoValidLiveAidRateRnScore() {
        return avgNtoValidLiveAidRateRnScore;
    }

    /**
     * 设置新转存开播率排名分值
     * @param avgNtoValidLiveAidRateRnScore 新转存开播率排名分值
     */
    public void setAvgNtoValidLiveAidRateRnScore(Integer avgNtoValidLiveAidRateRnScore) {
        this.avgNtoValidLiveAidRateRnScore = avgNtoValidLiveAidRateRnScore;
    }

    /**
     * 获取存量主播开播率排名分值
     * @return avg_stock_valid_live_aid_rate_rn_score 存量主播开播率排名分值
     */
    public Integer getAvgStockValidLiveAidRateRnScore() {
        return avgStockValidLiveAidRateRnScore;
    }

    /**
     * 设置存量主播开播率排名分值
     * @param avgStockValidLiveAidRateRnScore 存量主播开播率排名分值
     */
    public void setAvgStockValidLiveAidRateRnScore(Integer avgStockValidLiveAidRateRnScore) {
        this.avgStockValidLiveAidRateRnScore = avgStockValidLiveAidRateRnScore;
    }

    /**
     * 获取新授权金牌艺人排名分值
     * @return new_auth_golden_aid_num_rn_score 新授权金牌艺人排名分值
     */
    public Integer getNewAuthGoldenAidNumRnScore() {
        return newAuthGoldenAidNumRnScore;
    }

    /**
     * 设置新授权金牌艺人排名分值
     * @param newAuthGoldenAidNumRnScore 新授权金牌艺人排名分值
     */
    public void setNewAuthGoldenAidNumRnScore(Integer newAuthGoldenAidNumRnScore) {
        this.newAuthGoldenAidNumRnScore = newAuthGoldenAidNumRnScore;
    }

    /**
     * 获取新主播流水排名分值
     * @return valid_live_new_aid_prod_pay_amt_rn_score 新主播流水排名分值
     */
    public Integer getValidLiveNewAidProdPayAmtRnScore() {
        return validLiveNewAidProdPayAmtRnScore;
    }

    /**
     * 设置新主播流水排名分值
     * @param validLiveNewAidProdPayAmtRnScore 新主播流水排名分值
     */
    public void setValidLiveNewAidProdPayAmtRnScore(Integer validLiveNewAidProdPayAmtRnScore) {
        this.validLiveNewAidProdPayAmtRnScore = validLiveNewAidProdPayAmtRnScore;
    }

    /**
     * 获取存量主播流水排名分值
     * @return unvalid_live_new_aid_prod_pay_amt_rn_score 存量主播流水排名分值
     */
    public Integer getUnvalidLiveNewAidProdPayAmtRnScore() {
        return unvalidLiveNewAidProdPayAmtRnScore;
    }

    /**
     * 设置存量主播流水排名分值
     * @param unvalidLiveNewAidProdPayAmtRnScore 存量主播流水排名分值
     */
    public void setUnvalidLiveNewAidProdPayAmtRnScore(Integer unvalidLiveNewAidProdPayAmtRnScore) {
        this.unvalidLiveNewAidProdPayAmtRnScore = unvalidLiveNewAidProdPayAmtRnScore;
    }

    /**
     * 获取新主播流水diff分值
     * @return valid_live_new_aid_prod_pay_amt_diff_score 新主播流水diff分值
     */
    public Integer getValidLiveNewAidProdPayAmtDiffScore() {
        return validLiveNewAidProdPayAmtDiffScore;
    }

    /**
     * 设置新主播流水diff分值
     * @param validLiveNewAidProdPayAmtDiffScore 新主播流水diff分值
     */
    public void setValidLiveNewAidProdPayAmtDiffScore(Integer validLiveNewAidProdPayAmtDiffScore) {
        this.validLiveNewAidProdPayAmtDiffScore = validLiveNewAidProdPayAmtDiffScore;
    }

    /**
     * 获取存量主播流水diff分值
     * @return unvalid_live_new_aid_prod_pay_amt_diff_score 存量主播流水diff分值
     */
    public Integer getUnvalidLiveNewAidProdPayAmtDiffScore() {
        return unvalidLiveNewAidProdPayAmtDiffScore;
    }

    /**
     * 设置存量主播流水diff分值
     * @param unvalidLiveNewAidProdPayAmtDiffScore 存量主播流水diff分值
     */
    public void setUnvalidLiveNewAidProdPayAmtDiffScore(Integer unvalidLiveNewAidProdPayAmtDiffScore) {
        this.unvalidLiveNewAidProdPayAmtDiffScore = unvalidLiveNewAidProdPayAmtDiffScore;
    }

    /**
     * 获取存量主播流水占比对比大盘存量流水占比差值分值
     * @return unvalid_live_new_aid_prod_pay_amt_rate_diff_score 存量主播流水占比对比大盘存量流水占比差值分值
     */
    public Integer getUnvalidLiveNewAidProdPayAmtRateDiffScore() {
        return unvalidLiveNewAidProdPayAmtRateDiffScore;
    }

    /**
     * 设置存量主播流水占比对比大盘存量流水占比差值分值
     * @param unvalidLiveNewAidProdPayAmtRateDiffScore 存量主播流水占比对比大盘存量流水占比差值分值
     */
    public void setUnvalidLiveNewAidProdPayAmtRateDiffScore(Integer unvalidLiveNewAidProdPayAmtRateDiffScore) {
        this.unvalidLiveNewAidProdPayAmtRateDiffScore = unvalidLiveNewAidProdPayAmtRateDiffScore;
    }

    /**
     * 获取新主播蓝钻排名分值
     * @return valid_live_new_aid_incm_amt_rn_score 新主播蓝钻排名分值
     */
    public Integer getValidLiveNewAidIncmAmtRnScore() {
        return validLiveNewAidIncmAmtRnScore;
    }

    /**
     * 设置新主播蓝钻排名分值
     * @param validLiveNewAidIncmAmtRnScore 新主播蓝钻排名分值
     */
    public void setValidLiveNewAidIncmAmtRnScore(Integer validLiveNewAidIncmAmtRnScore) {
        this.validLiveNewAidIncmAmtRnScore = validLiveNewAidIncmAmtRnScore;
    }

    /**
     * 获取存量主播蓝钻排名分值
     * @return unvalid_live_new_aid_incm_amt_rn_score 存量主播蓝钻排名分值
     */
    public Integer getUnvalidLiveNewAidIncmAmtRnScore() {
        return unvalidLiveNewAidIncmAmtRnScore;
    }

    /**
     * 设置存量主播蓝钻排名分值
     * @param unvalidLiveNewAidIncmAmtRnScore 存量主播蓝钻排名分值
     */
    public void setUnvalidLiveNewAidIncmAmtRnScore(Integer unvalidLiveNewAidIncmAmtRnScore) {
        this.unvalidLiveNewAidIncmAmtRnScore = unvalidLiveNewAidIncmAmtRnScore;
    }

    /**
     * 获取新主播蓝钻diff分值
     * @return valid_live_new_aid_incm_amt_diff_score 新主播蓝钻diff分值
     */
    public Integer getValidLiveNewAidIncmAmtDiffScore() {
        return validLiveNewAidIncmAmtDiffScore;
    }

    /**
     * 设置新主播蓝钻diff分值
     * @param validLiveNewAidIncmAmtDiffScore 新主播蓝钻diff分值
     */
    public void setValidLiveNewAidIncmAmtDiffScore(Integer validLiveNewAidIncmAmtDiffScore) {
        this.validLiveNewAidIncmAmtDiffScore = validLiveNewAidIncmAmtDiffScore;
    }

    /**
     * 获取存量主播蓝钻diff分值
     * @return unvalid_live_new_aid_incm_amt_diff_score 存量主播蓝钻diff分值
     */
    public Integer getUnvalidLiveNewAidIncmAmtDiffScore() {
        return unvalidLiveNewAidIncmAmtDiffScore;
    }

    /**
     * 设置存量主播蓝钻diff分值
     * @param unvalidLiveNewAidIncmAmtDiffScore 存量主播蓝钻diff分值
     */
    public void setUnvalidLiveNewAidIncmAmtDiffScore(Integer unvalidLiveNewAidIncmAmtDiffScore) {
        this.unvalidLiveNewAidIncmAmtDiffScore = unvalidLiveNewAidIncmAmtDiffScore;
    }

    /**
     * 获取存量主播蓝钻占比对比大盘存量蓝钻占比差值分值
     * @return unvalid_live_new_aid_incm_amt_rate_diff_score 存量主播蓝钻占比对比大盘存量蓝钻占比差值分值
     */
    public Integer getUnvalidLiveNewAidIncmAmtRateDiffScore() {
        return unvalidLiveNewAidIncmAmtRateDiffScore;
    }

    /**
     * 设置存量主播蓝钻占比对比大盘存量蓝钻占比差值分值
     * @param unvalidLiveNewAidIncmAmtRateDiffScore 存量主播蓝钻占比对比大盘存量蓝钻占比差值分值
     */
    public void setUnvalidLiveNewAidIncmAmtRateDiffScore(Integer unvalidLiveNewAidIncmAmtRateDiffScore) {
        this.unvalidLiveNewAidIncmAmtRateDiffScore = unvalidLiveNewAidIncmAmtRateDiffScore;
    }

    /**
     * 获取公会健康分分值
     * @return guild_health_point_score 公会健康分分值
     */
    public Integer getGuildHealthPointScore() {
        return guildHealthPointScore;
    }

    /**
     * 设置公会健康分分值
     * @param guildHealthPointScore 公会健康分分值
     */
    public void setGuildHealthPointScore(Integer guildHealthPointScore) {
        this.guildHealthPointScore = guildHealthPointScore;
    }

    /**
     * 获取招募(15分)
     * @return recru_score 招募(15分)
     */
    public Integer getRecruScore() {
        return recruScore;
    }

    /**
     * 设置招募(15分)
     * @param recruScore 招募(15分)
     */
    public void setRecruScore(Integer recruScore) {
        this.recruScore = recruScore;
    }

    /**
     * 获取孵化(30分)
     * @return hatch_score 孵化(30分)
     */
    public Integer getHatchScore() {
        return hatchScore;
    }

    /**
     * 设置孵化(30分)
     * @param hatchScore 孵化(30分)
     */
    public void setHatchScore(Integer hatchScore) {
        this.hatchScore = hatchScore;
    }

    /**
     * 获取留存(20分)
     * @return ret_score 留存(20分)
     */
    public Integer getRetScore() {
        return retScore;
    }

    /**
     * 设置留存(20分)
     * @param retScore 留存(20分)
     */
    public void setRetScore(Integer retScore) {
        this.retScore = retScore;
    }

    /**
     * 获取营收(30分)
     * @return rvnu_score 营收(30分)
     */
    public Integer getRvnuScore() {
        return rvnuScore;
    }

    /**
     * 设置营收(30分)
     * @param rvnuScore 营收(30分)
     */
    public void setRvnuScore(Integer rvnuScore) {
        this.rvnuScore = rvnuScore;
    }

    /**
     * 获取综合(5分)
     * @return com_score 综合(5分)
     */
    public Integer getComScore() {
        return comScore;
    }

    /**
     * 设置综合(5分)
     * @param comScore 综合(5分)
     */
    public void setComScore(Integer comScore) {
        this.comScore = comScore;
    }

    /**
     * 获取数据指标总得分
     * @return data_total_score 数据指标总得分
     */
    public Integer getDataTotalScore() {
        return dataTotalScore;
    }

    /**
     * 设置数据指标总得分
     * @param dataTotalScore 数据指标总得分
     */
    public void setDataTotalScore(Integer dataTotalScore) {
        this.dataTotalScore = dataTotalScore;
    }

    /**
     * 获取数据指标主体星级	
     * @return data_cmp_star_lvl 数据指标主体星级	
     */
    public Integer getDataCmpStarLvl() {
        return dataCmpStarLvl;
    }

    /**
     * 设置数据指标主体星级	
     * @param dataCmpStarLvl 数据指标主体星级	
     */
    public void setDataCmpStarLvl(Integer dataCmpStarLvl) {
        this.dataCmpStarLvl = dataCmpStarLvl;
    }

    /**
     * 获取主体星级
     * @return cmp_star_lvl 主体星级
     */
    public Integer getCmpStarLvl() {
        return cmpStarLvl;
    }

    /**
     * 设置主体星级
     * @param cmpStarLvl 主体星级
     */
    public void setCmpStarLvl(Integer cmpStarLvl) {
        this.cmpStarLvl = cmpStarLvl;
    }

    /**
     * 获取新签3&4星主播数得分
     * @return new_aid_rat_3_4_aid_num_score 新签3&4星主播数得分
     */
    public Integer getNewAidRat34AidNumScore() {
        return newAidRat34AidNumScore;
    }

    /**
     * 设置新签3&4星主播数得分
     * @param newAidRat34AidNumScore 新签3&4星主播数得分
     */
    public void setNewAidRat34AidNumScore(Integer newAidRat34AidNumScore) {
        this.newAidRat34AidNumScore = newAidRat34AidNumScore;
    }

    /**
     * 获取新增头部主播数得分
     * @return upgrd_high_aid_num_score 新增头部主播数得分
     */
    public Integer getUpgrdHighAidNumScore() {
        return upgrdHighAidNumScore;
    }

    /**
     * 设置新增头部主播数得分
     * @param upgrdHighAidNumScore 新增头部主播数得分
     */
    public void setUpgrdHighAidNumScore(Integer upgrdHighAidNumScore) {
        this.upgrdHighAidNumScore = upgrdHighAidNumScore;
    }

    /**
     * 获取新增腰部主播数得分
     * @return upgrd_waist_aid_num_score 新增腰部主播数得分
     */
    public Integer getUpgrdWaistAidNumScore() {
        return upgrdWaistAidNumScore;
    }

    /**
     * 设置新增腰部主播数得分
     * @param upgrdWaistAidNumScore 新增腰部主播数得分
     */
    public void setUpgrdWaistAidNumScore(Integer upgrdWaistAidNumScore) {
        this.upgrdWaistAidNumScore = upgrdWaistAidNumScore;
    }

    /**
     * 获取续约重点金牌艺人（续约1-6级金牌艺人）得分
     * @return ext_golden_aid_num_score 续约重点金牌艺人（续约1-6级金牌艺人）得分
     */
    public Integer getExtGoldenAidNumScore() {
        return extGoldenAidNumScore;
    }

    /**
     * 设置续约重点金牌艺人（续约1-6级金牌艺人）得分
     * @param extGoldenAidNumScore 续约重点金牌艺人（续约1-6级金牌艺人）得分
     */
    public void setExtGoldenAidNumScore(Integer extGoldenAidNumScore) {
        this.extGoldenAidNumScore = extGoldenAidNumScore;
    }

    /**
     * 获取礼物流水占比大于70%得分
     * @return actv_prod_pay_amt_rate_score 礼物流水占比大于70%得分
     */
    public Integer getActvProdPayAmtRateScore() {
        return actvProdPayAmtRateScore;
    }

    /**
     * 设置礼物流水占比大于70%得分
     * @param actvProdPayAmtRateScore 礼物流水占比大于70%得分
     */
    public void setActvProdPayAmtRateScore(Integer actvProdPayAmtRateScore) {
        this.actvProdPayAmtRateScore = actvProdPayAmtRateScore;
    }

    /**
     * 获取活动蓝钻收入占比大于70%得分
     * @return actv_incm_amt_rate_score 活动蓝钻收入占比大于70%得分
     */
    public Integer getActvIncmAmtRateScore() {
        return actvIncmAmtRateScore;
    }

    /**
     * 设置活动蓝钻收入占比大于70%得分
     * @param actvIncmAmtRateScore 活动蓝钻收入占比大于70%得分
     */
    public void setActvIncmAmtRateScore(Integer actvIncmAmtRateScore) {
        this.actvIncmAmtRateScore = actvIncmAmtRateScore;
    }

    /**
     * 获取任务指标总得分
     * @return task_total_score 任务指标总得分
     */
    public Integer getTaskTotalScore() {
        return taskTotalScore;
    }

    /**
     * 设置任务指标总得分
     * @param taskTotalScore 任务指标总得分
     */
    public void setTaskTotalScore(Integer taskTotalScore) {
        this.taskTotalScore = taskTotalScore;
    }

    /**
     * 获取新签3&4星主播数积分
     * @return new_aid_rat_3_4_aid_num_itg 新签3&4星主播数积分
     */
    public Integer getNewAidRat34AidNumItg() {
        return newAidRat34AidNumItg;
    }

    /**
     * 设置新签3&4星主播数积分
     * @param newAidRat34AidNumItg 新签3&4星主播数积分
     */
    public void setNewAidRat34AidNumItg(Integer newAidRat34AidNumItg) {
        this.newAidRat34AidNumItg = newAidRat34AidNumItg;
    }

    /**
     * 获取新增头部主播数积分
     * @return upgrd_high_aid_num_itg 新增头部主播数积分
     */
    public Integer getUpgrdHighAidNumItg() {
        return upgrdHighAidNumItg;
    }

    /**
     * 设置新增头部主播数积分
     * @param upgrdHighAidNumItg 新增头部主播数积分
     */
    public void setUpgrdHighAidNumItg(Integer upgrdHighAidNumItg) {
        this.upgrdHighAidNumItg = upgrdHighAidNumItg;
    }

    /**
     * 获取新增腰部主播数积分
     * @return upgrd_waist_aid_num_itg 新增腰部主播数积分
     */
    public Integer getUpgrdWaistAidNumItg() {
        return upgrdWaistAidNumItg;
    }

    /**
     * 设置新增腰部主播数积分
     * @param upgrdWaistAidNumItg 新增腰部主播数积分
     */
    public void setUpgrdWaistAidNumItg(Integer upgrdWaistAidNumItg) {
        this.upgrdWaistAidNumItg = upgrdWaistAidNumItg;
    }

    /**
     * 获取续约重点金牌艺人（续约1-6级金牌艺人）积分
     * @return ext_golden_aid_num_itg 续约重点金牌艺人（续约1-6级金牌艺人）积分
     */
    public Integer getExtGoldenAidNumItg() {
        return extGoldenAidNumItg;
    }

    /**
     * 设置续约重点金牌艺人（续约1-6级金牌艺人）积分
     * @param extGoldenAidNumItg 续约重点金牌艺人（续约1-6级金牌艺人）积分
     */
    public void setExtGoldenAidNumItg(Integer extGoldenAidNumItg) {
        this.extGoldenAidNumItg = extGoldenAidNumItg;
    }

    /**
     * 获取礼物流水占比大于70%积分
     * @return actv_prod_pay_amt_rate_itg 礼物流水占比大于70%积分
     */
    public Integer getActvProdPayAmtRateItg() {
        return actvProdPayAmtRateItg;
    }

    /**
     * 设置礼物流水占比大于70%积分
     * @param actvProdPayAmtRateItg 礼物流水占比大于70%积分
     */
    public void setActvProdPayAmtRateItg(Integer actvProdPayAmtRateItg) {
        this.actvProdPayAmtRateItg = actvProdPayAmtRateItg;
    }

    /**
     * 获取活动蓝钻收入占比大于70%积分
     * @return actv_incm_amt_rate_itg 活动蓝钻收入占比大于70%积分
     */
    public Integer getActvIncmAmtRateItg() {
        return actvIncmAmtRateItg;
    }

    /**
     * 设置活动蓝钻收入占比大于70%积分
     * @param actvIncmAmtRateItg 活动蓝钻收入占比大于70%积分
     */
    public void setActvIncmAmtRateItg(Integer actvIncmAmtRateItg) {
        this.actvIncmAmtRateItg = actvIncmAmtRateItg;
    }

    /**
     * 获取任务指标总积分
     * @return task_total_itg 任务指标总积分
     */
    public Integer getTaskTotalItg() {
        return taskTotalItg;
    }

    /**
     * 设置任务指标总积分
     * @param taskTotalItg 任务指标总积分
     */
    public void setTaskTotalItg(Integer taskTotalItg) {
        this.taskTotalItg = taskTotalItg;
    }

    /**
     * 获取总得分
     * @return total_score 总得分
     */
    public Integer getTotalScore() {
        return totalScore;
    }

    /**
     * 设置总得分
     * @param totalScore 总得分
     */
    public void setTotalScore(Integer totalScore) {
        this.totalScore = totalScore;
    }

    /**
     * 获取日期
     * @return dt 日期
     */
    public Date getDt() {
        return dt;
    }

    /**
     * 设置日期
     * @param dt 日期
     */
    public void setDt(Date dt) {
        this.dt = dt;
    }

    /**
     * 获取公会近3个自然月月均新签3&4星主播数，保留两位小数后四舍五入取整
     * @return avg_new_aid_rat_3_4_aid_num_3m 公会近3个自然月月均新签3&4星主播数，保留两位小数后四舍五入取整
     */
    public BigDecimal getAvgNewAidRat34AidNum3m() {
        return avgNewAidRat34AidNum3m;
    }

    /**
     * 设置公会近3个自然月月均新签3&4星主播数，保留两位小数后四舍五入取整
     * @param avgNewAidRat34AidNum3m 公会近3个自然月月均新签3&4星主播数，保留两位小数后四舍五入取整
     */
    public void setAvgNewAidRat34AidNum3m(BigDecimal avgNewAidRat34AidNum3m) {
        this.avgNewAidRat34AidNum3m = avgNewAidRat34AidNum3m;
    }

    /**
     * 获取新签3星主播数，当月3星主播签约数量
     * @return new_aid_rat_3_aid_num 新签3星主播数，当月3星主播签约数量
     */
    public Long getNewAidRat3AidNum() {
        return newAidRat3AidNum;
    }

    /**
     * 设置新签3星主播数，当月3星主播签约数量
     * @param newAidRat3AidNum 新签3星主播数，当月3星主播签约数量
     */
    public void setNewAidRat3AidNum(Long newAidRat3AidNum) {
        this.newAidRat3AidNum = newAidRat3AidNum;
    }

    /**
     * 获取新签4星主播数，当月4星主播签约数量
     * @return new_aid_rat_4_aid_num 新签4星主播数，当月4星主播签约数量
     */
    public Long getNewAidRat4AidNum() {
        return newAidRat4AidNum;
    }

    /**
     * 设置新签4星主播数，当月4星主播签约数量
     * @param newAidRat4AidNum 新签4星主播数，当月4星主播签约数量
     */
    public void setNewAidRat4AidNum(Long newAidRat4AidNum) {
        this.newAidRat4AidNum = newAidRat4AidNum;
    }

    /**
     * 获取线上绑定3&4星主播任务要求
     * @return avg_new_aid_rat_3_4_aid_num_3m_aim 线上绑定3&4星主播任务要求
     */
    public Integer getAvgNewAidRat34AidNum3mAim() {
        return avgNewAidRat34AidNum3mAim;
    }

    /**
     * 设置线上绑定3&4星主播任务要求
     * @param avgNewAidRat34AidNum3mAim 线上绑定3&4星主播任务要求
     */
    public void setAvgNewAidRat34AidNum3mAim(Integer avgNewAidRat34AidNum3mAim) {
        this.avgNewAidRat34AidNum3mAim = avgNewAidRat34AidNum3mAim;
    }

    /**
     * 获取线上绑定3&4星主播任务得分
     * @return avg_new_aid_rat_3_4_aid_num_3m_score 线上绑定3&4星主播任务得分
     */
    public Integer getAvgNewAidRat34AidNum3mScore() {
        return avgNewAidRat34AidNum3mScore;
    }

    /**
     * 设置线上绑定3&4星主播任务得分
     * @param avgNewAidRat34AidNum3mScore 线上绑定3&4星主播任务得分
     */
    public void setAvgNewAidRat34AidNum3mScore(Integer avgNewAidRat34AidNum3mScore) {
        this.avgNewAidRat34AidNum3mScore = avgNewAidRat34AidNum3mScore;
    }

    /**
     * 获取公会近3个自然月月均新增头&腰部主播数，保留两位小数后四舍五入取整
     * @return avg_upgrd_high_waist_aid_num_3m 公会近3个自然月月均新增头&腰部主播数，保留两位小数后四舍五入取整
     */
    public BigDecimal getAvgUpgrdHighWaistAidNum3m() {
        return avgUpgrdHighWaistAidNum3m;
    }

    /**
     * 设置公会近3个自然月月均新增头&腰部主播数，保留两位小数后四舍五入取整
     * @param avgUpgrdHighWaistAidNum3m 公会近3个自然月月均新增头&腰部主播数，保留两位小数后四舍五入取整
     */
    public void setAvgUpgrdHighWaistAidNum3m(BigDecimal avgUpgrdHighWaistAidNum3m) {
        this.avgUpgrdHighWaistAidNum3m = avgUpgrdHighWaistAidNum3m;
    }

    /**
     * 获取新增头&腰部主播任务要求
     * @return avg_upgrd_high_waist_aid_num_3m_aim 新增头&腰部主播任务要求
     */
    public Integer getAvgUpgrdHighWaistAidNum3mAim() {
        return avgUpgrdHighWaistAidNum3mAim;
    }

    /**
     * 设置新增头&腰部主播任务要求
     * @param avgUpgrdHighWaistAidNum3mAim 新增头&腰部主播任务要求
     */
    public void setAvgUpgrdHighWaistAidNum3mAim(Integer avgUpgrdHighWaistAidNum3mAim) {
        this.avgUpgrdHighWaistAidNum3mAim = avgUpgrdHighWaistAidNum3mAim;
    }

    /**
     * 获取新增头&腰部主播任务得分
     * @return avg_upgrd_high_waist_aid_num_3m_score 新增头&腰部主播任务得分
     */
    public Integer getAvgUpgrdHighWaistAidNum3mScore() {
        return avgUpgrdHighWaistAidNum3mScore;
    }

    /**
     * 设置新增头&腰部主播任务得分
     * @param avgUpgrdHighWaistAidNum3mScore 新增头&腰部主播任务得分
     */
    public void setAvgUpgrdHighWaistAidNum3mScore(Integer avgUpgrdHighWaistAidNum3mScore) {
        this.avgUpgrdHighWaistAidNum3mScore = avgUpgrdHighWaistAidNum3mScore;
    }

    /**
     * 获取自然月新主播蓝钻任务完成率，保留整数
     * @return new_aid_incm_com_rate 自然月新主播蓝钻任务完成率，保留整数
     */
    public BigDecimal getNewAidIncmComRate() {
        return newAidIncmComRate;
    }

    /**
     * 设置自然月新主播蓝钻任务完成率，保留整数
     * @param newAidIncmComRate 自然月新主播蓝钻任务完成率，保留整数
     */
    public void setNewAidIncmComRate(BigDecimal newAidIncmComRate) {
        this.newAidIncmComRate = newAidIncmComRate;
    }

    /**
     * 获取新主播蓝钻任务完成率要求
     * @return new_aid_incm_com_rate_aim 新主播蓝钻任务完成率要求
     */
    public BigDecimal getNewAidIncmComRateAim() {
        return newAidIncmComRateAim;
    }

    /**
     * 设置新主播蓝钻任务完成率要求
     * @param newAidIncmComRateAim 新主播蓝钻任务完成率要求
     */
    public void setNewAidIncmComRateAim(BigDecimal newAidIncmComRateAim) {
        this.newAidIncmComRateAim = newAidIncmComRateAim;
    }

    /**
     * 获取新主播蓝钻任务得分
     * @return new_aid_incm_com_rate_score 新主播蓝钻任务得分
     */
    public Integer getNewAidIncmComRateScore() {
        return newAidIncmComRateScore;
    }

    /**
     * 设置新主播蓝钻任务得分
     * @param newAidIncmComRateScore 新主播蓝钻任务得分
     */
    public void setNewAidIncmComRateScore(Integer newAidIncmComRateScore) {
        this.newAidIncmComRateScore = newAidIncmComRateScore;
    }

    /**
     * 获取公会蓝钻任务完成档位
     * @return guild_task_incm_lvl 公会蓝钻任务完成档位
     */
    public Integer getGuildTaskIncmLvl() {
        return guildTaskIncmLvl;
    }

    /**
     * 设置公会蓝钻任务完成档位
     * @param guildTaskIncmLvl 公会蓝钻任务完成档位
     */
    public void setGuildTaskIncmLvl(Integer guildTaskIncmLvl) {
        this.guildTaskIncmLvl = guildTaskIncmLvl;
    }

    /**
     * 获取公会蓝钻任务目标档位
     * @return guild_task_incm_lvl_aim 公会蓝钻任务目标档位
     */
    public Integer getGuildTaskIncmLvlAim() {
        return guildTaskIncmLvlAim;
    }

    /**
     * 设置公会蓝钻任务目标档位
     * @param guildTaskIncmLvlAim 公会蓝钻任务目标档位
     */
    public void setGuildTaskIncmLvlAim(Integer guildTaskIncmLvlAim) {
        this.guildTaskIncmLvlAim = guildTaskIncmLvlAim;
    }

    /**
     * 获取公会蓝钻任务得分
     * @return guild_task_incm_lvl_score 公会蓝钻任务得分
     */
    public Integer getGuildTaskIncmLvlScore() {
        return guildTaskIncmLvlScore;
    }

    /**
     * 设置公会蓝钻任务得分
     * @param guildTaskIncmLvlScore 公会蓝钻任务得分
     */
    public void setGuildTaskIncmLvlScore(Integer guildTaskIncmLvlScore) {
        this.guildTaskIncmLvlScore = guildTaskIncmLvlScore;
    }

    /**
     * 获取自然月月累计蓝钻diff
     * @return accum_incm_amt_diff 自然月月累计蓝钻diff
     */
    public Long getAccumIncmAmtDiff() {
        return accumIncmAmtDiff;
    }

    /**
     * 设置自然月月累计蓝钻diff
     * @param accumIncmAmtDiff 自然月月累计蓝钻diff
     */
    public void setAccumIncmAmtDiff(Long accumIncmAmtDiff) {
        this.accumIncmAmtDiff = accumIncmAmtDiff;
    }

    /**
     * 获取自然月月累计蓝钻任务要求
     * @return accum_incm_amt_aim 自然月月累计蓝钻任务要求
     */
    public Long getAccumIncmAmtAim() {
        return accumIncmAmtAim;
    }

    /**
     * 设置自然月月累计蓝钻任务要求
     * @param accumIncmAmtAim 自然月月累计蓝钻任务要求
     */
    public void setAccumIncmAmtAim(Long accumIncmAmtAim) {
        this.accumIncmAmtAim = accumIncmAmtAim;
    }

    /**
     * 获取自然月月累计蓝钻任务得分
     * @return accum_incm_amt_socre 自然月月累计蓝钻任务得分
     */
    public Integer getAccumIncmAmtSocre() {
        return accumIncmAmtSocre;
    }

    /**
     * 设置自然月月累计蓝钻任务得分
     * @param accumIncmAmtSocre 自然月月累计蓝钻任务得分
     */
    public void setAccumIncmAmtSocre(Integer accumIncmAmtSocre) {
        this.accumIncmAmtSocre = accumIncmAmtSocre;
    }

    /**
     * 获取公司名
     * @return guild_cmp_name 公司名
     */
    public String getGuildCmpName() {
        return guildCmpName;
    }

    /**
     * 设置公司名
     * @param guildCmpName 公司名
     */
    public void setGuildCmpName(String guildCmpName) {
        this.guildCmpName = guildCmpName;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", guildCmpOwnrId=").append(guildCmpOwnrId);
        sb.append(", newAidRat34AidNum=").append(newAidRat34AidNum);
        sb.append(", newAidRat34AidNumRn=").append(newAidRat34AidNumRn);
        sb.append(", newAidRat34AidNumDiff=").append(newAidRat34AidNumDiff);
        sb.append(", upgrdHighAidNum=").append(upgrdHighAidNum);
        sb.append(", upgrdHighAidNumDiff=").append(upgrdHighAidNumDiff);
        sb.append(", upgrdWaistAidNum=").append(upgrdWaistAidNum);
        sb.append(", upgrdWaistAidNumDiff=").append(upgrdWaistAidNumDiff);
        sb.append(", avgHighValidLiveAidNum=").append(avgHighValidLiveAidNum);
        sb.append(", avgHighValidLiveAidNumRn=").append(avgHighValidLiveAidNumRn);
        sb.append(", avgHighValidLiveAidNumDiff=").append(avgHighValidLiveAidNumDiff);
        sb.append(", avgWaistValidLiveAidNum=").append(avgWaistValidLiveAidNum);
        sb.append(", avgWaistValidLiveAidNumRn=").append(avgWaistValidLiveAidNumRn);
        sb.append(", avgWaistValidLiveAidNumDiff=").append(avgWaistValidLiveAidNumDiff);
        sb.append(", avgNtoValidLiveAidRate=").append(avgNtoValidLiveAidRate);
        sb.append(", avgNtoValidLiveAidRateRn=").append(avgNtoValidLiveAidRateRn);
        sb.append(", avgStockValidLiveAidRate=").append(avgStockValidLiveAidRate);
        sb.append(", avgStockValidLiveAidRateRn=").append(avgStockValidLiveAidRateRn);
        sb.append(", newAuthGoldenAidNum=").append(newAuthGoldenAidNum);
        sb.append(", newAuthGoldenAidNumRn=").append(newAuthGoldenAidNumRn);
        sb.append(", extGoldenAidNum=").append(extGoldenAidNum);
        sb.append(", actvProdPayAmt=").append(actvProdPayAmt);
        sb.append(", prodPayAmt=").append(prodPayAmt);
        sb.append(", actvProdPayAmtRate=").append(actvProdPayAmtRate);
        sb.append(", validLiveNewAidProdPayAmt=").append(validLiveNewAidProdPayAmt);
        sb.append(", validLiveNewAidProdPayAmtRn=").append(validLiveNewAidProdPayAmtRn);
        sb.append(", validLiveNewAidProdPayAmtDiff=").append(validLiveNewAidProdPayAmtDiff);
        sb.append(", unvalidLiveNewAidProdPayAmt=").append(unvalidLiveNewAidProdPayAmt);
        sb.append(", unvalidLiveNewAidProdPayAmtRn=").append(unvalidLiveNewAidProdPayAmtRn);
        sb.append(", unvalidLiveNewAidProdPayAmtDiff=").append(unvalidLiveNewAidProdPayAmtDiff);
        sb.append(", unvalidLiveNewAidProdPayAmtRate=").append(unvalidLiveNewAidProdPayAmtRate);
        sb.append(", totalUnvalidLiveNewAidProdPayAmtRate=").append(totalUnvalidLiveNewAidProdPayAmtRate);
        sb.append(", unvalidLiveNewAidProdPayAmtRateDiff=").append(unvalidLiveNewAidProdPayAmtRateDiff);
        sb.append(", actvIncmAmt=").append(actvIncmAmt);
        sb.append(", incmAmt=").append(incmAmt);
        sb.append(", actvIncmAmtRate=").append(actvIncmAmtRate);
        sb.append(", validLiveNewAidIncmAmt=").append(validLiveNewAidIncmAmt);
        sb.append(", validLiveNewAidIncmAmtRn=").append(validLiveNewAidIncmAmtRn);
        sb.append(", validLiveNewAidIncmAmtDiff=").append(validLiveNewAidIncmAmtDiff);
        sb.append(", unvalidLiveNewAidIncmAmt=").append(unvalidLiveNewAidIncmAmt);
        sb.append(", unvalidLiveNewAidIncmAmtRn=").append(unvalidLiveNewAidIncmAmtRn);
        sb.append(", unvalidLiveNewAidIncmAmtDiff=").append(unvalidLiveNewAidIncmAmtDiff);
        sb.append(", unvalidLiveNewAidIncmAmtRate=").append(unvalidLiveNewAidIncmAmtRate);
        sb.append(", totalUnvalidLiveNewAidIncmAmtRate=").append(totalUnvalidLiveNewAidIncmAmtRate);
        sb.append(", unvalidLiveNewAidIncmAmtRateDiff=").append(unvalidLiveNewAidIncmAmtRateDiff);
        sb.append(", guildHealthPoint=").append(guildHealthPoint);
        sb.append(", newAidRat34AidNumRnScore=").append(newAidRat34AidNumRnScore);
        sb.append(", newAidRat34AidNumDiffScore=").append(newAidRat34AidNumDiffScore);
        sb.append(", upgrdHighAidNumDiffScore=").append(upgrdHighAidNumDiffScore);
        sb.append(", upgrdWaistAidNumDiffScore=").append(upgrdWaistAidNumDiffScore);
        sb.append(", avgHighValidLiveAidNumRnScore=").append(avgHighValidLiveAidNumRnScore);
        sb.append(", avgWaistValidLiveAidNumRnScore=").append(avgWaistValidLiveAidNumRnScore);
        sb.append(", avgHighValidLiveAidNumDiffScore=").append(avgHighValidLiveAidNumDiffScore);
        sb.append(", avgWaistValidLiveAidNumDiffScore=").append(avgWaistValidLiveAidNumDiffScore);
        sb.append(", avgNtoValidLiveAidRateRnScore=").append(avgNtoValidLiveAidRateRnScore);
        sb.append(", avgStockValidLiveAidRateRnScore=").append(avgStockValidLiveAidRateRnScore);
        sb.append(", newAuthGoldenAidNumRnScore=").append(newAuthGoldenAidNumRnScore);
        sb.append(", validLiveNewAidProdPayAmtRnScore=").append(validLiveNewAidProdPayAmtRnScore);
        sb.append(", unvalidLiveNewAidProdPayAmtRnScore=").append(unvalidLiveNewAidProdPayAmtRnScore);
        sb.append(", validLiveNewAidProdPayAmtDiffScore=").append(validLiveNewAidProdPayAmtDiffScore);
        sb.append(", unvalidLiveNewAidProdPayAmtDiffScore=").append(unvalidLiveNewAidProdPayAmtDiffScore);
        sb.append(", unvalidLiveNewAidProdPayAmtRateDiffScore=").append(unvalidLiveNewAidProdPayAmtRateDiffScore);
        sb.append(", validLiveNewAidIncmAmtRnScore=").append(validLiveNewAidIncmAmtRnScore);
        sb.append(", unvalidLiveNewAidIncmAmtRnScore=").append(unvalidLiveNewAidIncmAmtRnScore);
        sb.append(", validLiveNewAidIncmAmtDiffScore=").append(validLiveNewAidIncmAmtDiffScore);
        sb.append(", unvalidLiveNewAidIncmAmtDiffScore=").append(unvalidLiveNewAidIncmAmtDiffScore);
        sb.append(", unvalidLiveNewAidIncmAmtRateDiffScore=").append(unvalidLiveNewAidIncmAmtRateDiffScore);
        sb.append(", guildHealthPointScore=").append(guildHealthPointScore);
        sb.append(", recruScore=").append(recruScore);
        sb.append(", hatchScore=").append(hatchScore);
        sb.append(", retScore=").append(retScore);
        sb.append(", rvnuScore=").append(rvnuScore);
        sb.append(", comScore=").append(comScore);
        sb.append(", dataTotalScore=").append(dataTotalScore);
        sb.append(", dataCmpStarLvl=").append(dataCmpStarLvl);
        sb.append(", cmpStarLvl=").append(cmpStarLvl);
        sb.append(", newAidRat34AidNumScore=").append(newAidRat34AidNumScore);
        sb.append(", upgrdHighAidNumScore=").append(upgrdHighAidNumScore);
        sb.append(", upgrdWaistAidNumScore=").append(upgrdWaistAidNumScore);
        sb.append(", extGoldenAidNumScore=").append(extGoldenAidNumScore);
        sb.append(", actvProdPayAmtRateScore=").append(actvProdPayAmtRateScore);
        sb.append(", actvIncmAmtRateScore=").append(actvIncmAmtRateScore);
        sb.append(", taskTotalScore=").append(taskTotalScore);
        sb.append(", newAidRat34AidNumItg=").append(newAidRat34AidNumItg);
        sb.append(", upgrdHighAidNumItg=").append(upgrdHighAidNumItg);
        sb.append(", upgrdWaistAidNumItg=").append(upgrdWaistAidNumItg);
        sb.append(", extGoldenAidNumItg=").append(extGoldenAidNumItg);
        sb.append(", actvProdPayAmtRateItg=").append(actvProdPayAmtRateItg);
        sb.append(", actvIncmAmtRateItg=").append(actvIncmAmtRateItg);
        sb.append(", taskTotalItg=").append(taskTotalItg);
        sb.append(", totalScore=").append(totalScore);
        sb.append(", dt=").append(dt);
        sb.append(", avgNewAidRat34AidNum3m=").append(avgNewAidRat34AidNum3m);
        sb.append(", newAidRat3AidNum=").append(newAidRat3AidNum);
        sb.append(", newAidRat4AidNum=").append(newAidRat4AidNum);
        sb.append(", avgNewAidRat34AidNum3mAim=").append(avgNewAidRat34AidNum3mAim);
        sb.append(", avgNewAidRat34AidNum3mScore=").append(avgNewAidRat34AidNum3mScore);
        sb.append(", avgUpgrdHighWaistAidNum3m=").append(avgUpgrdHighWaistAidNum3m);
        sb.append(", avgUpgrdHighWaistAidNum3mAim=").append(avgUpgrdHighWaistAidNum3mAim);
        sb.append(", avgUpgrdHighWaistAidNum3mScore=").append(avgUpgrdHighWaistAidNum3mScore);
        sb.append(", newAidIncmComRate=").append(newAidIncmComRate);
        sb.append(", newAidIncmComRateAim=").append(newAidIncmComRateAim);
        sb.append(", newAidIncmComRateScore=").append(newAidIncmComRateScore);
        sb.append(", guildTaskIncmLvl=").append(guildTaskIncmLvl);
        sb.append(", guildTaskIncmLvlAim=").append(guildTaskIncmLvlAim);
        sb.append(", guildTaskIncmLvlScore=").append(guildTaskIncmLvlScore);
        sb.append(", accumIncmAmtDiff=").append(accumIncmAmtDiff);
        sb.append(", accumIncmAmtAim=").append(accumIncmAmtAim);
        sb.append(", accumIncmAmtSocre=").append(accumIncmAmtSocre);
        sb.append(", guildCmpName=").append(guildCmpName);
        sb.append("]");
        return sb.toString();
    }
}