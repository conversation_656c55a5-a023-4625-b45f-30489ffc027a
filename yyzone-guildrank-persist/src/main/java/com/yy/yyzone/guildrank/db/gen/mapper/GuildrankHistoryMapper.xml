<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yy.yyzone.guildrank.db.gen.mapper.GuildrankHistoryMapper" >
  <resultMap id="BaseResultMap" type="com.yy.yyzone.guildrank.db.gen.model.GuildrankHistory" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="month" property="month" jdbcType="DATE" />
    <result column="main_guild_uid" property="mainGuildUid" jdbcType="BIGINT" />
    <result column="guild_uid" property="guildUid" jdbcType="BIGINT" />
    <result column="level" property="level" jdbcType="INTEGER" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, month, main_guild_uid, guild_uid, level, type, update_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankHistoryExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from guildrank_history
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit > 0" >
      limit ${limit}
    </if>
    <if test="offset > 0" >
      offset ${offset}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from guildrank_history
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from guildrank_history
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankHistoryExample" >
    delete from guildrank_history
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankHistory" useGeneratedKeys="true" keyProperty="id" >
    insert into guildrank_history (month, main_guild_uid, guild_uid, 
      level, type, update_time
      )
    values (#{month,jdbcType=DATE}, #{mainGuildUid,jdbcType=BIGINT}, #{guildUid,jdbcType=BIGINT}, 
      #{level,jdbcType=INTEGER}, #{type,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankHistory" useGeneratedKeys="true" keyProperty="id" >
    insert into guildrank_history
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="month != null" >
        month,
      </if>
      <if test="mainGuildUid != null" >
        main_guild_uid,
      </if>
      <if test="guildUid != null" >
        guild_uid,
      </if>
      <if test="level != null" >
        level,
      </if>
      <if test="type != null" >
        type,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="month != null" >
        #{month,jdbcType=DATE},
      </if>
      <if test="mainGuildUid != null" >
        #{mainGuildUid,jdbcType=BIGINT},
      </if>
      <if test="guildUid != null" >
        #{guildUid,jdbcType=BIGINT},
      </if>
      <if test="level != null" >
        #{level,jdbcType=INTEGER},
      </if>
      <if test="type != null" >
        #{type,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankHistoryExample" resultType="java.lang.Integer" >
    select count(*) from guildrank_history
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update guildrank_history
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.month != null" >
        month = #{record.month,jdbcType=DATE},
      </if>
      <if test="record.mainGuildUid != null" >
        main_guild_uid = #{record.mainGuildUid,jdbcType=BIGINT},
      </if>
      <if test="record.guildUid != null" >
        guild_uid = #{record.guildUid,jdbcType=BIGINT},
      </if>
      <if test="record.level != null" >
        level = #{record.level,jdbcType=INTEGER},
      </if>
      <if test="record.type != null" >
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update guildrank_history
    set id = #{record.id,jdbcType=BIGINT},
      month = #{record.month,jdbcType=DATE},
      main_guild_uid = #{record.mainGuildUid,jdbcType=BIGINT},
      guild_uid = #{record.guildUid,jdbcType=BIGINT},
      level = #{record.level,jdbcType=INTEGER},
      type = #{record.type,jdbcType=INTEGER},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankHistory" >
    update guildrank_history
    <set >
      <if test="month != null" >
        month = #{month,jdbcType=DATE},
      </if>
      <if test="mainGuildUid != null" >
        main_guild_uid = #{mainGuildUid,jdbcType=BIGINT},
      </if>
      <if test="guildUid != null" >
        guild_uid = #{guildUid,jdbcType=BIGINT},
      </if>
      <if test="level != null" >
        level = #{level,jdbcType=INTEGER},
      </if>
      <if test="type != null" >
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankHistory" >
    update guildrank_history
    set month = #{month,jdbcType=DATE},
      main_guild_uid = #{mainGuildUid,jdbcType=BIGINT},
      guild_uid = #{guildUid,jdbcType=BIGINT},
      level = #{level,jdbcType=INTEGER},
      type = #{type,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>