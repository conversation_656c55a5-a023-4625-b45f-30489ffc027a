package com.yy.yyzone.guildrank.db.gen.model;

import java.util.Date;

public class YyDmEntityGuildCmpHealthAnalysisExptDi {
    /**
     * id
     */
    private Long id;

    /**
     * 主体UID
     */
    private Long guildCmpOwnrId;

    /**
     * 3&4星主播签约数量
     */
    private Long newAidRat34AidNum;

    /**
     * 3&4星主播签约数量排名
     */
    private Long newAidRat34AidNumRn;

    /**
     * 3&4星主播签约数量排名分值
     */
    private Integer newAidRat34AidNumRnScore;

    /**
     * 3&4星主播签约数量排名下一等级分值
     */
    private Integer newAidRat34AidNumRnNextScore;

    /**
     * 3&4星主播签约数量目标差值
     */
    private Long newAidRat34AidNumTrgtDiff;

    /**
     * 3&4星主播签约数量趋势
     */
    private Long newAidRat34AidNumDiff;

    /**
     * 3&4星主播签约数量趋势分值
     */
    private Integer newAidRat34AidNumDiffScore;

    /**
     * 3&4星主播签约数量趋势下一等级分值
     */
    private Integer newAidRat34AidNumDiffNextScore;

    /**
     * 3&4星主播签约数量趋势目标差值
     */
    private Long newAidRat34AidNumDiffTrgtDiff;

    /**
     * 升级高优主播数趋势
     */
    private Integer upgrdHighAidNumDiff;

    /**
     * 升级高优主播数趋势分值
     */
    private Integer upgrdHighAidNumDiffScore;

    /**
     * 升级高优主播数趋势下一等级分值
     */
    private Integer upgrdHighAidNumDiffNextScore;

    /**
     * 升级高优主播数趋势目标差值
     */
    private Integer upgrdHighAidNumDiffTrgtDiff;

    /**
     * 升级腰部主播数趋势
     */
    private Integer upgrdWaistAidNumDiff;

    /**
     * 升级腰部主播数趋势分值
     */
    private Integer upgrdWaistAidNumDiffScore;

    /**
     * 升级腰部主播数趋势下一等级分值
     */
    private Integer upgrdWaistAidNumDiffNextScore;

    /**
     * 升级腰部主播数趋势目标差值
     */
    private Integer upgrdWaistAidNumDiffTrgtDiff;

    /**
     * 月日均高优有效开播主播数
     */
    private Double avgHighValidLiveAidNum;

    /**
     * 月日均高优有效开播主播数排名
     */
    private Long avgHighValidLiveAidNumRn;

    /**
     * 月日均高优有效开播主播数排名分值
     */
    private Integer avgHighValidLiveAidNumRnScore;

    /**
     * 月日均高优有效开播主播数排名下一等级分值
     */
    private Integer avgHighValidLiveAidNumRnNextScore;

    /**
     * 月日均高优有效开播主播数目标差值
     */
    private Double avgHighValidLiveAidNumTrgtDiff;

    /**
     * 月日均高优有效开播主播数趋势
     */
    private Double avgHighValidLiveAidNumDiff;

    /**
     * 月日均高优有效开播主播数趋势分值
     */
    private Integer avgHighValidLiveAidNumDiffScore;

    /**
     * 月日均高优有效开播主播数趋势下一等级分值
     */
    private Integer avgHighValidLiveAidNumDiffNextScore;

    /**
     * 月日均高优有效开播主播数趋势目标差值
     */
    private Double avgHighValidLiveAidNumDiffTrgtDiff;

    /**
     * 月日均腰部有效开播主播数
     */
    private Double avgWaistValidLiveAidNum;

    /**
     * 月日均腰部有效开播主播数排名
     */
    private Long avgWaistValidLiveAidNumRn;

    /**
     * 月日均腰部有效开播主播数排名分值
     */
    private Integer avgWaistValidLiveAidNumRnScore;

    /**
     * 月日均腰部有效开播主播数排名下一等级分值
     */
    private Integer avgWaistValidLiveAidNumRnNextScore;

    /**
     * 月日均腰部有效开播主播数目标差值
     */
    private Double avgWaistValidLiveAidNumTrgtDiff;

    /**
     * 月日均腰部有效开播主播数趋势
     */
    private Double avgWaistValidLiveAidNumDiff;

    /**
     * 月日均腰部有效开播主播数趋势分值
     */
    private Integer avgWaistValidLiveAidNumDiffScore;

    /**
     * 月日均腰部有效开播主播数趋势下一等级分值
     */
    private Integer avgWaistValidLiveAidNumDiffNextScore;

    /**
     * 月日均腰部有效开播主播数趋势目标差值
     */
    private Double avgWaistValidLiveAidNumDiffTrgtDiff;

    /**
     * 新转存主播开播率
     */
    private Double avgNtoValidLiveAidRate;

    /**
     * 新转存主播开播率排名
     */
    private Long avgNtoValidLiveAidRateRn;

    /**
     * 新转存主播开播率排名分值
     */
    private Integer avgNtoValidLiveAidRateRnScore;

    /**
     * 新转存主播开播率排名下一等级分值
     */
    private Integer avgNtoValidLiveAidRateRnNextScore;

    /**
     * 新转存主播开播率目标差值
     */
    private Double avgNtoValidLiveAidRateTrgtDiff;

    /**
     * 存量主播开播率
     */
    private Double avgStockValidLiveAidRate;

    /**
     * 存量主播开播率排名
     */
    private Long avgStockValidLiveAidRateRn;

    /**
     * 存量主播开播率排名分值
     */
    private Integer avgStockValidLiveAidRateRnScore;

    /**
     * 存量主播开播率排名下一等级分值
     */
    private Integer avgStockValidLiveAidRateRnNextScore;

    /**
     * 存量主播开播率目标差值
     */
    private Double avgStockValidLiveAidRateTrgtDiff;

    /**
     * 新授权金牌艺人
     */
    private Long newAuthGoldenAidNum;

    /**
     * 新授权金牌艺人排名
     */
    private Long newAuthGoldenAidNumRn;

    /**
     * 新授权金牌艺人排名分值
     */
    private Integer newAuthGoldenAidNumRnScore;

    /**
     * 新授权金牌艺人排名下一等级分值
     */
    private Integer newAuthGoldenAidNumRnNextScore;

    /**
     * 新授权金牌艺人目标差值
     */
    private Long newAuthGoldenAidNumTrgtDiff;

    /**
     * 新主播蓝钻收入
     */
    private Double validLiveNewAidIncmAmt;

    /**
     * 新主播蓝钻收入排名
     */
    private Long validLiveNewAidIncmAmtRn;

    /**
     * 新主播蓝钻收入排名分值
     */
    private Integer validLiveNewAidIncmAmtRnScore;

    /**
     * 新主播蓝钻收入排名下一等级分值
     */
    private Integer validLiveNewAidIncmAmtRnNextScore;

    /**
     * 新主播蓝钻收入目标差值
     */
    private Double validLiveNewAidIncmAmtTrgtDiff;

    /**
     * 新主播蓝钻收入趋势
     */
    private Double validLiveNewAidIncmAmtDiff;

    /**
     * 新主播蓝钻收入趋势分值
     */
    private Integer validLiveNewAidIncmAmtDiffScore;

    /**
     * 新主播蓝钻收入趋势下一等级分值
     */
    private Integer validLiveNewAidIncmAmtDiffNextScore;

    /**
     * 新主播蓝钻收入趋势目标差值@@
     */
    private Double validLiveNewAidIncmAmtDiffTrgtDiff;

    /**
     * 存量主播蓝钻收入
     */
    private Double unvalidLiveNewAidIncmAmt;

    /**
     * 存量主播蓝钻收入排名
     */
    private Long unvalidLiveNewAidIncmAmtRn;

    /**
     * 存量主播蓝钻收入排名分值
     */
    private Integer unvalidLiveNewAidIncmAmtRnScore;

    /**
     * 存量主播蓝钻收入排名下一等级分值
     */
    private Integer unvalidLiveNewAidIncmAmtRnNextScore;

    /**
     * 存量主播蓝钻收入目标差值
     */
    private Double unvalidLiveNewAidIncmAmtTrgtDiff;

    /**
     * 存量主播蓝钻收入趋势
     */
    private Double unvalidLiveNewAidIncmAmtDiff;

    /**
     * 存量主播蓝钻收入趋势分值
     */
    private Integer unvalidLiveNewAidIncmAmtDiffScore;

    /**
     * 存量主播蓝钻收入趋势下一等级分值
     */
    private Integer unvalidLiveNewAidIncmAmtDiffNextScore;

    /**
     * 存量主播蓝钻收入趋势目标差值
     */
    private Double unvalidLiveNewAidIncmAmtDiffTrgtDiff;

    /**
     * 存量主播蓝钻收入占比
     */
    private Double unvalidLiveNewAidIncmAmtRateDiff;

    /**
     * 存量主播蓝钻收入占比分值
     */
    private Integer unvalidLiveNewAidIncmAmtRateDiffScore;

    /**
     * 存量主播蓝钻收入占比下一等级分值
     */
    private Integer unvalidLiveNewAidIncmAmtRateDiffNextScore;

    /**
     * 存量主播蓝钻收入占比目标差值
     */
    private Double unvalidLiveNewAidIncmAmtRateDiffTrgtDiff;

    /**
     * 公会健康分
     */
    private Integer guildHealthPoint;

    /**
     *  公会健康分分值
     */
    private Integer guildHealthPointScore;

    /**
     *  公会健康分下一等级分值
     */
    private Integer guildHealthPointNextScore;

    /**
     *  公会健康分目标差值
     */
    private Integer guildHealthPointTrgtDiff;

    /**
     * 日期
     */
    private Date dt;

    /**
     * 获取id
     * @return id id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置id
     * @param id id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取主体UID
     * @return guild_cmp_ownr_id 主体UID
     */
    public Long getGuildCmpOwnrId() {
        return guildCmpOwnrId;
    }

    /**
     * 设置主体UID
     * @param guildCmpOwnrId 主体UID
     */
    public void setGuildCmpOwnrId(Long guildCmpOwnrId) {
        this.guildCmpOwnrId = guildCmpOwnrId;
    }

    /**
     * 获取3&4星主播签约数量
     * @return new_aid_rat_3_4_aid_num 3&4星主播签约数量
     */
    public Long getNewAidRat34AidNum() {
        return newAidRat34AidNum;
    }

    /**
     * 设置3&4星主播签约数量
     * @param newAidRat34AidNum 3&4星主播签约数量
     */
    public void setNewAidRat34AidNum(Long newAidRat34AidNum) {
        this.newAidRat34AidNum = newAidRat34AidNum;
    }

    /**
     * 获取3&4星主播签约数量排名
     * @return new_aid_rat_3_4_aid_num_rn 3&4星主播签约数量排名
     */
    public Long getNewAidRat34AidNumRn() {
        return newAidRat34AidNumRn;
    }

    /**
     * 设置3&4星主播签约数量排名
     * @param newAidRat34AidNumRn 3&4星主播签约数量排名
     */
    public void setNewAidRat34AidNumRn(Long newAidRat34AidNumRn) {
        this.newAidRat34AidNumRn = newAidRat34AidNumRn;
    }

    /**
     * 获取3&4星主播签约数量排名分值
     * @return new_aid_rat_3_4_aid_num_rn_score 3&4星主播签约数量排名分值
     */
    public Integer getNewAidRat34AidNumRnScore() {
        return newAidRat34AidNumRnScore;
    }

    /**
     * 设置3&4星主播签约数量排名分值
     * @param newAidRat34AidNumRnScore 3&4星主播签约数量排名分值
     */
    public void setNewAidRat34AidNumRnScore(Integer newAidRat34AidNumRnScore) {
        this.newAidRat34AidNumRnScore = newAidRat34AidNumRnScore;
    }

    /**
     * 获取3&4星主播签约数量排名下一等级分值
     * @return new_aid_rat_3_4_aid_num_rn_next_score 3&4星主播签约数量排名下一等级分值
     */
    public Integer getNewAidRat34AidNumRnNextScore() {
        return newAidRat34AidNumRnNextScore;
    }

    /**
     * 设置3&4星主播签约数量排名下一等级分值
     * @param newAidRat34AidNumRnNextScore 3&4星主播签约数量排名下一等级分值
     */
    public void setNewAidRat34AidNumRnNextScore(Integer newAidRat34AidNumRnNextScore) {
        this.newAidRat34AidNumRnNextScore = newAidRat34AidNumRnNextScore;
    }

    /**
     * 获取3&4星主播签约数量目标差值
     * @return new_aid_rat_3_4_aid_num_trgt_diff 3&4星主播签约数量目标差值
     */
    public Long getNewAidRat34AidNumTrgtDiff() {
        return newAidRat34AidNumTrgtDiff;
    }

    /**
     * 设置3&4星主播签约数量目标差值
     * @param newAidRat34AidNumTrgtDiff 3&4星主播签约数量目标差值
     */
    public void setNewAidRat34AidNumTrgtDiff(Long newAidRat34AidNumTrgtDiff) {
        this.newAidRat34AidNumTrgtDiff = newAidRat34AidNumTrgtDiff;
    }

    /**
     * 获取3&4星主播签约数量趋势
     * @return new_aid_rat_3_4_aid_num_diff 3&4星主播签约数量趋势
     */
    public Long getNewAidRat34AidNumDiff() {
        return newAidRat34AidNumDiff;
    }

    /**
     * 设置3&4星主播签约数量趋势
     * @param newAidRat34AidNumDiff 3&4星主播签约数量趋势
     */
    public void setNewAidRat34AidNumDiff(Long newAidRat34AidNumDiff) {
        this.newAidRat34AidNumDiff = newAidRat34AidNumDiff;
    }

    /**
     * 获取3&4星主播签约数量趋势分值
     * @return new_aid_rat_3_4_aid_num_diff_score 3&4星主播签约数量趋势分值
     */
    public Integer getNewAidRat34AidNumDiffScore() {
        return newAidRat34AidNumDiffScore;
    }

    /**
     * 设置3&4星主播签约数量趋势分值
     * @param newAidRat34AidNumDiffScore 3&4星主播签约数量趋势分值
     */
    public void setNewAidRat34AidNumDiffScore(Integer newAidRat34AidNumDiffScore) {
        this.newAidRat34AidNumDiffScore = newAidRat34AidNumDiffScore;
    }

    /**
     * 获取3&4星主播签约数量趋势下一等级分值
     * @return new_aid_rat_3_4_aid_num_diff_next_score 3&4星主播签约数量趋势下一等级分值
     */
    public Integer getNewAidRat34AidNumDiffNextScore() {
        return newAidRat34AidNumDiffNextScore;
    }

    /**
     * 设置3&4星主播签约数量趋势下一等级分值
     * @param newAidRat34AidNumDiffNextScore 3&4星主播签约数量趋势下一等级分值
     */
    public void setNewAidRat34AidNumDiffNextScore(Integer newAidRat34AidNumDiffNextScore) {
        this.newAidRat34AidNumDiffNextScore = newAidRat34AidNumDiffNextScore;
    }

    /**
     * 获取3&4星主播签约数量趋势目标差值
     * @return new_aid_rat_3_4_aid_num_diff_trgt_diff 3&4星主播签约数量趋势目标差值
     */
    public Long getNewAidRat34AidNumDiffTrgtDiff() {
        return newAidRat34AidNumDiffTrgtDiff;
    }

    /**
     * 设置3&4星主播签约数量趋势目标差值
     * @param newAidRat34AidNumDiffTrgtDiff 3&4星主播签约数量趋势目标差值
     */
    public void setNewAidRat34AidNumDiffTrgtDiff(Long newAidRat34AidNumDiffTrgtDiff) {
        this.newAidRat34AidNumDiffTrgtDiff = newAidRat34AidNumDiffTrgtDiff;
    }

    /**
     * 获取升级高优主播数趋势
     * @return upgrd_high_aid_num_diff 升级高优主播数趋势
     */
    public Integer getUpgrdHighAidNumDiff() {
        return upgrdHighAidNumDiff;
    }

    /**
     * 设置升级高优主播数趋势
     * @param upgrdHighAidNumDiff 升级高优主播数趋势
     */
    public void setUpgrdHighAidNumDiff(Integer upgrdHighAidNumDiff) {
        this.upgrdHighAidNumDiff = upgrdHighAidNumDiff;
    }

    /**
     * 获取升级高优主播数趋势分值
     * @return upgrd_high_aid_num_diff_score 升级高优主播数趋势分值
     */
    public Integer getUpgrdHighAidNumDiffScore() {
        return upgrdHighAidNumDiffScore;
    }

    /**
     * 设置升级高优主播数趋势分值
     * @param upgrdHighAidNumDiffScore 升级高优主播数趋势分值
     */
    public void setUpgrdHighAidNumDiffScore(Integer upgrdHighAidNumDiffScore) {
        this.upgrdHighAidNumDiffScore = upgrdHighAidNumDiffScore;
    }

    /**
     * 获取升级高优主播数趋势下一等级分值
     * @return upgrd_high_aid_num_diff_next_score 升级高优主播数趋势下一等级分值
     */
    public Integer getUpgrdHighAidNumDiffNextScore() {
        return upgrdHighAidNumDiffNextScore;
    }

    /**
     * 设置升级高优主播数趋势下一等级分值
     * @param upgrdHighAidNumDiffNextScore 升级高优主播数趋势下一等级分值
     */
    public void setUpgrdHighAidNumDiffNextScore(Integer upgrdHighAidNumDiffNextScore) {
        this.upgrdHighAidNumDiffNextScore = upgrdHighAidNumDiffNextScore;
    }

    /**
     * 获取升级高优主播数趋势目标差值
     * @return upgrd_high_aid_num_diff_trgt_diff 升级高优主播数趋势目标差值
     */
    public Integer getUpgrdHighAidNumDiffTrgtDiff() {
        return upgrdHighAidNumDiffTrgtDiff;
    }

    /**
     * 设置升级高优主播数趋势目标差值
     * @param upgrdHighAidNumDiffTrgtDiff 升级高优主播数趋势目标差值
     */
    public void setUpgrdHighAidNumDiffTrgtDiff(Integer upgrdHighAidNumDiffTrgtDiff) {
        this.upgrdHighAidNumDiffTrgtDiff = upgrdHighAidNumDiffTrgtDiff;
    }

    /**
     * 获取升级腰部主播数趋势
     * @return upgrd_waist_aid_num_diff 升级腰部主播数趋势
     */
    public Integer getUpgrdWaistAidNumDiff() {
        return upgrdWaistAidNumDiff;
    }

    /**
     * 设置升级腰部主播数趋势
     * @param upgrdWaistAidNumDiff 升级腰部主播数趋势
     */
    public void setUpgrdWaistAidNumDiff(Integer upgrdWaistAidNumDiff) {
        this.upgrdWaistAidNumDiff = upgrdWaistAidNumDiff;
    }

    /**
     * 获取升级腰部主播数趋势分值
     * @return upgrd_waist_aid_num_diff_score 升级腰部主播数趋势分值
     */
    public Integer getUpgrdWaistAidNumDiffScore() {
        return upgrdWaistAidNumDiffScore;
    }

    /**
     * 设置升级腰部主播数趋势分值
     * @param upgrdWaistAidNumDiffScore 升级腰部主播数趋势分值
     */
    public void setUpgrdWaistAidNumDiffScore(Integer upgrdWaistAidNumDiffScore) {
        this.upgrdWaistAidNumDiffScore = upgrdWaistAidNumDiffScore;
    }

    /**
     * 获取升级腰部主播数趋势下一等级分值
     * @return upgrd_waist_aid_num_diff_next_score 升级腰部主播数趋势下一等级分值
     */
    public Integer getUpgrdWaistAidNumDiffNextScore() {
        return upgrdWaistAidNumDiffNextScore;
    }

    /**
     * 设置升级腰部主播数趋势下一等级分值
     * @param upgrdWaistAidNumDiffNextScore 升级腰部主播数趋势下一等级分值
     */
    public void setUpgrdWaistAidNumDiffNextScore(Integer upgrdWaistAidNumDiffNextScore) {
        this.upgrdWaistAidNumDiffNextScore = upgrdWaistAidNumDiffNextScore;
    }

    /**
     * 获取升级腰部主播数趋势目标差值
     * @return upgrd_waist_aid_num_diff_trgt_diff 升级腰部主播数趋势目标差值
     */
    public Integer getUpgrdWaistAidNumDiffTrgtDiff() {
        return upgrdWaistAidNumDiffTrgtDiff;
    }

    /**
     * 设置升级腰部主播数趋势目标差值
     * @param upgrdWaistAidNumDiffTrgtDiff 升级腰部主播数趋势目标差值
     */
    public void setUpgrdWaistAidNumDiffTrgtDiff(Integer upgrdWaistAidNumDiffTrgtDiff) {
        this.upgrdWaistAidNumDiffTrgtDiff = upgrdWaistAidNumDiffTrgtDiff;
    }

    /**
     * 获取月日均高优有效开播主播数
     * @return avg_high_valid_live_aid_num 月日均高优有效开播主播数
     */
    public Double getAvgHighValidLiveAidNum() {
        return avgHighValidLiveAidNum;
    }

    /**
     * 设置月日均高优有效开播主播数
     * @param avgHighValidLiveAidNum 月日均高优有效开播主播数
     */
    public void setAvgHighValidLiveAidNum(Double avgHighValidLiveAidNum) {
        this.avgHighValidLiveAidNum = avgHighValidLiveAidNum;
    }

    /**
     * 获取月日均高优有效开播主播数排名
     * @return avg_high_valid_live_aid_num_rn 月日均高优有效开播主播数排名
     */
    public Long getAvgHighValidLiveAidNumRn() {
        return avgHighValidLiveAidNumRn;
    }

    /**
     * 设置月日均高优有效开播主播数排名
     * @param avgHighValidLiveAidNumRn 月日均高优有效开播主播数排名
     */
    public void setAvgHighValidLiveAidNumRn(Long avgHighValidLiveAidNumRn) {
        this.avgHighValidLiveAidNumRn = avgHighValidLiveAidNumRn;
    }

    /**
     * 获取月日均高优有效开播主播数排名分值
     * @return avg_high_valid_live_aid_num_rn_score 月日均高优有效开播主播数排名分值
     */
    public Integer getAvgHighValidLiveAidNumRnScore() {
        return avgHighValidLiveAidNumRnScore;
    }

    /**
     * 设置月日均高优有效开播主播数排名分值
     * @param avgHighValidLiveAidNumRnScore 月日均高优有效开播主播数排名分值
     */
    public void setAvgHighValidLiveAidNumRnScore(Integer avgHighValidLiveAidNumRnScore) {
        this.avgHighValidLiveAidNumRnScore = avgHighValidLiveAidNumRnScore;
    }

    /**
     * 获取月日均高优有效开播主播数排名下一等级分值
     * @return avg_high_valid_live_aid_num_rn_next_score 月日均高优有效开播主播数排名下一等级分值
     */
    public Integer getAvgHighValidLiveAidNumRnNextScore() {
        return avgHighValidLiveAidNumRnNextScore;
    }

    /**
     * 设置月日均高优有效开播主播数排名下一等级分值
     * @param avgHighValidLiveAidNumRnNextScore 月日均高优有效开播主播数排名下一等级分值
     */
    public void setAvgHighValidLiveAidNumRnNextScore(Integer avgHighValidLiveAidNumRnNextScore) {
        this.avgHighValidLiveAidNumRnNextScore = avgHighValidLiveAidNumRnNextScore;
    }

    /**
     * 获取月日均高优有效开播主播数目标差值
     * @return avg_high_valid_live_aid_num_trgt_diff 月日均高优有效开播主播数目标差值
     */
    public Double getAvgHighValidLiveAidNumTrgtDiff() {
        return avgHighValidLiveAidNumTrgtDiff;
    }

    /**
     * 设置月日均高优有效开播主播数目标差值
     * @param avgHighValidLiveAidNumTrgtDiff 月日均高优有效开播主播数目标差值
     */
    public void setAvgHighValidLiveAidNumTrgtDiff(Double avgHighValidLiveAidNumTrgtDiff) {
        this.avgHighValidLiveAidNumTrgtDiff = avgHighValidLiveAidNumTrgtDiff;
    }

    /**
     * 获取月日均高优有效开播主播数趋势
     * @return avg_high_valid_live_aid_num_diff 月日均高优有效开播主播数趋势
     */
    public Double getAvgHighValidLiveAidNumDiff() {
        return avgHighValidLiveAidNumDiff;
    }

    /**
     * 设置月日均高优有效开播主播数趋势
     * @param avgHighValidLiveAidNumDiff 月日均高优有效开播主播数趋势
     */
    public void setAvgHighValidLiveAidNumDiff(Double avgHighValidLiveAidNumDiff) {
        this.avgHighValidLiveAidNumDiff = avgHighValidLiveAidNumDiff;
    }

    /**
     * 获取月日均高优有效开播主播数趋势分值
     * @return avg_high_valid_live_aid_num_diff_score 月日均高优有效开播主播数趋势分值
     */
    public Integer getAvgHighValidLiveAidNumDiffScore() {
        return avgHighValidLiveAidNumDiffScore;
    }

    /**
     * 设置月日均高优有效开播主播数趋势分值
     * @param avgHighValidLiveAidNumDiffScore 月日均高优有效开播主播数趋势分值
     */
    public void setAvgHighValidLiveAidNumDiffScore(Integer avgHighValidLiveAidNumDiffScore) {
        this.avgHighValidLiveAidNumDiffScore = avgHighValidLiveAidNumDiffScore;
    }

    /**
     * 获取月日均高优有效开播主播数趋势下一等级分值
     * @return avg_high_valid_live_aid_num_diff_next_score 月日均高优有效开播主播数趋势下一等级分值
     */
    public Integer getAvgHighValidLiveAidNumDiffNextScore() {
        return avgHighValidLiveAidNumDiffNextScore;
    }

    /**
     * 设置月日均高优有效开播主播数趋势下一等级分值
     * @param avgHighValidLiveAidNumDiffNextScore 月日均高优有效开播主播数趋势下一等级分值
     */
    public void setAvgHighValidLiveAidNumDiffNextScore(Integer avgHighValidLiveAidNumDiffNextScore) {
        this.avgHighValidLiveAidNumDiffNextScore = avgHighValidLiveAidNumDiffNextScore;
    }

    /**
     * 获取月日均高优有效开播主播数趋势目标差值
     * @return avg_high_valid_live_aid_num_diff_trgt_diff 月日均高优有效开播主播数趋势目标差值
     */
    public Double getAvgHighValidLiveAidNumDiffTrgtDiff() {
        return avgHighValidLiveAidNumDiffTrgtDiff;
    }

    /**
     * 设置月日均高优有效开播主播数趋势目标差值
     * @param avgHighValidLiveAidNumDiffTrgtDiff 月日均高优有效开播主播数趋势目标差值
     */
    public void setAvgHighValidLiveAidNumDiffTrgtDiff(Double avgHighValidLiveAidNumDiffTrgtDiff) {
        this.avgHighValidLiveAidNumDiffTrgtDiff = avgHighValidLiveAidNumDiffTrgtDiff;
    }

    /**
     * 获取月日均腰部有效开播主播数
     * @return avg_waist_valid_live_aid_num 月日均腰部有效开播主播数
     */
    public Double getAvgWaistValidLiveAidNum() {
        return avgWaistValidLiveAidNum;
    }

    /**
     * 设置月日均腰部有效开播主播数
     * @param avgWaistValidLiveAidNum 月日均腰部有效开播主播数
     */
    public void setAvgWaistValidLiveAidNum(Double avgWaistValidLiveAidNum) {
        this.avgWaistValidLiveAidNum = avgWaistValidLiveAidNum;
    }

    /**
     * 获取月日均腰部有效开播主播数排名
     * @return avg_waist_valid_live_aid_num_rn 月日均腰部有效开播主播数排名
     */
    public Long getAvgWaistValidLiveAidNumRn() {
        return avgWaistValidLiveAidNumRn;
    }

    /**
     * 设置月日均腰部有效开播主播数排名
     * @param avgWaistValidLiveAidNumRn 月日均腰部有效开播主播数排名
     */
    public void setAvgWaistValidLiveAidNumRn(Long avgWaistValidLiveAidNumRn) {
        this.avgWaistValidLiveAidNumRn = avgWaistValidLiveAidNumRn;
    }

    /**
     * 获取月日均腰部有效开播主播数排名分值
     * @return avg_waist_valid_live_aid_num_rn_score 月日均腰部有效开播主播数排名分值
     */
    public Integer getAvgWaistValidLiveAidNumRnScore() {
        return avgWaistValidLiveAidNumRnScore;
    }

    /**
     * 设置月日均腰部有效开播主播数排名分值
     * @param avgWaistValidLiveAidNumRnScore 月日均腰部有效开播主播数排名分值
     */
    public void setAvgWaistValidLiveAidNumRnScore(Integer avgWaistValidLiveAidNumRnScore) {
        this.avgWaistValidLiveAidNumRnScore = avgWaistValidLiveAidNumRnScore;
    }

    /**
     * 获取月日均腰部有效开播主播数排名下一等级分值
     * @return avg_waist_valid_live_aid_num_rn_next_score 月日均腰部有效开播主播数排名下一等级分值
     */
    public Integer getAvgWaistValidLiveAidNumRnNextScore() {
        return avgWaistValidLiveAidNumRnNextScore;
    }

    /**
     * 设置月日均腰部有效开播主播数排名下一等级分值
     * @param avgWaistValidLiveAidNumRnNextScore 月日均腰部有效开播主播数排名下一等级分值
     */
    public void setAvgWaistValidLiveAidNumRnNextScore(Integer avgWaistValidLiveAidNumRnNextScore) {
        this.avgWaistValidLiveAidNumRnNextScore = avgWaistValidLiveAidNumRnNextScore;
    }

    /**
     * 获取月日均腰部有效开播主播数目标差值
     * @return avg_waist_valid_live_aid_num_trgt_diff 月日均腰部有效开播主播数目标差值
     */
    public Double getAvgWaistValidLiveAidNumTrgtDiff() {
        return avgWaistValidLiveAidNumTrgtDiff;
    }

    /**
     * 设置月日均腰部有效开播主播数目标差值
     * @param avgWaistValidLiveAidNumTrgtDiff 月日均腰部有效开播主播数目标差值
     */
    public void setAvgWaistValidLiveAidNumTrgtDiff(Double avgWaistValidLiveAidNumTrgtDiff) {
        this.avgWaistValidLiveAidNumTrgtDiff = avgWaistValidLiveAidNumTrgtDiff;
    }

    /**
     * 获取月日均腰部有效开播主播数趋势
     * @return avg_waist_valid_live_aid_num_diff 月日均腰部有效开播主播数趋势
     */
    public Double getAvgWaistValidLiveAidNumDiff() {
        return avgWaistValidLiveAidNumDiff;
    }

    /**
     * 设置月日均腰部有效开播主播数趋势
     * @param avgWaistValidLiveAidNumDiff 月日均腰部有效开播主播数趋势
     */
    public void setAvgWaistValidLiveAidNumDiff(Double avgWaistValidLiveAidNumDiff) {
        this.avgWaistValidLiveAidNumDiff = avgWaistValidLiveAidNumDiff;
    }

    /**
     * 获取月日均腰部有效开播主播数趋势分值
     * @return avg_waist_valid_live_aid_num_diff_score 月日均腰部有效开播主播数趋势分值
     */
    public Integer getAvgWaistValidLiveAidNumDiffScore() {
        return avgWaistValidLiveAidNumDiffScore;
    }

    /**
     * 设置月日均腰部有效开播主播数趋势分值
     * @param avgWaistValidLiveAidNumDiffScore 月日均腰部有效开播主播数趋势分值
     */
    public void setAvgWaistValidLiveAidNumDiffScore(Integer avgWaistValidLiveAidNumDiffScore) {
        this.avgWaistValidLiveAidNumDiffScore = avgWaistValidLiveAidNumDiffScore;
    }

    /**
     * 获取月日均腰部有效开播主播数趋势下一等级分值
     * @return avg_waist_valid_live_aid_num_diff_next_score 月日均腰部有效开播主播数趋势下一等级分值
     */
    public Integer getAvgWaistValidLiveAidNumDiffNextScore() {
        return avgWaistValidLiveAidNumDiffNextScore;
    }

    /**
     * 设置月日均腰部有效开播主播数趋势下一等级分值
     * @param avgWaistValidLiveAidNumDiffNextScore 月日均腰部有效开播主播数趋势下一等级分值
     */
    public void setAvgWaistValidLiveAidNumDiffNextScore(Integer avgWaistValidLiveAidNumDiffNextScore) {
        this.avgWaistValidLiveAidNumDiffNextScore = avgWaistValidLiveAidNumDiffNextScore;
    }

    /**
     * 获取月日均腰部有效开播主播数趋势目标差值
     * @return avg_waist_valid_live_aid_num_diff_trgt_diff 月日均腰部有效开播主播数趋势目标差值
     */
    public Double getAvgWaistValidLiveAidNumDiffTrgtDiff() {
        return avgWaistValidLiveAidNumDiffTrgtDiff;
    }

    /**
     * 设置月日均腰部有效开播主播数趋势目标差值
     * @param avgWaistValidLiveAidNumDiffTrgtDiff 月日均腰部有效开播主播数趋势目标差值
     */
    public void setAvgWaistValidLiveAidNumDiffTrgtDiff(Double avgWaistValidLiveAidNumDiffTrgtDiff) {
        this.avgWaistValidLiveAidNumDiffTrgtDiff = avgWaistValidLiveAidNumDiffTrgtDiff;
    }

    /**
     * 获取新转存主播开播率
     * @return avg_nto_valid_live_aid_rate 新转存主播开播率
     */
    public Double getAvgNtoValidLiveAidRate() {
        return avgNtoValidLiveAidRate;
    }

    /**
     * 设置新转存主播开播率
     * @param avgNtoValidLiveAidRate 新转存主播开播率
     */
    public void setAvgNtoValidLiveAidRate(Double avgNtoValidLiveAidRate) {
        this.avgNtoValidLiveAidRate = avgNtoValidLiveAidRate;
    }

    /**
     * 获取新转存主播开播率排名
     * @return avg_nto_valid_live_aid_rate_rn 新转存主播开播率排名
     */
    public Long getAvgNtoValidLiveAidRateRn() {
        return avgNtoValidLiveAidRateRn;
    }

    /**
     * 设置新转存主播开播率排名
     * @param avgNtoValidLiveAidRateRn 新转存主播开播率排名
     */
    public void setAvgNtoValidLiveAidRateRn(Long avgNtoValidLiveAidRateRn) {
        this.avgNtoValidLiveAidRateRn = avgNtoValidLiveAidRateRn;
    }

    /**
     * 获取新转存主播开播率排名分值
     * @return avg_nto_valid_live_aid_rate_rn_score 新转存主播开播率排名分值
     */
    public Integer getAvgNtoValidLiveAidRateRnScore() {
        return avgNtoValidLiveAidRateRnScore;
    }

    /**
     * 设置新转存主播开播率排名分值
     * @param avgNtoValidLiveAidRateRnScore 新转存主播开播率排名分值
     */
    public void setAvgNtoValidLiveAidRateRnScore(Integer avgNtoValidLiveAidRateRnScore) {
        this.avgNtoValidLiveAidRateRnScore = avgNtoValidLiveAidRateRnScore;
    }

    /**
     * 获取新转存主播开播率排名下一等级分值
     * @return avg_nto_valid_live_aid_rate_rn_next_score 新转存主播开播率排名下一等级分值
     */
    public Integer getAvgNtoValidLiveAidRateRnNextScore() {
        return avgNtoValidLiveAidRateRnNextScore;
    }

    /**
     * 设置新转存主播开播率排名下一等级分值
     * @param avgNtoValidLiveAidRateRnNextScore 新转存主播开播率排名下一等级分值
     */
    public void setAvgNtoValidLiveAidRateRnNextScore(Integer avgNtoValidLiveAidRateRnNextScore) {
        this.avgNtoValidLiveAidRateRnNextScore = avgNtoValidLiveAidRateRnNextScore;
    }

    /**
     * 获取新转存主播开播率目标差值
     * @return avg_nto_valid_live_aid_rate_trgt_diff 新转存主播开播率目标差值
     */
    public Double getAvgNtoValidLiveAidRateTrgtDiff() {
        return avgNtoValidLiveAidRateTrgtDiff;
    }

    /**
     * 设置新转存主播开播率目标差值
     * @param avgNtoValidLiveAidRateTrgtDiff 新转存主播开播率目标差值
     */
    public void setAvgNtoValidLiveAidRateTrgtDiff(Double avgNtoValidLiveAidRateTrgtDiff) {
        this.avgNtoValidLiveAidRateTrgtDiff = avgNtoValidLiveAidRateTrgtDiff;
    }

    /**
     * 获取存量主播开播率
     * @return avg_stock_valid_live_aid_rate 存量主播开播率
     */
    public Double getAvgStockValidLiveAidRate() {
        return avgStockValidLiveAidRate;
    }

    /**
     * 设置存量主播开播率
     * @param avgStockValidLiveAidRate 存量主播开播率
     */
    public void setAvgStockValidLiveAidRate(Double avgStockValidLiveAidRate) {
        this.avgStockValidLiveAidRate = avgStockValidLiveAidRate;
    }

    /**
     * 获取存量主播开播率排名
     * @return avg_stock_valid_live_aid_rate_rn 存量主播开播率排名
     */
    public Long getAvgStockValidLiveAidRateRn() {
        return avgStockValidLiveAidRateRn;
    }

    /**
     * 设置存量主播开播率排名
     * @param avgStockValidLiveAidRateRn 存量主播开播率排名
     */
    public void setAvgStockValidLiveAidRateRn(Long avgStockValidLiveAidRateRn) {
        this.avgStockValidLiveAidRateRn = avgStockValidLiveAidRateRn;
    }

    /**
     * 获取存量主播开播率排名分值
     * @return avg_stock_valid_live_aid_rate_rn_score 存量主播开播率排名分值
     */
    public Integer getAvgStockValidLiveAidRateRnScore() {
        return avgStockValidLiveAidRateRnScore;
    }

    /**
     * 设置存量主播开播率排名分值
     * @param avgStockValidLiveAidRateRnScore 存量主播开播率排名分值
     */
    public void setAvgStockValidLiveAidRateRnScore(Integer avgStockValidLiveAidRateRnScore) {
        this.avgStockValidLiveAidRateRnScore = avgStockValidLiveAidRateRnScore;
    }

    /**
     * 获取存量主播开播率排名下一等级分值
     * @return avg_stock_valid_live_aid_rate_rn_next_score 存量主播开播率排名下一等级分值
     */
    public Integer getAvgStockValidLiveAidRateRnNextScore() {
        return avgStockValidLiveAidRateRnNextScore;
    }

    /**
     * 设置存量主播开播率排名下一等级分值
     * @param avgStockValidLiveAidRateRnNextScore 存量主播开播率排名下一等级分值
     */
    public void setAvgStockValidLiveAidRateRnNextScore(Integer avgStockValidLiveAidRateRnNextScore) {
        this.avgStockValidLiveAidRateRnNextScore = avgStockValidLiveAidRateRnNextScore;
    }

    /**
     * 获取存量主播开播率目标差值
     * @return avg_stock_valid_live_aid_rate_trgt_diff 存量主播开播率目标差值
     */
    public Double getAvgStockValidLiveAidRateTrgtDiff() {
        return avgStockValidLiveAidRateTrgtDiff;
    }

    /**
     * 设置存量主播开播率目标差值
     * @param avgStockValidLiveAidRateTrgtDiff 存量主播开播率目标差值
     */
    public void setAvgStockValidLiveAidRateTrgtDiff(Double avgStockValidLiveAidRateTrgtDiff) {
        this.avgStockValidLiveAidRateTrgtDiff = avgStockValidLiveAidRateTrgtDiff;
    }

    /**
     * 获取新授权金牌艺人
     * @return new_auth_golden_aid_num 新授权金牌艺人
     */
    public Long getNewAuthGoldenAidNum() {
        return newAuthGoldenAidNum;
    }

    /**
     * 设置新授权金牌艺人
     * @param newAuthGoldenAidNum 新授权金牌艺人
     */
    public void setNewAuthGoldenAidNum(Long newAuthGoldenAidNum) {
        this.newAuthGoldenAidNum = newAuthGoldenAidNum;
    }

    /**
     * 获取新授权金牌艺人排名
     * @return new_auth_golden_aid_num_rn 新授权金牌艺人排名
     */
    public Long getNewAuthGoldenAidNumRn() {
        return newAuthGoldenAidNumRn;
    }

    /**
     * 设置新授权金牌艺人排名
     * @param newAuthGoldenAidNumRn 新授权金牌艺人排名
     */
    public void setNewAuthGoldenAidNumRn(Long newAuthGoldenAidNumRn) {
        this.newAuthGoldenAidNumRn = newAuthGoldenAidNumRn;
    }

    /**
     * 获取新授权金牌艺人排名分值
     * @return new_auth_golden_aid_num_rn_score 新授权金牌艺人排名分值
     */
    public Integer getNewAuthGoldenAidNumRnScore() {
        return newAuthGoldenAidNumRnScore;
    }

    /**
     * 设置新授权金牌艺人排名分值
     * @param newAuthGoldenAidNumRnScore 新授权金牌艺人排名分值
     */
    public void setNewAuthGoldenAidNumRnScore(Integer newAuthGoldenAidNumRnScore) {
        this.newAuthGoldenAidNumRnScore = newAuthGoldenAidNumRnScore;
    }

    /**
     * 获取新授权金牌艺人排名下一等级分值
     * @return new_auth_golden_aid_num_rn_next_score 新授权金牌艺人排名下一等级分值
     */
    public Integer getNewAuthGoldenAidNumRnNextScore() {
        return newAuthGoldenAidNumRnNextScore;
    }

    /**
     * 设置新授权金牌艺人排名下一等级分值
     * @param newAuthGoldenAidNumRnNextScore 新授权金牌艺人排名下一等级分值
     */
    public void setNewAuthGoldenAidNumRnNextScore(Integer newAuthGoldenAidNumRnNextScore) {
        this.newAuthGoldenAidNumRnNextScore = newAuthGoldenAidNumRnNextScore;
    }

    /**
     * 获取新授权金牌艺人目标差值
     * @return new_auth_golden_aid_num_trgt_diff 新授权金牌艺人目标差值
     */
    public Long getNewAuthGoldenAidNumTrgtDiff() {
        return newAuthGoldenAidNumTrgtDiff;
    }

    /**
     * 设置新授权金牌艺人目标差值
     * @param newAuthGoldenAidNumTrgtDiff 新授权金牌艺人目标差值
     */
    public void setNewAuthGoldenAidNumTrgtDiff(Long newAuthGoldenAidNumTrgtDiff) {
        this.newAuthGoldenAidNumTrgtDiff = newAuthGoldenAidNumTrgtDiff;
    }

    /**
     * 获取新主播蓝钻收入
     * @return valid_live_new_aid_incm_amt 新主播蓝钻收入
     */
    public Double getValidLiveNewAidIncmAmt() {
        return validLiveNewAidIncmAmt;
    }

    /**
     * 设置新主播蓝钻收入
     * @param validLiveNewAidIncmAmt 新主播蓝钻收入
     */
    public void setValidLiveNewAidIncmAmt(Double validLiveNewAidIncmAmt) {
        this.validLiveNewAidIncmAmt = validLiveNewAidIncmAmt;
    }

    /**
     * 获取新主播蓝钻收入排名
     * @return valid_live_new_aid_incm_amt_rn 新主播蓝钻收入排名
     */
    public Long getValidLiveNewAidIncmAmtRn() {
        return validLiveNewAidIncmAmtRn;
    }

    /**
     * 设置新主播蓝钻收入排名
     * @param validLiveNewAidIncmAmtRn 新主播蓝钻收入排名
     */
    public void setValidLiveNewAidIncmAmtRn(Long validLiveNewAidIncmAmtRn) {
        this.validLiveNewAidIncmAmtRn = validLiveNewAidIncmAmtRn;
    }

    /**
     * 获取新主播蓝钻收入排名分值
     * @return valid_live_new_aid_incm_amt_rn_score 新主播蓝钻收入排名分值
     */
    public Integer getValidLiveNewAidIncmAmtRnScore() {
        return validLiveNewAidIncmAmtRnScore;
    }

    /**
     * 设置新主播蓝钻收入排名分值
     * @param validLiveNewAidIncmAmtRnScore 新主播蓝钻收入排名分值
     */
    public void setValidLiveNewAidIncmAmtRnScore(Integer validLiveNewAidIncmAmtRnScore) {
        this.validLiveNewAidIncmAmtRnScore = validLiveNewAidIncmAmtRnScore;
    }

    /**
     * 获取新主播蓝钻收入排名下一等级分值
     * @return valid_live_new_aid_incm_amt_rn_next_score 新主播蓝钻收入排名下一等级分值
     */
    public Integer getValidLiveNewAidIncmAmtRnNextScore() {
        return validLiveNewAidIncmAmtRnNextScore;
    }

    /**
     * 设置新主播蓝钻收入排名下一等级分值
     * @param validLiveNewAidIncmAmtRnNextScore 新主播蓝钻收入排名下一等级分值
     */
    public void setValidLiveNewAidIncmAmtRnNextScore(Integer validLiveNewAidIncmAmtRnNextScore) {
        this.validLiveNewAidIncmAmtRnNextScore = validLiveNewAidIncmAmtRnNextScore;
    }

    /**
     * 获取新主播蓝钻收入目标差值
     * @return valid_live_new_aid_incm_amt_trgt_diff 新主播蓝钻收入目标差值
     */
    public Double getValidLiveNewAidIncmAmtTrgtDiff() {
        return validLiveNewAidIncmAmtTrgtDiff;
    }

    /**
     * 设置新主播蓝钻收入目标差值
     * @param validLiveNewAidIncmAmtTrgtDiff 新主播蓝钻收入目标差值
     */
    public void setValidLiveNewAidIncmAmtTrgtDiff(Double validLiveNewAidIncmAmtTrgtDiff) {
        this.validLiveNewAidIncmAmtTrgtDiff = validLiveNewAidIncmAmtTrgtDiff;
    }

    /**
     * 获取新主播蓝钻收入趋势
     * @return valid_live_new_aid_incm_amt_diff 新主播蓝钻收入趋势
     */
    public Double getValidLiveNewAidIncmAmtDiff() {
        return validLiveNewAidIncmAmtDiff;
    }

    /**
     * 设置新主播蓝钻收入趋势
     * @param validLiveNewAidIncmAmtDiff 新主播蓝钻收入趋势
     */
    public void setValidLiveNewAidIncmAmtDiff(Double validLiveNewAidIncmAmtDiff) {
        this.validLiveNewAidIncmAmtDiff = validLiveNewAidIncmAmtDiff;
    }

    /**
     * 获取新主播蓝钻收入趋势分值
     * @return valid_live_new_aid_incm_amt_diff_score 新主播蓝钻收入趋势分值
     */
    public Integer getValidLiveNewAidIncmAmtDiffScore() {
        return validLiveNewAidIncmAmtDiffScore;
    }

    /**
     * 设置新主播蓝钻收入趋势分值
     * @param validLiveNewAidIncmAmtDiffScore 新主播蓝钻收入趋势分值
     */
    public void setValidLiveNewAidIncmAmtDiffScore(Integer validLiveNewAidIncmAmtDiffScore) {
        this.validLiveNewAidIncmAmtDiffScore = validLiveNewAidIncmAmtDiffScore;
    }

    /**
     * 获取新主播蓝钻收入趋势下一等级分值
     * @return valid_live_new_aid_incm_amt_diff_next_score 新主播蓝钻收入趋势下一等级分值
     */
    public Integer getValidLiveNewAidIncmAmtDiffNextScore() {
        return validLiveNewAidIncmAmtDiffNextScore;
    }

    /**
     * 设置新主播蓝钻收入趋势下一等级分值
     * @param validLiveNewAidIncmAmtDiffNextScore 新主播蓝钻收入趋势下一等级分值
     */
    public void setValidLiveNewAidIncmAmtDiffNextScore(Integer validLiveNewAidIncmAmtDiffNextScore) {
        this.validLiveNewAidIncmAmtDiffNextScore = validLiveNewAidIncmAmtDiffNextScore;
    }

    /**
     * 获取新主播蓝钻收入趋势目标差值@@
     * @return valid_live_new_aid_incm_amt_diff_trgt_diff 新主播蓝钻收入趋势目标差值@@
     */
    public Double getValidLiveNewAidIncmAmtDiffTrgtDiff() {
        return validLiveNewAidIncmAmtDiffTrgtDiff;
    }

    /**
     * 设置新主播蓝钻收入趋势目标差值@@
     * @param validLiveNewAidIncmAmtDiffTrgtDiff 新主播蓝钻收入趋势目标差值@@
     */
    public void setValidLiveNewAidIncmAmtDiffTrgtDiff(Double validLiveNewAidIncmAmtDiffTrgtDiff) {
        this.validLiveNewAidIncmAmtDiffTrgtDiff = validLiveNewAidIncmAmtDiffTrgtDiff;
    }

    /**
     * 获取存量主播蓝钻收入
     * @return unvalid_live_new_aid_incm_amt 存量主播蓝钻收入
     */
    public Double getUnvalidLiveNewAidIncmAmt() {
        return unvalidLiveNewAidIncmAmt;
    }

    /**
     * 设置存量主播蓝钻收入
     * @param unvalidLiveNewAidIncmAmt 存量主播蓝钻收入
     */
    public void setUnvalidLiveNewAidIncmAmt(Double unvalidLiveNewAidIncmAmt) {
        this.unvalidLiveNewAidIncmAmt = unvalidLiveNewAidIncmAmt;
    }

    /**
     * 获取存量主播蓝钻收入排名
     * @return unvalid_live_new_aid_incm_amt_rn 存量主播蓝钻收入排名
     */
    public Long getUnvalidLiveNewAidIncmAmtRn() {
        return unvalidLiveNewAidIncmAmtRn;
    }

    /**
     * 设置存量主播蓝钻收入排名
     * @param unvalidLiveNewAidIncmAmtRn 存量主播蓝钻收入排名
     */
    public void setUnvalidLiveNewAidIncmAmtRn(Long unvalidLiveNewAidIncmAmtRn) {
        this.unvalidLiveNewAidIncmAmtRn = unvalidLiveNewAidIncmAmtRn;
    }

    /**
     * 获取存量主播蓝钻收入排名分值
     * @return unvalid_live_new_aid_incm_amt_rn_score 存量主播蓝钻收入排名分值
     */
    public Integer getUnvalidLiveNewAidIncmAmtRnScore() {
        return unvalidLiveNewAidIncmAmtRnScore;
    }

    /**
     * 设置存量主播蓝钻收入排名分值
     * @param unvalidLiveNewAidIncmAmtRnScore 存量主播蓝钻收入排名分值
     */
    public void setUnvalidLiveNewAidIncmAmtRnScore(Integer unvalidLiveNewAidIncmAmtRnScore) {
        this.unvalidLiveNewAidIncmAmtRnScore = unvalidLiveNewAidIncmAmtRnScore;
    }

    /**
     * 获取存量主播蓝钻收入排名下一等级分值
     * @return unvalid_live_new_aid_incm_amt_rn_next_score 存量主播蓝钻收入排名下一等级分值
     */
    public Integer getUnvalidLiveNewAidIncmAmtRnNextScore() {
        return unvalidLiveNewAidIncmAmtRnNextScore;
    }

    /**
     * 设置存量主播蓝钻收入排名下一等级分值
     * @param unvalidLiveNewAidIncmAmtRnNextScore 存量主播蓝钻收入排名下一等级分值
     */
    public void setUnvalidLiveNewAidIncmAmtRnNextScore(Integer unvalidLiveNewAidIncmAmtRnNextScore) {
        this.unvalidLiveNewAidIncmAmtRnNextScore = unvalidLiveNewAidIncmAmtRnNextScore;
    }

    /**
     * 获取存量主播蓝钻收入目标差值
     * @return unvalid_live_new_aid_incm_amt_trgt_diff 存量主播蓝钻收入目标差值
     */
    public Double getUnvalidLiveNewAidIncmAmtTrgtDiff() {
        return unvalidLiveNewAidIncmAmtTrgtDiff;
    }

    /**
     * 设置存量主播蓝钻收入目标差值
     * @param unvalidLiveNewAidIncmAmtTrgtDiff 存量主播蓝钻收入目标差值
     */
    public void setUnvalidLiveNewAidIncmAmtTrgtDiff(Double unvalidLiveNewAidIncmAmtTrgtDiff) {
        this.unvalidLiveNewAidIncmAmtTrgtDiff = unvalidLiveNewAidIncmAmtTrgtDiff;
    }

    /**
     * 获取存量主播蓝钻收入趋势
     * @return unvalid_live_new_aid_incm_amt_diff 存量主播蓝钻收入趋势
     */
    public Double getUnvalidLiveNewAidIncmAmtDiff() {
        return unvalidLiveNewAidIncmAmtDiff;
    }

    /**
     * 设置存量主播蓝钻收入趋势
     * @param unvalidLiveNewAidIncmAmtDiff 存量主播蓝钻收入趋势
     */
    public void setUnvalidLiveNewAidIncmAmtDiff(Double unvalidLiveNewAidIncmAmtDiff) {
        this.unvalidLiveNewAidIncmAmtDiff = unvalidLiveNewAidIncmAmtDiff;
    }

    /**
     * 获取存量主播蓝钻收入趋势分值
     * @return unvalid_live_new_aid_incm_amt_diff_score 存量主播蓝钻收入趋势分值
     */
    public Integer getUnvalidLiveNewAidIncmAmtDiffScore() {
        return unvalidLiveNewAidIncmAmtDiffScore;
    }

    /**
     * 设置存量主播蓝钻收入趋势分值
     * @param unvalidLiveNewAidIncmAmtDiffScore 存量主播蓝钻收入趋势分值
     */
    public void setUnvalidLiveNewAidIncmAmtDiffScore(Integer unvalidLiveNewAidIncmAmtDiffScore) {
        this.unvalidLiveNewAidIncmAmtDiffScore = unvalidLiveNewAidIncmAmtDiffScore;
    }

    /**
     * 获取存量主播蓝钻收入趋势下一等级分值
     * @return unvalid_live_new_aid_incm_amt_diff_next_score 存量主播蓝钻收入趋势下一等级分值
     */
    public Integer getUnvalidLiveNewAidIncmAmtDiffNextScore() {
        return unvalidLiveNewAidIncmAmtDiffNextScore;
    }

    /**
     * 设置存量主播蓝钻收入趋势下一等级分值
     * @param unvalidLiveNewAidIncmAmtDiffNextScore 存量主播蓝钻收入趋势下一等级分值
     */
    public void setUnvalidLiveNewAidIncmAmtDiffNextScore(Integer unvalidLiveNewAidIncmAmtDiffNextScore) {
        this.unvalidLiveNewAidIncmAmtDiffNextScore = unvalidLiveNewAidIncmAmtDiffNextScore;
    }

    /**
     * 获取存量主播蓝钻收入趋势目标差值
     * @return unvalid_live_new_aid_incm_amt_diff_trgt_diff 存量主播蓝钻收入趋势目标差值
     */
    public Double getUnvalidLiveNewAidIncmAmtDiffTrgtDiff() {
        return unvalidLiveNewAidIncmAmtDiffTrgtDiff;
    }

    /**
     * 设置存量主播蓝钻收入趋势目标差值
     * @param unvalidLiveNewAidIncmAmtDiffTrgtDiff 存量主播蓝钻收入趋势目标差值
     */
    public void setUnvalidLiveNewAidIncmAmtDiffTrgtDiff(Double unvalidLiveNewAidIncmAmtDiffTrgtDiff) {
        this.unvalidLiveNewAidIncmAmtDiffTrgtDiff = unvalidLiveNewAidIncmAmtDiffTrgtDiff;
    }

    /**
     * 获取存量主播蓝钻收入占比
     * @return unvalid_live_new_aid_incm_amt_rate_diff 存量主播蓝钻收入占比
     */
    public Double getUnvalidLiveNewAidIncmAmtRateDiff() {
        return unvalidLiveNewAidIncmAmtRateDiff;
    }

    /**
     * 设置存量主播蓝钻收入占比
     * @param unvalidLiveNewAidIncmAmtRateDiff 存量主播蓝钻收入占比
     */
    public void setUnvalidLiveNewAidIncmAmtRateDiff(Double unvalidLiveNewAidIncmAmtRateDiff) {
        this.unvalidLiveNewAidIncmAmtRateDiff = unvalidLiveNewAidIncmAmtRateDiff;
    }

    /**
     * 获取存量主播蓝钻收入占比分值
     * @return unvalid_live_new_aid_incm_amt_rate_diff_score 存量主播蓝钻收入占比分值
     */
    public Integer getUnvalidLiveNewAidIncmAmtRateDiffScore() {
        return unvalidLiveNewAidIncmAmtRateDiffScore;
    }

    /**
     * 设置存量主播蓝钻收入占比分值
     * @param unvalidLiveNewAidIncmAmtRateDiffScore 存量主播蓝钻收入占比分值
     */
    public void setUnvalidLiveNewAidIncmAmtRateDiffScore(Integer unvalidLiveNewAidIncmAmtRateDiffScore) {
        this.unvalidLiveNewAidIncmAmtRateDiffScore = unvalidLiveNewAidIncmAmtRateDiffScore;
    }

    /**
     * 获取存量主播蓝钻收入占比下一等级分值
     * @return unvalid_live_new_aid_incm_amt_rate_diff_next_score 存量主播蓝钻收入占比下一等级分值
     */
    public Integer getUnvalidLiveNewAidIncmAmtRateDiffNextScore() {
        return unvalidLiveNewAidIncmAmtRateDiffNextScore;
    }

    /**
     * 设置存量主播蓝钻收入占比下一等级分值
     * @param unvalidLiveNewAidIncmAmtRateDiffNextScore 存量主播蓝钻收入占比下一等级分值
     */
    public void setUnvalidLiveNewAidIncmAmtRateDiffNextScore(Integer unvalidLiveNewAidIncmAmtRateDiffNextScore) {
        this.unvalidLiveNewAidIncmAmtRateDiffNextScore = unvalidLiveNewAidIncmAmtRateDiffNextScore;
    }

    /**
     * 获取存量主播蓝钻收入占比目标差值
     * @return unvalid_live_new_aid_incm_amt_rate_diff_trgt_diff 存量主播蓝钻收入占比目标差值
     */
    public Double getUnvalidLiveNewAidIncmAmtRateDiffTrgtDiff() {
        return unvalidLiveNewAidIncmAmtRateDiffTrgtDiff;
    }

    /**
     * 设置存量主播蓝钻收入占比目标差值
     * @param unvalidLiveNewAidIncmAmtRateDiffTrgtDiff 存量主播蓝钻收入占比目标差值
     */
    public void setUnvalidLiveNewAidIncmAmtRateDiffTrgtDiff(Double unvalidLiveNewAidIncmAmtRateDiffTrgtDiff) {
        this.unvalidLiveNewAidIncmAmtRateDiffTrgtDiff = unvalidLiveNewAidIncmAmtRateDiffTrgtDiff;
    }

    /**
     * 获取公会健康分
     * @return guild_health_point 公会健康分
     */
    public Integer getGuildHealthPoint() {
        return guildHealthPoint;
    }

    /**
     * 设置公会健康分
     * @param guildHealthPoint 公会健康分
     */
    public void setGuildHealthPoint(Integer guildHealthPoint) {
        this.guildHealthPoint = guildHealthPoint;
    }

    /**
     * 获取 公会健康分分值
     * @return guild_health_point_score  公会健康分分值
     */
    public Integer getGuildHealthPointScore() {
        return guildHealthPointScore;
    }

    /**
     * 设置 公会健康分分值
     * @param guildHealthPointScore  公会健康分分值
     */
    public void setGuildHealthPointScore(Integer guildHealthPointScore) {
        this.guildHealthPointScore = guildHealthPointScore;
    }

    /**
     * 获取 公会健康分下一等级分值
     * @return guild_health_point_next_score  公会健康分下一等级分值
     */
    public Integer getGuildHealthPointNextScore() {
        return guildHealthPointNextScore;
    }

    /**
     * 设置 公会健康分下一等级分值
     * @param guildHealthPointNextScore  公会健康分下一等级分值
     */
    public void setGuildHealthPointNextScore(Integer guildHealthPointNextScore) {
        this.guildHealthPointNextScore = guildHealthPointNextScore;
    }

    /**
     * 获取 公会健康分目标差值
     * @return guild_health_point_trgt_diff  公会健康分目标差值
     */
    public Integer getGuildHealthPointTrgtDiff() {
        return guildHealthPointTrgtDiff;
    }

    /**
     * 设置 公会健康分目标差值
     * @param guildHealthPointTrgtDiff  公会健康分目标差值
     */
    public void setGuildHealthPointTrgtDiff(Integer guildHealthPointTrgtDiff) {
        this.guildHealthPointTrgtDiff = guildHealthPointTrgtDiff;
    }

    /**
     * 获取日期
     * @return dt 日期
     */
    public Date getDt() {
        return dt;
    }

    /**
     * 设置日期
     * @param dt 日期
     */
    public void setDt(Date dt) {
        this.dt = dt;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", guildCmpOwnrId=").append(guildCmpOwnrId);
        sb.append(", newAidRat34AidNum=").append(newAidRat34AidNum);
        sb.append(", newAidRat34AidNumRn=").append(newAidRat34AidNumRn);
        sb.append(", newAidRat34AidNumRnScore=").append(newAidRat34AidNumRnScore);
        sb.append(", newAidRat34AidNumRnNextScore=").append(newAidRat34AidNumRnNextScore);
        sb.append(", newAidRat34AidNumTrgtDiff=").append(newAidRat34AidNumTrgtDiff);
        sb.append(", newAidRat34AidNumDiff=").append(newAidRat34AidNumDiff);
        sb.append(", newAidRat34AidNumDiffScore=").append(newAidRat34AidNumDiffScore);
        sb.append(", newAidRat34AidNumDiffNextScore=").append(newAidRat34AidNumDiffNextScore);
        sb.append(", newAidRat34AidNumDiffTrgtDiff=").append(newAidRat34AidNumDiffTrgtDiff);
        sb.append(", upgrdHighAidNumDiff=").append(upgrdHighAidNumDiff);
        sb.append(", upgrdHighAidNumDiffScore=").append(upgrdHighAidNumDiffScore);
        sb.append(", upgrdHighAidNumDiffNextScore=").append(upgrdHighAidNumDiffNextScore);
        sb.append(", upgrdHighAidNumDiffTrgtDiff=").append(upgrdHighAidNumDiffTrgtDiff);
        sb.append(", upgrdWaistAidNumDiff=").append(upgrdWaistAidNumDiff);
        sb.append(", upgrdWaistAidNumDiffScore=").append(upgrdWaistAidNumDiffScore);
        sb.append(", upgrdWaistAidNumDiffNextScore=").append(upgrdWaistAidNumDiffNextScore);
        sb.append(", upgrdWaistAidNumDiffTrgtDiff=").append(upgrdWaistAidNumDiffTrgtDiff);
        sb.append(", avgHighValidLiveAidNum=").append(avgHighValidLiveAidNum);
        sb.append(", avgHighValidLiveAidNumRn=").append(avgHighValidLiveAidNumRn);
        sb.append(", avgHighValidLiveAidNumRnScore=").append(avgHighValidLiveAidNumRnScore);
        sb.append(", avgHighValidLiveAidNumRnNextScore=").append(avgHighValidLiveAidNumRnNextScore);
        sb.append(", avgHighValidLiveAidNumTrgtDiff=").append(avgHighValidLiveAidNumTrgtDiff);
        sb.append(", avgHighValidLiveAidNumDiff=").append(avgHighValidLiveAidNumDiff);
        sb.append(", avgHighValidLiveAidNumDiffScore=").append(avgHighValidLiveAidNumDiffScore);
        sb.append(", avgHighValidLiveAidNumDiffNextScore=").append(avgHighValidLiveAidNumDiffNextScore);
        sb.append(", avgHighValidLiveAidNumDiffTrgtDiff=").append(avgHighValidLiveAidNumDiffTrgtDiff);
        sb.append(", avgWaistValidLiveAidNum=").append(avgWaistValidLiveAidNum);
        sb.append(", avgWaistValidLiveAidNumRn=").append(avgWaistValidLiveAidNumRn);
        sb.append(", avgWaistValidLiveAidNumRnScore=").append(avgWaistValidLiveAidNumRnScore);
        sb.append(", avgWaistValidLiveAidNumRnNextScore=").append(avgWaistValidLiveAidNumRnNextScore);
        sb.append(", avgWaistValidLiveAidNumTrgtDiff=").append(avgWaistValidLiveAidNumTrgtDiff);
        sb.append(", avgWaistValidLiveAidNumDiff=").append(avgWaistValidLiveAidNumDiff);
        sb.append(", avgWaistValidLiveAidNumDiffScore=").append(avgWaistValidLiveAidNumDiffScore);
        sb.append(", avgWaistValidLiveAidNumDiffNextScore=").append(avgWaistValidLiveAidNumDiffNextScore);
        sb.append(", avgWaistValidLiveAidNumDiffTrgtDiff=").append(avgWaistValidLiveAidNumDiffTrgtDiff);
        sb.append(", avgNtoValidLiveAidRate=").append(avgNtoValidLiveAidRate);
        sb.append(", avgNtoValidLiveAidRateRn=").append(avgNtoValidLiveAidRateRn);
        sb.append(", avgNtoValidLiveAidRateRnScore=").append(avgNtoValidLiveAidRateRnScore);
        sb.append(", avgNtoValidLiveAidRateRnNextScore=").append(avgNtoValidLiveAidRateRnNextScore);
        sb.append(", avgNtoValidLiveAidRateTrgtDiff=").append(avgNtoValidLiveAidRateTrgtDiff);
        sb.append(", avgStockValidLiveAidRate=").append(avgStockValidLiveAidRate);
        sb.append(", avgStockValidLiveAidRateRn=").append(avgStockValidLiveAidRateRn);
        sb.append(", avgStockValidLiveAidRateRnScore=").append(avgStockValidLiveAidRateRnScore);
        sb.append(", avgStockValidLiveAidRateRnNextScore=").append(avgStockValidLiveAidRateRnNextScore);
        sb.append(", avgStockValidLiveAidRateTrgtDiff=").append(avgStockValidLiveAidRateTrgtDiff);
        sb.append(", newAuthGoldenAidNum=").append(newAuthGoldenAidNum);
        sb.append(", newAuthGoldenAidNumRn=").append(newAuthGoldenAidNumRn);
        sb.append(", newAuthGoldenAidNumRnScore=").append(newAuthGoldenAidNumRnScore);
        sb.append(", newAuthGoldenAidNumRnNextScore=").append(newAuthGoldenAidNumRnNextScore);
        sb.append(", newAuthGoldenAidNumTrgtDiff=").append(newAuthGoldenAidNumTrgtDiff);
        sb.append(", validLiveNewAidIncmAmt=").append(validLiveNewAidIncmAmt);
        sb.append(", validLiveNewAidIncmAmtRn=").append(validLiveNewAidIncmAmtRn);
        sb.append(", validLiveNewAidIncmAmtRnScore=").append(validLiveNewAidIncmAmtRnScore);
        sb.append(", validLiveNewAidIncmAmtRnNextScore=").append(validLiveNewAidIncmAmtRnNextScore);
        sb.append(", validLiveNewAidIncmAmtTrgtDiff=").append(validLiveNewAidIncmAmtTrgtDiff);
        sb.append(", validLiveNewAidIncmAmtDiff=").append(validLiveNewAidIncmAmtDiff);
        sb.append(", validLiveNewAidIncmAmtDiffScore=").append(validLiveNewAidIncmAmtDiffScore);
        sb.append(", validLiveNewAidIncmAmtDiffNextScore=").append(validLiveNewAidIncmAmtDiffNextScore);
        sb.append(", validLiveNewAidIncmAmtDiffTrgtDiff=").append(validLiveNewAidIncmAmtDiffTrgtDiff);
        sb.append(", unvalidLiveNewAidIncmAmt=").append(unvalidLiveNewAidIncmAmt);
        sb.append(", unvalidLiveNewAidIncmAmtRn=").append(unvalidLiveNewAidIncmAmtRn);
        sb.append(", unvalidLiveNewAidIncmAmtRnScore=").append(unvalidLiveNewAidIncmAmtRnScore);
        sb.append(", unvalidLiveNewAidIncmAmtRnNextScore=").append(unvalidLiveNewAidIncmAmtRnNextScore);
        sb.append(", unvalidLiveNewAidIncmAmtTrgtDiff=").append(unvalidLiveNewAidIncmAmtTrgtDiff);
        sb.append(", unvalidLiveNewAidIncmAmtDiff=").append(unvalidLiveNewAidIncmAmtDiff);
        sb.append(", unvalidLiveNewAidIncmAmtDiffScore=").append(unvalidLiveNewAidIncmAmtDiffScore);
        sb.append(", unvalidLiveNewAidIncmAmtDiffNextScore=").append(unvalidLiveNewAidIncmAmtDiffNextScore);
        sb.append(", unvalidLiveNewAidIncmAmtDiffTrgtDiff=").append(unvalidLiveNewAidIncmAmtDiffTrgtDiff);
        sb.append(", unvalidLiveNewAidIncmAmtRateDiff=").append(unvalidLiveNewAidIncmAmtRateDiff);
        sb.append(", unvalidLiveNewAidIncmAmtRateDiffScore=").append(unvalidLiveNewAidIncmAmtRateDiffScore);
        sb.append(", unvalidLiveNewAidIncmAmtRateDiffNextScore=").append(unvalidLiveNewAidIncmAmtRateDiffNextScore);
        sb.append(", unvalidLiveNewAidIncmAmtRateDiffTrgtDiff=").append(unvalidLiveNewAidIncmAmtRateDiffTrgtDiff);
        sb.append(", guildHealthPoint=").append(guildHealthPoint);
        sb.append(", guildHealthPointScore=").append(guildHealthPointScore);
        sb.append(", guildHealthPointNextScore=").append(guildHealthPointNextScore);
        sb.append(", guildHealthPointTrgtDiff=").append(guildHealthPointTrgtDiff);
        sb.append(", dt=").append(dt);
        sb.append("]");
        return sb.toString();
    }
}