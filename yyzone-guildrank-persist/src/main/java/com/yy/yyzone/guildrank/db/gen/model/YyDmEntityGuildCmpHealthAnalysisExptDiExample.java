package com.yy.yyzone.guildrank.db.gen.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import javax.annotation.Generated;

@Generated("yy_dm_entity_guild_cmp_health_analysis_expt_di")
public class YyDmEntityGuildCmpHealthAnalysisExptDiExample {
    /**
     * yy_dm_entity_guild_cmp_health_analysis_expt_di
     */
    protected String orderByClause;

    /**
     * yy_dm_entity_guild_cmp_health_analysis_expt_di
     */
    protected boolean distinct;

    /**
     * yy_dm_entity_guild_cmp_health_analysis_expt_di
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public YyDmEntityGuildCmpHealthAnalysisExptDiExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    /**
     * yy_dm_entity_guild_cmp_health_analysis_expt_di null
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGuildCmpOwnrIdIsNull() {
            addCriterion("guild_cmp_ownr_id is null");
            return (Criteria) this;
        }

        public Criteria andGuildCmpOwnrIdIsNotNull() {
            addCriterion("guild_cmp_ownr_id is not null");
            return (Criteria) this;
        }

        public Criteria andGuildCmpOwnrIdEqualTo(Long value) {
            addCriterion("guild_cmp_ownr_id =", value, "guildCmpOwnrId");
            return (Criteria) this;
        }

        public Criteria andGuildCmpOwnrIdNotEqualTo(Long value) {
            addCriterion("guild_cmp_ownr_id <>", value, "guildCmpOwnrId");
            return (Criteria) this;
        }

        public Criteria andGuildCmpOwnrIdGreaterThan(Long value) {
            addCriterion("guild_cmp_ownr_id >", value, "guildCmpOwnrId");
            return (Criteria) this;
        }

        public Criteria andGuildCmpOwnrIdGreaterThanOrEqualTo(Long value) {
            addCriterion("guild_cmp_ownr_id >=", value, "guildCmpOwnrId");
            return (Criteria) this;
        }

        public Criteria andGuildCmpOwnrIdLessThan(Long value) {
            addCriterion("guild_cmp_ownr_id <", value, "guildCmpOwnrId");
            return (Criteria) this;
        }

        public Criteria andGuildCmpOwnrIdLessThanOrEqualTo(Long value) {
            addCriterion("guild_cmp_ownr_id <=", value, "guildCmpOwnrId");
            return (Criteria) this;
        }

        public Criteria andGuildCmpOwnrIdIn(List<Long> values) {
            addCriterion("guild_cmp_ownr_id in", values, "guildCmpOwnrId");
            return (Criteria) this;
        }

        public Criteria andGuildCmpOwnrIdNotIn(List<Long> values) {
            addCriterion("guild_cmp_ownr_id not in", values, "guildCmpOwnrId");
            return (Criteria) this;
        }

        public Criteria andGuildCmpOwnrIdBetween(Long value1, Long value2) {
            addCriterion("guild_cmp_ownr_id between", value1, value2, "guildCmpOwnrId");
            return (Criteria) this;
        }

        public Criteria andGuildCmpOwnrIdNotBetween(Long value1, Long value2) {
            addCriterion("guild_cmp_ownr_id not between", value1, value2, "guildCmpOwnrId");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumIsNull() {
            addCriterion("new_aid_rat_3_4_aid_num is null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumIsNotNull() {
            addCriterion("new_aid_rat_3_4_aid_num is not null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumEqualTo(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num =", value, "newAidRat34AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumNotEqualTo(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num <>", value, "newAidRat34AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumGreaterThan(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num >", value, "newAidRat34AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumGreaterThanOrEqualTo(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num >=", value, "newAidRat34AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumLessThan(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num <", value, "newAidRat34AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumLessThanOrEqualTo(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num <=", value, "newAidRat34AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumIn(List<Long> values) {
            addCriterion("new_aid_rat_3_4_aid_num in", values, "newAidRat34AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumNotIn(List<Long> values) {
            addCriterion("new_aid_rat_3_4_aid_num not in", values, "newAidRat34AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumBetween(Long value1, Long value2) {
            addCriterion("new_aid_rat_3_4_aid_num between", value1, value2, "newAidRat34AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumNotBetween(Long value1, Long value2) {
            addCriterion("new_aid_rat_3_4_aid_num not between", value1, value2, "newAidRat34AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnIsNull() {
            addCriterion("new_aid_rat_3_4_aid_num_rn is null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnIsNotNull() {
            addCriterion("new_aid_rat_3_4_aid_num_rn is not null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnEqualTo(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_rn =", value, "newAidRat34AidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnNotEqualTo(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_rn <>", value, "newAidRat34AidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnGreaterThan(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_rn >", value, "newAidRat34AidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnGreaterThanOrEqualTo(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_rn >=", value, "newAidRat34AidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnLessThan(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_rn <", value, "newAidRat34AidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnLessThanOrEqualTo(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_rn <=", value, "newAidRat34AidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnIn(List<Long> values) {
            addCriterion("new_aid_rat_3_4_aid_num_rn in", values, "newAidRat34AidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnNotIn(List<Long> values) {
            addCriterion("new_aid_rat_3_4_aid_num_rn not in", values, "newAidRat34AidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnBetween(Long value1, Long value2) {
            addCriterion("new_aid_rat_3_4_aid_num_rn between", value1, value2, "newAidRat34AidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnNotBetween(Long value1, Long value2) {
            addCriterion("new_aid_rat_3_4_aid_num_rn not between", value1, value2, "newAidRat34AidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnScoreIsNull() {
            addCriterion("new_aid_rat_3_4_aid_num_rn_score is null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnScoreIsNotNull() {
            addCriterion("new_aid_rat_3_4_aid_num_rn_score is not null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnScoreEqualTo(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_rn_score =", value, "newAidRat34AidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnScoreNotEqualTo(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_rn_score <>", value, "newAidRat34AidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnScoreGreaterThan(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_rn_score >", value, "newAidRat34AidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_rn_score >=", value, "newAidRat34AidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnScoreLessThan(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_rn_score <", value, "newAidRat34AidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnScoreLessThanOrEqualTo(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_rn_score <=", value, "newAidRat34AidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnScoreIn(List<Integer> values) {
            addCriterion("new_aid_rat_3_4_aid_num_rn_score in", values, "newAidRat34AidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnScoreNotIn(List<Integer> values) {
            addCriterion("new_aid_rat_3_4_aid_num_rn_score not in", values, "newAidRat34AidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnScoreBetween(Integer value1, Integer value2) {
            addCriterion("new_aid_rat_3_4_aid_num_rn_score between", value1, value2, "newAidRat34AidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("new_aid_rat_3_4_aid_num_rn_score not between", value1, value2, "newAidRat34AidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnNextScoreIsNull() {
            addCriterion("new_aid_rat_3_4_aid_num_rn_next_score is null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnNextScoreIsNotNull() {
            addCriterion("new_aid_rat_3_4_aid_num_rn_next_score is not null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnNextScoreEqualTo(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_rn_next_score =", value, "newAidRat34AidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnNextScoreNotEqualTo(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_rn_next_score <>", value, "newAidRat34AidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnNextScoreGreaterThan(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_rn_next_score >", value, "newAidRat34AidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnNextScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_rn_next_score >=", value, "newAidRat34AidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnNextScoreLessThan(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_rn_next_score <", value, "newAidRat34AidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnNextScoreLessThanOrEqualTo(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_rn_next_score <=", value, "newAidRat34AidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnNextScoreIn(List<Integer> values) {
            addCriterion("new_aid_rat_3_4_aid_num_rn_next_score in", values, "newAidRat34AidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnNextScoreNotIn(List<Integer> values) {
            addCriterion("new_aid_rat_3_4_aid_num_rn_next_score not in", values, "newAidRat34AidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnNextScoreBetween(Integer value1, Integer value2) {
            addCriterion("new_aid_rat_3_4_aid_num_rn_next_score between", value1, value2, "newAidRat34AidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnNextScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("new_aid_rat_3_4_aid_num_rn_next_score not between", value1, value2, "newAidRat34AidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumTrgtDiffIsNull() {
            addCriterion("new_aid_rat_3_4_aid_num_trgt_diff is null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumTrgtDiffIsNotNull() {
            addCriterion("new_aid_rat_3_4_aid_num_trgt_diff is not null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumTrgtDiffEqualTo(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_trgt_diff =", value, "newAidRat34AidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumTrgtDiffNotEqualTo(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_trgt_diff <>", value, "newAidRat34AidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumTrgtDiffGreaterThan(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_trgt_diff >", value, "newAidRat34AidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumTrgtDiffGreaterThanOrEqualTo(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_trgt_diff >=", value, "newAidRat34AidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumTrgtDiffLessThan(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_trgt_diff <", value, "newAidRat34AidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumTrgtDiffLessThanOrEqualTo(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_trgt_diff <=", value, "newAidRat34AidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumTrgtDiffIn(List<Long> values) {
            addCriterion("new_aid_rat_3_4_aid_num_trgt_diff in", values, "newAidRat34AidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumTrgtDiffNotIn(List<Long> values) {
            addCriterion("new_aid_rat_3_4_aid_num_trgt_diff not in", values, "newAidRat34AidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumTrgtDiffBetween(Long value1, Long value2) {
            addCriterion("new_aid_rat_3_4_aid_num_trgt_diff between", value1, value2, "newAidRat34AidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumTrgtDiffNotBetween(Long value1, Long value2) {
            addCriterion("new_aid_rat_3_4_aid_num_trgt_diff not between", value1, value2, "newAidRat34AidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffIsNull() {
            addCriterion("new_aid_rat_3_4_aid_num_diff is null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffIsNotNull() {
            addCriterion("new_aid_rat_3_4_aid_num_diff is not null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffEqualTo(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff =", value, "newAidRat34AidNumDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffNotEqualTo(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff <>", value, "newAidRat34AidNumDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffGreaterThan(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff >", value, "newAidRat34AidNumDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffGreaterThanOrEqualTo(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff >=", value, "newAidRat34AidNumDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffLessThan(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff <", value, "newAidRat34AidNumDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffLessThanOrEqualTo(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff <=", value, "newAidRat34AidNumDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffIn(List<Long> values) {
            addCriterion("new_aid_rat_3_4_aid_num_diff in", values, "newAidRat34AidNumDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffNotIn(List<Long> values) {
            addCriterion("new_aid_rat_3_4_aid_num_diff not in", values, "newAidRat34AidNumDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffBetween(Long value1, Long value2) {
            addCriterion("new_aid_rat_3_4_aid_num_diff between", value1, value2, "newAidRat34AidNumDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffNotBetween(Long value1, Long value2) {
            addCriterion("new_aid_rat_3_4_aid_num_diff not between", value1, value2, "newAidRat34AidNumDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffScoreIsNull() {
            addCriterion("new_aid_rat_3_4_aid_num_diff_score is null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffScoreIsNotNull() {
            addCriterion("new_aid_rat_3_4_aid_num_diff_score is not null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffScoreEqualTo(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_score =", value, "newAidRat34AidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffScoreNotEqualTo(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_score <>", value, "newAidRat34AidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffScoreGreaterThan(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_score >", value, "newAidRat34AidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_score >=", value, "newAidRat34AidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffScoreLessThan(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_score <", value, "newAidRat34AidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffScoreLessThanOrEqualTo(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_score <=", value, "newAidRat34AidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffScoreIn(List<Integer> values) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_score in", values, "newAidRat34AidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffScoreNotIn(List<Integer> values) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_score not in", values, "newAidRat34AidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffScoreBetween(Integer value1, Integer value2) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_score between", value1, value2, "newAidRat34AidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_score not between", value1, value2, "newAidRat34AidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffNextScoreIsNull() {
            addCriterion("new_aid_rat_3_4_aid_num_diff_next_score is null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffNextScoreIsNotNull() {
            addCriterion("new_aid_rat_3_4_aid_num_diff_next_score is not null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffNextScoreEqualTo(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_next_score =", value, "newAidRat34AidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffNextScoreNotEqualTo(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_next_score <>", value, "newAidRat34AidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffNextScoreGreaterThan(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_next_score >", value, "newAidRat34AidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffNextScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_next_score >=", value, "newAidRat34AidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffNextScoreLessThan(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_next_score <", value, "newAidRat34AidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffNextScoreLessThanOrEqualTo(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_next_score <=", value, "newAidRat34AidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffNextScoreIn(List<Integer> values) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_next_score in", values, "newAidRat34AidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffNextScoreNotIn(List<Integer> values) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_next_score not in", values, "newAidRat34AidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffNextScoreBetween(Integer value1, Integer value2) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_next_score between", value1, value2, "newAidRat34AidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffNextScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_next_score not between", value1, value2, "newAidRat34AidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffTrgtDiffIsNull() {
            addCriterion("new_aid_rat_3_4_aid_num_diff_trgt_diff is null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffTrgtDiffIsNotNull() {
            addCriterion("new_aid_rat_3_4_aid_num_diff_trgt_diff is not null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffTrgtDiffEqualTo(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_trgt_diff =", value, "newAidRat34AidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffTrgtDiffNotEqualTo(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_trgt_diff <>", value, "newAidRat34AidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffTrgtDiffGreaterThan(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_trgt_diff >", value, "newAidRat34AidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffTrgtDiffGreaterThanOrEqualTo(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_trgt_diff >=", value, "newAidRat34AidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffTrgtDiffLessThan(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_trgt_diff <", value, "newAidRat34AidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffTrgtDiffLessThanOrEqualTo(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_trgt_diff <=", value, "newAidRat34AidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffTrgtDiffIn(List<Long> values) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_trgt_diff in", values, "newAidRat34AidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffTrgtDiffNotIn(List<Long> values) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_trgt_diff not in", values, "newAidRat34AidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffTrgtDiffBetween(Long value1, Long value2) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_trgt_diff between", value1, value2, "newAidRat34AidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffTrgtDiffNotBetween(Long value1, Long value2) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_trgt_diff not between", value1, value2, "newAidRat34AidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffIsNull() {
            addCriterion("upgrd_high_aid_num_diff is null");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffIsNotNull() {
            addCriterion("upgrd_high_aid_num_diff is not null");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffEqualTo(Integer value) {
            addCriterion("upgrd_high_aid_num_diff =", value, "upgrdHighAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffNotEqualTo(Integer value) {
            addCriterion("upgrd_high_aid_num_diff <>", value, "upgrdHighAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffGreaterThan(Integer value) {
            addCriterion("upgrd_high_aid_num_diff >", value, "upgrdHighAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffGreaterThanOrEqualTo(Integer value) {
            addCriterion("upgrd_high_aid_num_diff >=", value, "upgrdHighAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffLessThan(Integer value) {
            addCriterion("upgrd_high_aid_num_diff <", value, "upgrdHighAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffLessThanOrEqualTo(Integer value) {
            addCriterion("upgrd_high_aid_num_diff <=", value, "upgrdHighAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffIn(List<Integer> values) {
            addCriterion("upgrd_high_aid_num_diff in", values, "upgrdHighAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffNotIn(List<Integer> values) {
            addCriterion("upgrd_high_aid_num_diff not in", values, "upgrdHighAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffBetween(Integer value1, Integer value2) {
            addCriterion("upgrd_high_aid_num_diff between", value1, value2, "upgrdHighAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffNotBetween(Integer value1, Integer value2) {
            addCriterion("upgrd_high_aid_num_diff not between", value1, value2, "upgrdHighAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffScoreIsNull() {
            addCriterion("upgrd_high_aid_num_diff_score is null");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffScoreIsNotNull() {
            addCriterion("upgrd_high_aid_num_diff_score is not null");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffScoreEqualTo(Integer value) {
            addCriterion("upgrd_high_aid_num_diff_score =", value, "upgrdHighAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffScoreNotEqualTo(Integer value) {
            addCriterion("upgrd_high_aid_num_diff_score <>", value, "upgrdHighAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffScoreGreaterThan(Integer value) {
            addCriterion("upgrd_high_aid_num_diff_score >", value, "upgrdHighAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("upgrd_high_aid_num_diff_score >=", value, "upgrdHighAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffScoreLessThan(Integer value) {
            addCriterion("upgrd_high_aid_num_diff_score <", value, "upgrdHighAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffScoreLessThanOrEqualTo(Integer value) {
            addCriterion("upgrd_high_aid_num_diff_score <=", value, "upgrdHighAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffScoreIn(List<Integer> values) {
            addCriterion("upgrd_high_aid_num_diff_score in", values, "upgrdHighAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffScoreNotIn(List<Integer> values) {
            addCriterion("upgrd_high_aid_num_diff_score not in", values, "upgrdHighAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffScoreBetween(Integer value1, Integer value2) {
            addCriterion("upgrd_high_aid_num_diff_score between", value1, value2, "upgrdHighAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("upgrd_high_aid_num_diff_score not between", value1, value2, "upgrdHighAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffNextScoreIsNull() {
            addCriterion("upgrd_high_aid_num_diff_next_score is null");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffNextScoreIsNotNull() {
            addCriterion("upgrd_high_aid_num_diff_next_score is not null");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffNextScoreEqualTo(Integer value) {
            addCriterion("upgrd_high_aid_num_diff_next_score =", value, "upgrdHighAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffNextScoreNotEqualTo(Integer value) {
            addCriterion("upgrd_high_aid_num_diff_next_score <>", value, "upgrdHighAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffNextScoreGreaterThan(Integer value) {
            addCriterion("upgrd_high_aid_num_diff_next_score >", value, "upgrdHighAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffNextScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("upgrd_high_aid_num_diff_next_score >=", value, "upgrdHighAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffNextScoreLessThan(Integer value) {
            addCriterion("upgrd_high_aid_num_diff_next_score <", value, "upgrdHighAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffNextScoreLessThanOrEqualTo(Integer value) {
            addCriterion("upgrd_high_aid_num_diff_next_score <=", value, "upgrdHighAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffNextScoreIn(List<Integer> values) {
            addCriterion("upgrd_high_aid_num_diff_next_score in", values, "upgrdHighAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffNextScoreNotIn(List<Integer> values) {
            addCriterion("upgrd_high_aid_num_diff_next_score not in", values, "upgrdHighAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffNextScoreBetween(Integer value1, Integer value2) {
            addCriterion("upgrd_high_aid_num_diff_next_score between", value1, value2, "upgrdHighAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffNextScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("upgrd_high_aid_num_diff_next_score not between", value1, value2, "upgrdHighAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffTrgtDiffIsNull() {
            addCriterion("upgrd_high_aid_num_diff_trgt_diff is null");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffTrgtDiffIsNotNull() {
            addCriterion("upgrd_high_aid_num_diff_trgt_diff is not null");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffTrgtDiffEqualTo(Integer value) {
            addCriterion("upgrd_high_aid_num_diff_trgt_diff =", value, "upgrdHighAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffTrgtDiffNotEqualTo(Integer value) {
            addCriterion("upgrd_high_aid_num_diff_trgt_diff <>", value, "upgrdHighAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffTrgtDiffGreaterThan(Integer value) {
            addCriterion("upgrd_high_aid_num_diff_trgt_diff >", value, "upgrdHighAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffTrgtDiffGreaterThanOrEqualTo(Integer value) {
            addCriterion("upgrd_high_aid_num_diff_trgt_diff >=", value, "upgrdHighAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffTrgtDiffLessThan(Integer value) {
            addCriterion("upgrd_high_aid_num_diff_trgt_diff <", value, "upgrdHighAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffTrgtDiffLessThanOrEqualTo(Integer value) {
            addCriterion("upgrd_high_aid_num_diff_trgt_diff <=", value, "upgrdHighAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffTrgtDiffIn(List<Integer> values) {
            addCriterion("upgrd_high_aid_num_diff_trgt_diff in", values, "upgrdHighAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffTrgtDiffNotIn(List<Integer> values) {
            addCriterion("upgrd_high_aid_num_diff_trgt_diff not in", values, "upgrdHighAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffTrgtDiffBetween(Integer value1, Integer value2) {
            addCriterion("upgrd_high_aid_num_diff_trgt_diff between", value1, value2, "upgrdHighAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffTrgtDiffNotBetween(Integer value1, Integer value2) {
            addCriterion("upgrd_high_aid_num_diff_trgt_diff not between", value1, value2, "upgrdHighAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffIsNull() {
            addCriterion("upgrd_waist_aid_num_diff is null");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffIsNotNull() {
            addCriterion("upgrd_waist_aid_num_diff is not null");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffEqualTo(Integer value) {
            addCriterion("upgrd_waist_aid_num_diff =", value, "upgrdWaistAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffNotEqualTo(Integer value) {
            addCriterion("upgrd_waist_aid_num_diff <>", value, "upgrdWaistAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffGreaterThan(Integer value) {
            addCriterion("upgrd_waist_aid_num_diff >", value, "upgrdWaistAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffGreaterThanOrEqualTo(Integer value) {
            addCriterion("upgrd_waist_aid_num_diff >=", value, "upgrdWaistAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffLessThan(Integer value) {
            addCriterion("upgrd_waist_aid_num_diff <", value, "upgrdWaistAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffLessThanOrEqualTo(Integer value) {
            addCriterion("upgrd_waist_aid_num_diff <=", value, "upgrdWaistAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffIn(List<Integer> values) {
            addCriterion("upgrd_waist_aid_num_diff in", values, "upgrdWaistAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffNotIn(List<Integer> values) {
            addCriterion("upgrd_waist_aid_num_diff not in", values, "upgrdWaistAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffBetween(Integer value1, Integer value2) {
            addCriterion("upgrd_waist_aid_num_diff between", value1, value2, "upgrdWaistAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffNotBetween(Integer value1, Integer value2) {
            addCriterion("upgrd_waist_aid_num_diff not between", value1, value2, "upgrdWaistAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffScoreIsNull() {
            addCriterion("upgrd_waist_aid_num_diff_score is null");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffScoreIsNotNull() {
            addCriterion("upgrd_waist_aid_num_diff_score is not null");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffScoreEqualTo(Integer value) {
            addCriterion("upgrd_waist_aid_num_diff_score =", value, "upgrdWaistAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffScoreNotEqualTo(Integer value) {
            addCriterion("upgrd_waist_aid_num_diff_score <>", value, "upgrdWaistAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffScoreGreaterThan(Integer value) {
            addCriterion("upgrd_waist_aid_num_diff_score >", value, "upgrdWaistAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("upgrd_waist_aid_num_diff_score >=", value, "upgrdWaistAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffScoreLessThan(Integer value) {
            addCriterion("upgrd_waist_aid_num_diff_score <", value, "upgrdWaistAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffScoreLessThanOrEqualTo(Integer value) {
            addCriterion("upgrd_waist_aid_num_diff_score <=", value, "upgrdWaistAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffScoreIn(List<Integer> values) {
            addCriterion("upgrd_waist_aid_num_diff_score in", values, "upgrdWaistAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffScoreNotIn(List<Integer> values) {
            addCriterion("upgrd_waist_aid_num_diff_score not in", values, "upgrdWaistAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffScoreBetween(Integer value1, Integer value2) {
            addCriterion("upgrd_waist_aid_num_diff_score between", value1, value2, "upgrdWaistAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("upgrd_waist_aid_num_diff_score not between", value1, value2, "upgrdWaistAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffNextScoreIsNull() {
            addCriterion("upgrd_waist_aid_num_diff_next_score is null");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffNextScoreIsNotNull() {
            addCriterion("upgrd_waist_aid_num_diff_next_score is not null");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffNextScoreEqualTo(Integer value) {
            addCriterion("upgrd_waist_aid_num_diff_next_score =", value, "upgrdWaistAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffNextScoreNotEqualTo(Integer value) {
            addCriterion("upgrd_waist_aid_num_diff_next_score <>", value, "upgrdWaistAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffNextScoreGreaterThan(Integer value) {
            addCriterion("upgrd_waist_aid_num_diff_next_score >", value, "upgrdWaistAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffNextScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("upgrd_waist_aid_num_diff_next_score >=", value, "upgrdWaistAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffNextScoreLessThan(Integer value) {
            addCriterion("upgrd_waist_aid_num_diff_next_score <", value, "upgrdWaistAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffNextScoreLessThanOrEqualTo(Integer value) {
            addCriterion("upgrd_waist_aid_num_diff_next_score <=", value, "upgrdWaistAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffNextScoreIn(List<Integer> values) {
            addCriterion("upgrd_waist_aid_num_diff_next_score in", values, "upgrdWaistAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffNextScoreNotIn(List<Integer> values) {
            addCriterion("upgrd_waist_aid_num_diff_next_score not in", values, "upgrdWaistAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffNextScoreBetween(Integer value1, Integer value2) {
            addCriterion("upgrd_waist_aid_num_diff_next_score between", value1, value2, "upgrdWaistAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffNextScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("upgrd_waist_aid_num_diff_next_score not between", value1, value2, "upgrdWaistAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffTrgtDiffIsNull() {
            addCriterion("upgrd_waist_aid_num_diff_trgt_diff is null");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffTrgtDiffIsNotNull() {
            addCriterion("upgrd_waist_aid_num_diff_trgt_diff is not null");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffTrgtDiffEqualTo(Integer value) {
            addCriterion("upgrd_waist_aid_num_diff_trgt_diff =", value, "upgrdWaistAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffTrgtDiffNotEqualTo(Integer value) {
            addCriterion("upgrd_waist_aid_num_diff_trgt_diff <>", value, "upgrdWaistAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffTrgtDiffGreaterThan(Integer value) {
            addCriterion("upgrd_waist_aid_num_diff_trgt_diff >", value, "upgrdWaistAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffTrgtDiffGreaterThanOrEqualTo(Integer value) {
            addCriterion("upgrd_waist_aid_num_diff_trgt_diff >=", value, "upgrdWaistAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffTrgtDiffLessThan(Integer value) {
            addCriterion("upgrd_waist_aid_num_diff_trgt_diff <", value, "upgrdWaistAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffTrgtDiffLessThanOrEqualTo(Integer value) {
            addCriterion("upgrd_waist_aid_num_diff_trgt_diff <=", value, "upgrdWaistAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffTrgtDiffIn(List<Integer> values) {
            addCriterion("upgrd_waist_aid_num_diff_trgt_diff in", values, "upgrdWaistAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffTrgtDiffNotIn(List<Integer> values) {
            addCriterion("upgrd_waist_aid_num_diff_trgt_diff not in", values, "upgrdWaistAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffTrgtDiffBetween(Integer value1, Integer value2) {
            addCriterion("upgrd_waist_aid_num_diff_trgt_diff between", value1, value2, "upgrdWaistAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffTrgtDiffNotBetween(Integer value1, Integer value2) {
            addCriterion("upgrd_waist_aid_num_diff_trgt_diff not between", value1, value2, "upgrdWaistAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumIsNull() {
            addCriterion("avg_high_valid_live_aid_num is null");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumIsNotNull() {
            addCriterion("avg_high_valid_live_aid_num is not null");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumEqualTo(Double value) {
            addCriterion("avg_high_valid_live_aid_num =", value, "avgHighValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumNotEqualTo(Double value) {
            addCriterion("avg_high_valid_live_aid_num <>", value, "avgHighValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumGreaterThan(Double value) {
            addCriterion("avg_high_valid_live_aid_num >", value, "avgHighValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumGreaterThanOrEqualTo(Double value) {
            addCriterion("avg_high_valid_live_aid_num >=", value, "avgHighValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumLessThan(Double value) {
            addCriterion("avg_high_valid_live_aid_num <", value, "avgHighValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumLessThanOrEqualTo(Double value) {
            addCriterion("avg_high_valid_live_aid_num <=", value, "avgHighValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumIn(List<Double> values) {
            addCriterion("avg_high_valid_live_aid_num in", values, "avgHighValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumNotIn(List<Double> values) {
            addCriterion("avg_high_valid_live_aid_num not in", values, "avgHighValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumBetween(Double value1, Double value2) {
            addCriterion("avg_high_valid_live_aid_num between", value1, value2, "avgHighValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumNotBetween(Double value1, Double value2) {
            addCriterion("avg_high_valid_live_aid_num not between", value1, value2, "avgHighValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnIsNull() {
            addCriterion("avg_high_valid_live_aid_num_rn is null");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnIsNotNull() {
            addCriterion("avg_high_valid_live_aid_num_rn is not null");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnEqualTo(Long value) {
            addCriterion("avg_high_valid_live_aid_num_rn =", value, "avgHighValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnNotEqualTo(Long value) {
            addCriterion("avg_high_valid_live_aid_num_rn <>", value, "avgHighValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnGreaterThan(Long value) {
            addCriterion("avg_high_valid_live_aid_num_rn >", value, "avgHighValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnGreaterThanOrEqualTo(Long value) {
            addCriterion("avg_high_valid_live_aid_num_rn >=", value, "avgHighValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnLessThan(Long value) {
            addCriterion("avg_high_valid_live_aid_num_rn <", value, "avgHighValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnLessThanOrEqualTo(Long value) {
            addCriterion("avg_high_valid_live_aid_num_rn <=", value, "avgHighValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnIn(List<Long> values) {
            addCriterion("avg_high_valid_live_aid_num_rn in", values, "avgHighValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnNotIn(List<Long> values) {
            addCriterion("avg_high_valid_live_aid_num_rn not in", values, "avgHighValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnBetween(Long value1, Long value2) {
            addCriterion("avg_high_valid_live_aid_num_rn between", value1, value2, "avgHighValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnNotBetween(Long value1, Long value2) {
            addCriterion("avg_high_valid_live_aid_num_rn not between", value1, value2, "avgHighValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnScoreIsNull() {
            addCriterion("avg_high_valid_live_aid_num_rn_score is null");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnScoreIsNotNull() {
            addCriterion("avg_high_valid_live_aid_num_rn_score is not null");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnScoreEqualTo(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_rn_score =", value, "avgHighValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnScoreNotEqualTo(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_rn_score <>", value, "avgHighValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnScoreGreaterThan(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_rn_score >", value, "avgHighValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_rn_score >=", value, "avgHighValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnScoreLessThan(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_rn_score <", value, "avgHighValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnScoreLessThanOrEqualTo(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_rn_score <=", value, "avgHighValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnScoreIn(List<Integer> values) {
            addCriterion("avg_high_valid_live_aid_num_rn_score in", values, "avgHighValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnScoreNotIn(List<Integer> values) {
            addCriterion("avg_high_valid_live_aid_num_rn_score not in", values, "avgHighValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnScoreBetween(Integer value1, Integer value2) {
            addCriterion("avg_high_valid_live_aid_num_rn_score between", value1, value2, "avgHighValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("avg_high_valid_live_aid_num_rn_score not between", value1, value2, "avgHighValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnNextScoreIsNull() {
            addCriterion("avg_high_valid_live_aid_num_rn_next_score is null");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnNextScoreIsNotNull() {
            addCriterion("avg_high_valid_live_aid_num_rn_next_score is not null");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnNextScoreEqualTo(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_rn_next_score =", value, "avgHighValidLiveAidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnNextScoreNotEqualTo(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_rn_next_score <>", value, "avgHighValidLiveAidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnNextScoreGreaterThan(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_rn_next_score >", value, "avgHighValidLiveAidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnNextScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_rn_next_score >=", value, "avgHighValidLiveAidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnNextScoreLessThan(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_rn_next_score <", value, "avgHighValidLiveAidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnNextScoreLessThanOrEqualTo(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_rn_next_score <=", value, "avgHighValidLiveAidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnNextScoreIn(List<Integer> values) {
            addCriterion("avg_high_valid_live_aid_num_rn_next_score in", values, "avgHighValidLiveAidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnNextScoreNotIn(List<Integer> values) {
            addCriterion("avg_high_valid_live_aid_num_rn_next_score not in", values, "avgHighValidLiveAidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnNextScoreBetween(Integer value1, Integer value2) {
            addCriterion("avg_high_valid_live_aid_num_rn_next_score between", value1, value2, "avgHighValidLiveAidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnNextScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("avg_high_valid_live_aid_num_rn_next_score not between", value1, value2, "avgHighValidLiveAidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumTrgtDiffIsNull() {
            addCriterion("avg_high_valid_live_aid_num_trgt_diff is null");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumTrgtDiffIsNotNull() {
            addCriterion("avg_high_valid_live_aid_num_trgt_diff is not null");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumTrgtDiffEqualTo(Double value) {
            addCriterion("avg_high_valid_live_aid_num_trgt_diff =", value, "avgHighValidLiveAidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumTrgtDiffNotEqualTo(Double value) {
            addCriterion("avg_high_valid_live_aid_num_trgt_diff <>", value, "avgHighValidLiveAidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumTrgtDiffGreaterThan(Double value) {
            addCriterion("avg_high_valid_live_aid_num_trgt_diff >", value, "avgHighValidLiveAidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumTrgtDiffGreaterThanOrEqualTo(Double value) {
            addCriterion("avg_high_valid_live_aid_num_trgt_diff >=", value, "avgHighValidLiveAidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumTrgtDiffLessThan(Double value) {
            addCriterion("avg_high_valid_live_aid_num_trgt_diff <", value, "avgHighValidLiveAidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumTrgtDiffLessThanOrEqualTo(Double value) {
            addCriterion("avg_high_valid_live_aid_num_trgt_diff <=", value, "avgHighValidLiveAidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumTrgtDiffIn(List<Double> values) {
            addCriterion("avg_high_valid_live_aid_num_trgt_diff in", values, "avgHighValidLiveAidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumTrgtDiffNotIn(List<Double> values) {
            addCriterion("avg_high_valid_live_aid_num_trgt_diff not in", values, "avgHighValidLiveAidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumTrgtDiffBetween(Double value1, Double value2) {
            addCriterion("avg_high_valid_live_aid_num_trgt_diff between", value1, value2, "avgHighValidLiveAidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumTrgtDiffNotBetween(Double value1, Double value2) {
            addCriterion("avg_high_valid_live_aid_num_trgt_diff not between", value1, value2, "avgHighValidLiveAidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffIsNull() {
            addCriterion("avg_high_valid_live_aid_num_diff is null");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffIsNotNull() {
            addCriterion("avg_high_valid_live_aid_num_diff is not null");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffEqualTo(Double value) {
            addCriterion("avg_high_valid_live_aid_num_diff =", value, "avgHighValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffNotEqualTo(Double value) {
            addCriterion("avg_high_valid_live_aid_num_diff <>", value, "avgHighValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffGreaterThan(Double value) {
            addCriterion("avg_high_valid_live_aid_num_diff >", value, "avgHighValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffGreaterThanOrEqualTo(Double value) {
            addCriterion("avg_high_valid_live_aid_num_diff >=", value, "avgHighValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffLessThan(Double value) {
            addCriterion("avg_high_valid_live_aid_num_diff <", value, "avgHighValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffLessThanOrEqualTo(Double value) {
            addCriterion("avg_high_valid_live_aid_num_diff <=", value, "avgHighValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffIn(List<Double> values) {
            addCriterion("avg_high_valid_live_aid_num_diff in", values, "avgHighValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffNotIn(List<Double> values) {
            addCriterion("avg_high_valid_live_aid_num_diff not in", values, "avgHighValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffBetween(Double value1, Double value2) {
            addCriterion("avg_high_valid_live_aid_num_diff between", value1, value2, "avgHighValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffNotBetween(Double value1, Double value2) {
            addCriterion("avg_high_valid_live_aid_num_diff not between", value1, value2, "avgHighValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffScoreIsNull() {
            addCriterion("avg_high_valid_live_aid_num_diff_score is null");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffScoreIsNotNull() {
            addCriterion("avg_high_valid_live_aid_num_diff_score is not null");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffScoreEqualTo(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_diff_score =", value, "avgHighValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffScoreNotEqualTo(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_diff_score <>", value, "avgHighValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffScoreGreaterThan(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_diff_score >", value, "avgHighValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_diff_score >=", value, "avgHighValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffScoreLessThan(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_diff_score <", value, "avgHighValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffScoreLessThanOrEqualTo(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_diff_score <=", value, "avgHighValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffScoreIn(List<Integer> values) {
            addCriterion("avg_high_valid_live_aid_num_diff_score in", values, "avgHighValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffScoreNotIn(List<Integer> values) {
            addCriterion("avg_high_valid_live_aid_num_diff_score not in", values, "avgHighValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffScoreBetween(Integer value1, Integer value2) {
            addCriterion("avg_high_valid_live_aid_num_diff_score between", value1, value2, "avgHighValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("avg_high_valid_live_aid_num_diff_score not between", value1, value2, "avgHighValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffNextScoreIsNull() {
            addCriterion("avg_high_valid_live_aid_num_diff_next_score is null");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffNextScoreIsNotNull() {
            addCriterion("avg_high_valid_live_aid_num_diff_next_score is not null");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffNextScoreEqualTo(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_diff_next_score =", value, "avgHighValidLiveAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffNextScoreNotEqualTo(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_diff_next_score <>", value, "avgHighValidLiveAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffNextScoreGreaterThan(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_diff_next_score >", value, "avgHighValidLiveAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffNextScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_diff_next_score >=", value, "avgHighValidLiveAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffNextScoreLessThan(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_diff_next_score <", value, "avgHighValidLiveAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffNextScoreLessThanOrEqualTo(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_diff_next_score <=", value, "avgHighValidLiveAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffNextScoreIn(List<Integer> values) {
            addCriterion("avg_high_valid_live_aid_num_diff_next_score in", values, "avgHighValidLiveAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffNextScoreNotIn(List<Integer> values) {
            addCriterion("avg_high_valid_live_aid_num_diff_next_score not in", values, "avgHighValidLiveAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffNextScoreBetween(Integer value1, Integer value2) {
            addCriterion("avg_high_valid_live_aid_num_diff_next_score between", value1, value2, "avgHighValidLiveAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffNextScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("avg_high_valid_live_aid_num_diff_next_score not between", value1, value2, "avgHighValidLiveAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffTrgtDiffIsNull() {
            addCriterion("avg_high_valid_live_aid_num_diff_trgt_diff is null");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffTrgtDiffIsNotNull() {
            addCriterion("avg_high_valid_live_aid_num_diff_trgt_diff is not null");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffTrgtDiffEqualTo(Double value) {
            addCriterion("avg_high_valid_live_aid_num_diff_trgt_diff =", value, "avgHighValidLiveAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffTrgtDiffNotEqualTo(Double value) {
            addCriterion("avg_high_valid_live_aid_num_diff_trgt_diff <>", value, "avgHighValidLiveAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffTrgtDiffGreaterThan(Double value) {
            addCriterion("avg_high_valid_live_aid_num_diff_trgt_diff >", value, "avgHighValidLiveAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffTrgtDiffGreaterThanOrEqualTo(Double value) {
            addCriterion("avg_high_valid_live_aid_num_diff_trgt_diff >=", value, "avgHighValidLiveAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffTrgtDiffLessThan(Double value) {
            addCriterion("avg_high_valid_live_aid_num_diff_trgt_diff <", value, "avgHighValidLiveAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffTrgtDiffLessThanOrEqualTo(Double value) {
            addCriterion("avg_high_valid_live_aid_num_diff_trgt_diff <=", value, "avgHighValidLiveAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffTrgtDiffIn(List<Double> values) {
            addCriterion("avg_high_valid_live_aid_num_diff_trgt_diff in", values, "avgHighValidLiveAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffTrgtDiffNotIn(List<Double> values) {
            addCriterion("avg_high_valid_live_aid_num_diff_trgt_diff not in", values, "avgHighValidLiveAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffTrgtDiffBetween(Double value1, Double value2) {
            addCriterion("avg_high_valid_live_aid_num_diff_trgt_diff between", value1, value2, "avgHighValidLiveAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffTrgtDiffNotBetween(Double value1, Double value2) {
            addCriterion("avg_high_valid_live_aid_num_diff_trgt_diff not between", value1, value2, "avgHighValidLiveAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumIsNull() {
            addCriterion("avg_waist_valid_live_aid_num is null");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumIsNotNull() {
            addCriterion("avg_waist_valid_live_aid_num is not null");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumEqualTo(Double value) {
            addCriterion("avg_waist_valid_live_aid_num =", value, "avgWaistValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumNotEqualTo(Double value) {
            addCriterion("avg_waist_valid_live_aid_num <>", value, "avgWaistValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumGreaterThan(Double value) {
            addCriterion("avg_waist_valid_live_aid_num >", value, "avgWaistValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumGreaterThanOrEqualTo(Double value) {
            addCriterion("avg_waist_valid_live_aid_num >=", value, "avgWaistValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumLessThan(Double value) {
            addCriterion("avg_waist_valid_live_aid_num <", value, "avgWaistValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumLessThanOrEqualTo(Double value) {
            addCriterion("avg_waist_valid_live_aid_num <=", value, "avgWaistValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumIn(List<Double> values) {
            addCriterion("avg_waist_valid_live_aid_num in", values, "avgWaistValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumNotIn(List<Double> values) {
            addCriterion("avg_waist_valid_live_aid_num not in", values, "avgWaistValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumBetween(Double value1, Double value2) {
            addCriterion("avg_waist_valid_live_aid_num between", value1, value2, "avgWaistValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumNotBetween(Double value1, Double value2) {
            addCriterion("avg_waist_valid_live_aid_num not between", value1, value2, "avgWaistValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnIsNull() {
            addCriterion("avg_waist_valid_live_aid_num_rn is null");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnIsNotNull() {
            addCriterion("avg_waist_valid_live_aid_num_rn is not null");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnEqualTo(Long value) {
            addCriterion("avg_waist_valid_live_aid_num_rn =", value, "avgWaistValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnNotEqualTo(Long value) {
            addCriterion("avg_waist_valid_live_aid_num_rn <>", value, "avgWaistValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnGreaterThan(Long value) {
            addCriterion("avg_waist_valid_live_aid_num_rn >", value, "avgWaistValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnGreaterThanOrEqualTo(Long value) {
            addCriterion("avg_waist_valid_live_aid_num_rn >=", value, "avgWaistValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnLessThan(Long value) {
            addCriterion("avg_waist_valid_live_aid_num_rn <", value, "avgWaistValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnLessThanOrEqualTo(Long value) {
            addCriterion("avg_waist_valid_live_aid_num_rn <=", value, "avgWaistValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnIn(List<Long> values) {
            addCriterion("avg_waist_valid_live_aid_num_rn in", values, "avgWaistValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnNotIn(List<Long> values) {
            addCriterion("avg_waist_valid_live_aid_num_rn not in", values, "avgWaistValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnBetween(Long value1, Long value2) {
            addCriterion("avg_waist_valid_live_aid_num_rn between", value1, value2, "avgWaistValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnNotBetween(Long value1, Long value2) {
            addCriterion("avg_waist_valid_live_aid_num_rn not between", value1, value2, "avgWaistValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnScoreIsNull() {
            addCriterion("avg_waist_valid_live_aid_num_rn_score is null");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnScoreIsNotNull() {
            addCriterion("avg_waist_valid_live_aid_num_rn_score is not null");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnScoreEqualTo(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_rn_score =", value, "avgWaistValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnScoreNotEqualTo(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_rn_score <>", value, "avgWaistValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnScoreGreaterThan(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_rn_score >", value, "avgWaistValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_rn_score >=", value, "avgWaistValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnScoreLessThan(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_rn_score <", value, "avgWaistValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnScoreLessThanOrEqualTo(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_rn_score <=", value, "avgWaistValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnScoreIn(List<Integer> values) {
            addCriterion("avg_waist_valid_live_aid_num_rn_score in", values, "avgWaistValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnScoreNotIn(List<Integer> values) {
            addCriterion("avg_waist_valid_live_aid_num_rn_score not in", values, "avgWaistValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnScoreBetween(Integer value1, Integer value2) {
            addCriterion("avg_waist_valid_live_aid_num_rn_score between", value1, value2, "avgWaistValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("avg_waist_valid_live_aid_num_rn_score not between", value1, value2, "avgWaistValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnNextScoreIsNull() {
            addCriterion("avg_waist_valid_live_aid_num_rn_next_score is null");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnNextScoreIsNotNull() {
            addCriterion("avg_waist_valid_live_aid_num_rn_next_score is not null");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnNextScoreEqualTo(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_rn_next_score =", value, "avgWaistValidLiveAidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnNextScoreNotEqualTo(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_rn_next_score <>", value, "avgWaistValidLiveAidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnNextScoreGreaterThan(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_rn_next_score >", value, "avgWaistValidLiveAidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnNextScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_rn_next_score >=", value, "avgWaistValidLiveAidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnNextScoreLessThan(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_rn_next_score <", value, "avgWaistValidLiveAidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnNextScoreLessThanOrEqualTo(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_rn_next_score <=", value, "avgWaistValidLiveAidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnNextScoreIn(List<Integer> values) {
            addCriterion("avg_waist_valid_live_aid_num_rn_next_score in", values, "avgWaistValidLiveAidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnNextScoreNotIn(List<Integer> values) {
            addCriterion("avg_waist_valid_live_aid_num_rn_next_score not in", values, "avgWaistValidLiveAidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnNextScoreBetween(Integer value1, Integer value2) {
            addCriterion("avg_waist_valid_live_aid_num_rn_next_score between", value1, value2, "avgWaistValidLiveAidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnNextScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("avg_waist_valid_live_aid_num_rn_next_score not between", value1, value2, "avgWaistValidLiveAidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumTrgtDiffIsNull() {
            addCriterion("avg_waist_valid_live_aid_num_trgt_diff is null");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumTrgtDiffIsNotNull() {
            addCriterion("avg_waist_valid_live_aid_num_trgt_diff is not null");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumTrgtDiffEqualTo(Double value) {
            addCriterion("avg_waist_valid_live_aid_num_trgt_diff =", value, "avgWaistValidLiveAidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumTrgtDiffNotEqualTo(Double value) {
            addCriterion("avg_waist_valid_live_aid_num_trgt_diff <>", value, "avgWaistValidLiveAidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumTrgtDiffGreaterThan(Double value) {
            addCriterion("avg_waist_valid_live_aid_num_trgt_diff >", value, "avgWaistValidLiveAidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumTrgtDiffGreaterThanOrEqualTo(Double value) {
            addCriterion("avg_waist_valid_live_aid_num_trgt_diff >=", value, "avgWaistValidLiveAidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumTrgtDiffLessThan(Double value) {
            addCriterion("avg_waist_valid_live_aid_num_trgt_diff <", value, "avgWaistValidLiveAidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumTrgtDiffLessThanOrEqualTo(Double value) {
            addCriterion("avg_waist_valid_live_aid_num_trgt_diff <=", value, "avgWaistValidLiveAidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumTrgtDiffIn(List<Double> values) {
            addCriterion("avg_waist_valid_live_aid_num_trgt_diff in", values, "avgWaistValidLiveAidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumTrgtDiffNotIn(List<Double> values) {
            addCriterion("avg_waist_valid_live_aid_num_trgt_diff not in", values, "avgWaistValidLiveAidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumTrgtDiffBetween(Double value1, Double value2) {
            addCriterion("avg_waist_valid_live_aid_num_trgt_diff between", value1, value2, "avgWaistValidLiveAidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumTrgtDiffNotBetween(Double value1, Double value2) {
            addCriterion("avg_waist_valid_live_aid_num_trgt_diff not between", value1, value2, "avgWaistValidLiveAidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffIsNull() {
            addCriterion("avg_waist_valid_live_aid_num_diff is null");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffIsNotNull() {
            addCriterion("avg_waist_valid_live_aid_num_diff is not null");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffEqualTo(Double value) {
            addCriterion("avg_waist_valid_live_aid_num_diff =", value, "avgWaistValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffNotEqualTo(Double value) {
            addCriterion("avg_waist_valid_live_aid_num_diff <>", value, "avgWaistValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffGreaterThan(Double value) {
            addCriterion("avg_waist_valid_live_aid_num_diff >", value, "avgWaistValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffGreaterThanOrEqualTo(Double value) {
            addCriterion("avg_waist_valid_live_aid_num_diff >=", value, "avgWaistValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffLessThan(Double value) {
            addCriterion("avg_waist_valid_live_aid_num_diff <", value, "avgWaistValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffLessThanOrEqualTo(Double value) {
            addCriterion("avg_waist_valid_live_aid_num_diff <=", value, "avgWaistValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffIn(List<Double> values) {
            addCriterion("avg_waist_valid_live_aid_num_diff in", values, "avgWaistValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffNotIn(List<Double> values) {
            addCriterion("avg_waist_valid_live_aid_num_diff not in", values, "avgWaistValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffBetween(Double value1, Double value2) {
            addCriterion("avg_waist_valid_live_aid_num_diff between", value1, value2, "avgWaistValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffNotBetween(Double value1, Double value2) {
            addCriterion("avg_waist_valid_live_aid_num_diff not between", value1, value2, "avgWaistValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffScoreIsNull() {
            addCriterion("avg_waist_valid_live_aid_num_diff_score is null");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffScoreIsNotNull() {
            addCriterion("avg_waist_valid_live_aid_num_diff_score is not null");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffScoreEqualTo(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_diff_score =", value, "avgWaistValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffScoreNotEqualTo(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_diff_score <>", value, "avgWaistValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffScoreGreaterThan(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_diff_score >", value, "avgWaistValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_diff_score >=", value, "avgWaistValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffScoreLessThan(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_diff_score <", value, "avgWaistValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffScoreLessThanOrEqualTo(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_diff_score <=", value, "avgWaistValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffScoreIn(List<Integer> values) {
            addCriterion("avg_waist_valid_live_aid_num_diff_score in", values, "avgWaistValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffScoreNotIn(List<Integer> values) {
            addCriterion("avg_waist_valid_live_aid_num_diff_score not in", values, "avgWaistValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffScoreBetween(Integer value1, Integer value2) {
            addCriterion("avg_waist_valid_live_aid_num_diff_score between", value1, value2, "avgWaistValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("avg_waist_valid_live_aid_num_diff_score not between", value1, value2, "avgWaistValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffNextScoreIsNull() {
            addCriterion("avg_waist_valid_live_aid_num_diff_next_score is null");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffNextScoreIsNotNull() {
            addCriterion("avg_waist_valid_live_aid_num_diff_next_score is not null");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffNextScoreEqualTo(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_diff_next_score =", value, "avgWaistValidLiveAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffNextScoreNotEqualTo(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_diff_next_score <>", value, "avgWaistValidLiveAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffNextScoreGreaterThan(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_diff_next_score >", value, "avgWaistValidLiveAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffNextScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_diff_next_score >=", value, "avgWaistValidLiveAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffNextScoreLessThan(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_diff_next_score <", value, "avgWaistValidLiveAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffNextScoreLessThanOrEqualTo(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_diff_next_score <=", value, "avgWaistValidLiveAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffNextScoreIn(List<Integer> values) {
            addCriterion("avg_waist_valid_live_aid_num_diff_next_score in", values, "avgWaistValidLiveAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffNextScoreNotIn(List<Integer> values) {
            addCriterion("avg_waist_valid_live_aid_num_diff_next_score not in", values, "avgWaistValidLiveAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffNextScoreBetween(Integer value1, Integer value2) {
            addCriterion("avg_waist_valid_live_aid_num_diff_next_score between", value1, value2, "avgWaistValidLiveAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffNextScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("avg_waist_valid_live_aid_num_diff_next_score not between", value1, value2, "avgWaistValidLiveAidNumDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffTrgtDiffIsNull() {
            addCriterion("avg_waist_valid_live_aid_num_diff_trgt_diff is null");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffTrgtDiffIsNotNull() {
            addCriterion("avg_waist_valid_live_aid_num_diff_trgt_diff is not null");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffTrgtDiffEqualTo(Double value) {
            addCriterion("avg_waist_valid_live_aid_num_diff_trgt_diff =", value, "avgWaistValidLiveAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffTrgtDiffNotEqualTo(Double value) {
            addCriterion("avg_waist_valid_live_aid_num_diff_trgt_diff <>", value, "avgWaistValidLiveAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffTrgtDiffGreaterThan(Double value) {
            addCriterion("avg_waist_valid_live_aid_num_diff_trgt_diff >", value, "avgWaistValidLiveAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffTrgtDiffGreaterThanOrEqualTo(Double value) {
            addCriterion("avg_waist_valid_live_aid_num_diff_trgt_diff >=", value, "avgWaistValidLiveAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffTrgtDiffLessThan(Double value) {
            addCriterion("avg_waist_valid_live_aid_num_diff_trgt_diff <", value, "avgWaistValidLiveAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffTrgtDiffLessThanOrEqualTo(Double value) {
            addCriterion("avg_waist_valid_live_aid_num_diff_trgt_diff <=", value, "avgWaistValidLiveAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffTrgtDiffIn(List<Double> values) {
            addCriterion("avg_waist_valid_live_aid_num_diff_trgt_diff in", values, "avgWaistValidLiveAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffTrgtDiffNotIn(List<Double> values) {
            addCriterion("avg_waist_valid_live_aid_num_diff_trgt_diff not in", values, "avgWaistValidLiveAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffTrgtDiffBetween(Double value1, Double value2) {
            addCriterion("avg_waist_valid_live_aid_num_diff_trgt_diff between", value1, value2, "avgWaistValidLiveAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffTrgtDiffNotBetween(Double value1, Double value2) {
            addCriterion("avg_waist_valid_live_aid_num_diff_trgt_diff not between", value1, value2, "avgWaistValidLiveAidNumDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateIsNull() {
            addCriterion("avg_nto_valid_live_aid_rate is null");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateIsNotNull() {
            addCriterion("avg_nto_valid_live_aid_rate is not null");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateEqualTo(Double value) {
            addCriterion("avg_nto_valid_live_aid_rate =", value, "avgNtoValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateNotEqualTo(Double value) {
            addCriterion("avg_nto_valid_live_aid_rate <>", value, "avgNtoValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateGreaterThan(Double value) {
            addCriterion("avg_nto_valid_live_aid_rate >", value, "avgNtoValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateGreaterThanOrEqualTo(Double value) {
            addCriterion("avg_nto_valid_live_aid_rate >=", value, "avgNtoValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateLessThan(Double value) {
            addCriterion("avg_nto_valid_live_aid_rate <", value, "avgNtoValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateLessThanOrEqualTo(Double value) {
            addCriterion("avg_nto_valid_live_aid_rate <=", value, "avgNtoValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateIn(List<Double> values) {
            addCriterion("avg_nto_valid_live_aid_rate in", values, "avgNtoValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateNotIn(List<Double> values) {
            addCriterion("avg_nto_valid_live_aid_rate not in", values, "avgNtoValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateBetween(Double value1, Double value2) {
            addCriterion("avg_nto_valid_live_aid_rate between", value1, value2, "avgNtoValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateNotBetween(Double value1, Double value2) {
            addCriterion("avg_nto_valid_live_aid_rate not between", value1, value2, "avgNtoValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnIsNull() {
            addCriterion("avg_nto_valid_live_aid_rate_rn is null");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnIsNotNull() {
            addCriterion("avg_nto_valid_live_aid_rate_rn is not null");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnEqualTo(Long value) {
            addCriterion("avg_nto_valid_live_aid_rate_rn =", value, "avgNtoValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnNotEqualTo(Long value) {
            addCriterion("avg_nto_valid_live_aid_rate_rn <>", value, "avgNtoValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnGreaterThan(Long value) {
            addCriterion("avg_nto_valid_live_aid_rate_rn >", value, "avgNtoValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnGreaterThanOrEqualTo(Long value) {
            addCriterion("avg_nto_valid_live_aid_rate_rn >=", value, "avgNtoValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnLessThan(Long value) {
            addCriterion("avg_nto_valid_live_aid_rate_rn <", value, "avgNtoValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnLessThanOrEqualTo(Long value) {
            addCriterion("avg_nto_valid_live_aid_rate_rn <=", value, "avgNtoValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnIn(List<Long> values) {
            addCriterion("avg_nto_valid_live_aid_rate_rn in", values, "avgNtoValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnNotIn(List<Long> values) {
            addCriterion("avg_nto_valid_live_aid_rate_rn not in", values, "avgNtoValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnBetween(Long value1, Long value2) {
            addCriterion("avg_nto_valid_live_aid_rate_rn between", value1, value2, "avgNtoValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnNotBetween(Long value1, Long value2) {
            addCriterion("avg_nto_valid_live_aid_rate_rn not between", value1, value2, "avgNtoValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnScoreIsNull() {
            addCriterion("avg_nto_valid_live_aid_rate_rn_score is null");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnScoreIsNotNull() {
            addCriterion("avg_nto_valid_live_aid_rate_rn_score is not null");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnScoreEqualTo(Integer value) {
            addCriterion("avg_nto_valid_live_aid_rate_rn_score =", value, "avgNtoValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnScoreNotEqualTo(Integer value) {
            addCriterion("avg_nto_valid_live_aid_rate_rn_score <>", value, "avgNtoValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnScoreGreaterThan(Integer value) {
            addCriterion("avg_nto_valid_live_aid_rate_rn_score >", value, "avgNtoValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("avg_nto_valid_live_aid_rate_rn_score >=", value, "avgNtoValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnScoreLessThan(Integer value) {
            addCriterion("avg_nto_valid_live_aid_rate_rn_score <", value, "avgNtoValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnScoreLessThanOrEqualTo(Integer value) {
            addCriterion("avg_nto_valid_live_aid_rate_rn_score <=", value, "avgNtoValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnScoreIn(List<Integer> values) {
            addCriterion("avg_nto_valid_live_aid_rate_rn_score in", values, "avgNtoValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnScoreNotIn(List<Integer> values) {
            addCriterion("avg_nto_valid_live_aid_rate_rn_score not in", values, "avgNtoValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnScoreBetween(Integer value1, Integer value2) {
            addCriterion("avg_nto_valid_live_aid_rate_rn_score between", value1, value2, "avgNtoValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("avg_nto_valid_live_aid_rate_rn_score not between", value1, value2, "avgNtoValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnNextScoreIsNull() {
            addCriterion("avg_nto_valid_live_aid_rate_rn_next_score is null");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnNextScoreIsNotNull() {
            addCriterion("avg_nto_valid_live_aid_rate_rn_next_score is not null");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnNextScoreEqualTo(Integer value) {
            addCriterion("avg_nto_valid_live_aid_rate_rn_next_score =", value, "avgNtoValidLiveAidRateRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnNextScoreNotEqualTo(Integer value) {
            addCriterion("avg_nto_valid_live_aid_rate_rn_next_score <>", value, "avgNtoValidLiveAidRateRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnNextScoreGreaterThan(Integer value) {
            addCriterion("avg_nto_valid_live_aid_rate_rn_next_score >", value, "avgNtoValidLiveAidRateRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnNextScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("avg_nto_valid_live_aid_rate_rn_next_score >=", value, "avgNtoValidLiveAidRateRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnNextScoreLessThan(Integer value) {
            addCriterion("avg_nto_valid_live_aid_rate_rn_next_score <", value, "avgNtoValidLiveAidRateRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnNextScoreLessThanOrEqualTo(Integer value) {
            addCriterion("avg_nto_valid_live_aid_rate_rn_next_score <=", value, "avgNtoValidLiveAidRateRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnNextScoreIn(List<Integer> values) {
            addCriterion("avg_nto_valid_live_aid_rate_rn_next_score in", values, "avgNtoValidLiveAidRateRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnNextScoreNotIn(List<Integer> values) {
            addCriterion("avg_nto_valid_live_aid_rate_rn_next_score not in", values, "avgNtoValidLiveAidRateRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnNextScoreBetween(Integer value1, Integer value2) {
            addCriterion("avg_nto_valid_live_aid_rate_rn_next_score between", value1, value2, "avgNtoValidLiveAidRateRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnNextScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("avg_nto_valid_live_aid_rate_rn_next_score not between", value1, value2, "avgNtoValidLiveAidRateRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateTrgtDiffIsNull() {
            addCriterion("avg_nto_valid_live_aid_rate_trgt_diff is null");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateTrgtDiffIsNotNull() {
            addCriterion("avg_nto_valid_live_aid_rate_trgt_diff is not null");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateTrgtDiffEqualTo(Double value) {
            addCriterion("avg_nto_valid_live_aid_rate_trgt_diff =", value, "avgNtoValidLiveAidRateTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateTrgtDiffNotEqualTo(Double value) {
            addCriterion("avg_nto_valid_live_aid_rate_trgt_diff <>", value, "avgNtoValidLiveAidRateTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateTrgtDiffGreaterThan(Double value) {
            addCriterion("avg_nto_valid_live_aid_rate_trgt_diff >", value, "avgNtoValidLiveAidRateTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateTrgtDiffGreaterThanOrEqualTo(Double value) {
            addCriterion("avg_nto_valid_live_aid_rate_trgt_diff >=", value, "avgNtoValidLiveAidRateTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateTrgtDiffLessThan(Double value) {
            addCriterion("avg_nto_valid_live_aid_rate_trgt_diff <", value, "avgNtoValidLiveAidRateTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateTrgtDiffLessThanOrEqualTo(Double value) {
            addCriterion("avg_nto_valid_live_aid_rate_trgt_diff <=", value, "avgNtoValidLiveAidRateTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateTrgtDiffIn(List<Double> values) {
            addCriterion("avg_nto_valid_live_aid_rate_trgt_diff in", values, "avgNtoValidLiveAidRateTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateTrgtDiffNotIn(List<Double> values) {
            addCriterion("avg_nto_valid_live_aid_rate_trgt_diff not in", values, "avgNtoValidLiveAidRateTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateTrgtDiffBetween(Double value1, Double value2) {
            addCriterion("avg_nto_valid_live_aid_rate_trgt_diff between", value1, value2, "avgNtoValidLiveAidRateTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateTrgtDiffNotBetween(Double value1, Double value2) {
            addCriterion("avg_nto_valid_live_aid_rate_trgt_diff not between", value1, value2, "avgNtoValidLiveAidRateTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateIsNull() {
            addCriterion("avg_stock_valid_live_aid_rate is null");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateIsNotNull() {
            addCriterion("avg_stock_valid_live_aid_rate is not null");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateEqualTo(Double value) {
            addCriterion("avg_stock_valid_live_aid_rate =", value, "avgStockValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateNotEqualTo(Double value) {
            addCriterion("avg_stock_valid_live_aid_rate <>", value, "avgStockValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateGreaterThan(Double value) {
            addCriterion("avg_stock_valid_live_aid_rate >", value, "avgStockValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateGreaterThanOrEqualTo(Double value) {
            addCriterion("avg_stock_valid_live_aid_rate >=", value, "avgStockValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateLessThan(Double value) {
            addCriterion("avg_stock_valid_live_aid_rate <", value, "avgStockValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateLessThanOrEqualTo(Double value) {
            addCriterion("avg_stock_valid_live_aid_rate <=", value, "avgStockValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateIn(List<Double> values) {
            addCriterion("avg_stock_valid_live_aid_rate in", values, "avgStockValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateNotIn(List<Double> values) {
            addCriterion("avg_stock_valid_live_aid_rate not in", values, "avgStockValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateBetween(Double value1, Double value2) {
            addCriterion("avg_stock_valid_live_aid_rate between", value1, value2, "avgStockValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateNotBetween(Double value1, Double value2) {
            addCriterion("avg_stock_valid_live_aid_rate not between", value1, value2, "avgStockValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnIsNull() {
            addCriterion("avg_stock_valid_live_aid_rate_rn is null");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnIsNotNull() {
            addCriterion("avg_stock_valid_live_aid_rate_rn is not null");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnEqualTo(Long value) {
            addCriterion("avg_stock_valid_live_aid_rate_rn =", value, "avgStockValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnNotEqualTo(Long value) {
            addCriterion("avg_stock_valid_live_aid_rate_rn <>", value, "avgStockValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnGreaterThan(Long value) {
            addCriterion("avg_stock_valid_live_aid_rate_rn >", value, "avgStockValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnGreaterThanOrEqualTo(Long value) {
            addCriterion("avg_stock_valid_live_aid_rate_rn >=", value, "avgStockValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnLessThan(Long value) {
            addCriterion("avg_stock_valid_live_aid_rate_rn <", value, "avgStockValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnLessThanOrEqualTo(Long value) {
            addCriterion("avg_stock_valid_live_aid_rate_rn <=", value, "avgStockValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnIn(List<Long> values) {
            addCriterion("avg_stock_valid_live_aid_rate_rn in", values, "avgStockValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnNotIn(List<Long> values) {
            addCriterion("avg_stock_valid_live_aid_rate_rn not in", values, "avgStockValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnBetween(Long value1, Long value2) {
            addCriterion("avg_stock_valid_live_aid_rate_rn between", value1, value2, "avgStockValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnNotBetween(Long value1, Long value2) {
            addCriterion("avg_stock_valid_live_aid_rate_rn not between", value1, value2, "avgStockValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnScoreIsNull() {
            addCriterion("avg_stock_valid_live_aid_rate_rn_score is null");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnScoreIsNotNull() {
            addCriterion("avg_stock_valid_live_aid_rate_rn_score is not null");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnScoreEqualTo(Integer value) {
            addCriterion("avg_stock_valid_live_aid_rate_rn_score =", value, "avgStockValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnScoreNotEqualTo(Integer value) {
            addCriterion("avg_stock_valid_live_aid_rate_rn_score <>", value, "avgStockValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnScoreGreaterThan(Integer value) {
            addCriterion("avg_stock_valid_live_aid_rate_rn_score >", value, "avgStockValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("avg_stock_valid_live_aid_rate_rn_score >=", value, "avgStockValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnScoreLessThan(Integer value) {
            addCriterion("avg_stock_valid_live_aid_rate_rn_score <", value, "avgStockValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnScoreLessThanOrEqualTo(Integer value) {
            addCriterion("avg_stock_valid_live_aid_rate_rn_score <=", value, "avgStockValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnScoreIn(List<Integer> values) {
            addCriterion("avg_stock_valid_live_aid_rate_rn_score in", values, "avgStockValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnScoreNotIn(List<Integer> values) {
            addCriterion("avg_stock_valid_live_aid_rate_rn_score not in", values, "avgStockValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnScoreBetween(Integer value1, Integer value2) {
            addCriterion("avg_stock_valid_live_aid_rate_rn_score between", value1, value2, "avgStockValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("avg_stock_valid_live_aid_rate_rn_score not between", value1, value2, "avgStockValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnNextScoreIsNull() {
            addCriterion("avg_stock_valid_live_aid_rate_rn_next_score is null");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnNextScoreIsNotNull() {
            addCriterion("avg_stock_valid_live_aid_rate_rn_next_score is not null");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnNextScoreEqualTo(Integer value) {
            addCriterion("avg_stock_valid_live_aid_rate_rn_next_score =", value, "avgStockValidLiveAidRateRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnNextScoreNotEqualTo(Integer value) {
            addCriterion("avg_stock_valid_live_aid_rate_rn_next_score <>", value, "avgStockValidLiveAidRateRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnNextScoreGreaterThan(Integer value) {
            addCriterion("avg_stock_valid_live_aid_rate_rn_next_score >", value, "avgStockValidLiveAidRateRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnNextScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("avg_stock_valid_live_aid_rate_rn_next_score >=", value, "avgStockValidLiveAidRateRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnNextScoreLessThan(Integer value) {
            addCriterion("avg_stock_valid_live_aid_rate_rn_next_score <", value, "avgStockValidLiveAidRateRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnNextScoreLessThanOrEqualTo(Integer value) {
            addCriterion("avg_stock_valid_live_aid_rate_rn_next_score <=", value, "avgStockValidLiveAidRateRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnNextScoreIn(List<Integer> values) {
            addCriterion("avg_stock_valid_live_aid_rate_rn_next_score in", values, "avgStockValidLiveAidRateRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnNextScoreNotIn(List<Integer> values) {
            addCriterion("avg_stock_valid_live_aid_rate_rn_next_score not in", values, "avgStockValidLiveAidRateRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnNextScoreBetween(Integer value1, Integer value2) {
            addCriterion("avg_stock_valid_live_aid_rate_rn_next_score between", value1, value2, "avgStockValidLiveAidRateRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnNextScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("avg_stock_valid_live_aid_rate_rn_next_score not between", value1, value2, "avgStockValidLiveAidRateRnNextScore");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateTrgtDiffIsNull() {
            addCriterion("avg_stock_valid_live_aid_rate_trgt_diff is null");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateTrgtDiffIsNotNull() {
            addCriterion("avg_stock_valid_live_aid_rate_trgt_diff is not null");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateTrgtDiffEqualTo(Double value) {
            addCriterion("avg_stock_valid_live_aid_rate_trgt_diff =", value, "avgStockValidLiveAidRateTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateTrgtDiffNotEqualTo(Double value) {
            addCriterion("avg_stock_valid_live_aid_rate_trgt_diff <>", value, "avgStockValidLiveAidRateTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateTrgtDiffGreaterThan(Double value) {
            addCriterion("avg_stock_valid_live_aid_rate_trgt_diff >", value, "avgStockValidLiveAidRateTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateTrgtDiffGreaterThanOrEqualTo(Double value) {
            addCriterion("avg_stock_valid_live_aid_rate_trgt_diff >=", value, "avgStockValidLiveAidRateTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateTrgtDiffLessThan(Double value) {
            addCriterion("avg_stock_valid_live_aid_rate_trgt_diff <", value, "avgStockValidLiveAidRateTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateTrgtDiffLessThanOrEqualTo(Double value) {
            addCriterion("avg_stock_valid_live_aid_rate_trgt_diff <=", value, "avgStockValidLiveAidRateTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateTrgtDiffIn(List<Double> values) {
            addCriterion("avg_stock_valid_live_aid_rate_trgt_diff in", values, "avgStockValidLiveAidRateTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateTrgtDiffNotIn(List<Double> values) {
            addCriterion("avg_stock_valid_live_aid_rate_trgt_diff not in", values, "avgStockValidLiveAidRateTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateTrgtDiffBetween(Double value1, Double value2) {
            addCriterion("avg_stock_valid_live_aid_rate_trgt_diff between", value1, value2, "avgStockValidLiveAidRateTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateTrgtDiffNotBetween(Double value1, Double value2) {
            addCriterion("avg_stock_valid_live_aid_rate_trgt_diff not between", value1, value2, "avgStockValidLiveAidRateTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumIsNull() {
            addCriterion("new_auth_golden_aid_num is null");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumIsNotNull() {
            addCriterion("new_auth_golden_aid_num is not null");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumEqualTo(Long value) {
            addCriterion("new_auth_golden_aid_num =", value, "newAuthGoldenAidNum");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumNotEqualTo(Long value) {
            addCriterion("new_auth_golden_aid_num <>", value, "newAuthGoldenAidNum");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumGreaterThan(Long value) {
            addCriterion("new_auth_golden_aid_num >", value, "newAuthGoldenAidNum");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumGreaterThanOrEqualTo(Long value) {
            addCriterion("new_auth_golden_aid_num >=", value, "newAuthGoldenAidNum");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumLessThan(Long value) {
            addCriterion("new_auth_golden_aid_num <", value, "newAuthGoldenAidNum");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumLessThanOrEqualTo(Long value) {
            addCriterion("new_auth_golden_aid_num <=", value, "newAuthGoldenAidNum");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumIn(List<Long> values) {
            addCriterion("new_auth_golden_aid_num in", values, "newAuthGoldenAidNum");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumNotIn(List<Long> values) {
            addCriterion("new_auth_golden_aid_num not in", values, "newAuthGoldenAidNum");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumBetween(Long value1, Long value2) {
            addCriterion("new_auth_golden_aid_num between", value1, value2, "newAuthGoldenAidNum");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumNotBetween(Long value1, Long value2) {
            addCriterion("new_auth_golden_aid_num not between", value1, value2, "newAuthGoldenAidNum");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnIsNull() {
            addCriterion("new_auth_golden_aid_num_rn is null");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnIsNotNull() {
            addCriterion("new_auth_golden_aid_num_rn is not null");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnEqualTo(Long value) {
            addCriterion("new_auth_golden_aid_num_rn =", value, "newAuthGoldenAidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnNotEqualTo(Long value) {
            addCriterion("new_auth_golden_aid_num_rn <>", value, "newAuthGoldenAidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnGreaterThan(Long value) {
            addCriterion("new_auth_golden_aid_num_rn >", value, "newAuthGoldenAidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnGreaterThanOrEqualTo(Long value) {
            addCriterion("new_auth_golden_aid_num_rn >=", value, "newAuthGoldenAidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnLessThan(Long value) {
            addCriterion("new_auth_golden_aid_num_rn <", value, "newAuthGoldenAidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnLessThanOrEqualTo(Long value) {
            addCriterion("new_auth_golden_aid_num_rn <=", value, "newAuthGoldenAidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnIn(List<Long> values) {
            addCriterion("new_auth_golden_aid_num_rn in", values, "newAuthGoldenAidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnNotIn(List<Long> values) {
            addCriterion("new_auth_golden_aid_num_rn not in", values, "newAuthGoldenAidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnBetween(Long value1, Long value2) {
            addCriterion("new_auth_golden_aid_num_rn between", value1, value2, "newAuthGoldenAidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnNotBetween(Long value1, Long value2) {
            addCriterion("new_auth_golden_aid_num_rn not between", value1, value2, "newAuthGoldenAidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnScoreIsNull() {
            addCriterion("new_auth_golden_aid_num_rn_score is null");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnScoreIsNotNull() {
            addCriterion("new_auth_golden_aid_num_rn_score is not null");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnScoreEqualTo(Integer value) {
            addCriterion("new_auth_golden_aid_num_rn_score =", value, "newAuthGoldenAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnScoreNotEqualTo(Integer value) {
            addCriterion("new_auth_golden_aid_num_rn_score <>", value, "newAuthGoldenAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnScoreGreaterThan(Integer value) {
            addCriterion("new_auth_golden_aid_num_rn_score >", value, "newAuthGoldenAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("new_auth_golden_aid_num_rn_score >=", value, "newAuthGoldenAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnScoreLessThan(Integer value) {
            addCriterion("new_auth_golden_aid_num_rn_score <", value, "newAuthGoldenAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnScoreLessThanOrEqualTo(Integer value) {
            addCriterion("new_auth_golden_aid_num_rn_score <=", value, "newAuthGoldenAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnScoreIn(List<Integer> values) {
            addCriterion("new_auth_golden_aid_num_rn_score in", values, "newAuthGoldenAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnScoreNotIn(List<Integer> values) {
            addCriterion("new_auth_golden_aid_num_rn_score not in", values, "newAuthGoldenAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnScoreBetween(Integer value1, Integer value2) {
            addCriterion("new_auth_golden_aid_num_rn_score between", value1, value2, "newAuthGoldenAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("new_auth_golden_aid_num_rn_score not between", value1, value2, "newAuthGoldenAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnNextScoreIsNull() {
            addCriterion("new_auth_golden_aid_num_rn_next_score is null");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnNextScoreIsNotNull() {
            addCriterion("new_auth_golden_aid_num_rn_next_score is not null");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnNextScoreEqualTo(Integer value) {
            addCriterion("new_auth_golden_aid_num_rn_next_score =", value, "newAuthGoldenAidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnNextScoreNotEqualTo(Integer value) {
            addCriterion("new_auth_golden_aid_num_rn_next_score <>", value, "newAuthGoldenAidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnNextScoreGreaterThan(Integer value) {
            addCriterion("new_auth_golden_aid_num_rn_next_score >", value, "newAuthGoldenAidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnNextScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("new_auth_golden_aid_num_rn_next_score >=", value, "newAuthGoldenAidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnNextScoreLessThan(Integer value) {
            addCriterion("new_auth_golden_aid_num_rn_next_score <", value, "newAuthGoldenAidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnNextScoreLessThanOrEqualTo(Integer value) {
            addCriterion("new_auth_golden_aid_num_rn_next_score <=", value, "newAuthGoldenAidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnNextScoreIn(List<Integer> values) {
            addCriterion("new_auth_golden_aid_num_rn_next_score in", values, "newAuthGoldenAidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnNextScoreNotIn(List<Integer> values) {
            addCriterion("new_auth_golden_aid_num_rn_next_score not in", values, "newAuthGoldenAidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnNextScoreBetween(Integer value1, Integer value2) {
            addCriterion("new_auth_golden_aid_num_rn_next_score between", value1, value2, "newAuthGoldenAidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnNextScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("new_auth_golden_aid_num_rn_next_score not between", value1, value2, "newAuthGoldenAidNumRnNextScore");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumTrgtDiffIsNull() {
            addCriterion("new_auth_golden_aid_num_trgt_diff is null");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumTrgtDiffIsNotNull() {
            addCriterion("new_auth_golden_aid_num_trgt_diff is not null");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumTrgtDiffEqualTo(Long value) {
            addCriterion("new_auth_golden_aid_num_trgt_diff =", value, "newAuthGoldenAidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumTrgtDiffNotEqualTo(Long value) {
            addCriterion("new_auth_golden_aid_num_trgt_diff <>", value, "newAuthGoldenAidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumTrgtDiffGreaterThan(Long value) {
            addCriterion("new_auth_golden_aid_num_trgt_diff >", value, "newAuthGoldenAidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumTrgtDiffGreaterThanOrEqualTo(Long value) {
            addCriterion("new_auth_golden_aid_num_trgt_diff >=", value, "newAuthGoldenAidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumTrgtDiffLessThan(Long value) {
            addCriterion("new_auth_golden_aid_num_trgt_diff <", value, "newAuthGoldenAidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumTrgtDiffLessThanOrEqualTo(Long value) {
            addCriterion("new_auth_golden_aid_num_trgt_diff <=", value, "newAuthGoldenAidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumTrgtDiffIn(List<Long> values) {
            addCriterion("new_auth_golden_aid_num_trgt_diff in", values, "newAuthGoldenAidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumTrgtDiffNotIn(List<Long> values) {
            addCriterion("new_auth_golden_aid_num_trgt_diff not in", values, "newAuthGoldenAidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumTrgtDiffBetween(Long value1, Long value2) {
            addCriterion("new_auth_golden_aid_num_trgt_diff between", value1, value2, "newAuthGoldenAidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumTrgtDiffNotBetween(Long value1, Long value2) {
            addCriterion("new_auth_golden_aid_num_trgt_diff not between", value1, value2, "newAuthGoldenAidNumTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtIsNull() {
            addCriterion("valid_live_new_aid_incm_amt is null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtIsNotNull() {
            addCriterion("valid_live_new_aid_incm_amt is not null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtEqualTo(Double value) {
            addCriterion("valid_live_new_aid_incm_amt =", value, "validLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtNotEqualTo(Double value) {
            addCriterion("valid_live_new_aid_incm_amt <>", value, "validLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtGreaterThan(Double value) {
            addCriterion("valid_live_new_aid_incm_amt >", value, "validLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtGreaterThanOrEqualTo(Double value) {
            addCriterion("valid_live_new_aid_incm_amt >=", value, "validLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtLessThan(Double value) {
            addCriterion("valid_live_new_aid_incm_amt <", value, "validLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtLessThanOrEqualTo(Double value) {
            addCriterion("valid_live_new_aid_incm_amt <=", value, "validLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtIn(List<Double> values) {
            addCriterion("valid_live_new_aid_incm_amt in", values, "validLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtNotIn(List<Double> values) {
            addCriterion("valid_live_new_aid_incm_amt not in", values, "validLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtBetween(Double value1, Double value2) {
            addCriterion("valid_live_new_aid_incm_amt between", value1, value2, "validLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtNotBetween(Double value1, Double value2) {
            addCriterion("valid_live_new_aid_incm_amt not between", value1, value2, "validLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnIsNull() {
            addCriterion("valid_live_new_aid_incm_amt_rn is null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnIsNotNull() {
            addCriterion("valid_live_new_aid_incm_amt_rn is not null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnEqualTo(Long value) {
            addCriterion("valid_live_new_aid_incm_amt_rn =", value, "validLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnNotEqualTo(Long value) {
            addCriterion("valid_live_new_aid_incm_amt_rn <>", value, "validLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnGreaterThan(Long value) {
            addCriterion("valid_live_new_aid_incm_amt_rn >", value, "validLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnGreaterThanOrEqualTo(Long value) {
            addCriterion("valid_live_new_aid_incm_amt_rn >=", value, "validLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnLessThan(Long value) {
            addCriterion("valid_live_new_aid_incm_amt_rn <", value, "validLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnLessThanOrEqualTo(Long value) {
            addCriterion("valid_live_new_aid_incm_amt_rn <=", value, "validLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnIn(List<Long> values) {
            addCriterion("valid_live_new_aid_incm_amt_rn in", values, "validLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnNotIn(List<Long> values) {
            addCriterion("valid_live_new_aid_incm_amt_rn not in", values, "validLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnBetween(Long value1, Long value2) {
            addCriterion("valid_live_new_aid_incm_amt_rn between", value1, value2, "validLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnNotBetween(Long value1, Long value2) {
            addCriterion("valid_live_new_aid_incm_amt_rn not between", value1, value2, "validLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnScoreIsNull() {
            addCriterion("valid_live_new_aid_incm_amt_rn_score is null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnScoreIsNotNull() {
            addCriterion("valid_live_new_aid_incm_amt_rn_score is not null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnScoreEqualTo(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_rn_score =", value, "validLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnScoreNotEqualTo(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_rn_score <>", value, "validLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnScoreGreaterThan(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_rn_score >", value, "validLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_rn_score >=", value, "validLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnScoreLessThan(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_rn_score <", value, "validLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnScoreLessThanOrEqualTo(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_rn_score <=", value, "validLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnScoreIn(List<Integer> values) {
            addCriterion("valid_live_new_aid_incm_amt_rn_score in", values, "validLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnScoreNotIn(List<Integer> values) {
            addCriterion("valid_live_new_aid_incm_amt_rn_score not in", values, "validLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnScoreBetween(Integer value1, Integer value2) {
            addCriterion("valid_live_new_aid_incm_amt_rn_score between", value1, value2, "validLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("valid_live_new_aid_incm_amt_rn_score not between", value1, value2, "validLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnNextScoreIsNull() {
            addCriterion("valid_live_new_aid_incm_amt_rn_next_score is null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnNextScoreIsNotNull() {
            addCriterion("valid_live_new_aid_incm_amt_rn_next_score is not null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnNextScoreEqualTo(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_rn_next_score =", value, "validLiveNewAidIncmAmtRnNextScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnNextScoreNotEqualTo(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_rn_next_score <>", value, "validLiveNewAidIncmAmtRnNextScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnNextScoreGreaterThan(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_rn_next_score >", value, "validLiveNewAidIncmAmtRnNextScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnNextScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_rn_next_score >=", value, "validLiveNewAidIncmAmtRnNextScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnNextScoreLessThan(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_rn_next_score <", value, "validLiveNewAidIncmAmtRnNextScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnNextScoreLessThanOrEqualTo(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_rn_next_score <=", value, "validLiveNewAidIncmAmtRnNextScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnNextScoreIn(List<Integer> values) {
            addCriterion("valid_live_new_aid_incm_amt_rn_next_score in", values, "validLiveNewAidIncmAmtRnNextScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnNextScoreNotIn(List<Integer> values) {
            addCriterion("valid_live_new_aid_incm_amt_rn_next_score not in", values, "validLiveNewAidIncmAmtRnNextScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnNextScoreBetween(Integer value1, Integer value2) {
            addCriterion("valid_live_new_aid_incm_amt_rn_next_score between", value1, value2, "validLiveNewAidIncmAmtRnNextScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnNextScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("valid_live_new_aid_incm_amt_rn_next_score not between", value1, value2, "validLiveNewAidIncmAmtRnNextScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtTrgtDiffIsNull() {
            addCriterion("valid_live_new_aid_incm_amt_trgt_diff is null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtTrgtDiffIsNotNull() {
            addCriterion("valid_live_new_aid_incm_amt_trgt_diff is not null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtTrgtDiffEqualTo(Double value) {
            addCriterion("valid_live_new_aid_incm_amt_trgt_diff =", value, "validLiveNewAidIncmAmtTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtTrgtDiffNotEqualTo(Double value) {
            addCriterion("valid_live_new_aid_incm_amt_trgt_diff <>", value, "validLiveNewAidIncmAmtTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtTrgtDiffGreaterThan(Double value) {
            addCriterion("valid_live_new_aid_incm_amt_trgt_diff >", value, "validLiveNewAidIncmAmtTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtTrgtDiffGreaterThanOrEqualTo(Double value) {
            addCriterion("valid_live_new_aid_incm_amt_trgt_diff >=", value, "validLiveNewAidIncmAmtTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtTrgtDiffLessThan(Double value) {
            addCriterion("valid_live_new_aid_incm_amt_trgt_diff <", value, "validLiveNewAidIncmAmtTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtTrgtDiffLessThanOrEqualTo(Double value) {
            addCriterion("valid_live_new_aid_incm_amt_trgt_diff <=", value, "validLiveNewAidIncmAmtTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtTrgtDiffIn(List<Double> values) {
            addCriterion("valid_live_new_aid_incm_amt_trgt_diff in", values, "validLiveNewAidIncmAmtTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtTrgtDiffNotIn(List<Double> values) {
            addCriterion("valid_live_new_aid_incm_amt_trgt_diff not in", values, "validLiveNewAidIncmAmtTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtTrgtDiffBetween(Double value1, Double value2) {
            addCriterion("valid_live_new_aid_incm_amt_trgt_diff between", value1, value2, "validLiveNewAidIncmAmtTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtTrgtDiffNotBetween(Double value1, Double value2) {
            addCriterion("valid_live_new_aid_incm_amt_trgt_diff not between", value1, value2, "validLiveNewAidIncmAmtTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffIsNull() {
            addCriterion("valid_live_new_aid_incm_amt_diff is null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffIsNotNull() {
            addCriterion("valid_live_new_aid_incm_amt_diff is not null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffEqualTo(Double value) {
            addCriterion("valid_live_new_aid_incm_amt_diff =", value, "validLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffNotEqualTo(Double value) {
            addCriterion("valid_live_new_aid_incm_amt_diff <>", value, "validLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffGreaterThan(Double value) {
            addCriterion("valid_live_new_aid_incm_amt_diff >", value, "validLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffGreaterThanOrEqualTo(Double value) {
            addCriterion("valid_live_new_aid_incm_amt_diff >=", value, "validLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffLessThan(Double value) {
            addCriterion("valid_live_new_aid_incm_amt_diff <", value, "validLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffLessThanOrEqualTo(Double value) {
            addCriterion("valid_live_new_aid_incm_amt_diff <=", value, "validLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffIn(List<Double> values) {
            addCriterion("valid_live_new_aid_incm_amt_diff in", values, "validLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffNotIn(List<Double> values) {
            addCriterion("valid_live_new_aid_incm_amt_diff not in", values, "validLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffBetween(Double value1, Double value2) {
            addCriterion("valid_live_new_aid_incm_amt_diff between", value1, value2, "validLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffNotBetween(Double value1, Double value2) {
            addCriterion("valid_live_new_aid_incm_amt_diff not between", value1, value2, "validLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffScoreIsNull() {
            addCriterion("valid_live_new_aid_incm_amt_diff_score is null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffScoreIsNotNull() {
            addCriterion("valid_live_new_aid_incm_amt_diff_score is not null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffScoreEqualTo(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_diff_score =", value, "validLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffScoreNotEqualTo(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_diff_score <>", value, "validLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffScoreGreaterThan(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_diff_score >", value, "validLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_diff_score >=", value, "validLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffScoreLessThan(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_diff_score <", value, "validLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffScoreLessThanOrEqualTo(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_diff_score <=", value, "validLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffScoreIn(List<Integer> values) {
            addCriterion("valid_live_new_aid_incm_amt_diff_score in", values, "validLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffScoreNotIn(List<Integer> values) {
            addCriterion("valid_live_new_aid_incm_amt_diff_score not in", values, "validLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffScoreBetween(Integer value1, Integer value2) {
            addCriterion("valid_live_new_aid_incm_amt_diff_score between", value1, value2, "validLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("valid_live_new_aid_incm_amt_diff_score not between", value1, value2, "validLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffNextScoreIsNull() {
            addCriterion("valid_live_new_aid_incm_amt_diff_next_score is null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffNextScoreIsNotNull() {
            addCriterion("valid_live_new_aid_incm_amt_diff_next_score is not null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffNextScoreEqualTo(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_diff_next_score =", value, "validLiveNewAidIncmAmtDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffNextScoreNotEqualTo(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_diff_next_score <>", value, "validLiveNewAidIncmAmtDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffNextScoreGreaterThan(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_diff_next_score >", value, "validLiveNewAidIncmAmtDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffNextScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_diff_next_score >=", value, "validLiveNewAidIncmAmtDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffNextScoreLessThan(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_diff_next_score <", value, "validLiveNewAidIncmAmtDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffNextScoreLessThanOrEqualTo(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_diff_next_score <=", value, "validLiveNewAidIncmAmtDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffNextScoreIn(List<Integer> values) {
            addCriterion("valid_live_new_aid_incm_amt_diff_next_score in", values, "validLiveNewAidIncmAmtDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffNextScoreNotIn(List<Integer> values) {
            addCriterion("valid_live_new_aid_incm_amt_diff_next_score not in", values, "validLiveNewAidIncmAmtDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffNextScoreBetween(Integer value1, Integer value2) {
            addCriterion("valid_live_new_aid_incm_amt_diff_next_score between", value1, value2, "validLiveNewAidIncmAmtDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffNextScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("valid_live_new_aid_incm_amt_diff_next_score not between", value1, value2, "validLiveNewAidIncmAmtDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffTrgtDiffIsNull() {
            addCriterion("valid_live_new_aid_incm_amt_diff_trgt_diff is null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffTrgtDiffIsNotNull() {
            addCriterion("valid_live_new_aid_incm_amt_diff_trgt_diff is not null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffTrgtDiffEqualTo(Double value) {
            addCriterion("valid_live_new_aid_incm_amt_diff_trgt_diff =", value, "validLiveNewAidIncmAmtDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffTrgtDiffNotEqualTo(Double value) {
            addCriterion("valid_live_new_aid_incm_amt_diff_trgt_diff <>", value, "validLiveNewAidIncmAmtDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffTrgtDiffGreaterThan(Double value) {
            addCriterion("valid_live_new_aid_incm_amt_diff_trgt_diff >", value, "validLiveNewAidIncmAmtDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffTrgtDiffGreaterThanOrEqualTo(Double value) {
            addCriterion("valid_live_new_aid_incm_amt_diff_trgt_diff >=", value, "validLiveNewAidIncmAmtDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffTrgtDiffLessThan(Double value) {
            addCriterion("valid_live_new_aid_incm_amt_diff_trgt_diff <", value, "validLiveNewAidIncmAmtDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffTrgtDiffLessThanOrEqualTo(Double value) {
            addCriterion("valid_live_new_aid_incm_amt_diff_trgt_diff <=", value, "validLiveNewAidIncmAmtDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffTrgtDiffIn(List<Double> values) {
            addCriterion("valid_live_new_aid_incm_amt_diff_trgt_diff in", values, "validLiveNewAidIncmAmtDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffTrgtDiffNotIn(List<Double> values) {
            addCriterion("valid_live_new_aid_incm_amt_diff_trgt_diff not in", values, "validLiveNewAidIncmAmtDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffTrgtDiffBetween(Double value1, Double value2) {
            addCriterion("valid_live_new_aid_incm_amt_diff_trgt_diff between", value1, value2, "validLiveNewAidIncmAmtDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffTrgtDiffNotBetween(Double value1, Double value2) {
            addCriterion("valid_live_new_aid_incm_amt_diff_trgt_diff not between", value1, value2, "validLiveNewAidIncmAmtDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtIsNull() {
            addCriterion("unvalid_live_new_aid_incm_amt is null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtIsNotNull() {
            addCriterion("unvalid_live_new_aid_incm_amt is not null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt =", value, "unvalidLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtNotEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt <>", value, "unvalidLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtGreaterThan(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt >", value, "unvalidLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtGreaterThanOrEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt >=", value, "unvalidLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtLessThan(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt <", value, "unvalidLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtLessThanOrEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt <=", value, "unvalidLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtIn(List<Double> values) {
            addCriterion("unvalid_live_new_aid_incm_amt in", values, "unvalidLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtNotIn(List<Double> values) {
            addCriterion("unvalid_live_new_aid_incm_amt not in", values, "unvalidLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtBetween(Double value1, Double value2) {
            addCriterion("unvalid_live_new_aid_incm_amt between", value1, value2, "unvalidLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtNotBetween(Double value1, Double value2) {
            addCriterion("unvalid_live_new_aid_incm_amt not between", value1, value2, "unvalidLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnIsNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_rn is null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnIsNotNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_rn is not null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnEqualTo(Long value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn =", value, "unvalidLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnNotEqualTo(Long value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn <>", value, "unvalidLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnGreaterThan(Long value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn >", value, "unvalidLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnGreaterThanOrEqualTo(Long value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn >=", value, "unvalidLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnLessThan(Long value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn <", value, "unvalidLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnLessThanOrEqualTo(Long value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn <=", value, "unvalidLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnIn(List<Long> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn in", values, "unvalidLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnNotIn(List<Long> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn not in", values, "unvalidLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnBetween(Long value1, Long value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn between", value1, value2, "unvalidLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnNotBetween(Long value1, Long value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn not between", value1, value2, "unvalidLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnScoreIsNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_score is null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnScoreIsNotNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_score is not null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnScoreEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_score =", value, "unvalidLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnScoreNotEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_score <>", value, "unvalidLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnScoreGreaterThan(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_score >", value, "unvalidLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_score >=", value, "unvalidLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnScoreLessThan(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_score <", value, "unvalidLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnScoreLessThanOrEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_score <=", value, "unvalidLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnScoreIn(List<Integer> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_score in", values, "unvalidLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnScoreNotIn(List<Integer> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_score not in", values, "unvalidLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnScoreBetween(Integer value1, Integer value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_score between", value1, value2, "unvalidLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_score not between", value1, value2, "unvalidLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnNextScoreIsNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_next_score is null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnNextScoreIsNotNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_next_score is not null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnNextScoreEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_next_score =", value, "unvalidLiveNewAidIncmAmtRnNextScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnNextScoreNotEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_next_score <>", value, "unvalidLiveNewAidIncmAmtRnNextScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnNextScoreGreaterThan(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_next_score >", value, "unvalidLiveNewAidIncmAmtRnNextScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnNextScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_next_score >=", value, "unvalidLiveNewAidIncmAmtRnNextScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnNextScoreLessThan(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_next_score <", value, "unvalidLiveNewAidIncmAmtRnNextScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnNextScoreLessThanOrEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_next_score <=", value, "unvalidLiveNewAidIncmAmtRnNextScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnNextScoreIn(List<Integer> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_next_score in", values, "unvalidLiveNewAidIncmAmtRnNextScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnNextScoreNotIn(List<Integer> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_next_score not in", values, "unvalidLiveNewAidIncmAmtRnNextScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnNextScoreBetween(Integer value1, Integer value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_next_score between", value1, value2, "unvalidLiveNewAidIncmAmtRnNextScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnNextScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_next_score not between", value1, value2, "unvalidLiveNewAidIncmAmtRnNextScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtTrgtDiffIsNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_trgt_diff is null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtTrgtDiffIsNotNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_trgt_diff is not null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtTrgtDiffEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_trgt_diff =", value, "unvalidLiveNewAidIncmAmtTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtTrgtDiffNotEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_trgt_diff <>", value, "unvalidLiveNewAidIncmAmtTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtTrgtDiffGreaterThan(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_trgt_diff >", value, "unvalidLiveNewAidIncmAmtTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtTrgtDiffGreaterThanOrEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_trgt_diff >=", value, "unvalidLiveNewAidIncmAmtTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtTrgtDiffLessThan(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_trgt_diff <", value, "unvalidLiveNewAidIncmAmtTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtTrgtDiffLessThanOrEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_trgt_diff <=", value, "unvalidLiveNewAidIncmAmtTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtTrgtDiffIn(List<Double> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_trgt_diff in", values, "unvalidLiveNewAidIncmAmtTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtTrgtDiffNotIn(List<Double> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_trgt_diff not in", values, "unvalidLiveNewAidIncmAmtTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtTrgtDiffBetween(Double value1, Double value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_trgt_diff between", value1, value2, "unvalidLiveNewAidIncmAmtTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtTrgtDiffNotBetween(Double value1, Double value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_trgt_diff not between", value1, value2, "unvalidLiveNewAidIncmAmtTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffIsNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_diff is null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffIsNotNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_diff is not null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff =", value, "unvalidLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffNotEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff <>", value, "unvalidLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffGreaterThan(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff >", value, "unvalidLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffGreaterThanOrEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff >=", value, "unvalidLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffLessThan(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff <", value, "unvalidLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffLessThanOrEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff <=", value, "unvalidLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffIn(List<Double> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff in", values, "unvalidLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffNotIn(List<Double> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff not in", values, "unvalidLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffBetween(Double value1, Double value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff between", value1, value2, "unvalidLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffNotBetween(Double value1, Double value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff not between", value1, value2, "unvalidLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffScoreIsNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_score is null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffScoreIsNotNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_score is not null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffScoreEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_score =", value, "unvalidLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffScoreNotEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_score <>", value, "unvalidLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffScoreGreaterThan(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_score >", value, "unvalidLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_score >=", value, "unvalidLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffScoreLessThan(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_score <", value, "unvalidLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffScoreLessThanOrEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_score <=", value, "unvalidLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffScoreIn(List<Integer> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_score in", values, "unvalidLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffScoreNotIn(List<Integer> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_score not in", values, "unvalidLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffScoreBetween(Integer value1, Integer value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_score between", value1, value2, "unvalidLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_score not between", value1, value2, "unvalidLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffNextScoreIsNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_next_score is null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffNextScoreIsNotNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_next_score is not null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffNextScoreEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_next_score =", value, "unvalidLiveNewAidIncmAmtDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffNextScoreNotEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_next_score <>", value, "unvalidLiveNewAidIncmAmtDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffNextScoreGreaterThan(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_next_score >", value, "unvalidLiveNewAidIncmAmtDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffNextScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_next_score >=", value, "unvalidLiveNewAidIncmAmtDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffNextScoreLessThan(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_next_score <", value, "unvalidLiveNewAidIncmAmtDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffNextScoreLessThanOrEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_next_score <=", value, "unvalidLiveNewAidIncmAmtDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffNextScoreIn(List<Integer> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_next_score in", values, "unvalidLiveNewAidIncmAmtDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffNextScoreNotIn(List<Integer> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_next_score not in", values, "unvalidLiveNewAidIncmAmtDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffNextScoreBetween(Integer value1, Integer value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_next_score between", value1, value2, "unvalidLiveNewAidIncmAmtDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffNextScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_next_score not between", value1, value2, "unvalidLiveNewAidIncmAmtDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffTrgtDiffIsNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_trgt_diff is null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffTrgtDiffIsNotNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_trgt_diff is not null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffTrgtDiffEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_trgt_diff =", value, "unvalidLiveNewAidIncmAmtDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffTrgtDiffNotEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_trgt_diff <>", value, "unvalidLiveNewAidIncmAmtDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffTrgtDiffGreaterThan(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_trgt_diff >", value, "unvalidLiveNewAidIncmAmtDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffTrgtDiffGreaterThanOrEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_trgt_diff >=", value, "unvalidLiveNewAidIncmAmtDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffTrgtDiffLessThan(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_trgt_diff <", value, "unvalidLiveNewAidIncmAmtDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffTrgtDiffLessThanOrEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_trgt_diff <=", value, "unvalidLiveNewAidIncmAmtDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffTrgtDiffIn(List<Double> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_trgt_diff in", values, "unvalidLiveNewAidIncmAmtDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffTrgtDiffNotIn(List<Double> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_trgt_diff not in", values, "unvalidLiveNewAidIncmAmtDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffTrgtDiffBetween(Double value1, Double value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_trgt_diff between", value1, value2, "unvalidLiveNewAidIncmAmtDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffTrgtDiffNotBetween(Double value1, Double value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_trgt_diff not between", value1, value2, "unvalidLiveNewAidIncmAmtDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffIsNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff is null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffIsNotNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff is not null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff =", value, "unvalidLiveNewAidIncmAmtRateDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffNotEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff <>", value, "unvalidLiveNewAidIncmAmtRateDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffGreaterThan(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff >", value, "unvalidLiveNewAidIncmAmtRateDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffGreaterThanOrEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff >=", value, "unvalidLiveNewAidIncmAmtRateDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffLessThan(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff <", value, "unvalidLiveNewAidIncmAmtRateDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffLessThanOrEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff <=", value, "unvalidLiveNewAidIncmAmtRateDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffIn(List<Double> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff in", values, "unvalidLiveNewAidIncmAmtRateDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffNotIn(List<Double> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff not in", values, "unvalidLiveNewAidIncmAmtRateDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffBetween(Double value1, Double value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff between", value1, value2, "unvalidLiveNewAidIncmAmtRateDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffNotBetween(Double value1, Double value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff not between", value1, value2, "unvalidLiveNewAidIncmAmtRateDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffScoreIsNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_score is null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffScoreIsNotNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_score is not null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffScoreEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_score =", value, "unvalidLiveNewAidIncmAmtRateDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffScoreNotEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_score <>", value, "unvalidLiveNewAidIncmAmtRateDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffScoreGreaterThan(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_score >", value, "unvalidLiveNewAidIncmAmtRateDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_score >=", value, "unvalidLiveNewAidIncmAmtRateDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffScoreLessThan(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_score <", value, "unvalidLiveNewAidIncmAmtRateDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffScoreLessThanOrEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_score <=", value, "unvalidLiveNewAidIncmAmtRateDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffScoreIn(List<Integer> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_score in", values, "unvalidLiveNewAidIncmAmtRateDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffScoreNotIn(List<Integer> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_score not in", values, "unvalidLiveNewAidIncmAmtRateDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffScoreBetween(Integer value1, Integer value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_score between", value1, value2, "unvalidLiveNewAidIncmAmtRateDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_score not between", value1, value2, "unvalidLiveNewAidIncmAmtRateDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffNextScoreIsNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_next_score is null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffNextScoreIsNotNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_next_score is not null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffNextScoreEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_next_score =", value, "unvalidLiveNewAidIncmAmtRateDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffNextScoreNotEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_next_score <>", value, "unvalidLiveNewAidIncmAmtRateDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffNextScoreGreaterThan(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_next_score >", value, "unvalidLiveNewAidIncmAmtRateDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffNextScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_next_score >=", value, "unvalidLiveNewAidIncmAmtRateDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffNextScoreLessThan(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_next_score <", value, "unvalidLiveNewAidIncmAmtRateDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffNextScoreLessThanOrEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_next_score <=", value, "unvalidLiveNewAidIncmAmtRateDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffNextScoreIn(List<Integer> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_next_score in", values, "unvalidLiveNewAidIncmAmtRateDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffNextScoreNotIn(List<Integer> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_next_score not in", values, "unvalidLiveNewAidIncmAmtRateDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffNextScoreBetween(Integer value1, Integer value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_next_score between", value1, value2, "unvalidLiveNewAidIncmAmtRateDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffNextScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_next_score not between", value1, value2, "unvalidLiveNewAidIncmAmtRateDiffNextScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffTrgtDiffIsNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_trgt_diff is null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffTrgtDiffIsNotNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_trgt_diff is not null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffTrgtDiffEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_trgt_diff =", value, "unvalidLiveNewAidIncmAmtRateDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffTrgtDiffNotEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_trgt_diff <>", value, "unvalidLiveNewAidIncmAmtRateDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffTrgtDiffGreaterThan(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_trgt_diff >", value, "unvalidLiveNewAidIncmAmtRateDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffTrgtDiffGreaterThanOrEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_trgt_diff >=", value, "unvalidLiveNewAidIncmAmtRateDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffTrgtDiffLessThan(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_trgt_diff <", value, "unvalidLiveNewAidIncmAmtRateDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffTrgtDiffLessThanOrEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_trgt_diff <=", value, "unvalidLiveNewAidIncmAmtRateDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffTrgtDiffIn(List<Double> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_trgt_diff in", values, "unvalidLiveNewAidIncmAmtRateDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffTrgtDiffNotIn(List<Double> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_trgt_diff not in", values, "unvalidLiveNewAidIncmAmtRateDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffTrgtDiffBetween(Double value1, Double value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_trgt_diff between", value1, value2, "unvalidLiveNewAidIncmAmtRateDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffTrgtDiffNotBetween(Double value1, Double value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_trgt_diff not between", value1, value2, "unvalidLiveNewAidIncmAmtRateDiffTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointIsNull() {
            addCriterion("guild_health_point is null");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointIsNotNull() {
            addCriterion("guild_health_point is not null");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointEqualTo(Integer value) {
            addCriterion("guild_health_point =", value, "guildHealthPoint");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointNotEqualTo(Integer value) {
            addCriterion("guild_health_point <>", value, "guildHealthPoint");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointGreaterThan(Integer value) {
            addCriterion("guild_health_point >", value, "guildHealthPoint");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointGreaterThanOrEqualTo(Integer value) {
            addCriterion("guild_health_point >=", value, "guildHealthPoint");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointLessThan(Integer value) {
            addCriterion("guild_health_point <", value, "guildHealthPoint");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointLessThanOrEqualTo(Integer value) {
            addCriterion("guild_health_point <=", value, "guildHealthPoint");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointIn(List<Integer> values) {
            addCriterion("guild_health_point in", values, "guildHealthPoint");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointNotIn(List<Integer> values) {
            addCriterion("guild_health_point not in", values, "guildHealthPoint");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointBetween(Integer value1, Integer value2) {
            addCriterion("guild_health_point between", value1, value2, "guildHealthPoint");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointNotBetween(Integer value1, Integer value2) {
            addCriterion("guild_health_point not between", value1, value2, "guildHealthPoint");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointScoreIsNull() {
            addCriterion("guild_health_point_score is null");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointScoreIsNotNull() {
            addCriterion("guild_health_point_score is not null");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointScoreEqualTo(Integer value) {
            addCriterion("guild_health_point_score =", value, "guildHealthPointScore");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointScoreNotEqualTo(Integer value) {
            addCriterion("guild_health_point_score <>", value, "guildHealthPointScore");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointScoreGreaterThan(Integer value) {
            addCriterion("guild_health_point_score >", value, "guildHealthPointScore");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("guild_health_point_score >=", value, "guildHealthPointScore");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointScoreLessThan(Integer value) {
            addCriterion("guild_health_point_score <", value, "guildHealthPointScore");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointScoreLessThanOrEqualTo(Integer value) {
            addCriterion("guild_health_point_score <=", value, "guildHealthPointScore");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointScoreIn(List<Integer> values) {
            addCriterion("guild_health_point_score in", values, "guildHealthPointScore");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointScoreNotIn(List<Integer> values) {
            addCriterion("guild_health_point_score not in", values, "guildHealthPointScore");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointScoreBetween(Integer value1, Integer value2) {
            addCriterion("guild_health_point_score between", value1, value2, "guildHealthPointScore");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("guild_health_point_score not between", value1, value2, "guildHealthPointScore");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointNextScoreIsNull() {
            addCriterion("guild_health_point_next_score is null");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointNextScoreIsNotNull() {
            addCriterion("guild_health_point_next_score is not null");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointNextScoreEqualTo(Integer value) {
            addCriterion("guild_health_point_next_score =", value, "guildHealthPointNextScore");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointNextScoreNotEqualTo(Integer value) {
            addCriterion("guild_health_point_next_score <>", value, "guildHealthPointNextScore");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointNextScoreGreaterThan(Integer value) {
            addCriterion("guild_health_point_next_score >", value, "guildHealthPointNextScore");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointNextScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("guild_health_point_next_score >=", value, "guildHealthPointNextScore");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointNextScoreLessThan(Integer value) {
            addCriterion("guild_health_point_next_score <", value, "guildHealthPointNextScore");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointNextScoreLessThanOrEqualTo(Integer value) {
            addCriterion("guild_health_point_next_score <=", value, "guildHealthPointNextScore");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointNextScoreIn(List<Integer> values) {
            addCriterion("guild_health_point_next_score in", values, "guildHealthPointNextScore");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointNextScoreNotIn(List<Integer> values) {
            addCriterion("guild_health_point_next_score not in", values, "guildHealthPointNextScore");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointNextScoreBetween(Integer value1, Integer value2) {
            addCriterion("guild_health_point_next_score between", value1, value2, "guildHealthPointNextScore");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointNextScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("guild_health_point_next_score not between", value1, value2, "guildHealthPointNextScore");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointTrgtDiffIsNull() {
            addCriterion("guild_health_point_trgt_diff is null");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointTrgtDiffIsNotNull() {
            addCriterion("guild_health_point_trgt_diff is not null");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointTrgtDiffEqualTo(Integer value) {
            addCriterion("guild_health_point_trgt_diff =", value, "guildHealthPointTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointTrgtDiffNotEqualTo(Integer value) {
            addCriterion("guild_health_point_trgt_diff <>", value, "guildHealthPointTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointTrgtDiffGreaterThan(Integer value) {
            addCriterion("guild_health_point_trgt_diff >", value, "guildHealthPointTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointTrgtDiffGreaterThanOrEqualTo(Integer value) {
            addCriterion("guild_health_point_trgt_diff >=", value, "guildHealthPointTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointTrgtDiffLessThan(Integer value) {
            addCriterion("guild_health_point_trgt_diff <", value, "guildHealthPointTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointTrgtDiffLessThanOrEqualTo(Integer value) {
            addCriterion("guild_health_point_trgt_diff <=", value, "guildHealthPointTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointTrgtDiffIn(List<Integer> values) {
            addCriterion("guild_health_point_trgt_diff in", values, "guildHealthPointTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointTrgtDiffNotIn(List<Integer> values) {
            addCriterion("guild_health_point_trgt_diff not in", values, "guildHealthPointTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointTrgtDiffBetween(Integer value1, Integer value2) {
            addCriterion("guild_health_point_trgt_diff between", value1, value2, "guildHealthPointTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointTrgtDiffNotBetween(Integer value1, Integer value2) {
            addCriterion("guild_health_point_trgt_diff not between", value1, value2, "guildHealthPointTrgtDiff");
            return (Criteria) this;
        }

        public Criteria andDtIsNull() {
            addCriterion("dt is null");
            return (Criteria) this;
        }

        public Criteria andDtIsNotNull() {
            addCriterion("dt is not null");
            return (Criteria) this;
        }

        public Criteria andDtEqualTo(Date value) {
            addCriterionForJDBCDate("dt =", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotEqualTo(Date value) {
            addCriterionForJDBCDate("dt <>", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtGreaterThan(Date value) {
            addCriterionForJDBCDate("dt >", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("dt >=", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtLessThan(Date value) {
            addCriterionForJDBCDate("dt <", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("dt <=", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtIn(List<Date> values) {
            addCriterionForJDBCDate("dt in", values, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotIn(List<Date> values) {
            addCriterionForJDBCDate("dt not in", values, "dt");
            return (Criteria) this;
        }

        public Criteria andDtBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("dt between", value1, value2, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("dt not between", value1, value2, "dt");
            return (Criteria) this;
        }
    }

    /**
     * yy_dm_entity_guild_cmp_health_analysis_expt_di
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * yy_dm_entity_guild_cmp_health_analysis_expt_di null
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}