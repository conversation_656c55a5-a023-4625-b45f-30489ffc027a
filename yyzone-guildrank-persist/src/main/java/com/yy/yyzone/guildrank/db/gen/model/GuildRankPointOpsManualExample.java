package com.yy.yyzone.guildrank.db.gen.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.annotation.Generated;

@Generated("guild_rank_point_ops_manual")
public class GuildRankPointOpsManualExample {
    /**
     * guild_rank_point_ops_manual
     */
    protected String orderByClause;

    /**
     * guild_rank_point_ops_manual
     */
    protected boolean distinct;

    /**
     * guild_rank_point_ops_manual
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public GuildRankPointOpsManualExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    /**
     * guild_rank_point_ops_manual null
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTargetMonthIsNull() {
            addCriterion("target_month is null");
            return (Criteria) this;
        }

        public Criteria andTargetMonthIsNotNull() {
            addCriterion("target_month is not null");
            return (Criteria) this;
        }

        public Criteria andTargetMonthEqualTo(Date value) {
            addCriterion("target_month =", value, "targetMonth");
            return (Criteria) this;
        }

        public Criteria andTargetMonthNotEqualTo(Date value) {
            addCriterion("target_month <>", value, "targetMonth");
            return (Criteria) this;
        }

        public Criteria andTargetMonthGreaterThan(Date value) {
            addCriterion("target_month >", value, "targetMonth");
            return (Criteria) this;
        }

        public Criteria andTargetMonthGreaterThanOrEqualTo(Date value) {
            addCriterion("target_month >=", value, "targetMonth");
            return (Criteria) this;
        }

        public Criteria andTargetMonthLessThan(Date value) {
            addCriterion("target_month <", value, "targetMonth");
            return (Criteria) this;
        }

        public Criteria andTargetMonthLessThanOrEqualTo(Date value) {
            addCriterion("target_month <=", value, "targetMonth");
            return (Criteria) this;
        }

        public Criteria andTargetMonthIn(List<Date> values) {
            addCriterion("target_month in", values, "targetMonth");
            return (Criteria) this;
        }

        public Criteria andTargetMonthNotIn(List<Date> values) {
            addCriterion("target_month not in", values, "targetMonth");
            return (Criteria) this;
        }

        public Criteria andTargetMonthBetween(Date value1, Date value2) {
            addCriterion("target_month between", value1, value2, "targetMonth");
            return (Criteria) this;
        }

        public Criteria andTargetMonthNotBetween(Date value1, Date value2) {
            addCriterion("target_month not between", value1, value2, "targetMonth");
            return (Criteria) this;
        }

        public Criteria andEntityUidIsNull() {
            addCriterion("entity_uid is null");
            return (Criteria) this;
        }

        public Criteria andEntityUidIsNotNull() {
            addCriterion("entity_uid is not null");
            return (Criteria) this;
        }

        public Criteria andEntityUidEqualTo(Long value) {
            addCriterion("entity_uid =", value, "entityUid");
            return (Criteria) this;
        }

        public Criteria andEntityUidNotEqualTo(Long value) {
            addCriterion("entity_uid <>", value, "entityUid");
            return (Criteria) this;
        }

        public Criteria andEntityUidGreaterThan(Long value) {
            addCriterion("entity_uid >", value, "entityUid");
            return (Criteria) this;
        }

        public Criteria andEntityUidGreaterThanOrEqualTo(Long value) {
            addCriterion("entity_uid >=", value, "entityUid");
            return (Criteria) this;
        }

        public Criteria andEntityUidLessThan(Long value) {
            addCriterion("entity_uid <", value, "entityUid");
            return (Criteria) this;
        }

        public Criteria andEntityUidLessThanOrEqualTo(Long value) {
            addCriterion("entity_uid <=", value, "entityUid");
            return (Criteria) this;
        }

        public Criteria andEntityUidIn(List<Long> values) {
            addCriterion("entity_uid in", values, "entityUid");
            return (Criteria) this;
        }

        public Criteria andEntityUidNotIn(List<Long> values) {
            addCriterion("entity_uid not in", values, "entityUid");
            return (Criteria) this;
        }

        public Criteria andEntityUidBetween(Long value1, Long value2) {
            addCriterion("entity_uid between", value1, value2, "entityUid");
            return (Criteria) this;
        }

        public Criteria andEntityUidNotBetween(Long value1, Long value2) {
            addCriterion("entity_uid not between", value1, value2, "entityUid");
            return (Criteria) this;
        }

        public Criteria andModifyTypeIsNull() {
            addCriterion("modify_type is null");
            return (Criteria) this;
        }

        public Criteria andModifyTypeIsNotNull() {
            addCriterion("modify_type is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTypeEqualTo(Byte value) {
            addCriterion("modify_type =", value, "modifyType");
            return (Criteria) this;
        }

        public Criteria andModifyTypeNotEqualTo(Byte value) {
            addCriterion("modify_type <>", value, "modifyType");
            return (Criteria) this;
        }

        public Criteria andModifyTypeGreaterThan(Byte value) {
            addCriterion("modify_type >", value, "modifyType");
            return (Criteria) this;
        }

        public Criteria andModifyTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("modify_type >=", value, "modifyType");
            return (Criteria) this;
        }

        public Criteria andModifyTypeLessThan(Byte value) {
            addCriterion("modify_type <", value, "modifyType");
            return (Criteria) this;
        }

        public Criteria andModifyTypeLessThanOrEqualTo(Byte value) {
            addCriterion("modify_type <=", value, "modifyType");
            return (Criteria) this;
        }

        public Criteria andModifyTypeIn(List<Byte> values) {
            addCriterion("modify_type in", values, "modifyType");
            return (Criteria) this;
        }

        public Criteria andModifyTypeNotIn(List<Byte> values) {
            addCriterion("modify_type not in", values, "modifyType");
            return (Criteria) this;
        }

        public Criteria andModifyTypeBetween(Byte value1, Byte value2) {
            addCriterion("modify_type between", value1, value2, "modifyType");
            return (Criteria) this;
        }

        public Criteria andModifyTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("modify_type not between", value1, value2, "modifyType");
            return (Criteria) this;
        }

        public Criteria andModifyNumIsNull() {
            addCriterion("modify_num is null");
            return (Criteria) this;
        }

        public Criteria andModifyNumIsNotNull() {
            addCriterion("modify_num is not null");
            return (Criteria) this;
        }

        public Criteria andModifyNumEqualTo(Integer value) {
            addCriterion("modify_num =", value, "modifyNum");
            return (Criteria) this;
        }

        public Criteria andModifyNumNotEqualTo(Integer value) {
            addCriterion("modify_num <>", value, "modifyNum");
            return (Criteria) this;
        }

        public Criteria andModifyNumGreaterThan(Integer value) {
            addCriterion("modify_num >", value, "modifyNum");
            return (Criteria) this;
        }

        public Criteria andModifyNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("modify_num >=", value, "modifyNum");
            return (Criteria) this;
        }

        public Criteria andModifyNumLessThan(Integer value) {
            addCriterion("modify_num <", value, "modifyNum");
            return (Criteria) this;
        }

        public Criteria andModifyNumLessThanOrEqualTo(Integer value) {
            addCriterion("modify_num <=", value, "modifyNum");
            return (Criteria) this;
        }

        public Criteria andModifyNumIn(List<Integer> values) {
            addCriterion("modify_num in", values, "modifyNum");
            return (Criteria) this;
        }

        public Criteria andModifyNumNotIn(List<Integer> values) {
            addCriterion("modify_num not in", values, "modifyNum");
            return (Criteria) this;
        }

        public Criteria andModifyNumBetween(Integer value1, Integer value2) {
            addCriterion("modify_num between", value1, value2, "modifyNum");
            return (Criteria) this;
        }

        public Criteria andModifyNumNotBetween(Integer value1, Integer value2) {
            addCriterion("modify_num not between", value1, value2, "modifyNum");
            return (Criteria) this;
        }

        public Criteria andModifyReasonIsNull() {
            addCriterion("modify_reason is null");
            return (Criteria) this;
        }

        public Criteria andModifyReasonIsNotNull() {
            addCriterion("modify_reason is not null");
            return (Criteria) this;
        }

        public Criteria andModifyReasonEqualTo(String value) {
            addCriterion("modify_reason =", value, "modifyReason");
            return (Criteria) this;
        }

        public Criteria andModifyReasonNotEqualTo(String value) {
            addCriterion("modify_reason <>", value, "modifyReason");
            return (Criteria) this;
        }

        public Criteria andModifyReasonGreaterThan(String value) {
            addCriterion("modify_reason >", value, "modifyReason");
            return (Criteria) this;
        }

        public Criteria andModifyReasonGreaterThanOrEqualTo(String value) {
            addCriterion("modify_reason >=", value, "modifyReason");
            return (Criteria) this;
        }

        public Criteria andModifyReasonLessThan(String value) {
            addCriterion("modify_reason <", value, "modifyReason");
            return (Criteria) this;
        }

        public Criteria andModifyReasonLessThanOrEqualTo(String value) {
            addCriterion("modify_reason <=", value, "modifyReason");
            return (Criteria) this;
        }

        public Criteria andModifyReasonLike(String value) {
            addCriterion("modify_reason like", value, "modifyReason");
            return (Criteria) this;
        }

        public Criteria andModifyReasonNotLike(String value) {
            addCriterion("modify_reason not like", value, "modifyReason");
            return (Criteria) this;
        }

        public Criteria andModifyReasonIn(List<String> values) {
            addCriterion("modify_reason in", values, "modifyReason");
            return (Criteria) this;
        }

        public Criteria andModifyReasonNotIn(List<String> values) {
            addCriterion("modify_reason not in", values, "modifyReason");
            return (Criteria) this;
        }

        public Criteria andModifyReasonBetween(String value1, String value2) {
            addCriterion("modify_reason between", value1, value2, "modifyReason");
            return (Criteria) this;
        }

        public Criteria andModifyReasonNotBetween(String value1, String value2) {
            addCriterion("modify_reason not between", value1, value2, "modifyReason");
            return (Criteria) this;
        }

        public Criteria andPassportIsNull() {
            addCriterion("passport is null");
            return (Criteria) this;
        }

        public Criteria andPassportIsNotNull() {
            addCriterion("passport is not null");
            return (Criteria) this;
        }

        public Criteria andPassportEqualTo(String value) {
            addCriterion("passport =", value, "passport");
            return (Criteria) this;
        }

        public Criteria andPassportNotEqualTo(String value) {
            addCriterion("passport <>", value, "passport");
            return (Criteria) this;
        }

        public Criteria andPassportGreaterThan(String value) {
            addCriterion("passport >", value, "passport");
            return (Criteria) this;
        }

        public Criteria andPassportGreaterThanOrEqualTo(String value) {
            addCriterion("passport >=", value, "passport");
            return (Criteria) this;
        }

        public Criteria andPassportLessThan(String value) {
            addCriterion("passport <", value, "passport");
            return (Criteria) this;
        }

        public Criteria andPassportLessThanOrEqualTo(String value) {
            addCriterion("passport <=", value, "passport");
            return (Criteria) this;
        }

        public Criteria andPassportLike(String value) {
            addCriterion("passport like", value, "passport");
            return (Criteria) this;
        }

        public Criteria andPassportNotLike(String value) {
            addCriterion("passport not like", value, "passport");
            return (Criteria) this;
        }

        public Criteria andPassportIn(List<String> values) {
            addCriterion("passport in", values, "passport");
            return (Criteria) this;
        }

        public Criteria andPassportNotIn(List<String> values) {
            addCriterion("passport not in", values, "passport");
            return (Criteria) this;
        }

        public Criteria andPassportBetween(String value1, String value2) {
            addCriterion("passport between", value1, value2, "passport");
            return (Criteria) this;
        }

        public Criteria andPassportNotBetween(String value1, String value2) {
            addCriterion("passport not between", value1, value2, "passport");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    /**
     * guild_rank_point_ops_manual
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * guild_rank_point_ops_manual null
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}