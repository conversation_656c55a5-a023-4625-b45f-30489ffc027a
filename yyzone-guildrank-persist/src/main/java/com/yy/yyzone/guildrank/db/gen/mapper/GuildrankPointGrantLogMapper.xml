<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yy.yyzone.guildrank.db.gen.mapper.GuildrankPointGrantLogMapper" >
  <resultMap id="BaseResultMap" type="com.yy.yyzone.guildrank.db.gen.model.GuildrankPointGrantLog" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="detail_id" property="detailId" jdbcType="BIGINT" />
    <result column="uid" property="uid" jdbcType="BIGINT" />
    <result column="point" property="point" jdbcType="INTEGER" />
    <result column="req_order_no" property="reqOrderNo" jdbcType="VARCHAR" />
    <result column="rsp_result" property="rspResult" jdbcType="INTEGER" />
    <result column="rsp_err_msg" property="rspErrMsg" jdbcType="VARCHAR" />
    <result column="rsp_ext" property="rspExt" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, detail_id, uid, point, req_order_no, rsp_result, rsp_err_msg, rsp_ext, create_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankPointGrantLogExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from guildrank_point_grant_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit > 0" >
      limit ${limit}
    </if>
    <if test="offset > 0" >
      offset ${offset}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from guildrank_point_grant_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from guildrank_point_grant_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankPointGrantLogExample" >
    delete from guildrank_point_grant_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankPointGrantLog" useGeneratedKeys="true" keyProperty="id" >
    insert into guildrank_point_grant_log (detail_id, uid, point, 
      req_order_no, rsp_result, rsp_err_msg, 
      rsp_ext, create_time)
    values (#{detailId,jdbcType=BIGINT}, #{uid,jdbcType=BIGINT}, #{point,jdbcType=INTEGER}, 
      #{reqOrderNo,jdbcType=VARCHAR}, #{rspResult,jdbcType=INTEGER}, #{rspErrMsg,jdbcType=VARCHAR}, 
      #{rspExt,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankPointGrantLog" useGeneratedKeys="true" keyProperty="id" >
    insert into guildrank_point_grant_log
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="detailId != null" >
        detail_id,
      </if>
      <if test="uid != null" >
        uid,
      </if>
      <if test="point != null" >
        point,
      </if>
      <if test="reqOrderNo != null" >
        req_order_no,
      </if>
      <if test="rspResult != null" >
        rsp_result,
      </if>
      <if test="rspErrMsg != null" >
        rsp_err_msg,
      </if>
      <if test="rspExt != null" >
        rsp_ext,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="detailId != null" >
        #{detailId,jdbcType=BIGINT},
      </if>
      <if test="uid != null" >
        #{uid,jdbcType=BIGINT},
      </if>
      <if test="point != null" >
        #{point,jdbcType=INTEGER},
      </if>
      <if test="reqOrderNo != null" >
        #{reqOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="rspResult != null" >
        #{rspResult,jdbcType=INTEGER},
      </if>
      <if test="rspErrMsg != null" >
        #{rspErrMsg,jdbcType=VARCHAR},
      </if>
      <if test="rspExt != null" >
        #{rspExt,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankPointGrantLogExample" resultType="java.lang.Integer" >
    select count(*) from guildrank_point_grant_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update guildrank_point_grant_log
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.detailId != null" >
        detail_id = #{record.detailId,jdbcType=BIGINT},
      </if>
      <if test="record.uid != null" >
        uid = #{record.uid,jdbcType=BIGINT},
      </if>
      <if test="record.point != null" >
        point = #{record.point,jdbcType=INTEGER},
      </if>
      <if test="record.reqOrderNo != null" >
        req_order_no = #{record.reqOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.rspResult != null" >
        rsp_result = #{record.rspResult,jdbcType=INTEGER},
      </if>
      <if test="record.rspErrMsg != null" >
        rsp_err_msg = #{record.rspErrMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.rspExt != null" >
        rsp_ext = #{record.rspExt,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update guildrank_point_grant_log
    set id = #{record.id,jdbcType=BIGINT},
      detail_id = #{record.detailId,jdbcType=BIGINT},
      uid = #{record.uid,jdbcType=BIGINT},
      point = #{record.point,jdbcType=INTEGER},
      req_order_no = #{record.reqOrderNo,jdbcType=VARCHAR},
      rsp_result = #{record.rspResult,jdbcType=INTEGER},
      rsp_err_msg = #{record.rspErrMsg,jdbcType=VARCHAR},
      rsp_ext = #{record.rspExt,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankPointGrantLog" >
    update guildrank_point_grant_log
    <set >
      <if test="detailId != null" >
        detail_id = #{detailId,jdbcType=BIGINT},
      </if>
      <if test="uid != null" >
        uid = #{uid,jdbcType=BIGINT},
      </if>
      <if test="point != null" >
        point = #{point,jdbcType=INTEGER},
      </if>
      <if test="reqOrderNo != null" >
        req_order_no = #{reqOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="rspResult != null" >
        rsp_result = #{rspResult,jdbcType=INTEGER},
      </if>
      <if test="rspErrMsg != null" >
        rsp_err_msg = #{rspErrMsg,jdbcType=VARCHAR},
      </if>
      <if test="rspExt != null" >
        rsp_ext = #{rspExt,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankPointGrantLog" >
    update guildrank_point_grant_log
    set detail_id = #{detailId,jdbcType=BIGINT},
      uid = #{uid,jdbcType=BIGINT},
      point = #{point,jdbcType=INTEGER},
      req_order_no = #{reqOrderNo,jdbcType=VARCHAR},
      rsp_result = #{rspResult,jdbcType=INTEGER},
      rsp_err_msg = #{rspErrMsg,jdbcType=VARCHAR},
      rsp_ext = #{rspExt,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>