package com.yy.yyzone.guildrank.db.gen.model;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated("dm_yy_sid_segment_origin_star_day")
public class DmYySidSegmentOriginStarDayExample {
    /**
     * dm_yy_sid_segment_origin_star_day
     */
    protected String orderByClause;

    /**
     * dm_yy_sid_segment_origin_star_day
     */
    protected boolean distinct;

    /**
     * dm_yy_sid_segment_origin_star_day
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public DmYySidSegmentOriginStarDayExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    /**
     * dm_yy_sid_segment_origin_star_day null
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSidOwneridIsNull() {
            addCriterion("sid_ownerid is null");
            return (Criteria) this;
        }

        public Criteria andSidOwneridIsNotNull() {
            addCriterion("sid_ownerid is not null");
            return (Criteria) this;
        }

        public Criteria andSidOwneridEqualTo(String value) {
            addCriterion("sid_ownerid =", value, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridNotEqualTo(String value) {
            addCriterion("sid_ownerid <>", value, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridGreaterThan(String value) {
            addCriterion("sid_ownerid >", value, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridGreaterThanOrEqualTo(String value) {
            addCriterion("sid_ownerid >=", value, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridLessThan(String value) {
            addCriterion("sid_ownerid <", value, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridLessThanOrEqualTo(String value) {
            addCriterion("sid_ownerid <=", value, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridLike(String value) {
            addCriterion("sid_ownerid like", value, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridNotLike(String value) {
            addCriterion("sid_ownerid not like", value, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridIn(List<String> values) {
            addCriterion("sid_ownerid in", values, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridNotIn(List<String> values) {
            addCriterion("sid_ownerid not in", values, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridBetween(String value1, String value2) {
            addCriterion("sid_ownerid between", value1, value2, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridNotBetween(String value1, String value2) {
            addCriterion("sid_ownerid not between", value1, value2, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidIsNull() {
            addCriterion("sid_owyyid is null");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidIsNotNull() {
            addCriterion("sid_owyyid is not null");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidEqualTo(String value) {
            addCriterion("sid_owyyid =", value, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidNotEqualTo(String value) {
            addCriterion("sid_owyyid <>", value, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidGreaterThan(String value) {
            addCriterion("sid_owyyid >", value, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidGreaterThanOrEqualTo(String value) {
            addCriterion("sid_owyyid >=", value, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidLessThan(String value) {
            addCriterion("sid_owyyid <", value, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidLessThanOrEqualTo(String value) {
            addCriterion("sid_owyyid <=", value, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidLike(String value) {
            addCriterion("sid_owyyid like", value, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidNotLike(String value) {
            addCriterion("sid_owyyid not like", value, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidIn(List<String> values) {
            addCriterion("sid_owyyid in", values, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidNotIn(List<String> values) {
            addCriterion("sid_owyyid not in", values, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidBetween(String value1, String value2) {
            addCriterion("sid_owyyid between", value1, value2, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidNotBetween(String value1, String value2) {
            addCriterion("sid_owyyid not between", value1, value2, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondIsNull() {
            addCriterion("month_diamond is null");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondIsNotNull() {
            addCriterion("month_diamond is not null");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondEqualTo(Double value) {
            addCriterion("month_diamond =", value, "monthDiamond");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondNotEqualTo(Double value) {
            addCriterion("month_diamond <>", value, "monthDiamond");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondGreaterThan(Double value) {
            addCriterion("month_diamond >", value, "monthDiamond");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondGreaterThanOrEqualTo(Double value) {
            addCriterion("month_diamond >=", value, "monthDiamond");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondLessThan(Double value) {
            addCriterion("month_diamond <", value, "monthDiamond");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondLessThanOrEqualTo(Double value) {
            addCriterion("month_diamond <=", value, "monthDiamond");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondIn(List<Double> values) {
            addCriterion("month_diamond in", values, "monthDiamond");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondNotIn(List<Double> values) {
            addCriterion("month_diamond not in", values, "monthDiamond");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondBetween(Double value1, Double value2) {
            addCriterion("month_diamond between", value1, value2, "monthDiamond");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondNotBetween(Double value1, Double value2) {
            addCriterion("month_diamond not between", value1, value2, "monthDiamond");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondSprintValueIsNull() {
            addCriterion("month_diamond_sprint_value is null");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondSprintValueIsNotNull() {
            addCriterion("month_diamond_sprint_value is not null");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondSprintValueEqualTo(Double value) {
            addCriterion("month_diamond_sprint_value =", value, "monthDiamondSprintValue");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondSprintValueNotEqualTo(Double value) {
            addCriterion("month_diamond_sprint_value <>", value, "monthDiamondSprintValue");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondSprintValueGreaterThan(Double value) {
            addCriterion("month_diamond_sprint_value >", value, "monthDiamondSprintValue");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondSprintValueGreaterThanOrEqualTo(Double value) {
            addCriterion("month_diamond_sprint_value >=", value, "monthDiamondSprintValue");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondSprintValueLessThan(Double value) {
            addCriterion("month_diamond_sprint_value <", value, "monthDiamondSprintValue");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondSprintValueLessThanOrEqualTo(Double value) {
            addCriterion("month_diamond_sprint_value <=", value, "monthDiamondSprintValue");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondSprintValueIn(List<Double> values) {
            addCriterion("month_diamond_sprint_value in", values, "monthDiamondSprintValue");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondSprintValueNotIn(List<Double> values) {
            addCriterion("month_diamond_sprint_value not in", values, "monthDiamondSprintValue");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondSprintValueBetween(Double value1, Double value2) {
            addCriterion("month_diamond_sprint_value between", value1, value2, "monthDiamondSprintValue");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondSprintValueNotBetween(Double value1, Double value2) {
            addCriterion("month_diamond_sprint_value not between", value1, value2, "monthDiamondSprintValue");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondDiffIsNull() {
            addCriterion("month_diamond_diff is null");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondDiffIsNotNull() {
            addCriterion("month_diamond_diff is not null");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondDiffEqualTo(Double value) {
            addCriterion("month_diamond_diff =", value, "monthDiamondDiff");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondDiffNotEqualTo(Double value) {
            addCriterion("month_diamond_diff <>", value, "monthDiamondDiff");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondDiffGreaterThan(Double value) {
            addCriterion("month_diamond_diff >", value, "monthDiamondDiff");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondDiffGreaterThanOrEqualTo(Double value) {
            addCriterion("month_diamond_diff >=", value, "monthDiamondDiff");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondDiffLessThan(Double value) {
            addCriterion("month_diamond_diff <", value, "monthDiamondDiff");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondDiffLessThanOrEqualTo(Double value) {
            addCriterion("month_diamond_diff <=", value, "monthDiamondDiff");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondDiffIn(List<Double> values) {
            addCriterion("month_diamond_diff in", values, "monthDiamondDiff");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondDiffNotIn(List<Double> values) {
            addCriterion("month_diamond_diff not in", values, "monthDiamondDiff");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondDiffBetween(Double value1, Double value2) {
            addCriterion("month_diamond_diff between", value1, value2, "monthDiamondDiff");
            return (Criteria) this;
        }

        public Criteria andMonthDiamondDiffNotBetween(Double value1, Double value2) {
            addCriterion("month_diamond_diff not between", value1, value2, "monthDiamondDiff");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvIsNull() {
            addCriterion("avg_valid_live_uv is null");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvIsNotNull() {
            addCriterion("avg_valid_live_uv is not null");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvEqualTo(Double value) {
            addCriterion("avg_valid_live_uv =", value, "avgValidLiveUv");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvNotEqualTo(Double value) {
            addCriterion("avg_valid_live_uv <>", value, "avgValidLiveUv");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvGreaterThan(Double value) {
            addCriterion("avg_valid_live_uv >", value, "avgValidLiveUv");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvGreaterThanOrEqualTo(Double value) {
            addCriterion("avg_valid_live_uv >=", value, "avgValidLiveUv");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvLessThan(Double value) {
            addCriterion("avg_valid_live_uv <", value, "avgValidLiveUv");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvLessThanOrEqualTo(Double value) {
            addCriterion("avg_valid_live_uv <=", value, "avgValidLiveUv");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvIn(List<Double> values) {
            addCriterion("avg_valid_live_uv in", values, "avgValidLiveUv");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvNotIn(List<Double> values) {
            addCriterion("avg_valid_live_uv not in", values, "avgValidLiveUv");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvBetween(Double value1, Double value2) {
            addCriterion("avg_valid_live_uv between", value1, value2, "avgValidLiveUv");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvNotBetween(Double value1, Double value2) {
            addCriterion("avg_valid_live_uv not between", value1, value2, "avgValidLiveUv");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvSprintValueIsNull() {
            addCriterion("avg_valid_live_uv_sprint_value is null");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvSprintValueIsNotNull() {
            addCriterion("avg_valid_live_uv_sprint_value is not null");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvSprintValueEqualTo(Double value) {
            addCriterion("avg_valid_live_uv_sprint_value =", value, "avgValidLiveUvSprintValue");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvSprintValueNotEqualTo(Double value) {
            addCriterion("avg_valid_live_uv_sprint_value <>", value, "avgValidLiveUvSprintValue");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvSprintValueGreaterThan(Double value) {
            addCriterion("avg_valid_live_uv_sprint_value >", value, "avgValidLiveUvSprintValue");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvSprintValueGreaterThanOrEqualTo(Double value) {
            addCriterion("avg_valid_live_uv_sprint_value >=", value, "avgValidLiveUvSprintValue");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvSprintValueLessThan(Double value) {
            addCriterion("avg_valid_live_uv_sprint_value <", value, "avgValidLiveUvSprintValue");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvSprintValueLessThanOrEqualTo(Double value) {
            addCriterion("avg_valid_live_uv_sprint_value <=", value, "avgValidLiveUvSprintValue");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvSprintValueIn(List<Double> values) {
            addCriterion("avg_valid_live_uv_sprint_value in", values, "avgValidLiveUvSprintValue");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvSprintValueNotIn(List<Double> values) {
            addCriterion("avg_valid_live_uv_sprint_value not in", values, "avgValidLiveUvSprintValue");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvSprintValueBetween(Double value1, Double value2) {
            addCriterion("avg_valid_live_uv_sprint_value between", value1, value2, "avgValidLiveUvSprintValue");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvSprintValueNotBetween(Double value1, Double value2) {
            addCriterion("avg_valid_live_uv_sprint_value not between", value1, value2, "avgValidLiveUvSprintValue");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvDiffIsNull() {
            addCriterion("avg_valid_live_uv_diff is null");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvDiffIsNotNull() {
            addCriterion("avg_valid_live_uv_diff is not null");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvDiffEqualTo(Double value) {
            addCriterion("avg_valid_live_uv_diff =", value, "avgValidLiveUvDiff");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvDiffNotEqualTo(Double value) {
            addCriterion("avg_valid_live_uv_diff <>", value, "avgValidLiveUvDiff");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvDiffGreaterThan(Double value) {
            addCriterion("avg_valid_live_uv_diff >", value, "avgValidLiveUvDiff");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvDiffGreaterThanOrEqualTo(Double value) {
            addCriterion("avg_valid_live_uv_diff >=", value, "avgValidLiveUvDiff");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvDiffLessThan(Double value) {
            addCriterion("avg_valid_live_uv_diff <", value, "avgValidLiveUvDiff");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvDiffLessThanOrEqualTo(Double value) {
            addCriterion("avg_valid_live_uv_diff <=", value, "avgValidLiveUvDiff");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvDiffIn(List<Double> values) {
            addCriterion("avg_valid_live_uv_diff in", values, "avgValidLiveUvDiff");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvDiffNotIn(List<Double> values) {
            addCriterion("avg_valid_live_uv_diff not in", values, "avgValidLiveUvDiff");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvDiffBetween(Double value1, Double value2) {
            addCriterion("avg_valid_live_uv_diff between", value1, value2, "avgValidLiveUvDiff");
            return (Criteria) this;
        }

        public Criteria andAvgValidLiveUvDiffNotBetween(Double value1, Double value2) {
            addCriterion("avg_valid_live_uv_diff not between", value1, value2, "avgValidLiveUvDiff");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreIsNull() {
            addCriterion("anchor_total_score is null");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreIsNotNull() {
            addCriterion("anchor_total_score is not null");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreEqualTo(Double value) {
            addCriterion("anchor_total_score =", value, "anchorTotalScore");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreNotEqualTo(Double value) {
            addCriterion("anchor_total_score <>", value, "anchorTotalScore");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreGreaterThan(Double value) {
            addCriterion("anchor_total_score >", value, "anchorTotalScore");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreGreaterThanOrEqualTo(Double value) {
            addCriterion("anchor_total_score >=", value, "anchorTotalScore");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreLessThan(Double value) {
            addCriterion("anchor_total_score <", value, "anchorTotalScore");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreLessThanOrEqualTo(Double value) {
            addCriterion("anchor_total_score <=", value, "anchorTotalScore");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreIn(List<Double> values) {
            addCriterion("anchor_total_score in", values, "anchorTotalScore");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreNotIn(List<Double> values) {
            addCriterion("anchor_total_score not in", values, "anchorTotalScore");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreBetween(Double value1, Double value2) {
            addCriterion("anchor_total_score between", value1, value2, "anchorTotalScore");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreNotBetween(Double value1, Double value2) {
            addCriterion("anchor_total_score not between", value1, value2, "anchorTotalScore");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreSprintValueIsNull() {
            addCriterion("anchor_total_score_sprint_value is null");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreSprintValueIsNotNull() {
            addCriterion("anchor_total_score_sprint_value is not null");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreSprintValueEqualTo(Double value) {
            addCriterion("anchor_total_score_sprint_value =", value, "anchorTotalScoreSprintValue");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreSprintValueNotEqualTo(Double value) {
            addCriterion("anchor_total_score_sprint_value <>", value, "anchorTotalScoreSprintValue");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreSprintValueGreaterThan(Double value) {
            addCriterion("anchor_total_score_sprint_value >", value, "anchorTotalScoreSprintValue");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreSprintValueGreaterThanOrEqualTo(Double value) {
            addCriterion("anchor_total_score_sprint_value >=", value, "anchorTotalScoreSprintValue");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreSprintValueLessThan(Double value) {
            addCriterion("anchor_total_score_sprint_value <", value, "anchorTotalScoreSprintValue");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreSprintValueLessThanOrEqualTo(Double value) {
            addCriterion("anchor_total_score_sprint_value <=", value, "anchorTotalScoreSprintValue");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreSprintValueIn(List<Double> values) {
            addCriterion("anchor_total_score_sprint_value in", values, "anchorTotalScoreSprintValue");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreSprintValueNotIn(List<Double> values) {
            addCriterion("anchor_total_score_sprint_value not in", values, "anchorTotalScoreSprintValue");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreSprintValueBetween(Double value1, Double value2) {
            addCriterion("anchor_total_score_sprint_value between", value1, value2, "anchorTotalScoreSprintValue");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreSprintValueNotBetween(Double value1, Double value2) {
            addCriterion("anchor_total_score_sprint_value not between", value1, value2, "anchorTotalScoreSprintValue");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreDiffIsNull() {
            addCriterion("anchor_total_score_diff is null");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreDiffIsNotNull() {
            addCriterion("anchor_total_score_diff is not null");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreDiffEqualTo(Double value) {
            addCriterion("anchor_total_score_diff =", value, "anchorTotalScoreDiff");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreDiffNotEqualTo(Double value) {
            addCriterion("anchor_total_score_diff <>", value, "anchorTotalScoreDiff");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreDiffGreaterThan(Double value) {
            addCriterion("anchor_total_score_diff >", value, "anchorTotalScoreDiff");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreDiffGreaterThanOrEqualTo(Double value) {
            addCriterion("anchor_total_score_diff >=", value, "anchorTotalScoreDiff");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreDiffLessThan(Double value) {
            addCriterion("anchor_total_score_diff <", value, "anchorTotalScoreDiff");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreDiffLessThanOrEqualTo(Double value) {
            addCriterion("anchor_total_score_diff <=", value, "anchorTotalScoreDiff");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreDiffIn(List<Double> values) {
            addCriterion("anchor_total_score_diff in", values, "anchorTotalScoreDiff");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreDiffNotIn(List<Double> values) {
            addCriterion("anchor_total_score_diff not in", values, "anchorTotalScoreDiff");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreDiffBetween(Double value1, Double value2) {
            addCriterion("anchor_total_score_diff between", value1, value2, "anchorTotalScoreDiff");
            return (Criteria) this;
        }

        public Criteria andAnchorTotalScoreDiffNotBetween(Double value1, Double value2) {
            addCriterion("anchor_total_score_diff not between", value1, value2, "anchorTotalScoreDiff");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateIsNull() {
            addCriterion("break_uv_rate is null");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateIsNotNull() {
            addCriterion("break_uv_rate is not null");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateEqualTo(Double value) {
            addCriterion("break_uv_rate =", value, "breakUvRate");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateNotEqualTo(Double value) {
            addCriterion("break_uv_rate <>", value, "breakUvRate");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateGreaterThan(Double value) {
            addCriterion("break_uv_rate >", value, "breakUvRate");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateGreaterThanOrEqualTo(Double value) {
            addCriterion("break_uv_rate >=", value, "breakUvRate");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateLessThan(Double value) {
            addCriterion("break_uv_rate <", value, "breakUvRate");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateLessThanOrEqualTo(Double value) {
            addCriterion("break_uv_rate <=", value, "breakUvRate");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateIn(List<Double> values) {
            addCriterion("break_uv_rate in", values, "breakUvRate");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateNotIn(List<Double> values) {
            addCriterion("break_uv_rate not in", values, "breakUvRate");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateBetween(Double value1, Double value2) {
            addCriterion("break_uv_rate between", value1, value2, "breakUvRate");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateNotBetween(Double value1, Double value2) {
            addCriterion("break_uv_rate not between", value1, value2, "breakUvRate");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateSprintValueIsNull() {
            addCriterion("break_uv_rate_sprint_value is null");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateSprintValueIsNotNull() {
            addCriterion("break_uv_rate_sprint_value is not null");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateSprintValueEqualTo(Double value) {
            addCriterion("break_uv_rate_sprint_value =", value, "breakUvRateSprintValue");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateSprintValueNotEqualTo(Double value) {
            addCriterion("break_uv_rate_sprint_value <>", value, "breakUvRateSprintValue");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateSprintValueGreaterThan(Double value) {
            addCriterion("break_uv_rate_sprint_value >", value, "breakUvRateSprintValue");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateSprintValueGreaterThanOrEqualTo(Double value) {
            addCriterion("break_uv_rate_sprint_value >=", value, "breakUvRateSprintValue");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateSprintValueLessThan(Double value) {
            addCriterion("break_uv_rate_sprint_value <", value, "breakUvRateSprintValue");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateSprintValueLessThanOrEqualTo(Double value) {
            addCriterion("break_uv_rate_sprint_value <=", value, "breakUvRateSprintValue");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateSprintValueIn(List<Double> values) {
            addCriterion("break_uv_rate_sprint_value in", values, "breakUvRateSprintValue");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateSprintValueNotIn(List<Double> values) {
            addCriterion("break_uv_rate_sprint_value not in", values, "breakUvRateSprintValue");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateSprintValueBetween(Double value1, Double value2) {
            addCriterion("break_uv_rate_sprint_value between", value1, value2, "breakUvRateSprintValue");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateSprintValueNotBetween(Double value1, Double value2) {
            addCriterion("break_uv_rate_sprint_value not between", value1, value2, "breakUvRateSprintValue");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateDiffIsNull() {
            addCriterion("break_uv_rate_diff is null");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateDiffIsNotNull() {
            addCriterion("break_uv_rate_diff is not null");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateDiffEqualTo(Double value) {
            addCriterion("break_uv_rate_diff =", value, "breakUvRateDiff");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateDiffNotEqualTo(Double value) {
            addCriterion("break_uv_rate_diff <>", value, "breakUvRateDiff");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateDiffGreaterThan(Double value) {
            addCriterion("break_uv_rate_diff >", value, "breakUvRateDiff");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateDiffGreaterThanOrEqualTo(Double value) {
            addCriterion("break_uv_rate_diff >=", value, "breakUvRateDiff");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateDiffLessThan(Double value) {
            addCriterion("break_uv_rate_diff <", value, "breakUvRateDiff");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateDiffLessThanOrEqualTo(Double value) {
            addCriterion("break_uv_rate_diff <=", value, "breakUvRateDiff");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateDiffIn(List<Double> values) {
            addCriterion("break_uv_rate_diff in", values, "breakUvRateDiff");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateDiffNotIn(List<Double> values) {
            addCriterion("break_uv_rate_diff not in", values, "breakUvRateDiff");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateDiffBetween(Double value1, Double value2) {
            addCriterion("break_uv_rate_diff between", value1, value2, "breakUvRateDiff");
            return (Criteria) this;
        }

        public Criteria andBreakUvRateDiffNotBetween(Double value1, Double value2) {
            addCriterion("break_uv_rate_diff not between", value1, value2, "breakUvRateDiff");
            return (Criteria) this;
        }

        public Criteria andDtIsNull() {
            addCriterion("dt is null");
            return (Criteria) this;
        }

        public Criteria andDtIsNotNull() {
            addCriterion("dt is not null");
            return (Criteria) this;
        }

        public Criteria andDtEqualTo(String value) {
            addCriterion("dt =", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotEqualTo(String value) {
            addCriterion("dt <>", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtGreaterThan(String value) {
            addCriterion("dt >", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtGreaterThanOrEqualTo(String value) {
            addCriterion("dt >=", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtLessThan(String value) {
            addCriterion("dt <", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtLessThanOrEqualTo(String value) {
            addCriterion("dt <=", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtLike(String value) {
            addCriterion("dt like", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotLike(String value) {
            addCriterion("dt not like", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtIn(List<String> values) {
            addCriterion("dt in", values, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotIn(List<String> values) {
            addCriterion("dt not in", values, "dt");
            return (Criteria) this;
        }

        public Criteria andDtBetween(String value1, String value2) {
            addCriterion("dt between", value1, value2, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotBetween(String value1, String value2) {
            addCriterion("dt not between", value1, value2, "dt");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreIsNull() {
            addCriterion("aid_value_score is null");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreIsNotNull() {
            addCriterion("aid_value_score is not null");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreEqualTo(Double value) {
            addCriterion("aid_value_score =", value, "aidValueScore");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreNotEqualTo(Double value) {
            addCriterion("aid_value_score <>", value, "aidValueScore");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreGreaterThan(Double value) {
            addCriterion("aid_value_score >", value, "aidValueScore");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreGreaterThanOrEqualTo(Double value) {
            addCriterion("aid_value_score >=", value, "aidValueScore");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreLessThan(Double value) {
            addCriterion("aid_value_score <", value, "aidValueScore");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreLessThanOrEqualTo(Double value) {
            addCriterion("aid_value_score <=", value, "aidValueScore");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreIn(List<Double> values) {
            addCriterion("aid_value_score in", values, "aidValueScore");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreNotIn(List<Double> values) {
            addCriterion("aid_value_score not in", values, "aidValueScore");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreBetween(Double value1, Double value2) {
            addCriterion("aid_value_score between", value1, value2, "aidValueScore");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreNotBetween(Double value1, Double value2) {
            addCriterion("aid_value_score not between", value1, value2, "aidValueScore");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreSprintValueIsNull() {
            addCriterion("aid_value_score_sprint_value is null");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreSprintValueIsNotNull() {
            addCriterion("aid_value_score_sprint_value is not null");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreSprintValueEqualTo(Double value) {
            addCriterion("aid_value_score_sprint_value =", value, "aidValueScoreSprintValue");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreSprintValueNotEqualTo(Double value) {
            addCriterion("aid_value_score_sprint_value <>", value, "aidValueScoreSprintValue");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreSprintValueGreaterThan(Double value) {
            addCriterion("aid_value_score_sprint_value >", value, "aidValueScoreSprintValue");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreSprintValueGreaterThanOrEqualTo(Double value) {
            addCriterion("aid_value_score_sprint_value >=", value, "aidValueScoreSprintValue");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreSprintValueLessThan(Double value) {
            addCriterion("aid_value_score_sprint_value <", value, "aidValueScoreSprintValue");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreSprintValueLessThanOrEqualTo(Double value) {
            addCriterion("aid_value_score_sprint_value <=", value, "aidValueScoreSprintValue");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreSprintValueIn(List<Double> values) {
            addCriterion("aid_value_score_sprint_value in", values, "aidValueScoreSprintValue");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreSprintValueNotIn(List<Double> values) {
            addCriterion("aid_value_score_sprint_value not in", values, "aidValueScoreSprintValue");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreSprintValueBetween(Double value1, Double value2) {
            addCriterion("aid_value_score_sprint_value between", value1, value2, "aidValueScoreSprintValue");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreSprintValueNotBetween(Double value1, Double value2) {
            addCriterion("aid_value_score_sprint_value not between", value1, value2, "aidValueScoreSprintValue");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreDiffIsNull() {
            addCriterion("aid_value_score_diff is null");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreDiffIsNotNull() {
            addCriterion("aid_value_score_diff is not null");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreDiffEqualTo(Double value) {
            addCriterion("aid_value_score_diff =", value, "aidValueScoreDiff");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreDiffNotEqualTo(Double value) {
            addCriterion("aid_value_score_diff <>", value, "aidValueScoreDiff");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreDiffGreaterThan(Double value) {
            addCriterion("aid_value_score_diff >", value, "aidValueScoreDiff");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreDiffGreaterThanOrEqualTo(Double value) {
            addCriterion("aid_value_score_diff >=", value, "aidValueScoreDiff");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreDiffLessThan(Double value) {
            addCriterion("aid_value_score_diff <", value, "aidValueScoreDiff");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreDiffLessThanOrEqualTo(Double value) {
            addCriterion("aid_value_score_diff <=", value, "aidValueScoreDiff");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreDiffIn(List<Double> values) {
            addCriterion("aid_value_score_diff in", values, "aidValueScoreDiff");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreDiffNotIn(List<Double> values) {
            addCriterion("aid_value_score_diff not in", values, "aidValueScoreDiff");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreDiffBetween(Double value1, Double value2) {
            addCriterion("aid_value_score_diff between", value1, value2, "aidValueScoreDiff");
            return (Criteria) this;
        }

        public Criteria andAidValueScoreDiffNotBetween(Double value1, Double value2) {
            addCriterion("aid_value_score_diff not between", value1, value2, "aidValueScoreDiff");
            return (Criteria) this;
        }

        public Criteria andIsMarsGuildIsNull() {
            addCriterion("is_mars_guild is null");
            return (Criteria) this;
        }

        public Criteria andIsMarsGuildIsNotNull() {
            addCriterion("is_mars_guild is not null");
            return (Criteria) this;
        }

        public Criteria andIsMarsGuildEqualTo(Integer value) {
            addCriterion("is_mars_guild =", value, "isMarsGuild");
            return (Criteria) this;
        }

        public Criteria andIsMarsGuildNotEqualTo(Integer value) {
            addCriterion("is_mars_guild <>", value, "isMarsGuild");
            return (Criteria) this;
        }

        public Criteria andIsMarsGuildGreaterThan(Integer value) {
            addCriterion("is_mars_guild >", value, "isMarsGuild");
            return (Criteria) this;
        }

        public Criteria andIsMarsGuildGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_mars_guild >=", value, "isMarsGuild");
            return (Criteria) this;
        }

        public Criteria andIsMarsGuildLessThan(Integer value) {
            addCriterion("is_mars_guild <", value, "isMarsGuild");
            return (Criteria) this;
        }

        public Criteria andIsMarsGuildLessThanOrEqualTo(Integer value) {
            addCriterion("is_mars_guild <=", value, "isMarsGuild");
            return (Criteria) this;
        }

        public Criteria andIsMarsGuildIn(List<Integer> values) {
            addCriterion("is_mars_guild in", values, "isMarsGuild");
            return (Criteria) this;
        }

        public Criteria andIsMarsGuildNotIn(List<Integer> values) {
            addCriterion("is_mars_guild not in", values, "isMarsGuild");
            return (Criteria) this;
        }

        public Criteria andIsMarsGuildBetween(Integer value1, Integer value2) {
            addCriterion("is_mars_guild between", value1, value2, "isMarsGuild");
            return (Criteria) this;
        }

        public Criteria andIsMarsGuildNotBetween(Integer value1, Integer value2) {
            addCriterion("is_mars_guild not between", value1, value2, "isMarsGuild");
            return (Criteria) this;
        }

        public Criteria andMarsSidOwneridIsNull() {
            addCriterion("mars_sid_ownerid is null");
            return (Criteria) this;
        }

        public Criteria andMarsSidOwneridIsNotNull() {
            addCriterion("mars_sid_ownerid is not null");
            return (Criteria) this;
        }

        public Criteria andMarsSidOwneridEqualTo(Long value) {
            addCriterion("mars_sid_ownerid =", value, "marsSidOwnerid");
            return (Criteria) this;
        }

        public Criteria andMarsSidOwneridNotEqualTo(Long value) {
            addCriterion("mars_sid_ownerid <>", value, "marsSidOwnerid");
            return (Criteria) this;
        }

        public Criteria andMarsSidOwneridGreaterThan(Long value) {
            addCriterion("mars_sid_ownerid >", value, "marsSidOwnerid");
            return (Criteria) this;
        }

        public Criteria andMarsSidOwneridGreaterThanOrEqualTo(Long value) {
            addCriterion("mars_sid_ownerid >=", value, "marsSidOwnerid");
            return (Criteria) this;
        }

        public Criteria andMarsSidOwneridLessThan(Long value) {
            addCriterion("mars_sid_ownerid <", value, "marsSidOwnerid");
            return (Criteria) this;
        }

        public Criteria andMarsSidOwneridLessThanOrEqualTo(Long value) {
            addCriterion("mars_sid_ownerid <=", value, "marsSidOwnerid");
            return (Criteria) this;
        }

        public Criteria andMarsSidOwneridIn(List<Long> values) {
            addCriterion("mars_sid_ownerid in", values, "marsSidOwnerid");
            return (Criteria) this;
        }

        public Criteria andMarsSidOwneridNotIn(List<Long> values) {
            addCriterion("mars_sid_ownerid not in", values, "marsSidOwnerid");
            return (Criteria) this;
        }

        public Criteria andMarsSidOwneridBetween(Long value1, Long value2) {
            addCriterion("mars_sid_ownerid between", value1, value2, "marsSidOwnerid");
            return (Criteria) this;
        }

        public Criteria andMarsSidOwneridNotBetween(Long value1, Long value2) {
            addCriterion("mars_sid_ownerid not between", value1, value2, "marsSidOwnerid");
            return (Criteria) this;
        }
    }

    /**
     * dm_yy_sid_segment_origin_star_day
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * dm_yy_sid_segment_origin_star_day null
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}