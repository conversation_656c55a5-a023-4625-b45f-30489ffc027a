package com.yy.yyzone.guildrank.db.gen.model;

import java.util.Date;

public class GuildrankConfig {
    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createPassport;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updatePassport;

    /**
     * 设置json
     */
    private String configJson;

    /**
     * 获取id
     * @return id id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置id
     * @param id id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取创建时间
     * @return create_time 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取创建人
     * @return create_passport 创建人
     */
    public String getCreatePassport() {
        return createPassport;
    }

    /**
     * 设置创建人
     * @param createPassport 创建人
     */
    public void setCreatePassport(String createPassport) {
        this.createPassport = createPassport;
    }

    /**
     * 获取更新时间
     * @return update_time 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取更新人
     * @return update_passport 更新人
     */
    public String getUpdatePassport() {
        return updatePassport;
    }

    /**
     * 设置更新人
     * @param updatePassport 更新人
     */
    public void setUpdatePassport(String updatePassport) {
        this.updatePassport = updatePassport;
    }

    /**
     * 获取设置json
     * @return config_json 设置json
     */
    public String getConfigJson() {
        return configJson;
    }

    /**
     * 设置设置json
     * @param configJson 设置json
     */
    public void setConfigJson(String configJson) {
        this.configJson = configJson;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", createTime=").append(createTime);
        sb.append(", createPassport=").append(createPassport);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updatePassport=").append(updatePassport);
        sb.append(", configJson=").append(configJson);
        sb.append("]");
        return sb.toString();
    }
}