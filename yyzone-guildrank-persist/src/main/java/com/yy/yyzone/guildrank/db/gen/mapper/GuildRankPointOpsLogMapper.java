package com.yy.yyzone.guildrank.db.gen.mapper;

import com.yy.yyzone.guildrank.db.gen.model.GuildRankPointOpsLog;
import com.yy.yyzone.guildrank.db.gen.model.GuildRankPointOpsLogExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface GuildRankPointOpsLogMapper {
    int countByExample(GuildRankPointOpsLogExample example);

    int deleteByExample(GuildRankPointOpsLogExample example);

    int deleteByPrimaryKey(Long id);

    int insert(GuildRankPointOpsLog record);

    int insertSelective(GuildRankPointOpsLog record);

    List<GuildRankPointOpsLog> selectByExample(GuildRankPointOpsLogExample example);

    GuildRankPointOpsLog selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") GuildRankPointOpsLog record, @Param("example") GuildRankPointOpsLogExample example);

    int updateByExample(@Param("record") GuildRankPointOpsLog record, @Param("example") GuildRankPointOpsLogExample example);

    int updateByPrimaryKeySelective(GuildRankPointOpsLog record);

    int updateByPrimaryKey(GuildRankPointOpsLog record);
}