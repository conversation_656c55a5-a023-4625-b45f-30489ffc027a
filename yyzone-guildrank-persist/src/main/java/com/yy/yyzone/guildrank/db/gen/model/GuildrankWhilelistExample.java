package com.yy.yyzone.guildrank.db.gen.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.annotation.Generated;

@Generated("guildrank_whilelist")
public class GuildrankWhilelistExample {
    /**
     * guildrank_whilelist
     */
    protected String orderByClause;

    /**
     * guildrank_whilelist
     */
    protected boolean distinct;

    /**
     * guildrank_whilelist
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public GuildrankWhilelistExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    /**
     * guildrank_whilelist null
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGuildUidIsNull() {
            addCriterion("guild_uid is null");
            return (Criteria) this;
        }

        public Criteria andGuildUidIsNotNull() {
            addCriterion("guild_uid is not null");
            return (Criteria) this;
        }

        public Criteria andGuildUidEqualTo(Long value) {
            addCriterion("guild_uid =", value, "guildUid");
            return (Criteria) this;
        }

        public Criteria andGuildUidNotEqualTo(Long value) {
            addCriterion("guild_uid <>", value, "guildUid");
            return (Criteria) this;
        }

        public Criteria andGuildUidGreaterThan(Long value) {
            addCriterion("guild_uid >", value, "guildUid");
            return (Criteria) this;
        }

        public Criteria andGuildUidGreaterThanOrEqualTo(Long value) {
            addCriterion("guild_uid >=", value, "guildUid");
            return (Criteria) this;
        }

        public Criteria andGuildUidLessThan(Long value) {
            addCriterion("guild_uid <", value, "guildUid");
            return (Criteria) this;
        }

        public Criteria andGuildUidLessThanOrEqualTo(Long value) {
            addCriterion("guild_uid <=", value, "guildUid");
            return (Criteria) this;
        }

        public Criteria andGuildUidIn(List<Long> values) {
            addCriterion("guild_uid in", values, "guildUid");
            return (Criteria) this;
        }

        public Criteria andGuildUidNotIn(List<Long> values) {
            addCriterion("guild_uid not in", values, "guildUid");
            return (Criteria) this;
        }

        public Criteria andGuildUidBetween(Long value1, Long value2) {
            addCriterion("guild_uid between", value1, value2, "guildUid");
            return (Criteria) this;
        }

        public Criteria andGuildUidNotBetween(Long value1, Long value2) {
            addCriterion("guild_uid not between", value1, value2, "guildUid");
            return (Criteria) this;
        }

        public Criteria andRankIsNull() {
            addCriterion("rank is null");
            return (Criteria) this;
        }

        public Criteria andRankIsNotNull() {
            addCriterion("rank is not null");
            return (Criteria) this;
        }

        public Criteria andRankEqualTo(Integer value) {
            addCriterion("rank =", value, "rank");
            return (Criteria) this;
        }

        public Criteria andRankNotEqualTo(Integer value) {
            addCriterion("rank <>", value, "rank");
            return (Criteria) this;
        }

        public Criteria andRankGreaterThan(Integer value) {
            addCriterion("rank >", value, "rank");
            return (Criteria) this;
        }

        public Criteria andRankGreaterThanOrEqualTo(Integer value) {
            addCriterion("rank >=", value, "rank");
            return (Criteria) this;
        }

        public Criteria andRankLessThan(Integer value) {
            addCriterion("rank <", value, "rank");
            return (Criteria) this;
        }

        public Criteria andRankLessThanOrEqualTo(Integer value) {
            addCriterion("rank <=", value, "rank");
            return (Criteria) this;
        }

        public Criteria andRankIn(List<Integer> values) {
            addCriterion("rank in", values, "rank");
            return (Criteria) this;
        }

        public Criteria andRankNotIn(List<Integer> values) {
            addCriterion("rank not in", values, "rank");
            return (Criteria) this;
        }

        public Criteria andRankBetween(Integer value1, Integer value2) {
            addCriterion("rank between", value1, value2, "rank");
            return (Criteria) this;
        }

        public Criteria andRankNotBetween(Integer value1, Integer value2) {
            addCriterion("rank not between", value1, value2, "rank");
            return (Criteria) this;
        }

        public Criteria andOpratorIsNull() {
            addCriterion("oprator is null");
            return (Criteria) this;
        }

        public Criteria andOpratorIsNotNull() {
            addCriterion("oprator is not null");
            return (Criteria) this;
        }

        public Criteria andOpratorEqualTo(String value) {
            addCriterion("oprator =", value, "oprator");
            return (Criteria) this;
        }

        public Criteria andOpratorNotEqualTo(String value) {
            addCriterion("oprator <>", value, "oprator");
            return (Criteria) this;
        }

        public Criteria andOpratorGreaterThan(String value) {
            addCriterion("oprator >", value, "oprator");
            return (Criteria) this;
        }

        public Criteria andOpratorGreaterThanOrEqualTo(String value) {
            addCriterion("oprator >=", value, "oprator");
            return (Criteria) this;
        }

        public Criteria andOpratorLessThan(String value) {
            addCriterion("oprator <", value, "oprator");
            return (Criteria) this;
        }

        public Criteria andOpratorLessThanOrEqualTo(String value) {
            addCriterion("oprator <=", value, "oprator");
            return (Criteria) this;
        }

        public Criteria andOpratorLike(String value) {
            addCriterion("oprator like", value, "oprator");
            return (Criteria) this;
        }

        public Criteria andOpratorNotLike(String value) {
            addCriterion("oprator not like", value, "oprator");
            return (Criteria) this;
        }

        public Criteria andOpratorIn(List<String> values) {
            addCriterion("oprator in", values, "oprator");
            return (Criteria) this;
        }

        public Criteria andOpratorNotIn(List<String> values) {
            addCriterion("oprator not in", values, "oprator");
            return (Criteria) this;
        }

        public Criteria andOpratorBetween(String value1, String value2) {
            addCriterion("oprator between", value1, value2, "oprator");
            return (Criteria) this;
        }

        public Criteria andOpratorNotBetween(String value1, String value2) {
            addCriterion("oprator not between", value1, value2, "oprator");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andMonthstrIsNull() {
            addCriterion("monthStr is null");
            return (Criteria) this;
        }

        public Criteria andMonthstrIsNotNull() {
            addCriterion("monthStr is not null");
            return (Criteria) this;
        }

        public Criteria andMonthstrEqualTo(String value) {
            addCriterion("monthStr =", value, "monthstr");
            return (Criteria) this;
        }

        public Criteria andMonthstrNotEqualTo(String value) {
            addCriterion("monthStr <>", value, "monthstr");
            return (Criteria) this;
        }

        public Criteria andMonthstrGreaterThan(String value) {
            addCriterion("monthStr >", value, "monthstr");
            return (Criteria) this;
        }

        public Criteria andMonthstrGreaterThanOrEqualTo(String value) {
            addCriterion("monthStr >=", value, "monthstr");
            return (Criteria) this;
        }

        public Criteria andMonthstrLessThan(String value) {
            addCriterion("monthStr <", value, "monthstr");
            return (Criteria) this;
        }

        public Criteria andMonthstrLessThanOrEqualTo(String value) {
            addCriterion("monthStr <=", value, "monthstr");
            return (Criteria) this;
        }

        public Criteria andMonthstrLike(String value) {
            addCriterion("monthStr like", value, "monthstr");
            return (Criteria) this;
        }

        public Criteria andMonthstrNotLike(String value) {
            addCriterion("monthStr not like", value, "monthstr");
            return (Criteria) this;
        }

        public Criteria andMonthstrIn(List<String> values) {
            addCriterion("monthStr in", values, "monthstr");
            return (Criteria) this;
        }

        public Criteria andMonthstrNotIn(List<String> values) {
            addCriterion("monthStr not in", values, "monthstr");
            return (Criteria) this;
        }

        public Criteria andMonthstrBetween(String value1, String value2) {
            addCriterion("monthStr between", value1, value2, "monthstr");
            return (Criteria) this;
        }

        public Criteria andMonthstrNotBetween(String value1, String value2) {
            addCriterion("monthStr not between", value1, value2, "monthstr");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }
    }

    /**
     * guildrank_whilelist
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * guildrank_whilelist null
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}