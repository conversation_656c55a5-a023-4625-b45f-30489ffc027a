<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yy.yyzone.guildrank.db.gen.mapper.GuildrankWhilelistMapper" >
  <resultMap id="BaseResultMap" type="com.yy.yyzone.guildrank.db.gen.model.GuildrankWhilelist" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="guild_uid" property="guildUid" jdbcType="BIGINT" />
    <result column="rank" property="rank" jdbcType="INTEGER" />
    <result column="oprator" property="oprator" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="monthStr" property="monthstr" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, guild_uid, rank, oprator, remark, monthStr, create_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankWhilelistExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from guildrank_whilelist
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit > 0" >
      limit ${limit}
    </if>
    <if test="offset > 0" >
      offset ${offset}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from guildrank_whilelist
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from guildrank_whilelist
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankWhilelistExample" >
    delete from guildrank_whilelist
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankWhilelist" useGeneratedKeys="true" keyProperty="id" >
    insert into guildrank_whilelist (guild_uid, rank, oprator, 
      remark, monthStr, create_time
      )
    values (#{guildUid,jdbcType=BIGINT}, #{rank,jdbcType=INTEGER}, #{oprator,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{monthstr,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankWhilelist" useGeneratedKeys="true" keyProperty="id" >
    insert into guildrank_whilelist
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="guildUid != null" >
        guild_uid,
      </if>
      <if test="rank != null" >
        rank,
      </if>
      <if test="oprator != null" >
        oprator,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="monthstr != null" >
        monthStr,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="guildUid != null" >
        #{guildUid,jdbcType=BIGINT},
      </if>
      <if test="rank != null" >
        #{rank,jdbcType=INTEGER},
      </if>
      <if test="oprator != null" >
        #{oprator,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="monthstr != null" >
        #{monthstr,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankWhilelistExample" resultType="java.lang.Integer" >
    select count(*) from guildrank_whilelist
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update guildrank_whilelist
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.guildUid != null" >
        guild_uid = #{record.guildUid,jdbcType=BIGINT},
      </if>
      <if test="record.rank != null" >
        rank = #{record.rank,jdbcType=INTEGER},
      </if>
      <if test="record.oprator != null" >
        oprator = #{record.oprator,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.monthstr != null" >
        monthStr = #{record.monthstr,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update guildrank_whilelist
    set id = #{record.id,jdbcType=BIGINT},
      guild_uid = #{record.guildUid,jdbcType=BIGINT},
      rank = #{record.rank,jdbcType=INTEGER},
      oprator = #{record.oprator,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      monthStr = #{record.monthstr,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankWhilelist" >
    update guildrank_whilelist
    <set >
      <if test="guildUid != null" >
        guild_uid = #{guildUid,jdbcType=BIGINT},
      </if>
      <if test="rank != null" >
        rank = #{rank,jdbcType=INTEGER},
      </if>
      <if test="oprator != null" >
        oprator = #{oprator,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="monthstr != null" >
        monthStr = #{monthstr,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankWhilelist" >
    update guildrank_whilelist
    set guild_uid = #{guildUid,jdbcType=BIGINT},
      rank = #{rank,jdbcType=INTEGER},
      oprator = #{oprator,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      monthStr = #{monthstr,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>