package com.yy.yyzone.guildrank.db.gen.model;

import java.util.Date;

/**
 * json配置类,采取 eventsource方式实现，这样同一个 configName 下面 id 比较小的就是历史配置
 */
public class JsonConfig {
    private long id;
    private String config;
    /**
     * 配置名
     */
    private String configName;
    private Date createTime;
    private Date updateTime;
    /**
     * 操作人
     */
    private String operator;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getConfig() {
        return config;
    }

    public void setConfig(String config) {
        this.config = config;
    }

    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    @Override
    public String toString() {
        return "JsonConfig{" +
                "id=" + id +
                ", config='" + config + '\'' +
                ", configName='" + configName + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", operator='" + operator + '\'' +
                '}';
    }
}
