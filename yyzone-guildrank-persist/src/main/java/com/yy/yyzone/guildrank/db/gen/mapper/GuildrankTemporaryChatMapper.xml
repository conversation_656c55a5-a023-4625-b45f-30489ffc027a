<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yy.yyzone.guildrank.db.gen.mapper.GuildrankTemporaryChatMapper" >
  <resultMap id="BaseResultMap" type="com.yy.yyzone.guildrank.db.gen.model.GuildrankTemporaryChat" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="serviceYY" property="serviceyy" jdbcType="BIGINT" />
    <result column="guildUid" property="guilduid" jdbcType="BIGINT" />
    <result column="createTime" property="createtime" jdbcType="TIMESTAMP" />
    <result column="updateTime" property="updatetime" jdbcType="TIMESTAMP" />
    <result column="addBy" property="addby" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="insertNum" property="insertnum" jdbcType="INTEGER" />
    <result column="bizType" property="biztype" jdbcType="INTEGER" />
    <result column="serviceNick" property="servicenick" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, serviceYY, guildUid, createTime, updateTime, addBy, status, insertNum, bizType, 
    serviceNick
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankTemporaryChatExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from guildrank_temporary_chat
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit > 0" >
      limit ${limit}
    </if>
    <if test="offset > 0" >
      offset ${offset}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from guildrank_temporary_chat
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from guildrank_temporary_chat
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankTemporaryChatExample" >
    delete from guildrank_temporary_chat
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankTemporaryChat" useGeneratedKeys="true" keyProperty="id" >
    insert into guildrank_temporary_chat (serviceYY, guildUid, createTime, 
      updateTime, addBy, status, 
      insertNum, bizType, serviceNick
      )
    values (#{serviceyy,jdbcType=BIGINT}, #{guilduid,jdbcType=BIGINT}, #{createtime,jdbcType=TIMESTAMP}, 
      #{updatetime,jdbcType=TIMESTAMP}, #{addby,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{insertnum,jdbcType=INTEGER}, #{biztype,jdbcType=INTEGER}, #{servicenick,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankTemporaryChat" useGeneratedKeys="true" keyProperty="id" >
    insert into guildrank_temporary_chat
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="serviceyy != null" >
        serviceYY,
      </if>
      <if test="guilduid != null" >
        guildUid,
      </if>
      <if test="createtime != null" >
        createTime,
      </if>
      <if test="updatetime != null" >
        updateTime,
      </if>
      <if test="addby != null" >
        addBy,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="insertnum != null" >
        insertNum,
      </if>
      <if test="biztype != null" >
        bizType,
      </if>
      <if test="servicenick != null" >
        serviceNick,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="serviceyy != null" >
        #{serviceyy,jdbcType=BIGINT},
      </if>
      <if test="guilduid != null" >
        #{guilduid,jdbcType=BIGINT},
      </if>
      <if test="createtime != null" >
        #{createtime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatetime != null" >
        #{updatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="addby != null" >
        #{addby,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="insertnum != null" >
        #{insertnum,jdbcType=INTEGER},
      </if>
      <if test="biztype != null" >
        #{biztype,jdbcType=INTEGER},
      </if>
      <if test="servicenick != null" >
        #{servicenick,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankTemporaryChatExample" resultType="java.lang.Integer" >
    select count(*) from guildrank_temporary_chat
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update guildrank_temporary_chat
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.serviceyy != null" >
        serviceYY = #{record.serviceyy,jdbcType=BIGINT},
      </if>
      <if test="record.guilduid != null" >
        guildUid = #{record.guilduid,jdbcType=BIGINT},
      </if>
      <if test="record.createtime != null" >
        createTime = #{record.createtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatetime != null" >
        updateTime = #{record.updatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.addby != null" >
        addBy = #{record.addby,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null" >
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.insertnum != null" >
        insertNum = #{record.insertnum,jdbcType=INTEGER},
      </if>
      <if test="record.biztype != null" >
        bizType = #{record.biztype,jdbcType=INTEGER},
      </if>
      <if test="record.servicenick != null" >
        serviceNick = #{record.servicenick,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update guildrank_temporary_chat
    set id = #{record.id,jdbcType=BIGINT},
      serviceYY = #{record.serviceyy,jdbcType=BIGINT},
      guildUid = #{record.guilduid,jdbcType=BIGINT},
      createTime = #{record.createtime,jdbcType=TIMESTAMP},
      updateTime = #{record.updatetime,jdbcType=TIMESTAMP},
      addBy = #{record.addby,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      insertNum = #{record.insertnum,jdbcType=INTEGER},
      bizType = #{record.biztype,jdbcType=INTEGER},
      serviceNick = #{record.servicenick,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankTemporaryChat" >
    update guildrank_temporary_chat
    <set >
      <if test="serviceyy != null" >
        serviceYY = #{serviceyy,jdbcType=BIGINT},
      </if>
      <if test="guilduid != null" >
        guildUid = #{guilduid,jdbcType=BIGINT},
      </if>
      <if test="createtime != null" >
        createTime = #{createtime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatetime != null" >
        updateTime = #{updatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="addby != null" >
        addBy = #{addby,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="insertnum != null" >
        insertNum = #{insertnum,jdbcType=INTEGER},
      </if>
      <if test="biztype != null" >
        bizType = #{biztype,jdbcType=INTEGER},
      </if>
      <if test="servicenick != null" >
        serviceNick = #{servicenick,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankTemporaryChat" >
    update guildrank_temporary_chat
    set serviceYY = #{serviceyy,jdbcType=BIGINT},
      guildUid = #{guilduid,jdbcType=BIGINT},
      createTime = #{createtime,jdbcType=TIMESTAMP},
      updateTime = #{updatetime,jdbcType=TIMESTAMP},
      addBy = #{addby,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      insertNum = #{insertnum,jdbcType=INTEGER},
      bizType = #{biztype,jdbcType=INTEGER},
      serviceNick = #{servicenick,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>