package com.yy.yyzone.guildrank.db.gen.model;

import java.util.Date;

public class GuildrankWhilelist {
    /**
     * id，自增主键
     */
    private Long id;

    /**
     * 公会uid
     */
    private Long guildUid;

    /**
     * 公会等级
     */
    private Integer rank;

    /**
     * 操作人
     */
    private String oprator;

    /**
     * 备注
     */
    private String remark;

    /**
     * 月份2018-01
     */
    private String monthstr;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 获取id，自增主键
     * @return id id，自增主键
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置id，自增主键
     * @param id id，自增主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取公会uid
     * @return guild_uid 公会uid
     */
    public Long getGuildUid() {
        return guildUid;
    }

    /**
     * 设置公会uid
     * @param guildUid 公会uid
     */
    public void setGuildUid(Long guildUid) {
        this.guildUid = guildUid;
    }

    /**
     * 获取公会等级
     * @return rank 公会等级
     */
    public Integer getRank() {
        return rank;
    }

    /**
     * 设置公会等级
     * @param rank 公会等级
     */
    public void setRank(Integer rank) {
        this.rank = rank;
    }

    /**
     * 获取操作人
     * @return oprator 操作人
     */
    public String getOprator() {
        return oprator;
    }

    /**
     * 设置操作人
     * @param oprator 操作人
     */
    public void setOprator(String oprator) {
        this.oprator = oprator;
    }

    /**
     * 获取备注
     * @return remark 备注
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 设置备注
     * @param remark 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 获取月份2018-01
     * @return monthStr 月份2018-01
     */
    public String getMonthstr() {
        return monthstr;
    }

    /**
     * 设置月份2018-01
     * @param monthstr 月份2018-01
     */
    public void setMonthstr(String monthstr) {
        this.monthstr = monthstr;
    }

    /**
     * 获取创建时间
     * @return create_time 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", guildUid=").append(guildUid);
        sb.append(", rank=").append(rank);
        sb.append(", oprator=").append(oprator);
        sb.append(", remark=").append(remark);
        sb.append(", monthstr=").append(monthstr);
        sb.append(", createTime=").append(createTime);
        sb.append("]");
        return sb.toString();
    }
}