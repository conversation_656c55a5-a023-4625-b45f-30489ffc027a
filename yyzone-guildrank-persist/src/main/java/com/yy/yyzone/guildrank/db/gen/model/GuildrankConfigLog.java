package com.yy.yyzone.guildrank.db.gen.model;

import java.util.Date;

public class GuildrankConfigLog {
    /**
     * id
     */
    private Long id;

    /**
     * 设置id
     */
    private Long configId;

    /**
     * 操作人
     */
    private String optPassport;

    /**
     * 操作时间
     */
    private Date optTime;

    /**
     * 类型
     */
    private String type;

    /**
     * 修改前数据
     */
    private String oldData;

    /**
     * 修改后数据
     */
    private String newData;

    /**
     * 获取id
     * @return id id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置id
     * @param id id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取设置id
     * @return config_id 设置id
     */
    public Long getConfigId() {
        return configId;
    }

    /**
     * 设置设置id
     * @param configId 设置id
     */
    public void setConfigId(Long configId) {
        this.configId = configId;
    }

    /**
     * 获取操作人
     * @return opt_passport 操作人
     */
    public String getOptPassport() {
        return optPassport;
    }

    /**
     * 设置操作人
     * @param optPassport 操作人
     */
    public void setOptPassport(String optPassport) {
        this.optPassport = optPassport;
    }

    /**
     * 获取操作时间
     * @return opt_time 操作时间
     */
    public Date getOptTime() {
        return optTime;
    }

    /**
     * 设置操作时间
     * @param optTime 操作时间
     */
    public void setOptTime(Date optTime) {
        this.optTime = optTime;
    }

    /**
     * 获取类型
     * @return type 类型
     */
    public String getType() {
        return type;
    }

    /**
     * 设置类型
     * @param type 类型
     */
    public void setType(String type) {
        this.type = type;
    }

    /**
     * 获取修改前数据
     * @return old_data 修改前数据
     */
    public String getOldData() {
        return oldData;
    }

    /**
     * 设置修改前数据
     * @param oldData 修改前数据
     */
    public void setOldData(String oldData) {
        this.oldData = oldData;
    }

    /**
     * 获取修改后数据
     * @return new_data 修改后数据
     */
    public String getNewData() {
        return newData;
    }

    /**
     * 设置修改后数据
     * @param newData 修改后数据
     */
    public void setNewData(String newData) {
        this.newData = newData;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", configId=").append(configId);
        sb.append(", optPassport=").append(optPassport);
        sb.append(", optTime=").append(optTime);
        sb.append(", type=").append(type);
        sb.append(", oldData=").append(oldData);
        sb.append(", newData=").append(newData);
        sb.append("]");
        return sb.toString();
    }
}