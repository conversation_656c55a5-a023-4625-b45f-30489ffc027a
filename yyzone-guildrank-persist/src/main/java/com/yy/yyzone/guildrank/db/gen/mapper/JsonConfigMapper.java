package com.yy.yyzone.guildrank.db.gen.mapper;

import com.yy.yyzone.guildrank.db.gen.model.JsonConfig;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;

public interface JsonConfigMapper {
    // 根据配置名查询最新的配置
    @Select("SELECT id, config, config_name AS configName, create_time AS createTime, update_time AS updateTime, operator " +
            "FROM guild_rank_json_config " +
            "WHERE config_name = #{configName} " +
            "ORDER BY id DESC LIMIT 1")
    JsonConfig getLatestConfigByName(@Param("configName") String configName);

    @Select("SELECT id, config, config_name AS configName, create_time AS createTime, update_time AS updateTime, operator " +
            "FROM guild_rank_json_config " +
            "WHERE config_name = #{configName} " +
            "AND (#{startTime} IS NULL OR create_time >= #{startTime}) " +
            "AND (#{endTime} IS NULL OR create_time <= #{endTime}) " +
            "AND (#{operator} IS NULL OR operator = #{operator}) " +
            "ORDER BY id DESC")
    List<JsonConfig> getConfigsByName(@Param("configName") String configName, @Param("startTime") Date startTime, @Param("endTime") Date endTime,@Param("operator") String operator);

    @Insert("INSERT INTO guild_rank_json_config (config , config_name,operator)  VALUES (#{config}, #{configName}, #{operator})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertConfig(JsonConfig config);


    @Update("UPDATE guild_rank_json_config_version SET version = #{newVersion} WHERE config_name = #{configName} AND version = #{oldVersion}")
    int updateConfigVersion(@Param("configName") String configName, @Param("newVersion") long newVersion, @Param("oldVersion") long oldVersion);

    // 插入配置版本号,存在就忽略
    @Insert("INSERT IGNORE INTO guild_rank_json_config_version (config_name, version) VALUES (#{configName}, #{version}) ")
    int ignoreInsertOrUpdateConfigVersion(@Param("configName") String configName, @Param("version") long version);

    @Select("SELECT id, config, config_name AS configName, create_time AS createTime, update_time AS updateTime, operator " +
            "FROM guild_rank_json_config " +
            "WHERE id = #{id}")
    JsonConfig selectById(@Param("id") long id);
}