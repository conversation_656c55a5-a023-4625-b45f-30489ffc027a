package com.yy.yyzone.guildrank.db.gen.mapper;

import com.yy.yyzone.guildrank.db.gen.model.GuildrankPointDetail;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankPointDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface GuildrankPointDetailMapper {
    int countByExample(GuildrankPointDetailExample example);

    int deleteByExample(GuildrankPointDetailExample example);

    int deleteByPrimaryKey(Long id);

    int insert(GuildrankPointDetail record);

    int insertSelective(GuildrankPointDetail record);

    List<GuildrankPointDetail> selectByExample(GuildrankPointDetailExample example);

    GuildrankPointDetail selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") GuildrankPointDetail record, @Param("example") GuildrankPointDetailExample example);

    int updateByExample(@Param("record") GuildrankPointDetail record, @Param("example") GuildrankPointDetailExample example);

    int updateByPrimaryKeySelective(GuildrankPointDetail record);

    int updateByPrimaryKey(GuildrankPointDetail record);
}