<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yy.yyzone.guildrank.db.gen.mapper.DmYySidSegmentScoreMonthMapper" >
  <resultMap id="BaseResultMap" type="com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreMonth" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="sid_ownerid" property="sidOwnerid" jdbcType="BIGINT" />
    <result column="sid_owyyid" property="sidOwyyid" jdbcType="BIGINT" />
    <result column="new_uv" property="newUv" jdbcType="BIGINT" />
    <result column="new_uv_rr" property="newUvRr" jdbcType="BIGINT" />
    <result column="valid_live_new_uv" property="validLiveNewUv" jdbcType="BIGINT" />
    <result column="valid_live_new_uv_rr" property="validLiveNewUvRr" jdbcType="BIGINT" />
    <result column="per_new_uv" property="perNewUv" jdbcType="DOUBLE" />
    <result column="per_valid_live_new_uv" property="perValidLiveNewUv" jdbcType="DOUBLE" />
    <result column="new_uv_rt" property="newUvRt" jdbcType="DOUBLE" />
    <result column="acu10_uv" property="acu10Uv" jdbcType="BIGINT" />
    <result column="acu10_uv_rr" property="acu10UvRr" jdbcType="BIGINT" />
    <result column="per_acu10_uv" property="perAcu10Uv" jdbcType="DOUBLE" />
    <result column="acu10_50_uv" property="acu1050Uv" jdbcType="BIGINT" />
    <result column="acu50_300_uv" property="acu50300Uv" jdbcType="BIGINT" />
    <result column="acu300_uv" property="acu300Uv" jdbcType="BIGINT" />
    <result column="h_act_uv" property="hActUv" jdbcType="BIGINT" />
    <result column="h_act_uv_rr" property="hActUvRr" jdbcType="BIGINT" />
    <result column="per_h_act_uv" property="perHActUv" jdbcType="DOUBLE" />
    <result column="month_diamond" property="monthDiamond" jdbcType="DOUBLE" />
    <result column="month_diamond_rr" property="monthDiamondRr" jdbcType="DOUBLE" />
    <result column="per_month_diamond" property="perMonthDiamond" jdbcType="DOUBLE" />
    <result column="break_a_num" property="breakANum" jdbcType="BIGINT" />
    <result column="break_b_num" property="breakBNum" jdbcType="BIGINT" />
    <result column="break_c_num" property="breakCNum" jdbcType="BIGINT" />
    <result column="break_e_num" property="breakENum" jdbcType="BIGINT" />
    <result column="live_uv" property="liveUv" jdbcType="BIGINT" />
    <result column="break_rt" property="breakRt" jdbcType="DOUBLE" />
    <result column="break_rr" property="breakRr" jdbcType="DOUBLE" />
    <result column="per_break" property="perBreak" jdbcType="DOUBLE" />
    <result column="p1" property="p1" jdbcType="DOUBLE" />
    <result column="ps_1" property="ps1" jdbcType="DOUBLE" />
    <result column="ps_s_1" property="psS1" jdbcType="BIGINT" />
    <result column="ps_s_1_sh" property="psS1Sh" jdbcType="VARCHAR" />
    <result column="ps_s_1_rn" property="psS1Rn" jdbcType="BIGINT" />
    <result column="p2" property="p2" jdbcType="DOUBLE" />
    <result column="ps_2" property="ps2" jdbcType="DOUBLE" />
    <result column="ps_s_2" property="psS2" jdbcType="BIGINT" />
    <result column="ps_s_2_sh" property="psS2Sh" jdbcType="VARCHAR" />
    <result column="ps_s_2_rn" property="psS2Rn" jdbcType="BIGINT" />
    <result column="p3" property="p3" jdbcType="DOUBLE" />
    <result column="ps_3" property="ps3" jdbcType="DOUBLE" />
    <result column="ps_s_3" property="psS3" jdbcType="BIGINT" />
    <result column="ps_s_3_sh" property="psS3Sh" jdbcType="VARCHAR" />
    <result column="ps_s_3_rn" property="psS3Rn" jdbcType="BIGINT" />
    <result column="p4" property="p4" jdbcType="DOUBLE" />
    <result column="ps_4" property="ps4" jdbcType="DOUBLE" />
    <result column="ps_s_4" property="psS4" jdbcType="BIGINT" />
    <result column="ps_s_4_sh" property="psS4Sh" jdbcType="VARCHAR" />
    <result column="ps_s_4_rn" property="psS4Rn" jdbcType="BIGINT" />
    <result column="p5" property="p5" jdbcType="DOUBLE" />
    <result column="ps_5" property="ps5" jdbcType="DOUBLE" />
    <result column="ps_s_5" property="psS5" jdbcType="BIGINT" />
    <result column="ps_s_5_sh" property="psS5Sh" jdbcType="VARCHAR" />
    <result column="ps_s_5_rn" property="psS5Rn" jdbcType="BIGINT" />
    <result column="ps_all" property="psAll" jdbcType="DOUBLE" />
    <result column="ps_s_all_cor" property="psSAllCor" jdbcType="BIGINT" />
    <result column="ps_s_all_cor_rr" property="psSAllCorRr" jdbcType="VARCHAR" />
    <result column="ps_s_all_rn_cor" property="psSAllRnCor" jdbcType="BIGINT" />
    <result column="ps_s_all_rn_cor_rr" property="psSAllRnCorRr" jdbcType="VARCHAR" />
    <result column="spr_new_uv" property="sprNewUv" jdbcType="DOUBLE" />
    <result column="spr_valid_live_new_uv" property="sprValidLiveNewUv" jdbcType="DOUBLE" />
    <result column="spr_acu10_uv" property="sprAcu10Uv" jdbcType="DOUBLE" />
    <result column="spr_h_act_uv" property="sprHActUv" jdbcType="DOUBLE" />
    <result column="spr_month_diamond" property="sprMonthDiamond" jdbcType="DOUBLE" />
    <result column="spr_p5" property="sprP5" jdbcType="DOUBLE" />
    <result column="dt" property="dt" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, sid_ownerid, sid_owyyid, new_uv, new_uv_rr, valid_live_new_uv, valid_live_new_uv_rr, 
    per_new_uv, per_valid_live_new_uv, new_uv_rt, acu10_uv, acu10_uv_rr, per_acu10_uv, 
    acu10_50_uv, acu50_300_uv, acu300_uv, h_act_uv, h_act_uv_rr, per_h_act_uv, month_diamond, 
    month_diamond_rr, per_month_diamond, break_a_num, break_b_num, break_c_num, break_e_num, 
    live_uv, break_rt, break_rr, per_break, p1, ps_1, ps_s_1, ps_s_1_sh, ps_s_1_rn, p2, 
    ps_2, ps_s_2, ps_s_2_sh, ps_s_2_rn, p3, ps_3, ps_s_3, ps_s_3_sh, ps_s_3_rn, p4, ps_4, 
    ps_s_4, ps_s_4_sh, ps_s_4_rn, p5, ps_5, ps_s_5, ps_s_5_sh, ps_s_5_rn, ps_all, ps_s_all_cor, 
    ps_s_all_cor_rr, ps_s_all_rn_cor, ps_s_all_rn_cor_rr, spr_new_uv, spr_valid_live_new_uv, 
    spr_acu10_uv, spr_h_act_uv, spr_month_diamond, spr_p5, dt
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreMonthExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from dm_yy_sid_segment_score_month
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit > 0" >
      limit ${limit}
    </if>
    <if test="offset > 0" >
      offset ${offset}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from dm_yy_sid_segment_score_month
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from dm_yy_sid_segment_score_month
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreMonthExample" >
    delete from dm_yy_sid_segment_score_month
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreMonth" useGeneratedKeys="true" keyProperty="id" >
    insert into dm_yy_sid_segment_score_month (sid_ownerid, sid_owyyid, new_uv, 
      new_uv_rr, valid_live_new_uv, valid_live_new_uv_rr, 
      per_new_uv, per_valid_live_new_uv, new_uv_rt, 
      acu10_uv, acu10_uv_rr, per_acu10_uv, 
      acu10_50_uv, acu50_300_uv, acu300_uv, 
      h_act_uv, h_act_uv_rr, per_h_act_uv, 
      month_diamond, month_diamond_rr, per_month_diamond, 
      break_a_num, break_b_num, break_c_num, 
      break_e_num, live_uv, break_rt, 
      break_rr, per_break, p1, 
      ps_1, ps_s_1, ps_s_1_sh, ps_s_1_rn, 
      p2, ps_2, ps_s_2, ps_s_2_sh, 
      ps_s_2_rn, p3, ps_3, ps_s_3, 
      ps_s_3_sh, ps_s_3_rn, p4, ps_4, 
      ps_s_4, ps_s_4_sh, ps_s_4_rn, 
      p5, ps_5, ps_s_5, ps_s_5_sh, 
      ps_s_5_rn, ps_all, ps_s_all_cor, 
      ps_s_all_cor_rr, ps_s_all_rn_cor, ps_s_all_rn_cor_rr, 
      spr_new_uv, spr_valid_live_new_uv, spr_acu10_uv, 
      spr_h_act_uv, spr_month_diamond, spr_p5, 
      dt)
    values (#{sidOwnerid,jdbcType=BIGINT}, #{sidOwyyid,jdbcType=BIGINT}, #{newUv,jdbcType=BIGINT}, 
      #{newUvRr,jdbcType=BIGINT}, #{validLiveNewUv,jdbcType=BIGINT}, #{validLiveNewUvRr,jdbcType=BIGINT}, 
      #{perNewUv,jdbcType=DOUBLE}, #{perValidLiveNewUv,jdbcType=DOUBLE}, #{newUvRt,jdbcType=DOUBLE}, 
      #{acu10Uv,jdbcType=BIGINT}, #{acu10UvRr,jdbcType=BIGINT}, #{perAcu10Uv,jdbcType=DOUBLE}, 
      #{acu1050Uv,jdbcType=BIGINT}, #{acu50300Uv,jdbcType=BIGINT}, #{acu300Uv,jdbcType=BIGINT}, 
      #{hActUv,jdbcType=BIGINT}, #{hActUvRr,jdbcType=BIGINT}, #{perHActUv,jdbcType=DOUBLE}, 
      #{monthDiamond,jdbcType=DOUBLE}, #{monthDiamondRr,jdbcType=DOUBLE}, #{perMonthDiamond,jdbcType=DOUBLE}, 
      #{breakANum,jdbcType=BIGINT}, #{breakBNum,jdbcType=BIGINT}, #{breakCNum,jdbcType=BIGINT}, 
      #{breakENum,jdbcType=BIGINT}, #{liveUv,jdbcType=BIGINT}, #{breakRt,jdbcType=DOUBLE}, 
      #{breakRr,jdbcType=DOUBLE}, #{perBreak,jdbcType=DOUBLE}, #{p1,jdbcType=DOUBLE}, 
      #{ps1,jdbcType=DOUBLE}, #{psS1,jdbcType=BIGINT}, #{psS1Sh,jdbcType=VARCHAR}, #{psS1Rn,jdbcType=BIGINT}, 
      #{p2,jdbcType=DOUBLE}, #{ps2,jdbcType=DOUBLE}, #{psS2,jdbcType=BIGINT}, #{psS2Sh,jdbcType=VARCHAR}, 
      #{psS2Rn,jdbcType=BIGINT}, #{p3,jdbcType=DOUBLE}, #{ps3,jdbcType=DOUBLE}, #{psS3,jdbcType=BIGINT}, 
      #{psS3Sh,jdbcType=VARCHAR}, #{psS3Rn,jdbcType=BIGINT}, #{p4,jdbcType=DOUBLE}, #{ps4,jdbcType=DOUBLE}, 
      #{psS4,jdbcType=BIGINT}, #{psS4Sh,jdbcType=VARCHAR}, #{psS4Rn,jdbcType=BIGINT}, 
      #{p5,jdbcType=DOUBLE}, #{ps5,jdbcType=DOUBLE}, #{psS5,jdbcType=BIGINT}, #{psS5Sh,jdbcType=VARCHAR}, 
      #{psS5Rn,jdbcType=BIGINT}, #{psAll,jdbcType=DOUBLE}, #{psSAllCor,jdbcType=BIGINT}, 
      #{psSAllCorRr,jdbcType=VARCHAR}, #{psSAllRnCor,jdbcType=BIGINT}, #{psSAllRnCorRr,jdbcType=VARCHAR}, 
      #{sprNewUv,jdbcType=DOUBLE}, #{sprValidLiveNewUv,jdbcType=DOUBLE}, #{sprAcu10Uv,jdbcType=DOUBLE}, 
      #{sprHActUv,jdbcType=DOUBLE}, #{sprMonthDiamond,jdbcType=DOUBLE}, #{sprP5,jdbcType=DOUBLE}, 
      #{dt,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreMonth" useGeneratedKeys="true" keyProperty="id" >
    insert into dm_yy_sid_segment_score_month
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="sidOwnerid != null" >
        sid_ownerid,
      </if>
      <if test="sidOwyyid != null" >
        sid_owyyid,
      </if>
      <if test="newUv != null" >
        new_uv,
      </if>
      <if test="newUvRr != null" >
        new_uv_rr,
      </if>
      <if test="validLiveNewUv != null" >
        valid_live_new_uv,
      </if>
      <if test="validLiveNewUvRr != null" >
        valid_live_new_uv_rr,
      </if>
      <if test="perNewUv != null" >
        per_new_uv,
      </if>
      <if test="perValidLiveNewUv != null" >
        per_valid_live_new_uv,
      </if>
      <if test="newUvRt != null" >
        new_uv_rt,
      </if>
      <if test="acu10Uv != null" >
        acu10_uv,
      </if>
      <if test="acu10UvRr != null" >
        acu10_uv_rr,
      </if>
      <if test="perAcu10Uv != null" >
        per_acu10_uv,
      </if>
      <if test="acu1050Uv != null" >
        acu10_50_uv,
      </if>
      <if test="acu50300Uv != null" >
        acu50_300_uv,
      </if>
      <if test="acu300Uv != null" >
        acu300_uv,
      </if>
      <if test="hActUv != null" >
        h_act_uv,
      </if>
      <if test="hActUvRr != null" >
        h_act_uv_rr,
      </if>
      <if test="perHActUv != null" >
        per_h_act_uv,
      </if>
      <if test="monthDiamond != null" >
        month_diamond,
      </if>
      <if test="monthDiamondRr != null" >
        month_diamond_rr,
      </if>
      <if test="perMonthDiamond != null" >
        per_month_diamond,
      </if>
      <if test="breakANum != null" >
        break_a_num,
      </if>
      <if test="breakBNum != null" >
        break_b_num,
      </if>
      <if test="breakCNum != null" >
        break_c_num,
      </if>
      <if test="breakENum != null" >
        break_e_num,
      </if>
      <if test="liveUv != null" >
        live_uv,
      </if>
      <if test="breakRt != null" >
        break_rt,
      </if>
      <if test="breakRr != null" >
        break_rr,
      </if>
      <if test="perBreak != null" >
        per_break,
      </if>
      <if test="p1 != null" >
        p1,
      </if>
      <if test="ps1 != null" >
        ps_1,
      </if>
      <if test="psS1 != null" >
        ps_s_1,
      </if>
      <if test="psS1Sh != null" >
        ps_s_1_sh,
      </if>
      <if test="psS1Rn != null" >
        ps_s_1_rn,
      </if>
      <if test="p2 != null" >
        p2,
      </if>
      <if test="ps2 != null" >
        ps_2,
      </if>
      <if test="psS2 != null" >
        ps_s_2,
      </if>
      <if test="psS2Sh != null" >
        ps_s_2_sh,
      </if>
      <if test="psS2Rn != null" >
        ps_s_2_rn,
      </if>
      <if test="p3 != null" >
        p3,
      </if>
      <if test="ps3 != null" >
        ps_3,
      </if>
      <if test="psS3 != null" >
        ps_s_3,
      </if>
      <if test="psS3Sh != null" >
        ps_s_3_sh,
      </if>
      <if test="psS3Rn != null" >
        ps_s_3_rn,
      </if>
      <if test="p4 != null" >
        p4,
      </if>
      <if test="ps4 != null" >
        ps_4,
      </if>
      <if test="psS4 != null" >
        ps_s_4,
      </if>
      <if test="psS4Sh != null" >
        ps_s_4_sh,
      </if>
      <if test="psS4Rn != null" >
        ps_s_4_rn,
      </if>
      <if test="p5 != null" >
        p5,
      </if>
      <if test="ps5 != null" >
        ps_5,
      </if>
      <if test="psS5 != null" >
        ps_s_5,
      </if>
      <if test="psS5Sh != null" >
        ps_s_5_sh,
      </if>
      <if test="psS5Rn != null" >
        ps_s_5_rn,
      </if>
      <if test="psAll != null" >
        ps_all,
      </if>
      <if test="psSAllCor != null" >
        ps_s_all_cor,
      </if>
      <if test="psSAllCorRr != null" >
        ps_s_all_cor_rr,
      </if>
      <if test="psSAllRnCor != null" >
        ps_s_all_rn_cor,
      </if>
      <if test="psSAllRnCorRr != null" >
        ps_s_all_rn_cor_rr,
      </if>
      <if test="sprNewUv != null" >
        spr_new_uv,
      </if>
      <if test="sprValidLiveNewUv != null" >
        spr_valid_live_new_uv,
      </if>
      <if test="sprAcu10Uv != null" >
        spr_acu10_uv,
      </if>
      <if test="sprHActUv != null" >
        spr_h_act_uv,
      </if>
      <if test="sprMonthDiamond != null" >
        spr_month_diamond,
      </if>
      <if test="sprP5 != null" >
        spr_p5,
      </if>
      <if test="dt != null" >
        dt,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="sidOwnerid != null" >
        #{sidOwnerid,jdbcType=BIGINT},
      </if>
      <if test="sidOwyyid != null" >
        #{sidOwyyid,jdbcType=BIGINT},
      </if>
      <if test="newUv != null" >
        #{newUv,jdbcType=BIGINT},
      </if>
      <if test="newUvRr != null" >
        #{newUvRr,jdbcType=BIGINT},
      </if>
      <if test="validLiveNewUv != null" >
        #{validLiveNewUv,jdbcType=BIGINT},
      </if>
      <if test="validLiveNewUvRr != null" >
        #{validLiveNewUvRr,jdbcType=BIGINT},
      </if>
      <if test="perNewUv != null" >
        #{perNewUv,jdbcType=DOUBLE},
      </if>
      <if test="perValidLiveNewUv != null" >
        #{perValidLiveNewUv,jdbcType=DOUBLE},
      </if>
      <if test="newUvRt != null" >
        #{newUvRt,jdbcType=DOUBLE},
      </if>
      <if test="acu10Uv != null" >
        #{acu10Uv,jdbcType=BIGINT},
      </if>
      <if test="acu10UvRr != null" >
        #{acu10UvRr,jdbcType=BIGINT},
      </if>
      <if test="perAcu10Uv != null" >
        #{perAcu10Uv,jdbcType=DOUBLE},
      </if>
      <if test="acu1050Uv != null" >
        #{acu1050Uv,jdbcType=BIGINT},
      </if>
      <if test="acu50300Uv != null" >
        #{acu50300Uv,jdbcType=BIGINT},
      </if>
      <if test="acu300Uv != null" >
        #{acu300Uv,jdbcType=BIGINT},
      </if>
      <if test="hActUv != null" >
        #{hActUv,jdbcType=BIGINT},
      </if>
      <if test="hActUvRr != null" >
        #{hActUvRr,jdbcType=BIGINT},
      </if>
      <if test="perHActUv != null" >
        #{perHActUv,jdbcType=DOUBLE},
      </if>
      <if test="monthDiamond != null" >
        #{monthDiamond,jdbcType=DOUBLE},
      </if>
      <if test="monthDiamondRr != null" >
        #{monthDiamondRr,jdbcType=DOUBLE},
      </if>
      <if test="perMonthDiamond != null" >
        #{perMonthDiamond,jdbcType=DOUBLE},
      </if>
      <if test="breakANum != null" >
        #{breakANum,jdbcType=BIGINT},
      </if>
      <if test="breakBNum != null" >
        #{breakBNum,jdbcType=BIGINT},
      </if>
      <if test="breakCNum != null" >
        #{breakCNum,jdbcType=BIGINT},
      </if>
      <if test="breakENum != null" >
        #{breakENum,jdbcType=BIGINT},
      </if>
      <if test="liveUv != null" >
        #{liveUv,jdbcType=BIGINT},
      </if>
      <if test="breakRt != null" >
        #{breakRt,jdbcType=DOUBLE},
      </if>
      <if test="breakRr != null" >
        #{breakRr,jdbcType=DOUBLE},
      </if>
      <if test="perBreak != null" >
        #{perBreak,jdbcType=DOUBLE},
      </if>
      <if test="p1 != null" >
        #{p1,jdbcType=DOUBLE},
      </if>
      <if test="ps1 != null" >
        #{ps1,jdbcType=DOUBLE},
      </if>
      <if test="psS1 != null" >
        #{psS1,jdbcType=BIGINT},
      </if>
      <if test="psS1Sh != null" >
        #{psS1Sh,jdbcType=VARCHAR},
      </if>
      <if test="psS1Rn != null" >
        #{psS1Rn,jdbcType=BIGINT},
      </if>
      <if test="p2 != null" >
        #{p2,jdbcType=DOUBLE},
      </if>
      <if test="ps2 != null" >
        #{ps2,jdbcType=DOUBLE},
      </if>
      <if test="psS2 != null" >
        #{psS2,jdbcType=BIGINT},
      </if>
      <if test="psS2Sh != null" >
        #{psS2Sh,jdbcType=VARCHAR},
      </if>
      <if test="psS2Rn != null" >
        #{psS2Rn,jdbcType=BIGINT},
      </if>
      <if test="p3 != null" >
        #{p3,jdbcType=DOUBLE},
      </if>
      <if test="ps3 != null" >
        #{ps3,jdbcType=DOUBLE},
      </if>
      <if test="psS3 != null" >
        #{psS3,jdbcType=BIGINT},
      </if>
      <if test="psS3Sh != null" >
        #{psS3Sh,jdbcType=VARCHAR},
      </if>
      <if test="psS3Rn != null" >
        #{psS3Rn,jdbcType=BIGINT},
      </if>
      <if test="p4 != null" >
        #{p4,jdbcType=DOUBLE},
      </if>
      <if test="ps4 != null" >
        #{ps4,jdbcType=DOUBLE},
      </if>
      <if test="psS4 != null" >
        #{psS4,jdbcType=BIGINT},
      </if>
      <if test="psS4Sh != null" >
        #{psS4Sh,jdbcType=VARCHAR},
      </if>
      <if test="psS4Rn != null" >
        #{psS4Rn,jdbcType=BIGINT},
      </if>
      <if test="p5 != null" >
        #{p5,jdbcType=DOUBLE},
      </if>
      <if test="ps5 != null" >
        #{ps5,jdbcType=DOUBLE},
      </if>
      <if test="psS5 != null" >
        #{psS5,jdbcType=BIGINT},
      </if>
      <if test="psS5Sh != null" >
        #{psS5Sh,jdbcType=VARCHAR},
      </if>
      <if test="psS5Rn != null" >
        #{psS5Rn,jdbcType=BIGINT},
      </if>
      <if test="psAll != null" >
        #{psAll,jdbcType=DOUBLE},
      </if>
      <if test="psSAllCor != null" >
        #{psSAllCor,jdbcType=BIGINT},
      </if>
      <if test="psSAllCorRr != null" >
        #{psSAllCorRr,jdbcType=VARCHAR},
      </if>
      <if test="psSAllRnCor != null" >
        #{psSAllRnCor,jdbcType=BIGINT},
      </if>
      <if test="psSAllRnCorRr != null" >
        #{psSAllRnCorRr,jdbcType=VARCHAR},
      </if>
      <if test="sprNewUv != null" >
        #{sprNewUv,jdbcType=DOUBLE},
      </if>
      <if test="sprValidLiveNewUv != null" >
        #{sprValidLiveNewUv,jdbcType=DOUBLE},
      </if>
      <if test="sprAcu10Uv != null" >
        #{sprAcu10Uv,jdbcType=DOUBLE},
      </if>
      <if test="sprHActUv != null" >
        #{sprHActUv,jdbcType=DOUBLE},
      </if>
      <if test="sprMonthDiamond != null" >
        #{sprMonthDiamond,jdbcType=DOUBLE},
      </if>
      <if test="sprP5 != null" >
        #{sprP5,jdbcType=DOUBLE},
      </if>
      <if test="dt != null" >
        #{dt,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreMonthExample" resultType="java.lang.Integer" >
    select count(*) from dm_yy_sid_segment_score_month
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update dm_yy_sid_segment_score_month
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.sidOwnerid != null" >
        sid_ownerid = #{record.sidOwnerid,jdbcType=BIGINT},
      </if>
      <if test="record.sidOwyyid != null" >
        sid_owyyid = #{record.sidOwyyid,jdbcType=BIGINT},
      </if>
      <if test="record.newUv != null" >
        new_uv = #{record.newUv,jdbcType=BIGINT},
      </if>
      <if test="record.newUvRr != null" >
        new_uv_rr = #{record.newUvRr,jdbcType=BIGINT},
      </if>
      <if test="record.validLiveNewUv != null" >
        valid_live_new_uv = #{record.validLiveNewUv,jdbcType=BIGINT},
      </if>
      <if test="record.validLiveNewUvRr != null" >
        valid_live_new_uv_rr = #{record.validLiveNewUvRr,jdbcType=BIGINT},
      </if>
      <if test="record.perNewUv != null" >
        per_new_uv = #{record.perNewUv,jdbcType=DOUBLE},
      </if>
      <if test="record.perValidLiveNewUv != null" >
        per_valid_live_new_uv = #{record.perValidLiveNewUv,jdbcType=DOUBLE},
      </if>
      <if test="record.newUvRt != null" >
        new_uv_rt = #{record.newUvRt,jdbcType=DOUBLE},
      </if>
      <if test="record.acu10Uv != null" >
        acu10_uv = #{record.acu10Uv,jdbcType=BIGINT},
      </if>
      <if test="record.acu10UvRr != null" >
        acu10_uv_rr = #{record.acu10UvRr,jdbcType=BIGINT},
      </if>
      <if test="record.perAcu10Uv != null" >
        per_acu10_uv = #{record.perAcu10Uv,jdbcType=DOUBLE},
      </if>
      <if test="record.acu1050Uv != null" >
        acu10_50_uv = #{record.acu1050Uv,jdbcType=BIGINT},
      </if>
      <if test="record.acu50300Uv != null" >
        acu50_300_uv = #{record.acu50300Uv,jdbcType=BIGINT},
      </if>
      <if test="record.acu300Uv != null" >
        acu300_uv = #{record.acu300Uv,jdbcType=BIGINT},
      </if>
      <if test="record.hActUv != null" >
        h_act_uv = #{record.hActUv,jdbcType=BIGINT},
      </if>
      <if test="record.hActUvRr != null" >
        h_act_uv_rr = #{record.hActUvRr,jdbcType=BIGINT},
      </if>
      <if test="record.perHActUv != null" >
        per_h_act_uv = #{record.perHActUv,jdbcType=DOUBLE},
      </if>
      <if test="record.monthDiamond != null" >
        month_diamond = #{record.monthDiamond,jdbcType=DOUBLE},
      </if>
      <if test="record.monthDiamondRr != null" >
        month_diamond_rr = #{record.monthDiamondRr,jdbcType=DOUBLE},
      </if>
      <if test="record.perMonthDiamond != null" >
        per_month_diamond = #{record.perMonthDiamond,jdbcType=DOUBLE},
      </if>
      <if test="record.breakANum != null" >
        break_a_num = #{record.breakANum,jdbcType=BIGINT},
      </if>
      <if test="record.breakBNum != null" >
        break_b_num = #{record.breakBNum,jdbcType=BIGINT},
      </if>
      <if test="record.breakCNum != null" >
        break_c_num = #{record.breakCNum,jdbcType=BIGINT},
      </if>
      <if test="record.breakENum != null" >
        break_e_num = #{record.breakENum,jdbcType=BIGINT},
      </if>
      <if test="record.liveUv != null" >
        live_uv = #{record.liveUv,jdbcType=BIGINT},
      </if>
      <if test="record.breakRt != null" >
        break_rt = #{record.breakRt,jdbcType=DOUBLE},
      </if>
      <if test="record.breakRr != null" >
        break_rr = #{record.breakRr,jdbcType=DOUBLE},
      </if>
      <if test="record.perBreak != null" >
        per_break = #{record.perBreak,jdbcType=DOUBLE},
      </if>
      <if test="record.p1 != null" >
        p1 = #{record.p1,jdbcType=DOUBLE},
      </if>
      <if test="record.ps1 != null" >
        ps_1 = #{record.ps1,jdbcType=DOUBLE},
      </if>
      <if test="record.psS1 != null" >
        ps_s_1 = #{record.psS1,jdbcType=BIGINT},
      </if>
      <if test="record.psS1Sh != null" >
        ps_s_1_sh = #{record.psS1Sh,jdbcType=VARCHAR},
      </if>
      <if test="record.psS1Rn != null" >
        ps_s_1_rn = #{record.psS1Rn,jdbcType=BIGINT},
      </if>
      <if test="record.p2 != null" >
        p2 = #{record.p2,jdbcType=DOUBLE},
      </if>
      <if test="record.ps2 != null" >
        ps_2 = #{record.ps2,jdbcType=DOUBLE},
      </if>
      <if test="record.psS2 != null" >
        ps_s_2 = #{record.psS2,jdbcType=BIGINT},
      </if>
      <if test="record.psS2Sh != null" >
        ps_s_2_sh = #{record.psS2Sh,jdbcType=VARCHAR},
      </if>
      <if test="record.psS2Rn != null" >
        ps_s_2_rn = #{record.psS2Rn,jdbcType=BIGINT},
      </if>
      <if test="record.p3 != null" >
        p3 = #{record.p3,jdbcType=DOUBLE},
      </if>
      <if test="record.ps3 != null" >
        ps_3 = #{record.ps3,jdbcType=DOUBLE},
      </if>
      <if test="record.psS3 != null" >
        ps_s_3 = #{record.psS3,jdbcType=BIGINT},
      </if>
      <if test="record.psS3Sh != null" >
        ps_s_3_sh = #{record.psS3Sh,jdbcType=VARCHAR},
      </if>
      <if test="record.psS3Rn != null" >
        ps_s_3_rn = #{record.psS3Rn,jdbcType=BIGINT},
      </if>
      <if test="record.p4 != null" >
        p4 = #{record.p4,jdbcType=DOUBLE},
      </if>
      <if test="record.ps4 != null" >
        ps_4 = #{record.ps4,jdbcType=DOUBLE},
      </if>
      <if test="record.psS4 != null" >
        ps_s_4 = #{record.psS4,jdbcType=BIGINT},
      </if>
      <if test="record.psS4Sh != null" >
        ps_s_4_sh = #{record.psS4Sh,jdbcType=VARCHAR},
      </if>
      <if test="record.psS4Rn != null" >
        ps_s_4_rn = #{record.psS4Rn,jdbcType=BIGINT},
      </if>
      <if test="record.p5 != null" >
        p5 = #{record.p5,jdbcType=DOUBLE},
      </if>
      <if test="record.ps5 != null" >
        ps_5 = #{record.ps5,jdbcType=DOUBLE},
      </if>
      <if test="record.psS5 != null" >
        ps_s_5 = #{record.psS5,jdbcType=BIGINT},
      </if>
      <if test="record.psS5Sh != null" >
        ps_s_5_sh = #{record.psS5Sh,jdbcType=VARCHAR},
      </if>
      <if test="record.psS5Rn != null" >
        ps_s_5_rn = #{record.psS5Rn,jdbcType=BIGINT},
      </if>
      <if test="record.psAll != null" >
        ps_all = #{record.psAll,jdbcType=DOUBLE},
      </if>
      <if test="record.psSAllCor != null" >
        ps_s_all_cor = #{record.psSAllCor,jdbcType=BIGINT},
      </if>
      <if test="record.psSAllCorRr != null" >
        ps_s_all_cor_rr = #{record.psSAllCorRr,jdbcType=VARCHAR},
      </if>
      <if test="record.psSAllRnCor != null" >
        ps_s_all_rn_cor = #{record.psSAllRnCor,jdbcType=BIGINT},
      </if>
      <if test="record.psSAllRnCorRr != null" >
        ps_s_all_rn_cor_rr = #{record.psSAllRnCorRr,jdbcType=VARCHAR},
      </if>
      <if test="record.sprNewUv != null" >
        spr_new_uv = #{record.sprNewUv,jdbcType=DOUBLE},
      </if>
      <if test="record.sprValidLiveNewUv != null" >
        spr_valid_live_new_uv = #{record.sprValidLiveNewUv,jdbcType=DOUBLE},
      </if>
      <if test="record.sprAcu10Uv != null" >
        spr_acu10_uv = #{record.sprAcu10Uv,jdbcType=DOUBLE},
      </if>
      <if test="record.sprHActUv != null" >
        spr_h_act_uv = #{record.sprHActUv,jdbcType=DOUBLE},
      </if>
      <if test="record.sprMonthDiamond != null" >
        spr_month_diamond = #{record.sprMonthDiamond,jdbcType=DOUBLE},
      </if>
      <if test="record.sprP5 != null" >
        spr_p5 = #{record.sprP5,jdbcType=DOUBLE},
      </if>
      <if test="record.dt != null" >
        dt = #{record.dt,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update dm_yy_sid_segment_score_month
    set id = #{record.id,jdbcType=BIGINT},
      sid_ownerid = #{record.sidOwnerid,jdbcType=BIGINT},
      sid_owyyid = #{record.sidOwyyid,jdbcType=BIGINT},
      new_uv = #{record.newUv,jdbcType=BIGINT},
      new_uv_rr = #{record.newUvRr,jdbcType=BIGINT},
      valid_live_new_uv = #{record.validLiveNewUv,jdbcType=BIGINT},
      valid_live_new_uv_rr = #{record.validLiveNewUvRr,jdbcType=BIGINT},
      per_new_uv = #{record.perNewUv,jdbcType=DOUBLE},
      per_valid_live_new_uv = #{record.perValidLiveNewUv,jdbcType=DOUBLE},
      new_uv_rt = #{record.newUvRt,jdbcType=DOUBLE},
      acu10_uv = #{record.acu10Uv,jdbcType=BIGINT},
      acu10_uv_rr = #{record.acu10UvRr,jdbcType=BIGINT},
      per_acu10_uv = #{record.perAcu10Uv,jdbcType=DOUBLE},
      acu10_50_uv = #{record.acu1050Uv,jdbcType=BIGINT},
      acu50_300_uv = #{record.acu50300Uv,jdbcType=BIGINT},
      acu300_uv = #{record.acu300Uv,jdbcType=BIGINT},
      h_act_uv = #{record.hActUv,jdbcType=BIGINT},
      h_act_uv_rr = #{record.hActUvRr,jdbcType=BIGINT},
      per_h_act_uv = #{record.perHActUv,jdbcType=DOUBLE},
      month_diamond = #{record.monthDiamond,jdbcType=DOUBLE},
      month_diamond_rr = #{record.monthDiamondRr,jdbcType=DOUBLE},
      per_month_diamond = #{record.perMonthDiamond,jdbcType=DOUBLE},
      break_a_num = #{record.breakANum,jdbcType=BIGINT},
      break_b_num = #{record.breakBNum,jdbcType=BIGINT},
      break_c_num = #{record.breakCNum,jdbcType=BIGINT},
      break_e_num = #{record.breakENum,jdbcType=BIGINT},
      live_uv = #{record.liveUv,jdbcType=BIGINT},
      break_rt = #{record.breakRt,jdbcType=DOUBLE},
      break_rr = #{record.breakRr,jdbcType=DOUBLE},
      per_break = #{record.perBreak,jdbcType=DOUBLE},
      p1 = #{record.p1,jdbcType=DOUBLE},
      ps_1 = #{record.ps1,jdbcType=DOUBLE},
      ps_s_1 = #{record.psS1,jdbcType=BIGINT},
      ps_s_1_sh = #{record.psS1Sh,jdbcType=VARCHAR},
      ps_s_1_rn = #{record.psS1Rn,jdbcType=BIGINT},
      p2 = #{record.p2,jdbcType=DOUBLE},
      ps_2 = #{record.ps2,jdbcType=DOUBLE},
      ps_s_2 = #{record.psS2,jdbcType=BIGINT},
      ps_s_2_sh = #{record.psS2Sh,jdbcType=VARCHAR},
      ps_s_2_rn = #{record.psS2Rn,jdbcType=BIGINT},
      p3 = #{record.p3,jdbcType=DOUBLE},
      ps_3 = #{record.ps3,jdbcType=DOUBLE},
      ps_s_3 = #{record.psS3,jdbcType=BIGINT},
      ps_s_3_sh = #{record.psS3Sh,jdbcType=VARCHAR},
      ps_s_3_rn = #{record.psS3Rn,jdbcType=BIGINT},
      p4 = #{record.p4,jdbcType=DOUBLE},
      ps_4 = #{record.ps4,jdbcType=DOUBLE},
      ps_s_4 = #{record.psS4,jdbcType=BIGINT},
      ps_s_4_sh = #{record.psS4Sh,jdbcType=VARCHAR},
      ps_s_4_rn = #{record.psS4Rn,jdbcType=BIGINT},
      p5 = #{record.p5,jdbcType=DOUBLE},
      ps_5 = #{record.ps5,jdbcType=DOUBLE},
      ps_s_5 = #{record.psS5,jdbcType=BIGINT},
      ps_s_5_sh = #{record.psS5Sh,jdbcType=VARCHAR},
      ps_s_5_rn = #{record.psS5Rn,jdbcType=BIGINT},
      ps_all = #{record.psAll,jdbcType=DOUBLE},
      ps_s_all_cor = #{record.psSAllCor,jdbcType=BIGINT},
      ps_s_all_cor_rr = #{record.psSAllCorRr,jdbcType=VARCHAR},
      ps_s_all_rn_cor = #{record.psSAllRnCor,jdbcType=BIGINT},
      ps_s_all_rn_cor_rr = #{record.psSAllRnCorRr,jdbcType=VARCHAR},
      spr_new_uv = #{record.sprNewUv,jdbcType=DOUBLE},
      spr_valid_live_new_uv = #{record.sprValidLiveNewUv,jdbcType=DOUBLE},
      spr_acu10_uv = #{record.sprAcu10Uv,jdbcType=DOUBLE},
      spr_h_act_uv = #{record.sprHActUv,jdbcType=DOUBLE},
      spr_month_diamond = #{record.sprMonthDiamond,jdbcType=DOUBLE},
      spr_p5 = #{record.sprP5,jdbcType=DOUBLE},
      dt = #{record.dt,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreMonth" >
    update dm_yy_sid_segment_score_month
    <set >
      <if test="sidOwnerid != null" >
        sid_ownerid = #{sidOwnerid,jdbcType=BIGINT},
      </if>
      <if test="sidOwyyid != null" >
        sid_owyyid = #{sidOwyyid,jdbcType=BIGINT},
      </if>
      <if test="newUv != null" >
        new_uv = #{newUv,jdbcType=BIGINT},
      </if>
      <if test="newUvRr != null" >
        new_uv_rr = #{newUvRr,jdbcType=BIGINT},
      </if>
      <if test="validLiveNewUv != null" >
        valid_live_new_uv = #{validLiveNewUv,jdbcType=BIGINT},
      </if>
      <if test="validLiveNewUvRr != null" >
        valid_live_new_uv_rr = #{validLiveNewUvRr,jdbcType=BIGINT},
      </if>
      <if test="perNewUv != null" >
        per_new_uv = #{perNewUv,jdbcType=DOUBLE},
      </if>
      <if test="perValidLiveNewUv != null" >
        per_valid_live_new_uv = #{perValidLiveNewUv,jdbcType=DOUBLE},
      </if>
      <if test="newUvRt != null" >
        new_uv_rt = #{newUvRt,jdbcType=DOUBLE},
      </if>
      <if test="acu10Uv != null" >
        acu10_uv = #{acu10Uv,jdbcType=BIGINT},
      </if>
      <if test="acu10UvRr != null" >
        acu10_uv_rr = #{acu10UvRr,jdbcType=BIGINT},
      </if>
      <if test="perAcu10Uv != null" >
        per_acu10_uv = #{perAcu10Uv,jdbcType=DOUBLE},
      </if>
      <if test="acu1050Uv != null" >
        acu10_50_uv = #{acu1050Uv,jdbcType=BIGINT},
      </if>
      <if test="acu50300Uv != null" >
        acu50_300_uv = #{acu50300Uv,jdbcType=BIGINT},
      </if>
      <if test="acu300Uv != null" >
        acu300_uv = #{acu300Uv,jdbcType=BIGINT},
      </if>
      <if test="hActUv != null" >
        h_act_uv = #{hActUv,jdbcType=BIGINT},
      </if>
      <if test="hActUvRr != null" >
        h_act_uv_rr = #{hActUvRr,jdbcType=BIGINT},
      </if>
      <if test="perHActUv != null" >
        per_h_act_uv = #{perHActUv,jdbcType=DOUBLE},
      </if>
      <if test="monthDiamond != null" >
        month_diamond = #{monthDiamond,jdbcType=DOUBLE},
      </if>
      <if test="monthDiamondRr != null" >
        month_diamond_rr = #{monthDiamondRr,jdbcType=DOUBLE},
      </if>
      <if test="perMonthDiamond != null" >
        per_month_diamond = #{perMonthDiamond,jdbcType=DOUBLE},
      </if>
      <if test="breakANum != null" >
        break_a_num = #{breakANum,jdbcType=BIGINT},
      </if>
      <if test="breakBNum != null" >
        break_b_num = #{breakBNum,jdbcType=BIGINT},
      </if>
      <if test="breakCNum != null" >
        break_c_num = #{breakCNum,jdbcType=BIGINT},
      </if>
      <if test="breakENum != null" >
        break_e_num = #{breakENum,jdbcType=BIGINT},
      </if>
      <if test="liveUv != null" >
        live_uv = #{liveUv,jdbcType=BIGINT},
      </if>
      <if test="breakRt != null" >
        break_rt = #{breakRt,jdbcType=DOUBLE},
      </if>
      <if test="breakRr != null" >
        break_rr = #{breakRr,jdbcType=DOUBLE},
      </if>
      <if test="perBreak != null" >
        per_break = #{perBreak,jdbcType=DOUBLE},
      </if>
      <if test="p1 != null" >
        p1 = #{p1,jdbcType=DOUBLE},
      </if>
      <if test="ps1 != null" >
        ps_1 = #{ps1,jdbcType=DOUBLE},
      </if>
      <if test="psS1 != null" >
        ps_s_1 = #{psS1,jdbcType=BIGINT},
      </if>
      <if test="psS1Sh != null" >
        ps_s_1_sh = #{psS1Sh,jdbcType=VARCHAR},
      </if>
      <if test="psS1Rn != null" >
        ps_s_1_rn = #{psS1Rn,jdbcType=BIGINT},
      </if>
      <if test="p2 != null" >
        p2 = #{p2,jdbcType=DOUBLE},
      </if>
      <if test="ps2 != null" >
        ps_2 = #{ps2,jdbcType=DOUBLE},
      </if>
      <if test="psS2 != null" >
        ps_s_2 = #{psS2,jdbcType=BIGINT},
      </if>
      <if test="psS2Sh != null" >
        ps_s_2_sh = #{psS2Sh,jdbcType=VARCHAR},
      </if>
      <if test="psS2Rn != null" >
        ps_s_2_rn = #{psS2Rn,jdbcType=BIGINT},
      </if>
      <if test="p3 != null" >
        p3 = #{p3,jdbcType=DOUBLE},
      </if>
      <if test="ps3 != null" >
        ps_3 = #{ps3,jdbcType=DOUBLE},
      </if>
      <if test="psS3 != null" >
        ps_s_3 = #{psS3,jdbcType=BIGINT},
      </if>
      <if test="psS3Sh != null" >
        ps_s_3_sh = #{psS3Sh,jdbcType=VARCHAR},
      </if>
      <if test="psS3Rn != null" >
        ps_s_3_rn = #{psS3Rn,jdbcType=BIGINT},
      </if>
      <if test="p4 != null" >
        p4 = #{p4,jdbcType=DOUBLE},
      </if>
      <if test="ps4 != null" >
        ps_4 = #{ps4,jdbcType=DOUBLE},
      </if>
      <if test="psS4 != null" >
        ps_s_4 = #{psS4,jdbcType=BIGINT},
      </if>
      <if test="psS4Sh != null" >
        ps_s_4_sh = #{psS4Sh,jdbcType=VARCHAR},
      </if>
      <if test="psS4Rn != null" >
        ps_s_4_rn = #{psS4Rn,jdbcType=BIGINT},
      </if>
      <if test="p5 != null" >
        p5 = #{p5,jdbcType=DOUBLE},
      </if>
      <if test="ps5 != null" >
        ps_5 = #{ps5,jdbcType=DOUBLE},
      </if>
      <if test="psS5 != null" >
        ps_s_5 = #{psS5,jdbcType=BIGINT},
      </if>
      <if test="psS5Sh != null" >
        ps_s_5_sh = #{psS5Sh,jdbcType=VARCHAR},
      </if>
      <if test="psS5Rn != null" >
        ps_s_5_rn = #{psS5Rn,jdbcType=BIGINT},
      </if>
      <if test="psAll != null" >
        ps_all = #{psAll,jdbcType=DOUBLE},
      </if>
      <if test="psSAllCor != null" >
        ps_s_all_cor = #{psSAllCor,jdbcType=BIGINT},
      </if>
      <if test="psSAllCorRr != null" >
        ps_s_all_cor_rr = #{psSAllCorRr,jdbcType=VARCHAR},
      </if>
      <if test="psSAllRnCor != null" >
        ps_s_all_rn_cor = #{psSAllRnCor,jdbcType=BIGINT},
      </if>
      <if test="psSAllRnCorRr != null" >
        ps_s_all_rn_cor_rr = #{psSAllRnCorRr,jdbcType=VARCHAR},
      </if>
      <if test="sprNewUv != null" >
        spr_new_uv = #{sprNewUv,jdbcType=DOUBLE},
      </if>
      <if test="sprValidLiveNewUv != null" >
        spr_valid_live_new_uv = #{sprValidLiveNewUv,jdbcType=DOUBLE},
      </if>
      <if test="sprAcu10Uv != null" >
        spr_acu10_uv = #{sprAcu10Uv,jdbcType=DOUBLE},
      </if>
      <if test="sprHActUv != null" >
        spr_h_act_uv = #{sprHActUv,jdbcType=DOUBLE},
      </if>
      <if test="sprMonthDiamond != null" >
        spr_month_diamond = #{sprMonthDiamond,jdbcType=DOUBLE},
      </if>
      <if test="sprP5 != null" >
        spr_p5 = #{sprP5,jdbcType=DOUBLE},
      </if>
      <if test="dt != null" >
        dt = #{dt,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreMonth" >
    update dm_yy_sid_segment_score_month
    set sid_ownerid = #{sidOwnerid,jdbcType=BIGINT},
      sid_owyyid = #{sidOwyyid,jdbcType=BIGINT},
      new_uv = #{newUv,jdbcType=BIGINT},
      new_uv_rr = #{newUvRr,jdbcType=BIGINT},
      valid_live_new_uv = #{validLiveNewUv,jdbcType=BIGINT},
      valid_live_new_uv_rr = #{validLiveNewUvRr,jdbcType=BIGINT},
      per_new_uv = #{perNewUv,jdbcType=DOUBLE},
      per_valid_live_new_uv = #{perValidLiveNewUv,jdbcType=DOUBLE},
      new_uv_rt = #{newUvRt,jdbcType=DOUBLE},
      acu10_uv = #{acu10Uv,jdbcType=BIGINT},
      acu10_uv_rr = #{acu10UvRr,jdbcType=BIGINT},
      per_acu10_uv = #{perAcu10Uv,jdbcType=DOUBLE},
      acu10_50_uv = #{acu1050Uv,jdbcType=BIGINT},
      acu50_300_uv = #{acu50300Uv,jdbcType=BIGINT},
      acu300_uv = #{acu300Uv,jdbcType=BIGINT},
      h_act_uv = #{hActUv,jdbcType=BIGINT},
      h_act_uv_rr = #{hActUvRr,jdbcType=BIGINT},
      per_h_act_uv = #{perHActUv,jdbcType=DOUBLE},
      month_diamond = #{monthDiamond,jdbcType=DOUBLE},
      month_diamond_rr = #{monthDiamondRr,jdbcType=DOUBLE},
      per_month_diamond = #{perMonthDiamond,jdbcType=DOUBLE},
      break_a_num = #{breakANum,jdbcType=BIGINT},
      break_b_num = #{breakBNum,jdbcType=BIGINT},
      break_c_num = #{breakCNum,jdbcType=BIGINT},
      break_e_num = #{breakENum,jdbcType=BIGINT},
      live_uv = #{liveUv,jdbcType=BIGINT},
      break_rt = #{breakRt,jdbcType=DOUBLE},
      break_rr = #{breakRr,jdbcType=DOUBLE},
      per_break = #{perBreak,jdbcType=DOUBLE},
      p1 = #{p1,jdbcType=DOUBLE},
      ps_1 = #{ps1,jdbcType=DOUBLE},
      ps_s_1 = #{psS1,jdbcType=BIGINT},
      ps_s_1_sh = #{psS1Sh,jdbcType=VARCHAR},
      ps_s_1_rn = #{psS1Rn,jdbcType=BIGINT},
      p2 = #{p2,jdbcType=DOUBLE},
      ps_2 = #{ps2,jdbcType=DOUBLE},
      ps_s_2 = #{psS2,jdbcType=BIGINT},
      ps_s_2_sh = #{psS2Sh,jdbcType=VARCHAR},
      ps_s_2_rn = #{psS2Rn,jdbcType=BIGINT},
      p3 = #{p3,jdbcType=DOUBLE},
      ps_3 = #{ps3,jdbcType=DOUBLE},
      ps_s_3 = #{psS3,jdbcType=BIGINT},
      ps_s_3_sh = #{psS3Sh,jdbcType=VARCHAR},
      ps_s_3_rn = #{psS3Rn,jdbcType=BIGINT},
      p4 = #{p4,jdbcType=DOUBLE},
      ps_4 = #{ps4,jdbcType=DOUBLE},
      ps_s_4 = #{psS4,jdbcType=BIGINT},
      ps_s_4_sh = #{psS4Sh,jdbcType=VARCHAR},
      ps_s_4_rn = #{psS4Rn,jdbcType=BIGINT},
      p5 = #{p5,jdbcType=DOUBLE},
      ps_5 = #{ps5,jdbcType=DOUBLE},
      ps_s_5 = #{psS5,jdbcType=BIGINT},
      ps_s_5_sh = #{psS5Sh,jdbcType=VARCHAR},
      ps_s_5_rn = #{psS5Rn,jdbcType=BIGINT},
      ps_all = #{psAll,jdbcType=DOUBLE},
      ps_s_all_cor = #{psSAllCor,jdbcType=BIGINT},
      ps_s_all_cor_rr = #{psSAllCorRr,jdbcType=VARCHAR},
      ps_s_all_rn_cor = #{psSAllRnCor,jdbcType=BIGINT},
      ps_s_all_rn_cor_rr = #{psSAllRnCorRr,jdbcType=VARCHAR},
      spr_new_uv = #{sprNewUv,jdbcType=DOUBLE},
      spr_valid_live_new_uv = #{sprValidLiveNewUv,jdbcType=DOUBLE},
      spr_acu10_uv = #{sprAcu10Uv,jdbcType=DOUBLE},
      spr_h_act_uv = #{sprHActUv,jdbcType=DOUBLE},
      spr_month_diamond = #{sprMonthDiamond,jdbcType=DOUBLE},
      spr_p5 = #{sprP5,jdbcType=DOUBLE},
      dt = #{dt,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>