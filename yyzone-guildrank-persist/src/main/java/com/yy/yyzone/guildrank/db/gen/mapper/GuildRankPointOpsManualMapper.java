package com.yy.yyzone.guildrank.db.gen.mapper;

import com.yy.yyzone.guildrank.db.gen.model.GuildRankPointOpsManual;
import com.yy.yyzone.guildrank.db.gen.model.GuildRankPointOpsManualExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface GuildRankPointOpsManualMapper {
    int countByExample(GuildRankPointOpsManualExample example);

    int deleteByExample(GuildRankPointOpsManualExample example);

    int deleteByPrimaryKey(Long id);

    int insert(GuildRankPointOpsManual record);

    int insertSelective(GuildRankPointOpsManual record);

    List<GuildRankPointOpsManual> selectByExample(GuildRankPointOpsManualExample example);

    GuildRankPointOpsManual selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") GuildRankPointOpsManual record, @Param("example") GuildRankPointOpsManualExample example);

    int updateByExample(@Param("record") GuildRankPointOpsManual record, @Param("example") GuildRankPointOpsManualExample example);

    int updateByPrimaryKeySelective(GuildRankPointOpsManual record);

    int updateByPrimaryKey(GuildRankPointOpsManual record);
}