package com.yy.yyzone.guildrank.db.gen.model;

import java.util.Date;

public class GuildRankTaskDistribution {
    private Long id;
    private Long uid;
    private Integer dt; // yyyyMM
    private String taskType; // 任务类型
    private Long targetValue; // 目标值
    private String operator; // 操作人
    private Date createTime;
    private Date updateTime;
    private Boolean history; // 是否历史记录
    private String opType; // 操作类型: add, update, delete
    private String reason;
    /**
     * 修改或者删除时候，前一条记录的id
     */
    private Long previousId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Integer getDt() {
        return dt;
    }

    public void setDt(Integer dt) {
        this.dt = dt;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public Long getTargetValue() {
        return targetValue;
    }

    public void setTargetValue(Long targetValue) {
        this.targetValue = targetValue;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getHistory() {
        return history;
    }

    public void setHistory(Boolean history) {
        this.history = history;
    }

    public String getOpType() {
        return opType;
    }

    public void setOpType(String opType) {
        this.opType = opType;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Long getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Long previousId) {
        this.previousId = previousId;
    }

    @Override
    public String toString() {
        return "GuildRankTaskDistribution{" +
                "id=" + id +
                ", uid=" + uid +
                ", dt=" + dt +
                ", taskType='" + taskType + '\'' +
                ", targetValue=" + targetValue +
                ", operator='" + operator + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", history=" + history +
                ", opType='" + opType + '\'' +
                ", reason='" + reason + '\'' +
                ", previousId=" + previousId +
                '}';
    }
}