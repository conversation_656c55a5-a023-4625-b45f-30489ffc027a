package com.yy.yyzone.guildrank.db.gen.model;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated("dm_yy_sid_segment_score_accum_cm_2_day")
public class DmYySidSegmentScoreAccumCm2DayExample {
    /**
     * dm_yy_sid_segment_score_accum_cm_2_day
     */
    protected String orderByClause;

    /**
     * dm_yy_sid_segment_score_accum_cm_2_day
     */
    protected boolean distinct;

    /**
     * dm_yy_sid_segment_score_accum_cm_2_day
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public DmYySidSegmentScoreAccumCm2DayExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    /**
     * dm_yy_sid_segment_score_accum_cm_2_day null
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSidOwneridIsNull() {
            addCriterion("sid_ownerid is null");
            return (Criteria) this;
        }

        public Criteria andSidOwneridIsNotNull() {
            addCriterion("sid_ownerid is not null");
            return (Criteria) this;
        }

        public Criteria andSidOwneridEqualTo(Long value) {
            addCriterion("sid_ownerid =", value, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridNotEqualTo(Long value) {
            addCriterion("sid_ownerid <>", value, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridGreaterThan(Long value) {
            addCriterion("sid_ownerid >", value, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridGreaterThanOrEqualTo(Long value) {
            addCriterion("sid_ownerid >=", value, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridLessThan(Long value) {
            addCriterion("sid_ownerid <", value, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridLessThanOrEqualTo(Long value) {
            addCriterion("sid_ownerid <=", value, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridIn(List<Long> values) {
            addCriterion("sid_ownerid in", values, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridNotIn(List<Long> values) {
            addCriterion("sid_ownerid not in", values, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridBetween(Long value1, Long value2) {
            addCriterion("sid_ownerid between", value1, value2, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridNotBetween(Long value1, Long value2) {
            addCriterion("sid_ownerid not between", value1, value2, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andPsNIsNull() {
            addCriterion("ps_n is null");
            return (Criteria) this;
        }

        public Criteria andPsNIsNotNull() {
            addCriterion("ps_n is not null");
            return (Criteria) this;
        }

        public Criteria andPsNEqualTo(Double value) {
            addCriterion("ps_n =", value, "psN");
            return (Criteria) this;
        }

        public Criteria andPsNNotEqualTo(Double value) {
            addCriterion("ps_n <>", value, "psN");
            return (Criteria) this;
        }

        public Criteria andPsNGreaterThan(Double value) {
            addCriterion("ps_n >", value, "psN");
            return (Criteria) this;
        }

        public Criteria andPsNGreaterThanOrEqualTo(Double value) {
            addCriterion("ps_n >=", value, "psN");
            return (Criteria) this;
        }

        public Criteria andPsNLessThan(Double value) {
            addCriterion("ps_n <", value, "psN");
            return (Criteria) this;
        }

        public Criteria andPsNLessThanOrEqualTo(Double value) {
            addCriterion("ps_n <=", value, "psN");
            return (Criteria) this;
        }

        public Criteria andPsNIn(List<Double> values) {
            addCriterion("ps_n in", values, "psN");
            return (Criteria) this;
        }

        public Criteria andPsNNotIn(List<Double> values) {
            addCriterion("ps_n not in", values, "psN");
            return (Criteria) this;
        }

        public Criteria andPsNBetween(Double value1, Double value2) {
            addCriterion("ps_n between", value1, value2, "psN");
            return (Criteria) this;
        }

        public Criteria andPsNNotBetween(Double value1, Double value2) {
            addCriterion("ps_n not between", value1, value2, "psN");
            return (Criteria) this;
        }

        public Criteria andPsNAllRnIsNull() {
            addCriterion("ps_n_all_rn is null");
            return (Criteria) this;
        }

        public Criteria andPsNAllRnIsNotNull() {
            addCriterion("ps_n_all_rn is not null");
            return (Criteria) this;
        }

        public Criteria andPsNAllRnEqualTo(Integer value) {
            addCriterion("ps_n_all_rn =", value, "psNAllRn");
            return (Criteria) this;
        }

        public Criteria andPsNAllRnNotEqualTo(Integer value) {
            addCriterion("ps_n_all_rn <>", value, "psNAllRn");
            return (Criteria) this;
        }

        public Criteria andPsNAllRnGreaterThan(Integer value) {
            addCriterion("ps_n_all_rn >", value, "psNAllRn");
            return (Criteria) this;
        }

        public Criteria andPsNAllRnGreaterThanOrEqualTo(Integer value) {
            addCriterion("ps_n_all_rn >=", value, "psNAllRn");
            return (Criteria) this;
        }

        public Criteria andPsNAllRnLessThan(Integer value) {
            addCriterion("ps_n_all_rn <", value, "psNAllRn");
            return (Criteria) this;
        }

        public Criteria andPsNAllRnLessThanOrEqualTo(Integer value) {
            addCriterion("ps_n_all_rn <=", value, "psNAllRn");
            return (Criteria) this;
        }

        public Criteria andPsNAllRnIn(List<Integer> values) {
            addCriterion("ps_n_all_rn in", values, "psNAllRn");
            return (Criteria) this;
        }

        public Criteria andPsNAllRnNotIn(List<Integer> values) {
            addCriterion("ps_n_all_rn not in", values, "psNAllRn");
            return (Criteria) this;
        }

        public Criteria andPsNAllRnBetween(Integer value1, Integer value2) {
            addCriterion("ps_n_all_rn between", value1, value2, "psNAllRn");
            return (Criteria) this;
        }

        public Criteria andPsNAllRnNotBetween(Integer value1, Integer value2) {
            addCriterion("ps_n_all_rn not between", value1, value2, "psNAllRn");
            return (Criteria) this;
        }

        public Criteria andKpiNameIsNull() {
            addCriterion("kpi_name is null");
            return (Criteria) this;
        }

        public Criteria andKpiNameIsNotNull() {
            addCriterion("kpi_name is not null");
            return (Criteria) this;
        }

        public Criteria andKpiNameEqualTo(String value) {
            addCriterion("kpi_name =", value, "kpiName");
            return (Criteria) this;
        }

        public Criteria andKpiNameNotEqualTo(String value) {
            addCriterion("kpi_name <>", value, "kpiName");
            return (Criteria) this;
        }

        public Criteria andKpiNameGreaterThan(String value) {
            addCriterion("kpi_name >", value, "kpiName");
            return (Criteria) this;
        }

        public Criteria andKpiNameGreaterThanOrEqualTo(String value) {
            addCriterion("kpi_name >=", value, "kpiName");
            return (Criteria) this;
        }

        public Criteria andKpiNameLessThan(String value) {
            addCriterion("kpi_name <", value, "kpiName");
            return (Criteria) this;
        }

        public Criteria andKpiNameLessThanOrEqualTo(String value) {
            addCriterion("kpi_name <=", value, "kpiName");
            return (Criteria) this;
        }

        public Criteria andKpiNameLike(String value) {
            addCriterion("kpi_name like", value, "kpiName");
            return (Criteria) this;
        }

        public Criteria andKpiNameNotLike(String value) {
            addCriterion("kpi_name not like", value, "kpiName");
            return (Criteria) this;
        }

        public Criteria andKpiNameIn(List<String> values) {
            addCriterion("kpi_name in", values, "kpiName");
            return (Criteria) this;
        }

        public Criteria andKpiNameNotIn(List<String> values) {
            addCriterion("kpi_name not in", values, "kpiName");
            return (Criteria) this;
        }

        public Criteria andKpiNameBetween(String value1, String value2) {
            addCriterion("kpi_name between", value1, value2, "kpiName");
            return (Criteria) this;
        }

        public Criteria andKpiNameNotBetween(String value1, String value2) {
            addCriterion("kpi_name not between", value1, value2, "kpiName");
            return (Criteria) this;
        }

        public Criteria andDtIsNull() {
            addCriterion("dt is null");
            return (Criteria) this;
        }

        public Criteria andDtIsNotNull() {
            addCriterion("dt is not null");
            return (Criteria) this;
        }

        public Criteria andDtEqualTo(String value) {
            addCriterion("dt =", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotEqualTo(String value) {
            addCriterion("dt <>", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtGreaterThan(String value) {
            addCriterion("dt >", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtGreaterThanOrEqualTo(String value) {
            addCriterion("dt >=", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtLessThan(String value) {
            addCriterion("dt <", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtLessThanOrEqualTo(String value) {
            addCriterion("dt <=", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtLike(String value) {
            addCriterion("dt like", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotLike(String value) {
            addCriterion("dt not like", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtIn(List<String> values) {
            addCriterion("dt in", values, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotIn(List<String> values) {
            addCriterion("dt not in", values, "dt");
            return (Criteria) this;
        }

        public Criteria andDtBetween(String value1, String value2) {
            addCriterion("dt between", value1, value2, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotBetween(String value1, String value2) {
            addCriterion("dt not between", value1, value2, "dt");
            return (Criteria) this;
        }

        public Criteria andGradeIsNull() {
            addCriterion("grade is null");
            return (Criteria) this;
        }

        public Criteria andGradeIsNotNull() {
            addCriterion("grade is not null");
            return (Criteria) this;
        }

        public Criteria andGradeEqualTo(String value) {
            addCriterion("grade =", value, "grade");
            return (Criteria) this;
        }

        public Criteria andGradeNotEqualTo(String value) {
            addCriterion("grade <>", value, "grade");
            return (Criteria) this;
        }

        public Criteria andGradeGreaterThan(String value) {
            addCriterion("grade >", value, "grade");
            return (Criteria) this;
        }

        public Criteria andGradeGreaterThanOrEqualTo(String value) {
            addCriterion("grade >=", value, "grade");
            return (Criteria) this;
        }

        public Criteria andGradeLessThan(String value) {
            addCriterion("grade <", value, "grade");
            return (Criteria) this;
        }

        public Criteria andGradeLessThanOrEqualTo(String value) {
            addCriterion("grade <=", value, "grade");
            return (Criteria) this;
        }

        public Criteria andGradeLike(String value) {
            addCriterion("grade like", value, "grade");
            return (Criteria) this;
        }

        public Criteria andGradeNotLike(String value) {
            addCriterion("grade not like", value, "grade");
            return (Criteria) this;
        }

        public Criteria andGradeIn(List<String> values) {
            addCriterion("grade in", values, "grade");
            return (Criteria) this;
        }

        public Criteria andGradeNotIn(List<String> values) {
            addCriterion("grade not in", values, "grade");
            return (Criteria) this;
        }

        public Criteria andGradeBetween(String value1, String value2) {
            addCriterion("grade between", value1, value2, "grade");
            return (Criteria) this;
        }

        public Criteria andGradeNotBetween(String value1, String value2) {
            addCriterion("grade not between", value1, value2, "grade");
            return (Criteria) this;
        }
    }

    /**
     * dm_yy_sid_segment_score_accum_cm_2_day
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * dm_yy_sid_segment_score_accum_cm_2_day null
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}