package com.yy.yyzone.guildrank.db.gen.mapper;

import com.yy.yyzone.guildrank.db.gen.model.YyDmEntityGuildCmpHealthAnalysisDiTmp;
import com.yy.yyzone.guildrank.db.gen.model.YyDmEntityGuildCmpHealthAnalysisDiTmpExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface YyDmEntityGuildCmpHealthAnalysisDiTmpMapper {
    int countByExample(YyDmEntityGuildCmpHealthAnalysisDiTmpExample example);

    int deleteByExample(YyDmEntityGuildCmpHealthAnalysisDiTmpExample example);

    int deleteByPrimaryKey(Long id);

    int insert(YyDmEntityGuildCmpHealthAnalysisDiTmp record);

    int insertSelective(YyDmEntityGuildCmpHealthAnalysisDiTmp record);

    List<YyDmEntityGuildCmpHealthAnalysisDiTmp> selectByExample(YyDmEntityGuildCmpHealthAnalysisDiTmpExample example);

    YyDmEntityGuildCmpHealthAnalysisDiTmp selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") YyDmEntityGuildCmpHealthAnalysisDiTmp record, @Param("example") YyDmEntityGuildCmpHealthAnalysisDiTmpExample example);

    int updateByExample(@Param("record") YyDmEntityGuildCmpHealthAnalysisDiTmp record, @Param("example") YyDmEntityGuildCmpHealthAnalysisDiTmpExample example);

    int updateByPrimaryKeySelective(YyDmEntityGuildCmpHealthAnalysisDiTmp record);

    int updateByPrimaryKey(YyDmEntityGuildCmpHealthAnalysisDiTmp record);
}