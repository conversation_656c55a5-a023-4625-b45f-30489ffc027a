package com.yy.yyzone.guildrank.db.gen.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import javax.annotation.Generated;

@Generated("guildrank_renew_artist_count")
public class GuildrankRenewArtistCountExample {
    /**
     * guildrank_renew_artist_count
     */
    protected String orderByClause;

    /**
     * guildrank_renew_artist_count
     */
    protected boolean distinct;

    /**
     * guildrank_renew_artist_count
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public GuildrankRenewArtistCountExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    /**
     * guildrank_renew_artist_count null
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andDataMonthIsNull() {
            addCriterion("data_month is null");
            return (Criteria) this;
        }

        public Criteria andDataMonthIsNotNull() {
            addCriterion("data_month is not null");
            return (Criteria) this;
        }

        public Criteria andDataMonthEqualTo(Date value) {
            addCriterionForJDBCDate("data_month =", value, "dataMonth");
            return (Criteria) this;
        }

        public Criteria andDataMonthNotEqualTo(Date value) {
            addCriterionForJDBCDate("data_month <>", value, "dataMonth");
            return (Criteria) this;
        }

        public Criteria andDataMonthGreaterThan(Date value) {
            addCriterionForJDBCDate("data_month >", value, "dataMonth");
            return (Criteria) this;
        }

        public Criteria andDataMonthGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("data_month >=", value, "dataMonth");
            return (Criteria) this;
        }

        public Criteria andDataMonthLessThan(Date value) {
            addCriterionForJDBCDate("data_month <", value, "dataMonth");
            return (Criteria) this;
        }

        public Criteria andDataMonthLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("data_month <=", value, "dataMonth");
            return (Criteria) this;
        }

        public Criteria andDataMonthIn(List<Date> values) {
            addCriterionForJDBCDate("data_month in", values, "dataMonth");
            return (Criteria) this;
        }

        public Criteria andDataMonthNotIn(List<Date> values) {
            addCriterionForJDBCDate("data_month not in", values, "dataMonth");
            return (Criteria) this;
        }

        public Criteria andDataMonthBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("data_month between", value1, value2, "dataMonth");
            return (Criteria) this;
        }

        public Criteria andDataMonthNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("data_month not between", value1, value2, "dataMonth");
            return (Criteria) this;
        }

        public Criteria andMainGuildUidIsNull() {
            addCriterion("main_guild_uid is null");
            return (Criteria) this;
        }

        public Criteria andMainGuildUidIsNotNull() {
            addCriterion("main_guild_uid is not null");
            return (Criteria) this;
        }

        public Criteria andMainGuildUidEqualTo(Long value) {
            addCriterion("main_guild_uid =", value, "mainGuildUid");
            return (Criteria) this;
        }

        public Criteria andMainGuildUidNotEqualTo(Long value) {
            addCriterion("main_guild_uid <>", value, "mainGuildUid");
            return (Criteria) this;
        }

        public Criteria andMainGuildUidGreaterThan(Long value) {
            addCriterion("main_guild_uid >", value, "mainGuildUid");
            return (Criteria) this;
        }

        public Criteria andMainGuildUidGreaterThanOrEqualTo(Long value) {
            addCriterion("main_guild_uid >=", value, "mainGuildUid");
            return (Criteria) this;
        }

        public Criteria andMainGuildUidLessThan(Long value) {
            addCriterion("main_guild_uid <", value, "mainGuildUid");
            return (Criteria) this;
        }

        public Criteria andMainGuildUidLessThanOrEqualTo(Long value) {
            addCriterion("main_guild_uid <=", value, "mainGuildUid");
            return (Criteria) this;
        }

        public Criteria andMainGuildUidIn(List<Long> values) {
            addCriterion("main_guild_uid in", values, "mainGuildUid");
            return (Criteria) this;
        }

        public Criteria andMainGuildUidNotIn(List<Long> values) {
            addCriterion("main_guild_uid not in", values, "mainGuildUid");
            return (Criteria) this;
        }

        public Criteria andMainGuildUidBetween(Long value1, Long value2) {
            addCriterion("main_guild_uid between", value1, value2, "mainGuildUid");
            return (Criteria) this;
        }

        public Criteria andMainGuildUidNotBetween(Long value1, Long value2) {
            addCriterion("main_guild_uid not between", value1, value2, "mainGuildUid");
            return (Criteria) this;
        }

        public Criteria andRenewCountIsNull() {
            addCriterion("renew_count is null");
            return (Criteria) this;
        }

        public Criteria andRenewCountIsNotNull() {
            addCriterion("renew_count is not null");
            return (Criteria) this;
        }

        public Criteria andRenewCountEqualTo(Integer value) {
            addCriterion("renew_count =", value, "renewCount");
            return (Criteria) this;
        }

        public Criteria andRenewCountNotEqualTo(Integer value) {
            addCriterion("renew_count <>", value, "renewCount");
            return (Criteria) this;
        }

        public Criteria andRenewCountGreaterThan(Integer value) {
            addCriterion("renew_count >", value, "renewCount");
            return (Criteria) this;
        }

        public Criteria andRenewCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("renew_count >=", value, "renewCount");
            return (Criteria) this;
        }

        public Criteria andRenewCountLessThan(Integer value) {
            addCriterion("renew_count <", value, "renewCount");
            return (Criteria) this;
        }

        public Criteria andRenewCountLessThanOrEqualTo(Integer value) {
            addCriterion("renew_count <=", value, "renewCount");
            return (Criteria) this;
        }

        public Criteria andRenewCountIn(List<Integer> values) {
            addCriterion("renew_count in", values, "renewCount");
            return (Criteria) this;
        }

        public Criteria andRenewCountNotIn(List<Integer> values) {
            addCriterion("renew_count not in", values, "renewCount");
            return (Criteria) this;
        }

        public Criteria andRenewCountBetween(Integer value1, Integer value2) {
            addCriterion("renew_count between", value1, value2, "renewCount");
            return (Criteria) this;
        }

        public Criteria andRenewCountNotBetween(Integer value1, Integer value2) {
            addCriterion("renew_count not between", value1, value2, "renewCount");
            return (Criteria) this;
        }
    }

    /**
     * guildrank_renew_artist_count
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * guildrank_renew_artist_count null
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}