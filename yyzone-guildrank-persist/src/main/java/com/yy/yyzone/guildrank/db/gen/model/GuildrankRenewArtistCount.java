package com.yy.yyzone.guildrank.db.gen.model;

import java.util.Date;

public class GuildrankRenewArtistCount {
    /**
     * id
     */
    private Long id;

    /**
     * 数据月1号日期
     */
    private Date dataMonth;

    /**
     * 主体uid
     */
    private Long mainGuildUid;

    /**
     * 续约金牌艺人数
     */
    private Integer renewCount;

    /**
     * 获取id
     * @return id id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置id
     * @param id id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取数据月1号日期
     * @return data_month 数据月1号日期
     */
    public Date getDataMonth() {
        return dataMonth;
    }

    /**
     * 设置数据月1号日期
     * @param dataMonth 数据月1号日期
     */
    public void setDataMonth(Date dataMonth) {
        this.dataMonth = dataMonth;
    }

    /**
     * 获取主体uid
     * @return main_guild_uid 主体uid
     */
    public Long getMainGuildUid() {
        return mainGuildUid;
    }

    /**
     * 设置主体uid
     * @param mainGuildUid 主体uid
     */
    public void setMainGuildUid(Long mainGuildUid) {
        this.mainGuildUid = mainGuildUid;
    }

    /**
     * 获取续约金牌艺人数
     * @return renew_count 续约金牌艺人数
     */
    public Integer getRenewCount() {
        return renewCount;
    }

    /**
     * 设置续约金牌艺人数
     * @param renewCount 续约金牌艺人数
     */
    public void setRenewCount(Integer renewCount) {
        this.renewCount = renewCount;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", dataMonth=").append(dataMonth);
        sb.append(", mainGuildUid=").append(mainGuildUid);
        sb.append(", renewCount=").append(renewCount);
        sb.append("]");
        return sb.toString();
    }
}