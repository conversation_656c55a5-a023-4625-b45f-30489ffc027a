package com.yy.yyzone.guildrank.db.gen.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.annotation.Generated;

@Generated("guildrank_point_grant_log")
public class GuildrankPointGrantLogExample {
    /**
     * guildrank_point_grant_log
     */
    protected String orderByClause;

    /**
     * guildrank_point_grant_log
     */
    protected boolean distinct;

    /**
     * guildrank_point_grant_log
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public GuildrankPointGrantLogExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    /**
     * guildrank_point_grant_log null
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andDetailIdIsNull() {
            addCriterion("detail_id is null");
            return (Criteria) this;
        }

        public Criteria andDetailIdIsNotNull() {
            addCriterion("detail_id is not null");
            return (Criteria) this;
        }

        public Criteria andDetailIdEqualTo(Long value) {
            addCriterion("detail_id =", value, "detailId");
            return (Criteria) this;
        }

        public Criteria andDetailIdNotEqualTo(Long value) {
            addCriterion("detail_id <>", value, "detailId");
            return (Criteria) this;
        }

        public Criteria andDetailIdGreaterThan(Long value) {
            addCriterion("detail_id >", value, "detailId");
            return (Criteria) this;
        }

        public Criteria andDetailIdGreaterThanOrEqualTo(Long value) {
            addCriterion("detail_id >=", value, "detailId");
            return (Criteria) this;
        }

        public Criteria andDetailIdLessThan(Long value) {
            addCriterion("detail_id <", value, "detailId");
            return (Criteria) this;
        }

        public Criteria andDetailIdLessThanOrEqualTo(Long value) {
            addCriterion("detail_id <=", value, "detailId");
            return (Criteria) this;
        }

        public Criteria andDetailIdIn(List<Long> values) {
            addCriterion("detail_id in", values, "detailId");
            return (Criteria) this;
        }

        public Criteria andDetailIdNotIn(List<Long> values) {
            addCriterion("detail_id not in", values, "detailId");
            return (Criteria) this;
        }

        public Criteria andDetailIdBetween(Long value1, Long value2) {
            addCriterion("detail_id between", value1, value2, "detailId");
            return (Criteria) this;
        }

        public Criteria andDetailIdNotBetween(Long value1, Long value2) {
            addCriterion("detail_id not between", value1, value2, "detailId");
            return (Criteria) this;
        }

        public Criteria andUidIsNull() {
            addCriterion("uid is null");
            return (Criteria) this;
        }

        public Criteria andUidIsNotNull() {
            addCriterion("uid is not null");
            return (Criteria) this;
        }

        public Criteria andUidEqualTo(Long value) {
            addCriterion("uid =", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidNotEqualTo(Long value) {
            addCriterion("uid <>", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidGreaterThan(Long value) {
            addCriterion("uid >", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidGreaterThanOrEqualTo(Long value) {
            addCriterion("uid >=", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidLessThan(Long value) {
            addCriterion("uid <", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidLessThanOrEqualTo(Long value) {
            addCriterion("uid <=", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidIn(List<Long> values) {
            addCriterion("uid in", values, "uid");
            return (Criteria) this;
        }

        public Criteria andUidNotIn(List<Long> values) {
            addCriterion("uid not in", values, "uid");
            return (Criteria) this;
        }

        public Criteria andUidBetween(Long value1, Long value2) {
            addCriterion("uid between", value1, value2, "uid");
            return (Criteria) this;
        }

        public Criteria andUidNotBetween(Long value1, Long value2) {
            addCriterion("uid not between", value1, value2, "uid");
            return (Criteria) this;
        }

        public Criteria andPointIsNull() {
            addCriterion("point is null");
            return (Criteria) this;
        }

        public Criteria andPointIsNotNull() {
            addCriterion("point is not null");
            return (Criteria) this;
        }

        public Criteria andPointEqualTo(Integer value) {
            addCriterion("point =", value, "point");
            return (Criteria) this;
        }

        public Criteria andPointNotEqualTo(Integer value) {
            addCriterion("point <>", value, "point");
            return (Criteria) this;
        }

        public Criteria andPointGreaterThan(Integer value) {
            addCriterion("point >", value, "point");
            return (Criteria) this;
        }

        public Criteria andPointGreaterThanOrEqualTo(Integer value) {
            addCriterion("point >=", value, "point");
            return (Criteria) this;
        }

        public Criteria andPointLessThan(Integer value) {
            addCriterion("point <", value, "point");
            return (Criteria) this;
        }

        public Criteria andPointLessThanOrEqualTo(Integer value) {
            addCriterion("point <=", value, "point");
            return (Criteria) this;
        }

        public Criteria andPointIn(List<Integer> values) {
            addCriterion("point in", values, "point");
            return (Criteria) this;
        }

        public Criteria andPointNotIn(List<Integer> values) {
            addCriterion("point not in", values, "point");
            return (Criteria) this;
        }

        public Criteria andPointBetween(Integer value1, Integer value2) {
            addCriterion("point between", value1, value2, "point");
            return (Criteria) this;
        }

        public Criteria andPointNotBetween(Integer value1, Integer value2) {
            addCriterion("point not between", value1, value2, "point");
            return (Criteria) this;
        }

        public Criteria andReqOrderNoIsNull() {
            addCriterion("req_order_no is null");
            return (Criteria) this;
        }

        public Criteria andReqOrderNoIsNotNull() {
            addCriterion("req_order_no is not null");
            return (Criteria) this;
        }

        public Criteria andReqOrderNoEqualTo(String value) {
            addCriterion("req_order_no =", value, "reqOrderNo");
            return (Criteria) this;
        }

        public Criteria andReqOrderNoNotEqualTo(String value) {
            addCriterion("req_order_no <>", value, "reqOrderNo");
            return (Criteria) this;
        }

        public Criteria andReqOrderNoGreaterThan(String value) {
            addCriterion("req_order_no >", value, "reqOrderNo");
            return (Criteria) this;
        }

        public Criteria andReqOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("req_order_no >=", value, "reqOrderNo");
            return (Criteria) this;
        }

        public Criteria andReqOrderNoLessThan(String value) {
            addCriterion("req_order_no <", value, "reqOrderNo");
            return (Criteria) this;
        }

        public Criteria andReqOrderNoLessThanOrEqualTo(String value) {
            addCriterion("req_order_no <=", value, "reqOrderNo");
            return (Criteria) this;
        }

        public Criteria andReqOrderNoLike(String value) {
            addCriterion("req_order_no like", value, "reqOrderNo");
            return (Criteria) this;
        }

        public Criteria andReqOrderNoNotLike(String value) {
            addCriterion("req_order_no not like", value, "reqOrderNo");
            return (Criteria) this;
        }

        public Criteria andReqOrderNoIn(List<String> values) {
            addCriterion("req_order_no in", values, "reqOrderNo");
            return (Criteria) this;
        }

        public Criteria andReqOrderNoNotIn(List<String> values) {
            addCriterion("req_order_no not in", values, "reqOrderNo");
            return (Criteria) this;
        }

        public Criteria andReqOrderNoBetween(String value1, String value2) {
            addCriterion("req_order_no between", value1, value2, "reqOrderNo");
            return (Criteria) this;
        }

        public Criteria andReqOrderNoNotBetween(String value1, String value2) {
            addCriterion("req_order_no not between", value1, value2, "reqOrderNo");
            return (Criteria) this;
        }

        public Criteria andRspResultIsNull() {
            addCriterion("rsp_result is null");
            return (Criteria) this;
        }

        public Criteria andRspResultIsNotNull() {
            addCriterion("rsp_result is not null");
            return (Criteria) this;
        }

        public Criteria andRspResultEqualTo(Integer value) {
            addCriterion("rsp_result =", value, "rspResult");
            return (Criteria) this;
        }

        public Criteria andRspResultNotEqualTo(Integer value) {
            addCriterion("rsp_result <>", value, "rspResult");
            return (Criteria) this;
        }

        public Criteria andRspResultGreaterThan(Integer value) {
            addCriterion("rsp_result >", value, "rspResult");
            return (Criteria) this;
        }

        public Criteria andRspResultGreaterThanOrEqualTo(Integer value) {
            addCriterion("rsp_result >=", value, "rspResult");
            return (Criteria) this;
        }

        public Criteria andRspResultLessThan(Integer value) {
            addCriterion("rsp_result <", value, "rspResult");
            return (Criteria) this;
        }

        public Criteria andRspResultLessThanOrEqualTo(Integer value) {
            addCriterion("rsp_result <=", value, "rspResult");
            return (Criteria) this;
        }

        public Criteria andRspResultIn(List<Integer> values) {
            addCriterion("rsp_result in", values, "rspResult");
            return (Criteria) this;
        }

        public Criteria andRspResultNotIn(List<Integer> values) {
            addCriterion("rsp_result not in", values, "rspResult");
            return (Criteria) this;
        }

        public Criteria andRspResultBetween(Integer value1, Integer value2) {
            addCriterion("rsp_result between", value1, value2, "rspResult");
            return (Criteria) this;
        }

        public Criteria andRspResultNotBetween(Integer value1, Integer value2) {
            addCriterion("rsp_result not between", value1, value2, "rspResult");
            return (Criteria) this;
        }

        public Criteria andRspErrMsgIsNull() {
            addCriterion("rsp_err_msg is null");
            return (Criteria) this;
        }

        public Criteria andRspErrMsgIsNotNull() {
            addCriterion("rsp_err_msg is not null");
            return (Criteria) this;
        }

        public Criteria andRspErrMsgEqualTo(String value) {
            addCriterion("rsp_err_msg =", value, "rspErrMsg");
            return (Criteria) this;
        }

        public Criteria andRspErrMsgNotEqualTo(String value) {
            addCriterion("rsp_err_msg <>", value, "rspErrMsg");
            return (Criteria) this;
        }

        public Criteria andRspErrMsgGreaterThan(String value) {
            addCriterion("rsp_err_msg >", value, "rspErrMsg");
            return (Criteria) this;
        }

        public Criteria andRspErrMsgGreaterThanOrEqualTo(String value) {
            addCriterion("rsp_err_msg >=", value, "rspErrMsg");
            return (Criteria) this;
        }

        public Criteria andRspErrMsgLessThan(String value) {
            addCriterion("rsp_err_msg <", value, "rspErrMsg");
            return (Criteria) this;
        }

        public Criteria andRspErrMsgLessThanOrEqualTo(String value) {
            addCriterion("rsp_err_msg <=", value, "rspErrMsg");
            return (Criteria) this;
        }

        public Criteria andRspErrMsgLike(String value) {
            addCriterion("rsp_err_msg like", value, "rspErrMsg");
            return (Criteria) this;
        }

        public Criteria andRspErrMsgNotLike(String value) {
            addCriterion("rsp_err_msg not like", value, "rspErrMsg");
            return (Criteria) this;
        }

        public Criteria andRspErrMsgIn(List<String> values) {
            addCriterion("rsp_err_msg in", values, "rspErrMsg");
            return (Criteria) this;
        }

        public Criteria andRspErrMsgNotIn(List<String> values) {
            addCriterion("rsp_err_msg not in", values, "rspErrMsg");
            return (Criteria) this;
        }

        public Criteria andRspErrMsgBetween(String value1, String value2) {
            addCriterion("rsp_err_msg between", value1, value2, "rspErrMsg");
            return (Criteria) this;
        }

        public Criteria andRspErrMsgNotBetween(String value1, String value2) {
            addCriterion("rsp_err_msg not between", value1, value2, "rspErrMsg");
            return (Criteria) this;
        }

        public Criteria andRspExtIsNull() {
            addCriterion("rsp_ext is null");
            return (Criteria) this;
        }

        public Criteria andRspExtIsNotNull() {
            addCriterion("rsp_ext is not null");
            return (Criteria) this;
        }

        public Criteria andRspExtEqualTo(String value) {
            addCriterion("rsp_ext =", value, "rspExt");
            return (Criteria) this;
        }

        public Criteria andRspExtNotEqualTo(String value) {
            addCriterion("rsp_ext <>", value, "rspExt");
            return (Criteria) this;
        }

        public Criteria andRspExtGreaterThan(String value) {
            addCriterion("rsp_ext >", value, "rspExt");
            return (Criteria) this;
        }

        public Criteria andRspExtGreaterThanOrEqualTo(String value) {
            addCriterion("rsp_ext >=", value, "rspExt");
            return (Criteria) this;
        }

        public Criteria andRspExtLessThan(String value) {
            addCriterion("rsp_ext <", value, "rspExt");
            return (Criteria) this;
        }

        public Criteria andRspExtLessThanOrEqualTo(String value) {
            addCriterion("rsp_ext <=", value, "rspExt");
            return (Criteria) this;
        }

        public Criteria andRspExtLike(String value) {
            addCriterion("rsp_ext like", value, "rspExt");
            return (Criteria) this;
        }

        public Criteria andRspExtNotLike(String value) {
            addCriterion("rsp_ext not like", value, "rspExt");
            return (Criteria) this;
        }

        public Criteria andRspExtIn(List<String> values) {
            addCriterion("rsp_ext in", values, "rspExt");
            return (Criteria) this;
        }

        public Criteria andRspExtNotIn(List<String> values) {
            addCriterion("rsp_ext not in", values, "rspExt");
            return (Criteria) this;
        }

        public Criteria andRspExtBetween(String value1, String value2) {
            addCriterion("rsp_ext between", value1, value2, "rspExt");
            return (Criteria) this;
        }

        public Criteria andRspExtNotBetween(String value1, String value2) {
            addCriterion("rsp_ext not between", value1, value2, "rspExt");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }
    }

    /**
     * guildrank_point_grant_log
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * guildrank_point_grant_log null
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}