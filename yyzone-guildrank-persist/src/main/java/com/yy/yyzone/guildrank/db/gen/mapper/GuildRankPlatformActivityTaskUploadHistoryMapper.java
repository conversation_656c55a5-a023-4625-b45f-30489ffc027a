package com.yy.yyzone.guildrank.db.gen.mapper;

import com.yy.yyzone.guildrank.db.gen.model.GuildRankPlatformActivityTaskUploadHistory;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface GuildRankPlatformActivityTaskUploadHistoryMapper {

    @Insert({
            "INSERT INTO guild_rank_platform_activity_task_upload_history (",
            "dt, filename, upload_date, file_path, operator",
            ") VALUES (",
            "#{dt}, #{filename}, #{uploadDate}, #{filePath}, #{operator}",
            ") ON DUPLICATE KEY UPDATE",
            "filename = #{filename},",
            "upload_date =now(),",
            "file_path = #{filePath},",
            "operator = #{operator}"
    })
    void insertOrUpdate(GuildRankPlatformActivityTaskUploadHistory history);

    @Select({
            "<script>",
            "SELECT",
            "id AS id,",
            "dt AS dt,",
            "filename AS filename,",
            "upload_date AS uploadDate,",
            "file_path AS filePath,",
            "operator AS operator",
            "FROM guild_rank_platform_activity_task_upload_history",
            "WHERE  1= 1 <if test='dt != null'> AND dt = #{dt} </if>",
            "<if test='operator != null and operator != \"\"'> AND operator = #{operator}</if>",
            "</script>"
    })
    List<GuildRankPlatformActivityTaskUploadHistory> selectByDtOperator(@Param("dt") Integer dt, @Param("operator") String operator );

    @Select({
            "<script>",
            "SELECT",
            "id AS id,",
            "dt AS dt,",
            "filename AS filename,",
            "upload_date AS uploadDate,",
            "file_path AS filePath,",
            "operator AS operator",
            "FROM guild_rank_platform_activity_task_upload_history",
            "WHERE  1= 1 <if test='dt != null'> AND dt = #{dt} </if>",
            "</script>"
    })
    GuildRankPlatformActivityTaskUploadHistory selectByDt(@Param("dt") Integer dt);
}