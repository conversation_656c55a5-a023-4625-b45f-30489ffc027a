package com.yy.yyzone.guildrank.db.gen.mapper;

import com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentOriginStarDay;
import com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentOriginStarDayExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DmYySidSegmentOriginStarDayMapper {
    int countByExample(DmYySidSegmentOriginStarDayExample example);

    int deleteByExample(DmYySidSegmentOriginStarDayExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DmYySidSegmentOriginStarDay record);

    int insertSelective(DmYySidSegmentOriginStarDay record);

    List<DmYySidSegmentOriginStarDay> selectByExample(DmYySidSegmentOriginStarDayExample example);

    DmYySidSegmentOriginStarDay selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DmYySidSegmentOriginStarDay record, @Param("example") DmYySidSegmentOriginStarDayExample example);

    int updateByExample(@Param("record") DmYySidSegmentOriginStarDay record, @Param("example") DmYySidSegmentOriginStarDayExample example);

    int updateByPrimaryKeySelective(DmYySidSegmentOriginStarDay record);

    int updateByPrimaryKey(DmYySidSegmentOriginStarDay record);
}