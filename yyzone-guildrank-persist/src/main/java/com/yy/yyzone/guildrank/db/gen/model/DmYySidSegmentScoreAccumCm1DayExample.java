package com.yy.yyzone.guildrank.db.gen.model;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated("dm_yy_sid_segment_score_accum_cm_1_day")
public class DmYySidSegmentScoreAccumCm1DayExample {
    /**
     * dm_yy_sid_segment_score_accum_cm_1_day
     */
    protected String orderByClause;

    /**
     * dm_yy_sid_segment_score_accum_cm_1_day
     */
    protected boolean distinct;

    /**
     * dm_yy_sid_segment_score_accum_cm_1_day
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public DmYySidSegmentScoreAccumCm1DayExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    /**
     * dm_yy_sid_segment_score_accum_cm_1_day null
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSidOwneridIsNull() {
            addCriterion("sid_ownerid is null");
            return (Criteria) this;
        }

        public Criteria andSidOwneridIsNotNull() {
            addCriterion("sid_ownerid is not null");
            return (Criteria) this;
        }

        public Criteria andSidOwneridEqualTo(Long value) {
            addCriterion("sid_ownerid =", value, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridNotEqualTo(Long value) {
            addCriterion("sid_ownerid <>", value, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridGreaterThan(Long value) {
            addCriterion("sid_ownerid >", value, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridGreaterThanOrEqualTo(Long value) {
            addCriterion("sid_ownerid >=", value, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridLessThan(Long value) {
            addCriterion("sid_ownerid <", value, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridLessThanOrEqualTo(Long value) {
            addCriterion("sid_ownerid <=", value, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridIn(List<Long> values) {
            addCriterion("sid_ownerid in", values, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridNotIn(List<Long> values) {
            addCriterion("sid_ownerid not in", values, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridBetween(Long value1, Long value2) {
            addCriterion("sid_ownerid between", value1, value2, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andSidOwneridNotBetween(Long value1, Long value2) {
            addCriterion("sid_ownerid not between", value1, value2, "sidOwnerid");
            return (Criteria) this;
        }

        public Criteria andPsAllIsNull() {
            addCriterion("ps_all is null");
            return (Criteria) this;
        }

        public Criteria andPsAllIsNotNull() {
            addCriterion("ps_all is not null");
            return (Criteria) this;
        }

        public Criteria andPsAllEqualTo(Double value) {
            addCriterion("ps_all =", value, "psAll");
            return (Criteria) this;
        }

        public Criteria andPsAllNotEqualTo(Double value) {
            addCriterion("ps_all <>", value, "psAll");
            return (Criteria) this;
        }

        public Criteria andPsAllGreaterThan(Double value) {
            addCriterion("ps_all >", value, "psAll");
            return (Criteria) this;
        }

        public Criteria andPsAllGreaterThanOrEqualTo(Double value) {
            addCriterion("ps_all >=", value, "psAll");
            return (Criteria) this;
        }

        public Criteria andPsAllLessThan(Double value) {
            addCriterion("ps_all <", value, "psAll");
            return (Criteria) this;
        }

        public Criteria andPsAllLessThanOrEqualTo(Double value) {
            addCriterion("ps_all <=", value, "psAll");
            return (Criteria) this;
        }

        public Criteria andPsAllIn(List<Double> values) {
            addCriterion("ps_all in", values, "psAll");
            return (Criteria) this;
        }

        public Criteria andPsAllNotIn(List<Double> values) {
            addCriterion("ps_all not in", values, "psAll");
            return (Criteria) this;
        }

        public Criteria andPsAllBetween(Double value1, Double value2) {
            addCriterion("ps_all between", value1, value2, "psAll");
            return (Criteria) this;
        }

        public Criteria andPsAllNotBetween(Double value1, Double value2) {
            addCriterion("ps_all not between", value1, value2, "psAll");
            return (Criteria) this;
        }

        public Criteria andGradeIsNull() {
            addCriterion("grade is null");
            return (Criteria) this;
        }

        public Criteria andGradeIsNotNull() {
            addCriterion("grade is not null");
            return (Criteria) this;
        }

        public Criteria andGradeEqualTo(String value) {
            addCriterion("grade =", value, "grade");
            return (Criteria) this;
        }

        public Criteria andGradeNotEqualTo(String value) {
            addCriterion("grade <>", value, "grade");
            return (Criteria) this;
        }

        public Criteria andGradeGreaterThan(String value) {
            addCriterion("grade >", value, "grade");
            return (Criteria) this;
        }

        public Criteria andGradeGreaterThanOrEqualTo(String value) {
            addCriterion("grade >=", value, "grade");
            return (Criteria) this;
        }

        public Criteria andGradeLessThan(String value) {
            addCriterion("grade <", value, "grade");
            return (Criteria) this;
        }

        public Criteria andGradeLessThanOrEqualTo(String value) {
            addCriterion("grade <=", value, "grade");
            return (Criteria) this;
        }

        public Criteria andGradeLike(String value) {
            addCriterion("grade like", value, "grade");
            return (Criteria) this;
        }

        public Criteria andGradeNotLike(String value) {
            addCriterion("grade not like", value, "grade");
            return (Criteria) this;
        }

        public Criteria andGradeIn(List<String> values) {
            addCriterion("grade in", values, "grade");
            return (Criteria) this;
        }

        public Criteria andGradeNotIn(List<String> values) {
            addCriterion("grade not in", values, "grade");
            return (Criteria) this;
        }

        public Criteria andGradeBetween(String value1, String value2) {
            addCriterion("grade between", value1, value2, "grade");
            return (Criteria) this;
        }

        public Criteria andGradeNotBetween(String value1, String value2) {
            addCriterion("grade not between", value1, value2, "grade");
            return (Criteria) this;
        }

        public Criteria andGradeRnIsNull() {
            addCriterion("grade_rn is null");
            return (Criteria) this;
        }

        public Criteria andGradeRnIsNotNull() {
            addCriterion("grade_rn is not null");
            return (Criteria) this;
        }

        public Criteria andGradeRnEqualTo(Integer value) {
            addCriterion("grade_rn =", value, "gradeRn");
            return (Criteria) this;
        }

        public Criteria andGradeRnNotEqualTo(Integer value) {
            addCriterion("grade_rn <>", value, "gradeRn");
            return (Criteria) this;
        }

        public Criteria andGradeRnGreaterThan(Integer value) {
            addCriterion("grade_rn >", value, "gradeRn");
            return (Criteria) this;
        }

        public Criteria andGradeRnGreaterThanOrEqualTo(Integer value) {
            addCriterion("grade_rn >=", value, "gradeRn");
            return (Criteria) this;
        }

        public Criteria andGradeRnLessThan(Integer value) {
            addCriterion("grade_rn <", value, "gradeRn");
            return (Criteria) this;
        }

        public Criteria andGradeRnLessThanOrEqualTo(Integer value) {
            addCriterion("grade_rn <=", value, "gradeRn");
            return (Criteria) this;
        }

        public Criteria andGradeRnIn(List<Integer> values) {
            addCriterion("grade_rn in", values, "gradeRn");
            return (Criteria) this;
        }

        public Criteria andGradeRnNotIn(List<Integer> values) {
            addCriterion("grade_rn not in", values, "gradeRn");
            return (Criteria) this;
        }

        public Criteria andGradeRnBetween(Integer value1, Integer value2) {
            addCriterion("grade_rn between", value1, value2, "gradeRn");
            return (Criteria) this;
        }

        public Criteria andGradeRnNotBetween(Integer value1, Integer value2) {
            addCriterion("grade_rn not between", value1, value2, "gradeRn");
            return (Criteria) this;
        }

        public Criteria andDtIsNull() {
            addCriterion("dt is null");
            return (Criteria) this;
        }

        public Criteria andDtIsNotNull() {
            addCriterion("dt is not null");
            return (Criteria) this;
        }

        public Criteria andDtEqualTo(String value) {
            addCriterion("dt =", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotEqualTo(String value) {
            addCriterion("dt <>", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtGreaterThan(String value) {
            addCriterion("dt >", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtGreaterThanOrEqualTo(String value) {
            addCriterion("dt >=", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtLessThan(String value) {
            addCriterion("dt <", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtLessThanOrEqualTo(String value) {
            addCriterion("dt <=", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtLike(String value) {
            addCriterion("dt like", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotLike(String value) {
            addCriterion("dt not like", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtIn(List<String> values) {
            addCriterion("dt in", values, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotIn(List<String> values) {
            addCriterion("dt not in", values, "dt");
            return (Criteria) this;
        }

        public Criteria andDtBetween(String value1, String value2) {
            addCriterion("dt between", value1, value2, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotBetween(String value1, String value2) {
            addCriterion("dt not between", value1, value2, "dt");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidIsNull() {
            addCriterion("sid_owyyid is null");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidIsNotNull() {
            addCriterion("sid_owyyid is not null");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidEqualTo(Long value) {
            addCriterion("sid_owyyid =", value, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidNotEqualTo(Long value) {
            addCriterion("sid_owyyid <>", value, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidGreaterThan(Long value) {
            addCriterion("sid_owyyid >", value, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidGreaterThanOrEqualTo(Long value) {
            addCriterion("sid_owyyid >=", value, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidLessThan(Long value) {
            addCriterion("sid_owyyid <", value, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidLessThanOrEqualTo(Long value) {
            addCriterion("sid_owyyid <=", value, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidIn(List<Long> values) {
            addCriterion("sid_owyyid in", values, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidNotIn(List<Long> values) {
            addCriterion("sid_owyyid not in", values, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidBetween(Long value1, Long value2) {
            addCriterion("sid_owyyid between", value1, value2, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andSidOwyyidNotBetween(Long value1, Long value2) {
            addCriterion("sid_owyyid not between", value1, value2, "sidOwyyid");
            return (Criteria) this;
        }

        public Criteria andPsAllWeightIsNull() {
            addCriterion("ps_all_weight is null");
            return (Criteria) this;
        }

        public Criteria andPsAllWeightIsNotNull() {
            addCriterion("ps_all_weight is not null");
            return (Criteria) this;
        }

        public Criteria andPsAllWeightEqualTo(Double value) {
            addCriterion("ps_all_weight =", value, "psAllWeight");
            return (Criteria) this;
        }

        public Criteria andPsAllWeightNotEqualTo(Double value) {
            addCriterion("ps_all_weight <>", value, "psAllWeight");
            return (Criteria) this;
        }

        public Criteria andPsAllWeightGreaterThan(Double value) {
            addCriterion("ps_all_weight >", value, "psAllWeight");
            return (Criteria) this;
        }

        public Criteria andPsAllWeightGreaterThanOrEqualTo(Double value) {
            addCriterion("ps_all_weight >=", value, "psAllWeight");
            return (Criteria) this;
        }

        public Criteria andPsAllWeightLessThan(Double value) {
            addCriterion("ps_all_weight <", value, "psAllWeight");
            return (Criteria) this;
        }

        public Criteria andPsAllWeightLessThanOrEqualTo(Double value) {
            addCriterion("ps_all_weight <=", value, "psAllWeight");
            return (Criteria) this;
        }

        public Criteria andPsAllWeightIn(List<Double> values) {
            addCriterion("ps_all_weight in", values, "psAllWeight");
            return (Criteria) this;
        }

        public Criteria andPsAllWeightNotIn(List<Double> values) {
            addCriterion("ps_all_weight not in", values, "psAllWeight");
            return (Criteria) this;
        }

        public Criteria andPsAllWeightBetween(Double value1, Double value2) {
            addCriterion("ps_all_weight between", value1, value2, "psAllWeight");
            return (Criteria) this;
        }

        public Criteria andPsAllWeightNotBetween(Double value1, Double value2) {
            addCriterion("ps_all_weight not between", value1, value2, "psAllWeight");
            return (Criteria) this;
        }

        public Criteria andWeightAbIsNull() {
            addCriterion("weight_ab is null");
            return (Criteria) this;
        }

        public Criteria andWeightAbIsNotNull() {
            addCriterion("weight_ab is not null");
            return (Criteria) this;
        }

        public Criteria andWeightAbEqualTo(Double value) {
            addCriterion("weight_ab =", value, "weightAb");
            return (Criteria) this;
        }

        public Criteria andWeightAbNotEqualTo(Double value) {
            addCriterion("weight_ab <>", value, "weightAb");
            return (Criteria) this;
        }

        public Criteria andWeightAbGreaterThan(Double value) {
            addCriterion("weight_ab >", value, "weightAb");
            return (Criteria) this;
        }

        public Criteria andWeightAbGreaterThanOrEqualTo(Double value) {
            addCriterion("weight_ab >=", value, "weightAb");
            return (Criteria) this;
        }

        public Criteria andWeightAbLessThan(Double value) {
            addCriterion("weight_ab <", value, "weightAb");
            return (Criteria) this;
        }

        public Criteria andWeightAbLessThanOrEqualTo(Double value) {
            addCriterion("weight_ab <=", value, "weightAb");
            return (Criteria) this;
        }

        public Criteria andWeightAbIn(List<Double> values) {
            addCriterion("weight_ab in", values, "weightAb");
            return (Criteria) this;
        }

        public Criteria andWeightAbNotIn(List<Double> values) {
            addCriterion("weight_ab not in", values, "weightAb");
            return (Criteria) this;
        }

        public Criteria andWeightAbBetween(Double value1, Double value2) {
            addCriterion("weight_ab between", value1, value2, "weightAb");
            return (Criteria) this;
        }

        public Criteria andWeightAbNotBetween(Double value1, Double value2) {
            addCriterion("weight_ab not between", value1, value2, "weightAb");
            return (Criteria) this;
        }

        public Criteria andNewUvCmIsNull() {
            addCriterion("new_uv_cm is null");
            return (Criteria) this;
        }

        public Criteria andNewUvCmIsNotNull() {
            addCriterion("new_uv_cm is not null");
            return (Criteria) this;
        }

        public Criteria andNewUvCmEqualTo(Long value) {
            addCriterion("new_uv_cm =", value, "newUvCm");
            return (Criteria) this;
        }

        public Criteria andNewUvCmNotEqualTo(Long value) {
            addCriterion("new_uv_cm <>", value, "newUvCm");
            return (Criteria) this;
        }

        public Criteria andNewUvCmGreaterThan(Long value) {
            addCriterion("new_uv_cm >", value, "newUvCm");
            return (Criteria) this;
        }

        public Criteria andNewUvCmGreaterThanOrEqualTo(Long value) {
            addCriterion("new_uv_cm >=", value, "newUvCm");
            return (Criteria) this;
        }

        public Criteria andNewUvCmLessThan(Long value) {
            addCriterion("new_uv_cm <", value, "newUvCm");
            return (Criteria) this;
        }

        public Criteria andNewUvCmLessThanOrEqualTo(Long value) {
            addCriterion("new_uv_cm <=", value, "newUvCm");
            return (Criteria) this;
        }

        public Criteria andNewUvCmIn(List<Long> values) {
            addCriterion("new_uv_cm in", values, "newUvCm");
            return (Criteria) this;
        }

        public Criteria andNewUvCmNotIn(List<Long> values) {
            addCriterion("new_uv_cm not in", values, "newUvCm");
            return (Criteria) this;
        }

        public Criteria andNewUvCmBetween(Long value1, Long value2) {
            addCriterion("new_uv_cm between", value1, value2, "newUvCm");
            return (Criteria) this;
        }

        public Criteria andNewUvCmNotBetween(Long value1, Long value2) {
            addCriterion("new_uv_cm not between", value1, value2, "newUvCm");
            return (Criteria) this;
        }

        public Criteria andWeightAIsNull() {
            addCriterion("weight_a is null");
            return (Criteria) this;
        }

        public Criteria andWeightAIsNotNull() {
            addCriterion("weight_a is not null");
            return (Criteria) this;
        }

        public Criteria andWeightAEqualTo(Double value) {
            addCriterion("weight_a =", value, "weightA");
            return (Criteria) this;
        }

        public Criteria andWeightANotEqualTo(Double value) {
            addCriterion("weight_a <>", value, "weightA");
            return (Criteria) this;
        }

        public Criteria andWeightAGreaterThan(Double value) {
            addCriterion("weight_a >", value, "weightA");
            return (Criteria) this;
        }

        public Criteria andWeightAGreaterThanOrEqualTo(Double value) {
            addCriterion("weight_a >=", value, "weightA");
            return (Criteria) this;
        }

        public Criteria andWeightALessThan(Double value) {
            addCriterion("weight_a <", value, "weightA");
            return (Criteria) this;
        }

        public Criteria andWeightALessThanOrEqualTo(Double value) {
            addCriterion("weight_a <=", value, "weightA");
            return (Criteria) this;
        }

        public Criteria andWeightAIn(List<Double> values) {
            addCriterion("weight_a in", values, "weightA");
            return (Criteria) this;
        }

        public Criteria andWeightANotIn(List<Double> values) {
            addCriterion("weight_a not in", values, "weightA");
            return (Criteria) this;
        }

        public Criteria andWeightABetween(Double value1, Double value2) {
            addCriterion("weight_a between", value1, value2, "weightA");
            return (Criteria) this;
        }

        public Criteria andWeightANotBetween(Double value1, Double value2) {
            addCriterion("weight_a not between", value1, value2, "weightA");
            return (Criteria) this;
        }

        public Criteria andHActUvIsNull() {
            addCriterion("h_act_uv is null");
            return (Criteria) this;
        }

        public Criteria andHActUvIsNotNull() {
            addCriterion("h_act_uv is not null");
            return (Criteria) this;
        }

        public Criteria andHActUvEqualTo(Long value) {
            addCriterion("h_act_uv =", value, "hActUv");
            return (Criteria) this;
        }

        public Criteria andHActUvNotEqualTo(Long value) {
            addCriterion("h_act_uv <>", value, "hActUv");
            return (Criteria) this;
        }

        public Criteria andHActUvGreaterThan(Long value) {
            addCriterion("h_act_uv >", value, "hActUv");
            return (Criteria) this;
        }

        public Criteria andHActUvGreaterThanOrEqualTo(Long value) {
            addCriterion("h_act_uv >=", value, "hActUv");
            return (Criteria) this;
        }

        public Criteria andHActUvLessThan(Long value) {
            addCriterion("h_act_uv <", value, "hActUv");
            return (Criteria) this;
        }

        public Criteria andHActUvLessThanOrEqualTo(Long value) {
            addCriterion("h_act_uv <=", value, "hActUv");
            return (Criteria) this;
        }

        public Criteria andHActUvIn(List<Long> values) {
            addCriterion("h_act_uv in", values, "hActUv");
            return (Criteria) this;
        }

        public Criteria andHActUvNotIn(List<Long> values) {
            addCriterion("h_act_uv not in", values, "hActUv");
            return (Criteria) this;
        }

        public Criteria andHActUvBetween(Long value1, Long value2) {
            addCriterion("h_act_uv between", value1, value2, "hActUv");
            return (Criteria) this;
        }

        public Criteria andHActUvNotBetween(Long value1, Long value2) {
            addCriterion("h_act_uv not between", value1, value2, "hActUv");
            return (Criteria) this;
        }

        public Criteria andWeightBIsNull() {
            addCriterion("weight_b is null");
            return (Criteria) this;
        }

        public Criteria andWeightBIsNotNull() {
            addCriterion("weight_b is not null");
            return (Criteria) this;
        }

        public Criteria andWeightBEqualTo(Double value) {
            addCriterion("weight_b =", value, "weightB");
            return (Criteria) this;
        }

        public Criteria andWeightBNotEqualTo(Double value) {
            addCriterion("weight_b <>", value, "weightB");
            return (Criteria) this;
        }

        public Criteria andWeightBGreaterThan(Double value) {
            addCriterion("weight_b >", value, "weightB");
            return (Criteria) this;
        }

        public Criteria andWeightBGreaterThanOrEqualTo(Double value) {
            addCriterion("weight_b >=", value, "weightB");
            return (Criteria) this;
        }

        public Criteria andWeightBLessThan(Double value) {
            addCriterion("weight_b <", value, "weightB");
            return (Criteria) this;
        }

        public Criteria andWeightBLessThanOrEqualTo(Double value) {
            addCriterion("weight_b <=", value, "weightB");
            return (Criteria) this;
        }

        public Criteria andWeightBIn(List<Double> values) {
            addCriterion("weight_b in", values, "weightB");
            return (Criteria) this;
        }

        public Criteria andWeightBNotIn(List<Double> values) {
            addCriterion("weight_b not in", values, "weightB");
            return (Criteria) this;
        }

        public Criteria andWeightBBetween(Double value1, Double value2) {
            addCriterion("weight_b between", value1, value2, "weightB");
            return (Criteria) this;
        }

        public Criteria andWeightBNotBetween(Double value1, Double value2) {
            addCriterion("weight_b not between", value1, value2, "weightB");
            return (Criteria) this;
        }
    }

    /**
     * dm_yy_sid_segment_score_accum_cm_1_day
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * dm_yy_sid_segment_score_accum_cm_1_day null
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}