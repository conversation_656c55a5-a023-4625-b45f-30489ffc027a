<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yy.yyzone.guildrank.db.gen.mapper.YyDmEntityGuildCmpHealthAnalysisExptDiMapper" >
  <resultMap id="BaseResultMap" type="com.yy.yyzone.guildrank.db.gen.model.YyDmEntityGuildCmpHealthAnalysisExptDi" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="guild_cmp_ownr_id" property="guildCmpOwnrId" jdbcType="BIGINT" />
    <result column="new_aid_rat_3_4_aid_num" property="newAidRat34AidNum" jdbcType="BIGINT" />
    <result column="new_aid_rat_3_4_aid_num_rn" property="newAidRat34AidNumRn" jdbcType="BIGINT" />
    <result column="new_aid_rat_3_4_aid_num_rn_score" property="newAidRat34AidNumRnScore" jdbcType="INTEGER" />
    <result column="new_aid_rat_3_4_aid_num_rn_next_score" property="newAidRat34AidNumRnNextScore" jdbcType="INTEGER" />
    <result column="new_aid_rat_3_4_aid_num_trgt_diff" property="newAidRat34AidNumTrgtDiff" jdbcType="BIGINT" />
    <result column="new_aid_rat_3_4_aid_num_diff" property="newAidRat34AidNumDiff" jdbcType="BIGINT" />
    <result column="new_aid_rat_3_4_aid_num_diff_score" property="newAidRat34AidNumDiffScore" jdbcType="INTEGER" />
    <result column="new_aid_rat_3_4_aid_num_diff_next_score" property="newAidRat34AidNumDiffNextScore" jdbcType="INTEGER" />
    <result column="new_aid_rat_3_4_aid_num_diff_trgt_diff" property="newAidRat34AidNumDiffTrgtDiff" jdbcType="BIGINT" />
    <result column="upgrd_high_aid_num_diff" property="upgrdHighAidNumDiff" jdbcType="INTEGER" />
    <result column="upgrd_high_aid_num_diff_score" property="upgrdHighAidNumDiffScore" jdbcType="INTEGER" />
    <result column="upgrd_high_aid_num_diff_next_score" property="upgrdHighAidNumDiffNextScore" jdbcType="INTEGER" />
    <result column="upgrd_high_aid_num_diff_trgt_diff" property="upgrdHighAidNumDiffTrgtDiff" jdbcType="INTEGER" />
    <result column="upgrd_waist_aid_num_diff" property="upgrdWaistAidNumDiff" jdbcType="INTEGER" />
    <result column="upgrd_waist_aid_num_diff_score" property="upgrdWaistAidNumDiffScore" jdbcType="INTEGER" />
    <result column="upgrd_waist_aid_num_diff_next_score" property="upgrdWaistAidNumDiffNextScore" jdbcType="INTEGER" />
    <result column="upgrd_waist_aid_num_diff_trgt_diff" property="upgrdWaistAidNumDiffTrgtDiff" jdbcType="INTEGER" />
    <result column="avg_high_valid_live_aid_num" property="avgHighValidLiveAidNum" jdbcType="DOUBLE" />
    <result column="avg_high_valid_live_aid_num_rn" property="avgHighValidLiveAidNumRn" jdbcType="BIGINT" />
    <result column="avg_high_valid_live_aid_num_rn_score" property="avgHighValidLiveAidNumRnScore" jdbcType="INTEGER" />
    <result column="avg_high_valid_live_aid_num_rn_next_score" property="avgHighValidLiveAidNumRnNextScore" jdbcType="INTEGER" />
    <result column="avg_high_valid_live_aid_num_trgt_diff" property="avgHighValidLiveAidNumTrgtDiff" jdbcType="DOUBLE" />
    <result column="avg_high_valid_live_aid_num_diff" property="avgHighValidLiveAidNumDiff" jdbcType="DOUBLE" />
    <result column="avg_high_valid_live_aid_num_diff_score" property="avgHighValidLiveAidNumDiffScore" jdbcType="INTEGER" />
    <result column="avg_high_valid_live_aid_num_diff_next_score" property="avgHighValidLiveAidNumDiffNextScore" jdbcType="INTEGER" />
    <result column="avg_high_valid_live_aid_num_diff_trgt_diff" property="avgHighValidLiveAidNumDiffTrgtDiff" jdbcType="DOUBLE" />
    <result column="avg_waist_valid_live_aid_num" property="avgWaistValidLiveAidNum" jdbcType="DOUBLE" />
    <result column="avg_waist_valid_live_aid_num_rn" property="avgWaistValidLiveAidNumRn" jdbcType="BIGINT" />
    <result column="avg_waist_valid_live_aid_num_rn_score" property="avgWaistValidLiveAidNumRnScore" jdbcType="INTEGER" />
    <result column="avg_waist_valid_live_aid_num_rn_next_score" property="avgWaistValidLiveAidNumRnNextScore" jdbcType="INTEGER" />
    <result column="avg_waist_valid_live_aid_num_trgt_diff" property="avgWaistValidLiveAidNumTrgtDiff" jdbcType="DOUBLE" />
    <result column="avg_waist_valid_live_aid_num_diff" property="avgWaistValidLiveAidNumDiff" jdbcType="DOUBLE" />
    <result column="avg_waist_valid_live_aid_num_diff_score" property="avgWaistValidLiveAidNumDiffScore" jdbcType="INTEGER" />
    <result column="avg_waist_valid_live_aid_num_diff_next_score" property="avgWaistValidLiveAidNumDiffNextScore" jdbcType="INTEGER" />
    <result column="avg_waist_valid_live_aid_num_diff_trgt_diff" property="avgWaistValidLiveAidNumDiffTrgtDiff" jdbcType="DOUBLE" />
    <result column="avg_nto_valid_live_aid_rate" property="avgNtoValidLiveAidRate" jdbcType="DOUBLE" />
    <result column="avg_nto_valid_live_aid_rate_rn" property="avgNtoValidLiveAidRateRn" jdbcType="BIGINT" />
    <result column="avg_nto_valid_live_aid_rate_rn_score" property="avgNtoValidLiveAidRateRnScore" jdbcType="INTEGER" />
    <result column="avg_nto_valid_live_aid_rate_rn_next_score" property="avgNtoValidLiveAidRateRnNextScore" jdbcType="INTEGER" />
    <result column="avg_nto_valid_live_aid_rate_trgt_diff" property="avgNtoValidLiveAidRateTrgtDiff" jdbcType="DOUBLE" />
    <result column="avg_stock_valid_live_aid_rate" property="avgStockValidLiveAidRate" jdbcType="DOUBLE" />
    <result column="avg_stock_valid_live_aid_rate_rn" property="avgStockValidLiveAidRateRn" jdbcType="BIGINT" />
    <result column="avg_stock_valid_live_aid_rate_rn_score" property="avgStockValidLiveAidRateRnScore" jdbcType="INTEGER" />
    <result column="avg_stock_valid_live_aid_rate_rn_next_score" property="avgStockValidLiveAidRateRnNextScore" jdbcType="INTEGER" />
    <result column="avg_stock_valid_live_aid_rate_trgt_diff" property="avgStockValidLiveAidRateTrgtDiff" jdbcType="DOUBLE" />
    <result column="new_auth_golden_aid_num" property="newAuthGoldenAidNum" jdbcType="BIGINT" />
    <result column="new_auth_golden_aid_num_rn" property="newAuthGoldenAidNumRn" jdbcType="BIGINT" />
    <result column="new_auth_golden_aid_num_rn_score" property="newAuthGoldenAidNumRnScore" jdbcType="INTEGER" />
    <result column="new_auth_golden_aid_num_rn_next_score" property="newAuthGoldenAidNumRnNextScore" jdbcType="INTEGER" />
    <result column="new_auth_golden_aid_num_trgt_diff" property="newAuthGoldenAidNumTrgtDiff" jdbcType="BIGINT" />
    <result column="valid_live_new_aid_incm_amt" property="validLiveNewAidIncmAmt" jdbcType="DOUBLE" />
    <result column="valid_live_new_aid_incm_amt_rn" property="validLiveNewAidIncmAmtRn" jdbcType="BIGINT" />
    <result column="valid_live_new_aid_incm_amt_rn_score" property="validLiveNewAidIncmAmtRnScore" jdbcType="INTEGER" />
    <result column="valid_live_new_aid_incm_amt_rn_next_score" property="validLiveNewAidIncmAmtRnNextScore" jdbcType="INTEGER" />
    <result column="valid_live_new_aid_incm_amt_trgt_diff" property="validLiveNewAidIncmAmtTrgtDiff" jdbcType="DOUBLE" />
    <result column="valid_live_new_aid_incm_amt_diff" property="validLiveNewAidIncmAmtDiff" jdbcType="DOUBLE" />
    <result column="valid_live_new_aid_incm_amt_diff_score" property="validLiveNewAidIncmAmtDiffScore" jdbcType="INTEGER" />
    <result column="valid_live_new_aid_incm_amt_diff_next_score" property="validLiveNewAidIncmAmtDiffNextScore" jdbcType="INTEGER" />
    <result column="valid_live_new_aid_incm_amt_diff_trgt_diff" property="validLiveNewAidIncmAmtDiffTrgtDiff" jdbcType="DOUBLE" />
    <result column="unvalid_live_new_aid_incm_amt" property="unvalidLiveNewAidIncmAmt" jdbcType="DOUBLE" />
    <result column="unvalid_live_new_aid_incm_amt_rn" property="unvalidLiveNewAidIncmAmtRn" jdbcType="BIGINT" />
    <result column="unvalid_live_new_aid_incm_amt_rn_score" property="unvalidLiveNewAidIncmAmtRnScore" jdbcType="INTEGER" />
    <result column="unvalid_live_new_aid_incm_amt_rn_next_score" property="unvalidLiveNewAidIncmAmtRnNextScore" jdbcType="INTEGER" />
    <result column="unvalid_live_new_aid_incm_amt_trgt_diff" property="unvalidLiveNewAidIncmAmtTrgtDiff" jdbcType="DOUBLE" />
    <result column="unvalid_live_new_aid_incm_amt_diff" property="unvalidLiveNewAidIncmAmtDiff" jdbcType="DOUBLE" />
    <result column="unvalid_live_new_aid_incm_amt_diff_score" property="unvalidLiveNewAidIncmAmtDiffScore" jdbcType="INTEGER" />
    <result column="unvalid_live_new_aid_incm_amt_diff_next_score" property="unvalidLiveNewAidIncmAmtDiffNextScore" jdbcType="INTEGER" />
    <result column="unvalid_live_new_aid_incm_amt_diff_trgt_diff" property="unvalidLiveNewAidIncmAmtDiffTrgtDiff" jdbcType="DOUBLE" />
    <result column="unvalid_live_new_aid_incm_amt_rate_diff" property="unvalidLiveNewAidIncmAmtRateDiff" jdbcType="DOUBLE" />
    <result column="unvalid_live_new_aid_incm_amt_rate_diff_score" property="unvalidLiveNewAidIncmAmtRateDiffScore" jdbcType="INTEGER" />
    <result column="unvalid_live_new_aid_incm_amt_rate_diff_next_score" property="unvalidLiveNewAidIncmAmtRateDiffNextScore" jdbcType="INTEGER" />
    <result column="unvalid_live_new_aid_incm_amt_rate_diff_trgt_diff" property="unvalidLiveNewAidIncmAmtRateDiffTrgtDiff" jdbcType="DOUBLE" />
    <result column="guild_health_point" property="guildHealthPoint" jdbcType="INTEGER" />
    <result column="guild_health_point_score" property="guildHealthPointScore" jdbcType="INTEGER" />
    <result column="guild_health_point_next_score" property="guildHealthPointNextScore" jdbcType="INTEGER" />
    <result column="guild_health_point_trgt_diff" property="guildHealthPointTrgtDiff" jdbcType="INTEGER" />
    <result column="dt" property="dt" jdbcType="DATE" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, guild_cmp_ownr_id, new_aid_rat_3_4_aid_num, new_aid_rat_3_4_aid_num_rn, new_aid_rat_3_4_aid_num_rn_score, 
    new_aid_rat_3_4_aid_num_rn_next_score, new_aid_rat_3_4_aid_num_trgt_diff, new_aid_rat_3_4_aid_num_diff, 
    new_aid_rat_3_4_aid_num_diff_score, new_aid_rat_3_4_aid_num_diff_next_score, new_aid_rat_3_4_aid_num_diff_trgt_diff, 
    upgrd_high_aid_num_diff, upgrd_high_aid_num_diff_score, upgrd_high_aid_num_diff_next_score, 
    upgrd_high_aid_num_diff_trgt_diff, upgrd_waist_aid_num_diff, upgrd_waist_aid_num_diff_score, 
    upgrd_waist_aid_num_diff_next_score, upgrd_waist_aid_num_diff_trgt_diff, avg_high_valid_live_aid_num, 
    avg_high_valid_live_aid_num_rn, avg_high_valid_live_aid_num_rn_score, avg_high_valid_live_aid_num_rn_next_score, 
    avg_high_valid_live_aid_num_trgt_diff, avg_high_valid_live_aid_num_diff, avg_high_valid_live_aid_num_diff_score, 
    avg_high_valid_live_aid_num_diff_next_score, avg_high_valid_live_aid_num_diff_trgt_diff, 
    avg_waist_valid_live_aid_num, avg_waist_valid_live_aid_num_rn, avg_waist_valid_live_aid_num_rn_score, 
    avg_waist_valid_live_aid_num_rn_next_score, avg_waist_valid_live_aid_num_trgt_diff, 
    avg_waist_valid_live_aid_num_diff, avg_waist_valid_live_aid_num_diff_score, avg_waist_valid_live_aid_num_diff_next_score, 
    avg_waist_valid_live_aid_num_diff_trgt_diff, avg_nto_valid_live_aid_rate, avg_nto_valid_live_aid_rate_rn, 
    avg_nto_valid_live_aid_rate_rn_score, avg_nto_valid_live_aid_rate_rn_next_score, 
    avg_nto_valid_live_aid_rate_trgt_diff, avg_stock_valid_live_aid_rate, avg_stock_valid_live_aid_rate_rn, 
    avg_stock_valid_live_aid_rate_rn_score, avg_stock_valid_live_aid_rate_rn_next_score, 
    avg_stock_valid_live_aid_rate_trgt_diff, new_auth_golden_aid_num, new_auth_golden_aid_num_rn, 
    new_auth_golden_aid_num_rn_score, new_auth_golden_aid_num_rn_next_score, new_auth_golden_aid_num_trgt_diff, 
    valid_live_new_aid_incm_amt, valid_live_new_aid_incm_amt_rn, valid_live_new_aid_incm_amt_rn_score, 
    valid_live_new_aid_incm_amt_rn_next_score, valid_live_new_aid_incm_amt_trgt_diff, 
    valid_live_new_aid_incm_amt_diff, valid_live_new_aid_incm_amt_diff_score, valid_live_new_aid_incm_amt_diff_next_score, 
    valid_live_new_aid_incm_amt_diff_trgt_diff, unvalid_live_new_aid_incm_amt, unvalid_live_new_aid_incm_amt_rn, 
    unvalid_live_new_aid_incm_amt_rn_score, unvalid_live_new_aid_incm_amt_rn_next_score, 
    unvalid_live_new_aid_incm_amt_trgt_diff, unvalid_live_new_aid_incm_amt_diff, unvalid_live_new_aid_incm_amt_diff_score, 
    unvalid_live_new_aid_incm_amt_diff_next_score, unvalid_live_new_aid_incm_amt_diff_trgt_diff, 
    unvalid_live_new_aid_incm_amt_rate_diff, unvalid_live_new_aid_incm_amt_rate_diff_score, 
    unvalid_live_new_aid_incm_amt_rate_diff_next_score, unvalid_live_new_aid_incm_amt_rate_diff_trgt_diff, 
    guild_health_point, guild_health_point_score, guild_health_point_next_score, guild_health_point_trgt_diff, 
    dt
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.yy.yyzone.guildrank.db.gen.model.YyDmEntityGuildCmpHealthAnalysisExptDiExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from yy_dm_entity_guild_cmp_health_analysis_expt_di
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit > 0" >
      limit ${limit}
    </if>
    <if test="offset > 0" >
      offset ${offset}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from yy_dm_entity_guild_cmp_health_analysis_expt_di
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from yy_dm_entity_guild_cmp_health_analysis_expt_di
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.yy.yyzone.guildrank.db.gen.model.YyDmEntityGuildCmpHealthAnalysisExptDiExample" >
    delete from yy_dm_entity_guild_cmp_health_analysis_expt_di
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.yy.yyzone.guildrank.db.gen.model.YyDmEntityGuildCmpHealthAnalysisExptDi" useGeneratedKeys="true" keyProperty="id" >
    insert into yy_dm_entity_guild_cmp_health_analysis_expt_di (guild_cmp_ownr_id, new_aid_rat_3_4_aid_num, 
      new_aid_rat_3_4_aid_num_rn, new_aid_rat_3_4_aid_num_rn_score, 
      new_aid_rat_3_4_aid_num_rn_next_score, new_aid_rat_3_4_aid_num_trgt_diff, 
      new_aid_rat_3_4_aid_num_diff, new_aid_rat_3_4_aid_num_diff_score, 
      new_aid_rat_3_4_aid_num_diff_next_score, new_aid_rat_3_4_aid_num_diff_trgt_diff, 
      upgrd_high_aid_num_diff, upgrd_high_aid_num_diff_score, 
      upgrd_high_aid_num_diff_next_score, upgrd_high_aid_num_diff_trgt_diff, 
      upgrd_waist_aid_num_diff, upgrd_waist_aid_num_diff_score, 
      upgrd_waist_aid_num_diff_next_score, upgrd_waist_aid_num_diff_trgt_diff, 
      avg_high_valid_live_aid_num, avg_high_valid_live_aid_num_rn, 
      avg_high_valid_live_aid_num_rn_score, avg_high_valid_live_aid_num_rn_next_score, 
      avg_high_valid_live_aid_num_trgt_diff, avg_high_valid_live_aid_num_diff, 
      avg_high_valid_live_aid_num_diff_score, avg_high_valid_live_aid_num_diff_next_score, 
      avg_high_valid_live_aid_num_diff_trgt_diff, avg_waist_valid_live_aid_num, 
      avg_waist_valid_live_aid_num_rn, avg_waist_valid_live_aid_num_rn_score, 
      avg_waist_valid_live_aid_num_rn_next_score, avg_waist_valid_live_aid_num_trgt_diff, 
      avg_waist_valid_live_aid_num_diff, avg_waist_valid_live_aid_num_diff_score, 
      avg_waist_valid_live_aid_num_diff_next_score, avg_waist_valid_live_aid_num_diff_trgt_diff, 
      avg_nto_valid_live_aid_rate, avg_nto_valid_live_aid_rate_rn, 
      avg_nto_valid_live_aid_rate_rn_score, avg_nto_valid_live_aid_rate_rn_next_score, 
      avg_nto_valid_live_aid_rate_trgt_diff, avg_stock_valid_live_aid_rate, 
      avg_stock_valid_live_aid_rate_rn, avg_stock_valid_live_aid_rate_rn_score, 
      avg_stock_valid_live_aid_rate_rn_next_score, avg_stock_valid_live_aid_rate_trgt_diff, 
      new_auth_golden_aid_num, new_auth_golden_aid_num_rn, 
      new_auth_golden_aid_num_rn_score, new_auth_golden_aid_num_rn_next_score, 
      new_auth_golden_aid_num_trgt_diff, valid_live_new_aid_incm_amt, 
      valid_live_new_aid_incm_amt_rn, valid_live_new_aid_incm_amt_rn_score, 
      valid_live_new_aid_incm_amt_rn_next_score, valid_live_new_aid_incm_amt_trgt_diff, 
      valid_live_new_aid_incm_amt_diff, valid_live_new_aid_incm_amt_diff_score, 
      valid_live_new_aid_incm_amt_diff_next_score, valid_live_new_aid_incm_amt_diff_trgt_diff, 
      unvalid_live_new_aid_incm_amt, unvalid_live_new_aid_incm_amt_rn, 
      unvalid_live_new_aid_incm_amt_rn_score, unvalid_live_new_aid_incm_amt_rn_next_score, 
      unvalid_live_new_aid_incm_amt_trgt_diff, unvalid_live_new_aid_incm_amt_diff, 
      unvalid_live_new_aid_incm_amt_diff_score, unvalid_live_new_aid_incm_amt_diff_next_score, 
      unvalid_live_new_aid_incm_amt_diff_trgt_diff, unvalid_live_new_aid_incm_amt_rate_diff, 
      unvalid_live_new_aid_incm_amt_rate_diff_score, unvalid_live_new_aid_incm_amt_rate_diff_next_score, 
      unvalid_live_new_aid_incm_amt_rate_diff_trgt_diff, guild_health_point, 
      guild_health_point_score, guild_health_point_next_score, 
      guild_health_point_trgt_diff, dt)
    values (#{guildCmpOwnrId,jdbcType=BIGINT}, #{newAidRat34AidNum,jdbcType=BIGINT}, 
      #{newAidRat34AidNumRn,jdbcType=BIGINT}, #{newAidRat34AidNumRnScore,jdbcType=INTEGER}, 
      #{newAidRat34AidNumRnNextScore,jdbcType=INTEGER}, #{newAidRat34AidNumTrgtDiff,jdbcType=BIGINT}, 
      #{newAidRat34AidNumDiff,jdbcType=BIGINT}, #{newAidRat34AidNumDiffScore,jdbcType=INTEGER}, 
      #{newAidRat34AidNumDiffNextScore,jdbcType=INTEGER}, #{newAidRat34AidNumDiffTrgtDiff,jdbcType=BIGINT}, 
      #{upgrdHighAidNumDiff,jdbcType=INTEGER}, #{upgrdHighAidNumDiffScore,jdbcType=INTEGER}, 
      #{upgrdHighAidNumDiffNextScore,jdbcType=INTEGER}, #{upgrdHighAidNumDiffTrgtDiff,jdbcType=INTEGER}, 
      #{upgrdWaistAidNumDiff,jdbcType=INTEGER}, #{upgrdWaistAidNumDiffScore,jdbcType=INTEGER}, 
      #{upgrdWaistAidNumDiffNextScore,jdbcType=INTEGER}, #{upgrdWaistAidNumDiffTrgtDiff,jdbcType=INTEGER}, 
      #{avgHighValidLiveAidNum,jdbcType=DOUBLE}, #{avgHighValidLiveAidNumRn,jdbcType=BIGINT}, 
      #{avgHighValidLiveAidNumRnScore,jdbcType=INTEGER}, #{avgHighValidLiveAidNumRnNextScore,jdbcType=INTEGER}, 
      #{avgHighValidLiveAidNumTrgtDiff,jdbcType=DOUBLE}, #{avgHighValidLiveAidNumDiff,jdbcType=DOUBLE}, 
      #{avgHighValidLiveAidNumDiffScore,jdbcType=INTEGER}, #{avgHighValidLiveAidNumDiffNextScore,jdbcType=INTEGER}, 
      #{avgHighValidLiveAidNumDiffTrgtDiff,jdbcType=DOUBLE}, #{avgWaistValidLiveAidNum,jdbcType=DOUBLE}, 
      #{avgWaistValidLiveAidNumRn,jdbcType=BIGINT}, #{avgWaistValidLiveAidNumRnScore,jdbcType=INTEGER}, 
      #{avgWaistValidLiveAidNumRnNextScore,jdbcType=INTEGER}, #{avgWaistValidLiveAidNumTrgtDiff,jdbcType=DOUBLE}, 
      #{avgWaistValidLiveAidNumDiff,jdbcType=DOUBLE}, #{avgWaistValidLiveAidNumDiffScore,jdbcType=INTEGER}, 
      #{avgWaistValidLiveAidNumDiffNextScore,jdbcType=INTEGER}, #{avgWaistValidLiveAidNumDiffTrgtDiff,jdbcType=DOUBLE}, 
      #{avgNtoValidLiveAidRate,jdbcType=DOUBLE}, #{avgNtoValidLiveAidRateRn,jdbcType=BIGINT}, 
      #{avgNtoValidLiveAidRateRnScore,jdbcType=INTEGER}, #{avgNtoValidLiveAidRateRnNextScore,jdbcType=INTEGER}, 
      #{avgNtoValidLiveAidRateTrgtDiff,jdbcType=DOUBLE}, #{avgStockValidLiveAidRate,jdbcType=DOUBLE}, 
      #{avgStockValidLiveAidRateRn,jdbcType=BIGINT}, #{avgStockValidLiveAidRateRnScore,jdbcType=INTEGER}, 
      #{avgStockValidLiveAidRateRnNextScore,jdbcType=INTEGER}, #{avgStockValidLiveAidRateTrgtDiff,jdbcType=DOUBLE}, 
      #{newAuthGoldenAidNum,jdbcType=BIGINT}, #{newAuthGoldenAidNumRn,jdbcType=BIGINT}, 
      #{newAuthGoldenAidNumRnScore,jdbcType=INTEGER}, #{newAuthGoldenAidNumRnNextScore,jdbcType=INTEGER}, 
      #{newAuthGoldenAidNumTrgtDiff,jdbcType=BIGINT}, #{validLiveNewAidIncmAmt,jdbcType=DOUBLE}, 
      #{validLiveNewAidIncmAmtRn,jdbcType=BIGINT}, #{validLiveNewAidIncmAmtRnScore,jdbcType=INTEGER}, 
      #{validLiveNewAidIncmAmtRnNextScore,jdbcType=INTEGER}, #{validLiveNewAidIncmAmtTrgtDiff,jdbcType=DOUBLE}, 
      #{validLiveNewAidIncmAmtDiff,jdbcType=DOUBLE}, #{validLiveNewAidIncmAmtDiffScore,jdbcType=INTEGER}, 
      #{validLiveNewAidIncmAmtDiffNextScore,jdbcType=INTEGER}, #{validLiveNewAidIncmAmtDiffTrgtDiff,jdbcType=DOUBLE}, 
      #{unvalidLiveNewAidIncmAmt,jdbcType=DOUBLE}, #{unvalidLiveNewAidIncmAmtRn,jdbcType=BIGINT}, 
      #{unvalidLiveNewAidIncmAmtRnScore,jdbcType=INTEGER}, #{unvalidLiveNewAidIncmAmtRnNextScore,jdbcType=INTEGER}, 
      #{unvalidLiveNewAidIncmAmtTrgtDiff,jdbcType=DOUBLE}, #{unvalidLiveNewAidIncmAmtDiff,jdbcType=DOUBLE}, 
      #{unvalidLiveNewAidIncmAmtDiffScore,jdbcType=INTEGER}, #{unvalidLiveNewAidIncmAmtDiffNextScore,jdbcType=INTEGER}, 
      #{unvalidLiveNewAidIncmAmtDiffTrgtDiff,jdbcType=DOUBLE}, #{unvalidLiveNewAidIncmAmtRateDiff,jdbcType=DOUBLE}, 
      #{unvalidLiveNewAidIncmAmtRateDiffScore,jdbcType=INTEGER}, #{unvalidLiveNewAidIncmAmtRateDiffNextScore,jdbcType=INTEGER}, 
      #{unvalidLiveNewAidIncmAmtRateDiffTrgtDiff,jdbcType=DOUBLE}, #{guildHealthPoint,jdbcType=INTEGER}, 
      #{guildHealthPointScore,jdbcType=INTEGER}, #{guildHealthPointNextScore,jdbcType=INTEGER}, 
      #{guildHealthPointTrgtDiff,jdbcType=INTEGER}, #{dt,jdbcType=DATE})
  </insert>
  <insert id="insertSelective" parameterType="com.yy.yyzone.guildrank.db.gen.model.YyDmEntityGuildCmpHealthAnalysisExptDi" useGeneratedKeys="true" keyProperty="id" >
    insert into yy_dm_entity_guild_cmp_health_analysis_expt_di
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="guildCmpOwnrId != null" >
        guild_cmp_ownr_id,
      </if>
      <if test="newAidRat34AidNum != null" >
        new_aid_rat_3_4_aid_num,
      </if>
      <if test="newAidRat34AidNumRn != null" >
        new_aid_rat_3_4_aid_num_rn,
      </if>
      <if test="newAidRat34AidNumRnScore != null" >
        new_aid_rat_3_4_aid_num_rn_score,
      </if>
      <if test="newAidRat34AidNumRnNextScore != null" >
        new_aid_rat_3_4_aid_num_rn_next_score,
      </if>
      <if test="newAidRat34AidNumTrgtDiff != null" >
        new_aid_rat_3_4_aid_num_trgt_diff,
      </if>
      <if test="newAidRat34AidNumDiff != null" >
        new_aid_rat_3_4_aid_num_diff,
      </if>
      <if test="newAidRat34AidNumDiffScore != null" >
        new_aid_rat_3_4_aid_num_diff_score,
      </if>
      <if test="newAidRat34AidNumDiffNextScore != null" >
        new_aid_rat_3_4_aid_num_diff_next_score,
      </if>
      <if test="newAidRat34AidNumDiffTrgtDiff != null" >
        new_aid_rat_3_4_aid_num_diff_trgt_diff,
      </if>
      <if test="upgrdHighAidNumDiff != null" >
        upgrd_high_aid_num_diff,
      </if>
      <if test="upgrdHighAidNumDiffScore != null" >
        upgrd_high_aid_num_diff_score,
      </if>
      <if test="upgrdHighAidNumDiffNextScore != null" >
        upgrd_high_aid_num_diff_next_score,
      </if>
      <if test="upgrdHighAidNumDiffTrgtDiff != null" >
        upgrd_high_aid_num_diff_trgt_diff,
      </if>
      <if test="upgrdWaistAidNumDiff != null" >
        upgrd_waist_aid_num_diff,
      </if>
      <if test="upgrdWaistAidNumDiffScore != null" >
        upgrd_waist_aid_num_diff_score,
      </if>
      <if test="upgrdWaistAidNumDiffNextScore != null" >
        upgrd_waist_aid_num_diff_next_score,
      </if>
      <if test="upgrdWaistAidNumDiffTrgtDiff != null" >
        upgrd_waist_aid_num_diff_trgt_diff,
      </if>
      <if test="avgHighValidLiveAidNum != null" >
        avg_high_valid_live_aid_num,
      </if>
      <if test="avgHighValidLiveAidNumRn != null" >
        avg_high_valid_live_aid_num_rn,
      </if>
      <if test="avgHighValidLiveAidNumRnScore != null" >
        avg_high_valid_live_aid_num_rn_score,
      </if>
      <if test="avgHighValidLiveAidNumRnNextScore != null" >
        avg_high_valid_live_aid_num_rn_next_score,
      </if>
      <if test="avgHighValidLiveAidNumTrgtDiff != null" >
        avg_high_valid_live_aid_num_trgt_diff,
      </if>
      <if test="avgHighValidLiveAidNumDiff != null" >
        avg_high_valid_live_aid_num_diff,
      </if>
      <if test="avgHighValidLiveAidNumDiffScore != null" >
        avg_high_valid_live_aid_num_diff_score,
      </if>
      <if test="avgHighValidLiveAidNumDiffNextScore != null" >
        avg_high_valid_live_aid_num_diff_next_score,
      </if>
      <if test="avgHighValidLiveAidNumDiffTrgtDiff != null" >
        avg_high_valid_live_aid_num_diff_trgt_diff,
      </if>
      <if test="avgWaistValidLiveAidNum != null" >
        avg_waist_valid_live_aid_num,
      </if>
      <if test="avgWaistValidLiveAidNumRn != null" >
        avg_waist_valid_live_aid_num_rn,
      </if>
      <if test="avgWaistValidLiveAidNumRnScore != null" >
        avg_waist_valid_live_aid_num_rn_score,
      </if>
      <if test="avgWaistValidLiveAidNumRnNextScore != null" >
        avg_waist_valid_live_aid_num_rn_next_score,
      </if>
      <if test="avgWaistValidLiveAidNumTrgtDiff != null" >
        avg_waist_valid_live_aid_num_trgt_diff,
      </if>
      <if test="avgWaistValidLiveAidNumDiff != null" >
        avg_waist_valid_live_aid_num_diff,
      </if>
      <if test="avgWaistValidLiveAidNumDiffScore != null" >
        avg_waist_valid_live_aid_num_diff_score,
      </if>
      <if test="avgWaistValidLiveAidNumDiffNextScore != null" >
        avg_waist_valid_live_aid_num_diff_next_score,
      </if>
      <if test="avgWaistValidLiveAidNumDiffTrgtDiff != null" >
        avg_waist_valid_live_aid_num_diff_trgt_diff,
      </if>
      <if test="avgNtoValidLiveAidRate != null" >
        avg_nto_valid_live_aid_rate,
      </if>
      <if test="avgNtoValidLiveAidRateRn != null" >
        avg_nto_valid_live_aid_rate_rn,
      </if>
      <if test="avgNtoValidLiveAidRateRnScore != null" >
        avg_nto_valid_live_aid_rate_rn_score,
      </if>
      <if test="avgNtoValidLiveAidRateRnNextScore != null" >
        avg_nto_valid_live_aid_rate_rn_next_score,
      </if>
      <if test="avgNtoValidLiveAidRateTrgtDiff != null" >
        avg_nto_valid_live_aid_rate_trgt_diff,
      </if>
      <if test="avgStockValidLiveAidRate != null" >
        avg_stock_valid_live_aid_rate,
      </if>
      <if test="avgStockValidLiveAidRateRn != null" >
        avg_stock_valid_live_aid_rate_rn,
      </if>
      <if test="avgStockValidLiveAidRateRnScore != null" >
        avg_stock_valid_live_aid_rate_rn_score,
      </if>
      <if test="avgStockValidLiveAidRateRnNextScore != null" >
        avg_stock_valid_live_aid_rate_rn_next_score,
      </if>
      <if test="avgStockValidLiveAidRateTrgtDiff != null" >
        avg_stock_valid_live_aid_rate_trgt_diff,
      </if>
      <if test="newAuthGoldenAidNum != null" >
        new_auth_golden_aid_num,
      </if>
      <if test="newAuthGoldenAidNumRn != null" >
        new_auth_golden_aid_num_rn,
      </if>
      <if test="newAuthGoldenAidNumRnScore != null" >
        new_auth_golden_aid_num_rn_score,
      </if>
      <if test="newAuthGoldenAidNumRnNextScore != null" >
        new_auth_golden_aid_num_rn_next_score,
      </if>
      <if test="newAuthGoldenAidNumTrgtDiff != null" >
        new_auth_golden_aid_num_trgt_diff,
      </if>
      <if test="validLiveNewAidIncmAmt != null" >
        valid_live_new_aid_incm_amt,
      </if>
      <if test="validLiveNewAidIncmAmtRn != null" >
        valid_live_new_aid_incm_amt_rn,
      </if>
      <if test="validLiveNewAidIncmAmtRnScore != null" >
        valid_live_new_aid_incm_amt_rn_score,
      </if>
      <if test="validLiveNewAidIncmAmtRnNextScore != null" >
        valid_live_new_aid_incm_amt_rn_next_score,
      </if>
      <if test="validLiveNewAidIncmAmtTrgtDiff != null" >
        valid_live_new_aid_incm_amt_trgt_diff,
      </if>
      <if test="validLiveNewAidIncmAmtDiff != null" >
        valid_live_new_aid_incm_amt_diff,
      </if>
      <if test="validLiveNewAidIncmAmtDiffScore != null" >
        valid_live_new_aid_incm_amt_diff_score,
      </if>
      <if test="validLiveNewAidIncmAmtDiffNextScore != null" >
        valid_live_new_aid_incm_amt_diff_next_score,
      </if>
      <if test="validLiveNewAidIncmAmtDiffTrgtDiff != null" >
        valid_live_new_aid_incm_amt_diff_trgt_diff,
      </if>
      <if test="unvalidLiveNewAidIncmAmt != null" >
        unvalid_live_new_aid_incm_amt,
      </if>
      <if test="unvalidLiveNewAidIncmAmtRn != null" >
        unvalid_live_new_aid_incm_amt_rn,
      </if>
      <if test="unvalidLiveNewAidIncmAmtRnScore != null" >
        unvalid_live_new_aid_incm_amt_rn_score,
      </if>
      <if test="unvalidLiveNewAidIncmAmtRnNextScore != null" >
        unvalid_live_new_aid_incm_amt_rn_next_score,
      </if>
      <if test="unvalidLiveNewAidIncmAmtTrgtDiff != null" >
        unvalid_live_new_aid_incm_amt_trgt_diff,
      </if>
      <if test="unvalidLiveNewAidIncmAmtDiff != null" >
        unvalid_live_new_aid_incm_amt_diff,
      </if>
      <if test="unvalidLiveNewAidIncmAmtDiffScore != null" >
        unvalid_live_new_aid_incm_amt_diff_score,
      </if>
      <if test="unvalidLiveNewAidIncmAmtDiffNextScore != null" >
        unvalid_live_new_aid_incm_amt_diff_next_score,
      </if>
      <if test="unvalidLiveNewAidIncmAmtDiffTrgtDiff != null" >
        unvalid_live_new_aid_incm_amt_diff_trgt_diff,
      </if>
      <if test="unvalidLiveNewAidIncmAmtRateDiff != null" >
        unvalid_live_new_aid_incm_amt_rate_diff,
      </if>
      <if test="unvalidLiveNewAidIncmAmtRateDiffScore != null" >
        unvalid_live_new_aid_incm_amt_rate_diff_score,
      </if>
      <if test="unvalidLiveNewAidIncmAmtRateDiffNextScore != null" >
        unvalid_live_new_aid_incm_amt_rate_diff_next_score,
      </if>
      <if test="unvalidLiveNewAidIncmAmtRateDiffTrgtDiff != null" >
        unvalid_live_new_aid_incm_amt_rate_diff_trgt_diff,
      </if>
      <if test="guildHealthPoint != null" >
        guild_health_point,
      </if>
      <if test="guildHealthPointScore != null" >
        guild_health_point_score,
      </if>
      <if test="guildHealthPointNextScore != null" >
        guild_health_point_next_score,
      </if>
      <if test="guildHealthPointTrgtDiff != null" >
        guild_health_point_trgt_diff,
      </if>
      <if test="dt != null" >
        dt,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="guildCmpOwnrId != null" >
        #{guildCmpOwnrId,jdbcType=BIGINT},
      </if>
      <if test="newAidRat34AidNum != null" >
        #{newAidRat34AidNum,jdbcType=BIGINT},
      </if>
      <if test="newAidRat34AidNumRn != null" >
        #{newAidRat34AidNumRn,jdbcType=BIGINT},
      </if>
      <if test="newAidRat34AidNumRnScore != null" >
        #{newAidRat34AidNumRnScore,jdbcType=INTEGER},
      </if>
      <if test="newAidRat34AidNumRnNextScore != null" >
        #{newAidRat34AidNumRnNextScore,jdbcType=INTEGER},
      </if>
      <if test="newAidRat34AidNumTrgtDiff != null" >
        #{newAidRat34AidNumTrgtDiff,jdbcType=BIGINT},
      </if>
      <if test="newAidRat34AidNumDiff != null" >
        #{newAidRat34AidNumDiff,jdbcType=BIGINT},
      </if>
      <if test="newAidRat34AidNumDiffScore != null" >
        #{newAidRat34AidNumDiffScore,jdbcType=INTEGER},
      </if>
      <if test="newAidRat34AidNumDiffNextScore != null" >
        #{newAidRat34AidNumDiffNextScore,jdbcType=INTEGER},
      </if>
      <if test="newAidRat34AidNumDiffTrgtDiff != null" >
        #{newAidRat34AidNumDiffTrgtDiff,jdbcType=BIGINT},
      </if>
      <if test="upgrdHighAidNumDiff != null" >
        #{upgrdHighAidNumDiff,jdbcType=INTEGER},
      </if>
      <if test="upgrdHighAidNumDiffScore != null" >
        #{upgrdHighAidNumDiffScore,jdbcType=INTEGER},
      </if>
      <if test="upgrdHighAidNumDiffNextScore != null" >
        #{upgrdHighAidNumDiffNextScore,jdbcType=INTEGER},
      </if>
      <if test="upgrdHighAidNumDiffTrgtDiff != null" >
        #{upgrdHighAidNumDiffTrgtDiff,jdbcType=INTEGER},
      </if>
      <if test="upgrdWaistAidNumDiff != null" >
        #{upgrdWaistAidNumDiff,jdbcType=INTEGER},
      </if>
      <if test="upgrdWaistAidNumDiffScore != null" >
        #{upgrdWaistAidNumDiffScore,jdbcType=INTEGER},
      </if>
      <if test="upgrdWaistAidNumDiffNextScore != null" >
        #{upgrdWaistAidNumDiffNextScore,jdbcType=INTEGER},
      </if>
      <if test="upgrdWaistAidNumDiffTrgtDiff != null" >
        #{upgrdWaistAidNumDiffTrgtDiff,jdbcType=INTEGER},
      </if>
      <if test="avgHighValidLiveAidNum != null" >
        #{avgHighValidLiveAidNum,jdbcType=DOUBLE},
      </if>
      <if test="avgHighValidLiveAidNumRn != null" >
        #{avgHighValidLiveAidNumRn,jdbcType=BIGINT},
      </if>
      <if test="avgHighValidLiveAidNumRnScore != null" >
        #{avgHighValidLiveAidNumRnScore,jdbcType=INTEGER},
      </if>
      <if test="avgHighValidLiveAidNumRnNextScore != null" >
        #{avgHighValidLiveAidNumRnNextScore,jdbcType=INTEGER},
      </if>
      <if test="avgHighValidLiveAidNumTrgtDiff != null" >
        #{avgHighValidLiveAidNumTrgtDiff,jdbcType=DOUBLE},
      </if>
      <if test="avgHighValidLiveAidNumDiff != null" >
        #{avgHighValidLiveAidNumDiff,jdbcType=DOUBLE},
      </if>
      <if test="avgHighValidLiveAidNumDiffScore != null" >
        #{avgHighValidLiveAidNumDiffScore,jdbcType=INTEGER},
      </if>
      <if test="avgHighValidLiveAidNumDiffNextScore != null" >
        #{avgHighValidLiveAidNumDiffNextScore,jdbcType=INTEGER},
      </if>
      <if test="avgHighValidLiveAidNumDiffTrgtDiff != null" >
        #{avgHighValidLiveAidNumDiffTrgtDiff,jdbcType=DOUBLE},
      </if>
      <if test="avgWaistValidLiveAidNum != null" >
        #{avgWaistValidLiveAidNum,jdbcType=DOUBLE},
      </if>
      <if test="avgWaistValidLiveAidNumRn != null" >
        #{avgWaistValidLiveAidNumRn,jdbcType=BIGINT},
      </if>
      <if test="avgWaistValidLiveAidNumRnScore != null" >
        #{avgWaistValidLiveAidNumRnScore,jdbcType=INTEGER},
      </if>
      <if test="avgWaistValidLiveAidNumRnNextScore != null" >
        #{avgWaistValidLiveAidNumRnNextScore,jdbcType=INTEGER},
      </if>
      <if test="avgWaistValidLiveAidNumTrgtDiff != null" >
        #{avgWaistValidLiveAidNumTrgtDiff,jdbcType=DOUBLE},
      </if>
      <if test="avgWaistValidLiveAidNumDiff != null" >
        #{avgWaistValidLiveAidNumDiff,jdbcType=DOUBLE},
      </if>
      <if test="avgWaistValidLiveAidNumDiffScore != null" >
        #{avgWaistValidLiveAidNumDiffScore,jdbcType=INTEGER},
      </if>
      <if test="avgWaistValidLiveAidNumDiffNextScore != null" >
        #{avgWaistValidLiveAidNumDiffNextScore,jdbcType=INTEGER},
      </if>
      <if test="avgWaistValidLiveAidNumDiffTrgtDiff != null" >
        #{avgWaistValidLiveAidNumDiffTrgtDiff,jdbcType=DOUBLE},
      </if>
      <if test="avgNtoValidLiveAidRate != null" >
        #{avgNtoValidLiveAidRate,jdbcType=DOUBLE},
      </if>
      <if test="avgNtoValidLiveAidRateRn != null" >
        #{avgNtoValidLiveAidRateRn,jdbcType=BIGINT},
      </if>
      <if test="avgNtoValidLiveAidRateRnScore != null" >
        #{avgNtoValidLiveAidRateRnScore,jdbcType=INTEGER},
      </if>
      <if test="avgNtoValidLiveAidRateRnNextScore != null" >
        #{avgNtoValidLiveAidRateRnNextScore,jdbcType=INTEGER},
      </if>
      <if test="avgNtoValidLiveAidRateTrgtDiff != null" >
        #{avgNtoValidLiveAidRateTrgtDiff,jdbcType=DOUBLE},
      </if>
      <if test="avgStockValidLiveAidRate != null" >
        #{avgStockValidLiveAidRate,jdbcType=DOUBLE},
      </if>
      <if test="avgStockValidLiveAidRateRn != null" >
        #{avgStockValidLiveAidRateRn,jdbcType=BIGINT},
      </if>
      <if test="avgStockValidLiveAidRateRnScore != null" >
        #{avgStockValidLiveAidRateRnScore,jdbcType=INTEGER},
      </if>
      <if test="avgStockValidLiveAidRateRnNextScore != null" >
        #{avgStockValidLiveAidRateRnNextScore,jdbcType=INTEGER},
      </if>
      <if test="avgStockValidLiveAidRateTrgtDiff != null" >
        #{avgStockValidLiveAidRateTrgtDiff,jdbcType=DOUBLE},
      </if>
      <if test="newAuthGoldenAidNum != null" >
        #{newAuthGoldenAidNum,jdbcType=BIGINT},
      </if>
      <if test="newAuthGoldenAidNumRn != null" >
        #{newAuthGoldenAidNumRn,jdbcType=BIGINT},
      </if>
      <if test="newAuthGoldenAidNumRnScore != null" >
        #{newAuthGoldenAidNumRnScore,jdbcType=INTEGER},
      </if>
      <if test="newAuthGoldenAidNumRnNextScore != null" >
        #{newAuthGoldenAidNumRnNextScore,jdbcType=INTEGER},
      </if>
      <if test="newAuthGoldenAidNumTrgtDiff != null" >
        #{newAuthGoldenAidNumTrgtDiff,jdbcType=BIGINT},
      </if>
      <if test="validLiveNewAidIncmAmt != null" >
        #{validLiveNewAidIncmAmt,jdbcType=DOUBLE},
      </if>
      <if test="validLiveNewAidIncmAmtRn != null" >
        #{validLiveNewAidIncmAmtRn,jdbcType=BIGINT},
      </if>
      <if test="validLiveNewAidIncmAmtRnScore != null" >
        #{validLiveNewAidIncmAmtRnScore,jdbcType=INTEGER},
      </if>
      <if test="validLiveNewAidIncmAmtRnNextScore != null" >
        #{validLiveNewAidIncmAmtRnNextScore,jdbcType=INTEGER},
      </if>
      <if test="validLiveNewAidIncmAmtTrgtDiff != null" >
        #{validLiveNewAidIncmAmtTrgtDiff,jdbcType=DOUBLE},
      </if>
      <if test="validLiveNewAidIncmAmtDiff != null" >
        #{validLiveNewAidIncmAmtDiff,jdbcType=DOUBLE},
      </if>
      <if test="validLiveNewAidIncmAmtDiffScore != null" >
        #{validLiveNewAidIncmAmtDiffScore,jdbcType=INTEGER},
      </if>
      <if test="validLiveNewAidIncmAmtDiffNextScore != null" >
        #{validLiveNewAidIncmAmtDiffNextScore,jdbcType=INTEGER},
      </if>
      <if test="validLiveNewAidIncmAmtDiffTrgtDiff != null" >
        #{validLiveNewAidIncmAmtDiffTrgtDiff,jdbcType=DOUBLE},
      </if>
      <if test="unvalidLiveNewAidIncmAmt != null" >
        #{unvalidLiveNewAidIncmAmt,jdbcType=DOUBLE},
      </if>
      <if test="unvalidLiveNewAidIncmAmtRn != null" >
        #{unvalidLiveNewAidIncmAmtRn,jdbcType=BIGINT},
      </if>
      <if test="unvalidLiveNewAidIncmAmtRnScore != null" >
        #{unvalidLiveNewAidIncmAmtRnScore,jdbcType=INTEGER},
      </if>
      <if test="unvalidLiveNewAidIncmAmtRnNextScore != null" >
        #{unvalidLiveNewAidIncmAmtRnNextScore,jdbcType=INTEGER},
      </if>
      <if test="unvalidLiveNewAidIncmAmtTrgtDiff != null" >
        #{unvalidLiveNewAidIncmAmtTrgtDiff,jdbcType=DOUBLE},
      </if>
      <if test="unvalidLiveNewAidIncmAmtDiff != null" >
        #{unvalidLiveNewAidIncmAmtDiff,jdbcType=DOUBLE},
      </if>
      <if test="unvalidLiveNewAidIncmAmtDiffScore != null" >
        #{unvalidLiveNewAidIncmAmtDiffScore,jdbcType=INTEGER},
      </if>
      <if test="unvalidLiveNewAidIncmAmtDiffNextScore != null" >
        #{unvalidLiveNewAidIncmAmtDiffNextScore,jdbcType=INTEGER},
      </if>
      <if test="unvalidLiveNewAidIncmAmtDiffTrgtDiff != null" >
        #{unvalidLiveNewAidIncmAmtDiffTrgtDiff,jdbcType=DOUBLE},
      </if>
      <if test="unvalidLiveNewAidIncmAmtRateDiff != null" >
        #{unvalidLiveNewAidIncmAmtRateDiff,jdbcType=DOUBLE},
      </if>
      <if test="unvalidLiveNewAidIncmAmtRateDiffScore != null" >
        #{unvalidLiveNewAidIncmAmtRateDiffScore,jdbcType=INTEGER},
      </if>
      <if test="unvalidLiveNewAidIncmAmtRateDiffNextScore != null" >
        #{unvalidLiveNewAidIncmAmtRateDiffNextScore,jdbcType=INTEGER},
      </if>
      <if test="unvalidLiveNewAidIncmAmtRateDiffTrgtDiff != null" >
        #{unvalidLiveNewAidIncmAmtRateDiffTrgtDiff,jdbcType=DOUBLE},
      </if>
      <if test="guildHealthPoint != null" >
        #{guildHealthPoint,jdbcType=INTEGER},
      </if>
      <if test="guildHealthPointScore != null" >
        #{guildHealthPointScore,jdbcType=INTEGER},
      </if>
      <if test="guildHealthPointNextScore != null" >
        #{guildHealthPointNextScore,jdbcType=INTEGER},
      </if>
      <if test="guildHealthPointTrgtDiff != null" >
        #{guildHealthPointTrgtDiff,jdbcType=INTEGER},
      </if>
      <if test="dt != null" >
        #{dt,jdbcType=DATE},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.yy.yyzone.guildrank.db.gen.model.YyDmEntityGuildCmpHealthAnalysisExptDiExample" resultType="java.lang.Integer" >
    select count(*) from yy_dm_entity_guild_cmp_health_analysis_expt_di
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update yy_dm_entity_guild_cmp_health_analysis_expt_di
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.guildCmpOwnrId != null" >
        guild_cmp_ownr_id = #{record.guildCmpOwnrId,jdbcType=BIGINT},
      </if>
      <if test="record.newAidRat34AidNum != null" >
        new_aid_rat_3_4_aid_num = #{record.newAidRat34AidNum,jdbcType=BIGINT},
      </if>
      <if test="record.newAidRat34AidNumRn != null" >
        new_aid_rat_3_4_aid_num_rn = #{record.newAidRat34AidNumRn,jdbcType=BIGINT},
      </if>
      <if test="record.newAidRat34AidNumRnScore != null" >
        new_aid_rat_3_4_aid_num_rn_score = #{record.newAidRat34AidNumRnScore,jdbcType=INTEGER},
      </if>
      <if test="record.newAidRat34AidNumRnNextScore != null" >
        new_aid_rat_3_4_aid_num_rn_next_score = #{record.newAidRat34AidNumRnNextScore,jdbcType=INTEGER},
      </if>
      <if test="record.newAidRat34AidNumTrgtDiff != null" >
        new_aid_rat_3_4_aid_num_trgt_diff = #{record.newAidRat34AidNumTrgtDiff,jdbcType=BIGINT},
      </if>
      <if test="record.newAidRat34AidNumDiff != null" >
        new_aid_rat_3_4_aid_num_diff = #{record.newAidRat34AidNumDiff,jdbcType=BIGINT},
      </if>
      <if test="record.newAidRat34AidNumDiffScore != null" >
        new_aid_rat_3_4_aid_num_diff_score = #{record.newAidRat34AidNumDiffScore,jdbcType=INTEGER},
      </if>
      <if test="record.newAidRat34AidNumDiffNextScore != null" >
        new_aid_rat_3_4_aid_num_diff_next_score = #{record.newAidRat34AidNumDiffNextScore,jdbcType=INTEGER},
      </if>
      <if test="record.newAidRat34AidNumDiffTrgtDiff != null" >
        new_aid_rat_3_4_aid_num_diff_trgt_diff = #{record.newAidRat34AidNumDiffTrgtDiff,jdbcType=BIGINT},
      </if>
      <if test="record.upgrdHighAidNumDiff != null" >
        upgrd_high_aid_num_diff = #{record.upgrdHighAidNumDiff,jdbcType=INTEGER},
      </if>
      <if test="record.upgrdHighAidNumDiffScore != null" >
        upgrd_high_aid_num_diff_score = #{record.upgrdHighAidNumDiffScore,jdbcType=INTEGER},
      </if>
      <if test="record.upgrdHighAidNumDiffNextScore != null" >
        upgrd_high_aid_num_diff_next_score = #{record.upgrdHighAidNumDiffNextScore,jdbcType=INTEGER},
      </if>
      <if test="record.upgrdHighAidNumDiffTrgtDiff != null" >
        upgrd_high_aid_num_diff_trgt_diff = #{record.upgrdHighAidNumDiffTrgtDiff,jdbcType=INTEGER},
      </if>
      <if test="record.upgrdWaistAidNumDiff != null" >
        upgrd_waist_aid_num_diff = #{record.upgrdWaistAidNumDiff,jdbcType=INTEGER},
      </if>
      <if test="record.upgrdWaistAidNumDiffScore != null" >
        upgrd_waist_aid_num_diff_score = #{record.upgrdWaistAidNumDiffScore,jdbcType=INTEGER},
      </if>
      <if test="record.upgrdWaistAidNumDiffNextScore != null" >
        upgrd_waist_aid_num_diff_next_score = #{record.upgrdWaistAidNumDiffNextScore,jdbcType=INTEGER},
      </if>
      <if test="record.upgrdWaistAidNumDiffTrgtDiff != null" >
        upgrd_waist_aid_num_diff_trgt_diff = #{record.upgrdWaistAidNumDiffTrgtDiff,jdbcType=INTEGER},
      </if>
      <if test="record.avgHighValidLiveAidNum != null" >
        avg_high_valid_live_aid_num = #{record.avgHighValidLiveAidNum,jdbcType=DOUBLE},
      </if>
      <if test="record.avgHighValidLiveAidNumRn != null" >
        avg_high_valid_live_aid_num_rn = #{record.avgHighValidLiveAidNumRn,jdbcType=BIGINT},
      </if>
      <if test="record.avgHighValidLiveAidNumRnScore != null" >
        avg_high_valid_live_aid_num_rn_score = #{record.avgHighValidLiveAidNumRnScore,jdbcType=INTEGER},
      </if>
      <if test="record.avgHighValidLiveAidNumRnNextScore != null" >
        avg_high_valid_live_aid_num_rn_next_score = #{record.avgHighValidLiveAidNumRnNextScore,jdbcType=INTEGER},
      </if>
      <if test="record.avgHighValidLiveAidNumTrgtDiff != null" >
        avg_high_valid_live_aid_num_trgt_diff = #{record.avgHighValidLiveAidNumTrgtDiff,jdbcType=DOUBLE},
      </if>
      <if test="record.avgHighValidLiveAidNumDiff != null" >
        avg_high_valid_live_aid_num_diff = #{record.avgHighValidLiveAidNumDiff,jdbcType=DOUBLE},
      </if>
      <if test="record.avgHighValidLiveAidNumDiffScore != null" >
        avg_high_valid_live_aid_num_diff_score = #{record.avgHighValidLiveAidNumDiffScore,jdbcType=INTEGER},
      </if>
      <if test="record.avgHighValidLiveAidNumDiffNextScore != null" >
        avg_high_valid_live_aid_num_diff_next_score = #{record.avgHighValidLiveAidNumDiffNextScore,jdbcType=INTEGER},
      </if>
      <if test="record.avgHighValidLiveAidNumDiffTrgtDiff != null" >
        avg_high_valid_live_aid_num_diff_trgt_diff = #{record.avgHighValidLiveAidNumDiffTrgtDiff,jdbcType=DOUBLE},
      </if>
      <if test="record.avgWaistValidLiveAidNum != null" >
        avg_waist_valid_live_aid_num = #{record.avgWaistValidLiveAidNum,jdbcType=DOUBLE},
      </if>
      <if test="record.avgWaistValidLiveAidNumRn != null" >
        avg_waist_valid_live_aid_num_rn = #{record.avgWaistValidLiveAidNumRn,jdbcType=BIGINT},
      </if>
      <if test="record.avgWaistValidLiveAidNumRnScore != null" >
        avg_waist_valid_live_aid_num_rn_score = #{record.avgWaistValidLiveAidNumRnScore,jdbcType=INTEGER},
      </if>
      <if test="record.avgWaistValidLiveAidNumRnNextScore != null" >
        avg_waist_valid_live_aid_num_rn_next_score = #{record.avgWaistValidLiveAidNumRnNextScore,jdbcType=INTEGER},
      </if>
      <if test="record.avgWaistValidLiveAidNumTrgtDiff != null" >
        avg_waist_valid_live_aid_num_trgt_diff = #{record.avgWaistValidLiveAidNumTrgtDiff,jdbcType=DOUBLE},
      </if>
      <if test="record.avgWaistValidLiveAidNumDiff != null" >
        avg_waist_valid_live_aid_num_diff = #{record.avgWaistValidLiveAidNumDiff,jdbcType=DOUBLE},
      </if>
      <if test="record.avgWaistValidLiveAidNumDiffScore != null" >
        avg_waist_valid_live_aid_num_diff_score = #{record.avgWaistValidLiveAidNumDiffScore,jdbcType=INTEGER},
      </if>
      <if test="record.avgWaistValidLiveAidNumDiffNextScore != null" >
        avg_waist_valid_live_aid_num_diff_next_score = #{record.avgWaistValidLiveAidNumDiffNextScore,jdbcType=INTEGER},
      </if>
      <if test="record.avgWaistValidLiveAidNumDiffTrgtDiff != null" >
        avg_waist_valid_live_aid_num_diff_trgt_diff = #{record.avgWaistValidLiveAidNumDiffTrgtDiff,jdbcType=DOUBLE},
      </if>
      <if test="record.avgNtoValidLiveAidRate != null" >
        avg_nto_valid_live_aid_rate = #{record.avgNtoValidLiveAidRate,jdbcType=DOUBLE},
      </if>
      <if test="record.avgNtoValidLiveAidRateRn != null" >
        avg_nto_valid_live_aid_rate_rn = #{record.avgNtoValidLiveAidRateRn,jdbcType=BIGINT},
      </if>
      <if test="record.avgNtoValidLiveAidRateRnScore != null" >
        avg_nto_valid_live_aid_rate_rn_score = #{record.avgNtoValidLiveAidRateRnScore,jdbcType=INTEGER},
      </if>
      <if test="record.avgNtoValidLiveAidRateRnNextScore != null" >
        avg_nto_valid_live_aid_rate_rn_next_score = #{record.avgNtoValidLiveAidRateRnNextScore,jdbcType=INTEGER},
      </if>
      <if test="record.avgNtoValidLiveAidRateTrgtDiff != null" >
        avg_nto_valid_live_aid_rate_trgt_diff = #{record.avgNtoValidLiveAidRateTrgtDiff,jdbcType=DOUBLE},
      </if>
      <if test="record.avgStockValidLiveAidRate != null" >
        avg_stock_valid_live_aid_rate = #{record.avgStockValidLiveAidRate,jdbcType=DOUBLE},
      </if>
      <if test="record.avgStockValidLiveAidRateRn != null" >
        avg_stock_valid_live_aid_rate_rn = #{record.avgStockValidLiveAidRateRn,jdbcType=BIGINT},
      </if>
      <if test="record.avgStockValidLiveAidRateRnScore != null" >
        avg_stock_valid_live_aid_rate_rn_score = #{record.avgStockValidLiveAidRateRnScore,jdbcType=INTEGER},
      </if>
      <if test="record.avgStockValidLiveAidRateRnNextScore != null" >
        avg_stock_valid_live_aid_rate_rn_next_score = #{record.avgStockValidLiveAidRateRnNextScore,jdbcType=INTEGER},
      </if>
      <if test="record.avgStockValidLiveAidRateTrgtDiff != null" >
        avg_stock_valid_live_aid_rate_trgt_diff = #{record.avgStockValidLiveAidRateTrgtDiff,jdbcType=DOUBLE},
      </if>
      <if test="record.newAuthGoldenAidNum != null" >
        new_auth_golden_aid_num = #{record.newAuthGoldenAidNum,jdbcType=BIGINT},
      </if>
      <if test="record.newAuthGoldenAidNumRn != null" >
        new_auth_golden_aid_num_rn = #{record.newAuthGoldenAidNumRn,jdbcType=BIGINT},
      </if>
      <if test="record.newAuthGoldenAidNumRnScore != null" >
        new_auth_golden_aid_num_rn_score = #{record.newAuthGoldenAidNumRnScore,jdbcType=INTEGER},
      </if>
      <if test="record.newAuthGoldenAidNumRnNextScore != null" >
        new_auth_golden_aid_num_rn_next_score = #{record.newAuthGoldenAidNumRnNextScore,jdbcType=INTEGER},
      </if>
      <if test="record.newAuthGoldenAidNumTrgtDiff != null" >
        new_auth_golden_aid_num_trgt_diff = #{record.newAuthGoldenAidNumTrgtDiff,jdbcType=BIGINT},
      </if>
      <if test="record.validLiveNewAidIncmAmt != null" >
        valid_live_new_aid_incm_amt = #{record.validLiveNewAidIncmAmt,jdbcType=DOUBLE},
      </if>
      <if test="record.validLiveNewAidIncmAmtRn != null" >
        valid_live_new_aid_incm_amt_rn = #{record.validLiveNewAidIncmAmtRn,jdbcType=BIGINT},
      </if>
      <if test="record.validLiveNewAidIncmAmtRnScore != null" >
        valid_live_new_aid_incm_amt_rn_score = #{record.validLiveNewAidIncmAmtRnScore,jdbcType=INTEGER},
      </if>
      <if test="record.validLiveNewAidIncmAmtRnNextScore != null" >
        valid_live_new_aid_incm_amt_rn_next_score = #{record.validLiveNewAidIncmAmtRnNextScore,jdbcType=INTEGER},
      </if>
      <if test="record.validLiveNewAidIncmAmtTrgtDiff != null" >
        valid_live_new_aid_incm_amt_trgt_diff = #{record.validLiveNewAidIncmAmtTrgtDiff,jdbcType=DOUBLE},
      </if>
      <if test="record.validLiveNewAidIncmAmtDiff != null" >
        valid_live_new_aid_incm_amt_diff = #{record.validLiveNewAidIncmAmtDiff,jdbcType=DOUBLE},
      </if>
      <if test="record.validLiveNewAidIncmAmtDiffScore != null" >
        valid_live_new_aid_incm_amt_diff_score = #{record.validLiveNewAidIncmAmtDiffScore,jdbcType=INTEGER},
      </if>
      <if test="record.validLiveNewAidIncmAmtDiffNextScore != null" >
        valid_live_new_aid_incm_amt_diff_next_score = #{record.validLiveNewAidIncmAmtDiffNextScore,jdbcType=INTEGER},
      </if>
      <if test="record.validLiveNewAidIncmAmtDiffTrgtDiff != null" >
        valid_live_new_aid_incm_amt_diff_trgt_diff = #{record.validLiveNewAidIncmAmtDiffTrgtDiff,jdbcType=DOUBLE},
      </if>
      <if test="record.unvalidLiveNewAidIncmAmt != null" >
        unvalid_live_new_aid_incm_amt = #{record.unvalidLiveNewAidIncmAmt,jdbcType=DOUBLE},
      </if>
      <if test="record.unvalidLiveNewAidIncmAmtRn != null" >
        unvalid_live_new_aid_incm_amt_rn = #{record.unvalidLiveNewAidIncmAmtRn,jdbcType=BIGINT},
      </if>
      <if test="record.unvalidLiveNewAidIncmAmtRnScore != null" >
        unvalid_live_new_aid_incm_amt_rn_score = #{record.unvalidLiveNewAidIncmAmtRnScore,jdbcType=INTEGER},
      </if>
      <if test="record.unvalidLiveNewAidIncmAmtRnNextScore != null" >
        unvalid_live_new_aid_incm_amt_rn_next_score = #{record.unvalidLiveNewAidIncmAmtRnNextScore,jdbcType=INTEGER},
      </if>
      <if test="record.unvalidLiveNewAidIncmAmtTrgtDiff != null" >
        unvalid_live_new_aid_incm_amt_trgt_diff = #{record.unvalidLiveNewAidIncmAmtTrgtDiff,jdbcType=DOUBLE},
      </if>
      <if test="record.unvalidLiveNewAidIncmAmtDiff != null" >
        unvalid_live_new_aid_incm_amt_diff = #{record.unvalidLiveNewAidIncmAmtDiff,jdbcType=DOUBLE},
      </if>
      <if test="record.unvalidLiveNewAidIncmAmtDiffScore != null" >
        unvalid_live_new_aid_incm_amt_diff_score = #{record.unvalidLiveNewAidIncmAmtDiffScore,jdbcType=INTEGER},
      </if>
      <if test="record.unvalidLiveNewAidIncmAmtDiffNextScore != null" >
        unvalid_live_new_aid_incm_amt_diff_next_score = #{record.unvalidLiveNewAidIncmAmtDiffNextScore,jdbcType=INTEGER},
      </if>
      <if test="record.unvalidLiveNewAidIncmAmtDiffTrgtDiff != null" >
        unvalid_live_new_aid_incm_amt_diff_trgt_diff = #{record.unvalidLiveNewAidIncmAmtDiffTrgtDiff,jdbcType=DOUBLE},
      </if>
      <if test="record.unvalidLiveNewAidIncmAmtRateDiff != null" >
        unvalid_live_new_aid_incm_amt_rate_diff = #{record.unvalidLiveNewAidIncmAmtRateDiff,jdbcType=DOUBLE},
      </if>
      <if test="record.unvalidLiveNewAidIncmAmtRateDiffScore != null" >
        unvalid_live_new_aid_incm_amt_rate_diff_score = #{record.unvalidLiveNewAidIncmAmtRateDiffScore,jdbcType=INTEGER},
      </if>
      <if test="record.unvalidLiveNewAidIncmAmtRateDiffNextScore != null" >
        unvalid_live_new_aid_incm_amt_rate_diff_next_score = #{record.unvalidLiveNewAidIncmAmtRateDiffNextScore,jdbcType=INTEGER},
      </if>
      <if test="record.unvalidLiveNewAidIncmAmtRateDiffTrgtDiff != null" >
        unvalid_live_new_aid_incm_amt_rate_diff_trgt_diff = #{record.unvalidLiveNewAidIncmAmtRateDiffTrgtDiff,jdbcType=DOUBLE},
      </if>
      <if test="record.guildHealthPoint != null" >
        guild_health_point = #{record.guildHealthPoint,jdbcType=INTEGER},
      </if>
      <if test="record.guildHealthPointScore != null" >
        guild_health_point_score = #{record.guildHealthPointScore,jdbcType=INTEGER},
      </if>
      <if test="record.guildHealthPointNextScore != null" >
        guild_health_point_next_score = #{record.guildHealthPointNextScore,jdbcType=INTEGER},
      </if>
      <if test="record.guildHealthPointTrgtDiff != null" >
        guild_health_point_trgt_diff = #{record.guildHealthPointTrgtDiff,jdbcType=INTEGER},
      </if>
      <if test="record.dt != null" >
        dt = #{record.dt,jdbcType=DATE},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update yy_dm_entity_guild_cmp_health_analysis_expt_di
    set id = #{record.id,jdbcType=BIGINT},
      guild_cmp_ownr_id = #{record.guildCmpOwnrId,jdbcType=BIGINT},
      new_aid_rat_3_4_aid_num = #{record.newAidRat34AidNum,jdbcType=BIGINT},
      new_aid_rat_3_4_aid_num_rn = #{record.newAidRat34AidNumRn,jdbcType=BIGINT},
      new_aid_rat_3_4_aid_num_rn_score = #{record.newAidRat34AidNumRnScore,jdbcType=INTEGER},
      new_aid_rat_3_4_aid_num_rn_next_score = #{record.newAidRat34AidNumRnNextScore,jdbcType=INTEGER},
      new_aid_rat_3_4_aid_num_trgt_diff = #{record.newAidRat34AidNumTrgtDiff,jdbcType=BIGINT},
      new_aid_rat_3_4_aid_num_diff = #{record.newAidRat34AidNumDiff,jdbcType=BIGINT},
      new_aid_rat_3_4_aid_num_diff_score = #{record.newAidRat34AidNumDiffScore,jdbcType=INTEGER},
      new_aid_rat_3_4_aid_num_diff_next_score = #{record.newAidRat34AidNumDiffNextScore,jdbcType=INTEGER},
      new_aid_rat_3_4_aid_num_diff_trgt_diff = #{record.newAidRat34AidNumDiffTrgtDiff,jdbcType=BIGINT},
      upgrd_high_aid_num_diff = #{record.upgrdHighAidNumDiff,jdbcType=INTEGER},
      upgrd_high_aid_num_diff_score = #{record.upgrdHighAidNumDiffScore,jdbcType=INTEGER},
      upgrd_high_aid_num_diff_next_score = #{record.upgrdHighAidNumDiffNextScore,jdbcType=INTEGER},
      upgrd_high_aid_num_diff_trgt_diff = #{record.upgrdHighAidNumDiffTrgtDiff,jdbcType=INTEGER},
      upgrd_waist_aid_num_diff = #{record.upgrdWaistAidNumDiff,jdbcType=INTEGER},
      upgrd_waist_aid_num_diff_score = #{record.upgrdWaistAidNumDiffScore,jdbcType=INTEGER},
      upgrd_waist_aid_num_diff_next_score = #{record.upgrdWaistAidNumDiffNextScore,jdbcType=INTEGER},
      upgrd_waist_aid_num_diff_trgt_diff = #{record.upgrdWaistAidNumDiffTrgtDiff,jdbcType=INTEGER},
      avg_high_valid_live_aid_num = #{record.avgHighValidLiveAidNum,jdbcType=DOUBLE},
      avg_high_valid_live_aid_num_rn = #{record.avgHighValidLiveAidNumRn,jdbcType=BIGINT},
      avg_high_valid_live_aid_num_rn_score = #{record.avgHighValidLiveAidNumRnScore,jdbcType=INTEGER},
      avg_high_valid_live_aid_num_rn_next_score = #{record.avgHighValidLiveAidNumRnNextScore,jdbcType=INTEGER},
      avg_high_valid_live_aid_num_trgt_diff = #{record.avgHighValidLiveAidNumTrgtDiff,jdbcType=DOUBLE},
      avg_high_valid_live_aid_num_diff = #{record.avgHighValidLiveAidNumDiff,jdbcType=DOUBLE},
      avg_high_valid_live_aid_num_diff_score = #{record.avgHighValidLiveAidNumDiffScore,jdbcType=INTEGER},
      avg_high_valid_live_aid_num_diff_next_score = #{record.avgHighValidLiveAidNumDiffNextScore,jdbcType=INTEGER},
      avg_high_valid_live_aid_num_diff_trgt_diff = #{record.avgHighValidLiveAidNumDiffTrgtDiff,jdbcType=DOUBLE},
      avg_waist_valid_live_aid_num = #{record.avgWaistValidLiveAidNum,jdbcType=DOUBLE},
      avg_waist_valid_live_aid_num_rn = #{record.avgWaistValidLiveAidNumRn,jdbcType=BIGINT},
      avg_waist_valid_live_aid_num_rn_score = #{record.avgWaistValidLiveAidNumRnScore,jdbcType=INTEGER},
      avg_waist_valid_live_aid_num_rn_next_score = #{record.avgWaistValidLiveAidNumRnNextScore,jdbcType=INTEGER},
      avg_waist_valid_live_aid_num_trgt_diff = #{record.avgWaistValidLiveAidNumTrgtDiff,jdbcType=DOUBLE},
      avg_waist_valid_live_aid_num_diff = #{record.avgWaistValidLiveAidNumDiff,jdbcType=DOUBLE},
      avg_waist_valid_live_aid_num_diff_score = #{record.avgWaistValidLiveAidNumDiffScore,jdbcType=INTEGER},
      avg_waist_valid_live_aid_num_diff_next_score = #{record.avgWaistValidLiveAidNumDiffNextScore,jdbcType=INTEGER},
      avg_waist_valid_live_aid_num_diff_trgt_diff = #{record.avgWaistValidLiveAidNumDiffTrgtDiff,jdbcType=DOUBLE},
      avg_nto_valid_live_aid_rate = #{record.avgNtoValidLiveAidRate,jdbcType=DOUBLE},
      avg_nto_valid_live_aid_rate_rn = #{record.avgNtoValidLiveAidRateRn,jdbcType=BIGINT},
      avg_nto_valid_live_aid_rate_rn_score = #{record.avgNtoValidLiveAidRateRnScore,jdbcType=INTEGER},
      avg_nto_valid_live_aid_rate_rn_next_score = #{record.avgNtoValidLiveAidRateRnNextScore,jdbcType=INTEGER},
      avg_nto_valid_live_aid_rate_trgt_diff = #{record.avgNtoValidLiveAidRateTrgtDiff,jdbcType=DOUBLE},
      avg_stock_valid_live_aid_rate = #{record.avgStockValidLiveAidRate,jdbcType=DOUBLE},
      avg_stock_valid_live_aid_rate_rn = #{record.avgStockValidLiveAidRateRn,jdbcType=BIGINT},
      avg_stock_valid_live_aid_rate_rn_score = #{record.avgStockValidLiveAidRateRnScore,jdbcType=INTEGER},
      avg_stock_valid_live_aid_rate_rn_next_score = #{record.avgStockValidLiveAidRateRnNextScore,jdbcType=INTEGER},
      avg_stock_valid_live_aid_rate_trgt_diff = #{record.avgStockValidLiveAidRateTrgtDiff,jdbcType=DOUBLE},
      new_auth_golden_aid_num = #{record.newAuthGoldenAidNum,jdbcType=BIGINT},
      new_auth_golden_aid_num_rn = #{record.newAuthGoldenAidNumRn,jdbcType=BIGINT},
      new_auth_golden_aid_num_rn_score = #{record.newAuthGoldenAidNumRnScore,jdbcType=INTEGER},
      new_auth_golden_aid_num_rn_next_score = #{record.newAuthGoldenAidNumRnNextScore,jdbcType=INTEGER},
      new_auth_golden_aid_num_trgt_diff = #{record.newAuthGoldenAidNumTrgtDiff,jdbcType=BIGINT},
      valid_live_new_aid_incm_amt = #{record.validLiveNewAidIncmAmt,jdbcType=DOUBLE},
      valid_live_new_aid_incm_amt_rn = #{record.validLiveNewAidIncmAmtRn,jdbcType=BIGINT},
      valid_live_new_aid_incm_amt_rn_score = #{record.validLiveNewAidIncmAmtRnScore,jdbcType=INTEGER},
      valid_live_new_aid_incm_amt_rn_next_score = #{record.validLiveNewAidIncmAmtRnNextScore,jdbcType=INTEGER},
      valid_live_new_aid_incm_amt_trgt_diff = #{record.validLiveNewAidIncmAmtTrgtDiff,jdbcType=DOUBLE},
      valid_live_new_aid_incm_amt_diff = #{record.validLiveNewAidIncmAmtDiff,jdbcType=DOUBLE},
      valid_live_new_aid_incm_amt_diff_score = #{record.validLiveNewAidIncmAmtDiffScore,jdbcType=INTEGER},
      valid_live_new_aid_incm_amt_diff_next_score = #{record.validLiveNewAidIncmAmtDiffNextScore,jdbcType=INTEGER},
      valid_live_new_aid_incm_amt_diff_trgt_diff = #{record.validLiveNewAidIncmAmtDiffTrgtDiff,jdbcType=DOUBLE},
      unvalid_live_new_aid_incm_amt = #{record.unvalidLiveNewAidIncmAmt,jdbcType=DOUBLE},
      unvalid_live_new_aid_incm_amt_rn = #{record.unvalidLiveNewAidIncmAmtRn,jdbcType=BIGINT},
      unvalid_live_new_aid_incm_amt_rn_score = #{record.unvalidLiveNewAidIncmAmtRnScore,jdbcType=INTEGER},
      unvalid_live_new_aid_incm_amt_rn_next_score = #{record.unvalidLiveNewAidIncmAmtRnNextScore,jdbcType=INTEGER},
      unvalid_live_new_aid_incm_amt_trgt_diff = #{record.unvalidLiveNewAidIncmAmtTrgtDiff,jdbcType=DOUBLE},
      unvalid_live_new_aid_incm_amt_diff = #{record.unvalidLiveNewAidIncmAmtDiff,jdbcType=DOUBLE},
      unvalid_live_new_aid_incm_amt_diff_score = #{record.unvalidLiveNewAidIncmAmtDiffScore,jdbcType=INTEGER},
      unvalid_live_new_aid_incm_amt_diff_next_score = #{record.unvalidLiveNewAidIncmAmtDiffNextScore,jdbcType=INTEGER},
      unvalid_live_new_aid_incm_amt_diff_trgt_diff = #{record.unvalidLiveNewAidIncmAmtDiffTrgtDiff,jdbcType=DOUBLE},
      unvalid_live_new_aid_incm_amt_rate_diff = #{record.unvalidLiveNewAidIncmAmtRateDiff,jdbcType=DOUBLE},
      unvalid_live_new_aid_incm_amt_rate_diff_score = #{record.unvalidLiveNewAidIncmAmtRateDiffScore,jdbcType=INTEGER},
      unvalid_live_new_aid_incm_amt_rate_diff_next_score = #{record.unvalidLiveNewAidIncmAmtRateDiffNextScore,jdbcType=INTEGER},
      unvalid_live_new_aid_incm_amt_rate_diff_trgt_diff = #{record.unvalidLiveNewAidIncmAmtRateDiffTrgtDiff,jdbcType=DOUBLE},
      guild_health_point = #{record.guildHealthPoint,jdbcType=INTEGER},
      guild_health_point_score = #{record.guildHealthPointScore,jdbcType=INTEGER},
      guild_health_point_next_score = #{record.guildHealthPointNextScore,jdbcType=INTEGER},
      guild_health_point_trgt_diff = #{record.guildHealthPointTrgtDiff,jdbcType=INTEGER},
      dt = #{record.dt,jdbcType=DATE}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.yyzone.guildrank.db.gen.model.YyDmEntityGuildCmpHealthAnalysisExptDi" >
    update yy_dm_entity_guild_cmp_health_analysis_expt_di
    <set >
      <if test="guildCmpOwnrId != null" >
        guild_cmp_ownr_id = #{guildCmpOwnrId,jdbcType=BIGINT},
      </if>
      <if test="newAidRat34AidNum != null" >
        new_aid_rat_3_4_aid_num = #{newAidRat34AidNum,jdbcType=BIGINT},
      </if>
      <if test="newAidRat34AidNumRn != null" >
        new_aid_rat_3_4_aid_num_rn = #{newAidRat34AidNumRn,jdbcType=BIGINT},
      </if>
      <if test="newAidRat34AidNumRnScore != null" >
        new_aid_rat_3_4_aid_num_rn_score = #{newAidRat34AidNumRnScore,jdbcType=INTEGER},
      </if>
      <if test="newAidRat34AidNumRnNextScore != null" >
        new_aid_rat_3_4_aid_num_rn_next_score = #{newAidRat34AidNumRnNextScore,jdbcType=INTEGER},
      </if>
      <if test="newAidRat34AidNumTrgtDiff != null" >
        new_aid_rat_3_4_aid_num_trgt_diff = #{newAidRat34AidNumTrgtDiff,jdbcType=BIGINT},
      </if>
      <if test="newAidRat34AidNumDiff != null" >
        new_aid_rat_3_4_aid_num_diff = #{newAidRat34AidNumDiff,jdbcType=BIGINT},
      </if>
      <if test="newAidRat34AidNumDiffScore != null" >
        new_aid_rat_3_4_aid_num_diff_score = #{newAidRat34AidNumDiffScore,jdbcType=INTEGER},
      </if>
      <if test="newAidRat34AidNumDiffNextScore != null" >
        new_aid_rat_3_4_aid_num_diff_next_score = #{newAidRat34AidNumDiffNextScore,jdbcType=INTEGER},
      </if>
      <if test="newAidRat34AidNumDiffTrgtDiff != null" >
        new_aid_rat_3_4_aid_num_diff_trgt_diff = #{newAidRat34AidNumDiffTrgtDiff,jdbcType=BIGINT},
      </if>
      <if test="upgrdHighAidNumDiff != null" >
        upgrd_high_aid_num_diff = #{upgrdHighAidNumDiff,jdbcType=INTEGER},
      </if>
      <if test="upgrdHighAidNumDiffScore != null" >
        upgrd_high_aid_num_diff_score = #{upgrdHighAidNumDiffScore,jdbcType=INTEGER},
      </if>
      <if test="upgrdHighAidNumDiffNextScore != null" >
        upgrd_high_aid_num_diff_next_score = #{upgrdHighAidNumDiffNextScore,jdbcType=INTEGER},
      </if>
      <if test="upgrdHighAidNumDiffTrgtDiff != null" >
        upgrd_high_aid_num_diff_trgt_diff = #{upgrdHighAidNumDiffTrgtDiff,jdbcType=INTEGER},
      </if>
      <if test="upgrdWaistAidNumDiff != null" >
        upgrd_waist_aid_num_diff = #{upgrdWaistAidNumDiff,jdbcType=INTEGER},
      </if>
      <if test="upgrdWaistAidNumDiffScore != null" >
        upgrd_waist_aid_num_diff_score = #{upgrdWaistAidNumDiffScore,jdbcType=INTEGER},
      </if>
      <if test="upgrdWaistAidNumDiffNextScore != null" >
        upgrd_waist_aid_num_diff_next_score = #{upgrdWaistAidNumDiffNextScore,jdbcType=INTEGER},
      </if>
      <if test="upgrdWaistAidNumDiffTrgtDiff != null" >
        upgrd_waist_aid_num_diff_trgt_diff = #{upgrdWaistAidNumDiffTrgtDiff,jdbcType=INTEGER},
      </if>
      <if test="avgHighValidLiveAidNum != null" >
        avg_high_valid_live_aid_num = #{avgHighValidLiveAidNum,jdbcType=DOUBLE},
      </if>
      <if test="avgHighValidLiveAidNumRn != null" >
        avg_high_valid_live_aid_num_rn = #{avgHighValidLiveAidNumRn,jdbcType=BIGINT},
      </if>
      <if test="avgHighValidLiveAidNumRnScore != null" >
        avg_high_valid_live_aid_num_rn_score = #{avgHighValidLiveAidNumRnScore,jdbcType=INTEGER},
      </if>
      <if test="avgHighValidLiveAidNumRnNextScore != null" >
        avg_high_valid_live_aid_num_rn_next_score = #{avgHighValidLiveAidNumRnNextScore,jdbcType=INTEGER},
      </if>
      <if test="avgHighValidLiveAidNumTrgtDiff != null" >
        avg_high_valid_live_aid_num_trgt_diff = #{avgHighValidLiveAidNumTrgtDiff,jdbcType=DOUBLE},
      </if>
      <if test="avgHighValidLiveAidNumDiff != null" >
        avg_high_valid_live_aid_num_diff = #{avgHighValidLiveAidNumDiff,jdbcType=DOUBLE},
      </if>
      <if test="avgHighValidLiveAidNumDiffScore != null" >
        avg_high_valid_live_aid_num_diff_score = #{avgHighValidLiveAidNumDiffScore,jdbcType=INTEGER},
      </if>
      <if test="avgHighValidLiveAidNumDiffNextScore != null" >
        avg_high_valid_live_aid_num_diff_next_score = #{avgHighValidLiveAidNumDiffNextScore,jdbcType=INTEGER},
      </if>
      <if test="avgHighValidLiveAidNumDiffTrgtDiff != null" >
        avg_high_valid_live_aid_num_diff_trgt_diff = #{avgHighValidLiveAidNumDiffTrgtDiff,jdbcType=DOUBLE},
      </if>
      <if test="avgWaistValidLiveAidNum != null" >
        avg_waist_valid_live_aid_num = #{avgWaistValidLiveAidNum,jdbcType=DOUBLE},
      </if>
      <if test="avgWaistValidLiveAidNumRn != null" >
        avg_waist_valid_live_aid_num_rn = #{avgWaistValidLiveAidNumRn,jdbcType=BIGINT},
      </if>
      <if test="avgWaistValidLiveAidNumRnScore != null" >
        avg_waist_valid_live_aid_num_rn_score = #{avgWaistValidLiveAidNumRnScore,jdbcType=INTEGER},
      </if>
      <if test="avgWaistValidLiveAidNumRnNextScore != null" >
        avg_waist_valid_live_aid_num_rn_next_score = #{avgWaistValidLiveAidNumRnNextScore,jdbcType=INTEGER},
      </if>
      <if test="avgWaistValidLiveAidNumTrgtDiff != null" >
        avg_waist_valid_live_aid_num_trgt_diff = #{avgWaistValidLiveAidNumTrgtDiff,jdbcType=DOUBLE},
      </if>
      <if test="avgWaistValidLiveAidNumDiff != null" >
        avg_waist_valid_live_aid_num_diff = #{avgWaistValidLiveAidNumDiff,jdbcType=DOUBLE},
      </if>
      <if test="avgWaistValidLiveAidNumDiffScore != null" >
        avg_waist_valid_live_aid_num_diff_score = #{avgWaistValidLiveAidNumDiffScore,jdbcType=INTEGER},
      </if>
      <if test="avgWaistValidLiveAidNumDiffNextScore != null" >
        avg_waist_valid_live_aid_num_diff_next_score = #{avgWaistValidLiveAidNumDiffNextScore,jdbcType=INTEGER},
      </if>
      <if test="avgWaistValidLiveAidNumDiffTrgtDiff != null" >
        avg_waist_valid_live_aid_num_diff_trgt_diff = #{avgWaistValidLiveAidNumDiffTrgtDiff,jdbcType=DOUBLE},
      </if>
      <if test="avgNtoValidLiveAidRate != null" >
        avg_nto_valid_live_aid_rate = #{avgNtoValidLiveAidRate,jdbcType=DOUBLE},
      </if>
      <if test="avgNtoValidLiveAidRateRn != null" >
        avg_nto_valid_live_aid_rate_rn = #{avgNtoValidLiveAidRateRn,jdbcType=BIGINT},
      </if>
      <if test="avgNtoValidLiveAidRateRnScore != null" >
        avg_nto_valid_live_aid_rate_rn_score = #{avgNtoValidLiveAidRateRnScore,jdbcType=INTEGER},
      </if>
      <if test="avgNtoValidLiveAidRateRnNextScore != null" >
        avg_nto_valid_live_aid_rate_rn_next_score = #{avgNtoValidLiveAidRateRnNextScore,jdbcType=INTEGER},
      </if>
      <if test="avgNtoValidLiveAidRateTrgtDiff != null" >
        avg_nto_valid_live_aid_rate_trgt_diff = #{avgNtoValidLiveAidRateTrgtDiff,jdbcType=DOUBLE},
      </if>
      <if test="avgStockValidLiveAidRate != null" >
        avg_stock_valid_live_aid_rate = #{avgStockValidLiveAidRate,jdbcType=DOUBLE},
      </if>
      <if test="avgStockValidLiveAidRateRn != null" >
        avg_stock_valid_live_aid_rate_rn = #{avgStockValidLiveAidRateRn,jdbcType=BIGINT},
      </if>
      <if test="avgStockValidLiveAidRateRnScore != null" >
        avg_stock_valid_live_aid_rate_rn_score = #{avgStockValidLiveAidRateRnScore,jdbcType=INTEGER},
      </if>
      <if test="avgStockValidLiveAidRateRnNextScore != null" >
        avg_stock_valid_live_aid_rate_rn_next_score = #{avgStockValidLiveAidRateRnNextScore,jdbcType=INTEGER},
      </if>
      <if test="avgStockValidLiveAidRateTrgtDiff != null" >
        avg_stock_valid_live_aid_rate_trgt_diff = #{avgStockValidLiveAidRateTrgtDiff,jdbcType=DOUBLE},
      </if>
      <if test="newAuthGoldenAidNum != null" >
        new_auth_golden_aid_num = #{newAuthGoldenAidNum,jdbcType=BIGINT},
      </if>
      <if test="newAuthGoldenAidNumRn != null" >
        new_auth_golden_aid_num_rn = #{newAuthGoldenAidNumRn,jdbcType=BIGINT},
      </if>
      <if test="newAuthGoldenAidNumRnScore != null" >
        new_auth_golden_aid_num_rn_score = #{newAuthGoldenAidNumRnScore,jdbcType=INTEGER},
      </if>
      <if test="newAuthGoldenAidNumRnNextScore != null" >
        new_auth_golden_aid_num_rn_next_score = #{newAuthGoldenAidNumRnNextScore,jdbcType=INTEGER},
      </if>
      <if test="newAuthGoldenAidNumTrgtDiff != null" >
        new_auth_golden_aid_num_trgt_diff = #{newAuthGoldenAidNumTrgtDiff,jdbcType=BIGINT},
      </if>
      <if test="validLiveNewAidIncmAmt != null" >
        valid_live_new_aid_incm_amt = #{validLiveNewAidIncmAmt,jdbcType=DOUBLE},
      </if>
      <if test="validLiveNewAidIncmAmtRn != null" >
        valid_live_new_aid_incm_amt_rn = #{validLiveNewAidIncmAmtRn,jdbcType=BIGINT},
      </if>
      <if test="validLiveNewAidIncmAmtRnScore != null" >
        valid_live_new_aid_incm_amt_rn_score = #{validLiveNewAidIncmAmtRnScore,jdbcType=INTEGER},
      </if>
      <if test="validLiveNewAidIncmAmtRnNextScore != null" >
        valid_live_new_aid_incm_amt_rn_next_score = #{validLiveNewAidIncmAmtRnNextScore,jdbcType=INTEGER},
      </if>
      <if test="validLiveNewAidIncmAmtTrgtDiff != null" >
        valid_live_new_aid_incm_amt_trgt_diff = #{validLiveNewAidIncmAmtTrgtDiff,jdbcType=DOUBLE},
      </if>
      <if test="validLiveNewAidIncmAmtDiff != null" >
        valid_live_new_aid_incm_amt_diff = #{validLiveNewAidIncmAmtDiff,jdbcType=DOUBLE},
      </if>
      <if test="validLiveNewAidIncmAmtDiffScore != null" >
        valid_live_new_aid_incm_amt_diff_score = #{validLiveNewAidIncmAmtDiffScore,jdbcType=INTEGER},
      </if>
      <if test="validLiveNewAidIncmAmtDiffNextScore != null" >
        valid_live_new_aid_incm_amt_diff_next_score = #{validLiveNewAidIncmAmtDiffNextScore,jdbcType=INTEGER},
      </if>
      <if test="validLiveNewAidIncmAmtDiffTrgtDiff != null" >
        valid_live_new_aid_incm_amt_diff_trgt_diff = #{validLiveNewAidIncmAmtDiffTrgtDiff,jdbcType=DOUBLE},
      </if>
      <if test="unvalidLiveNewAidIncmAmt != null" >
        unvalid_live_new_aid_incm_amt = #{unvalidLiveNewAidIncmAmt,jdbcType=DOUBLE},
      </if>
      <if test="unvalidLiveNewAidIncmAmtRn != null" >
        unvalid_live_new_aid_incm_amt_rn = #{unvalidLiveNewAidIncmAmtRn,jdbcType=BIGINT},
      </if>
      <if test="unvalidLiveNewAidIncmAmtRnScore != null" >
        unvalid_live_new_aid_incm_amt_rn_score = #{unvalidLiveNewAidIncmAmtRnScore,jdbcType=INTEGER},
      </if>
      <if test="unvalidLiveNewAidIncmAmtRnNextScore != null" >
        unvalid_live_new_aid_incm_amt_rn_next_score = #{unvalidLiveNewAidIncmAmtRnNextScore,jdbcType=INTEGER},
      </if>
      <if test="unvalidLiveNewAidIncmAmtTrgtDiff != null" >
        unvalid_live_new_aid_incm_amt_trgt_diff = #{unvalidLiveNewAidIncmAmtTrgtDiff,jdbcType=DOUBLE},
      </if>
      <if test="unvalidLiveNewAidIncmAmtDiff != null" >
        unvalid_live_new_aid_incm_amt_diff = #{unvalidLiveNewAidIncmAmtDiff,jdbcType=DOUBLE},
      </if>
      <if test="unvalidLiveNewAidIncmAmtDiffScore != null" >
        unvalid_live_new_aid_incm_amt_diff_score = #{unvalidLiveNewAidIncmAmtDiffScore,jdbcType=INTEGER},
      </if>
      <if test="unvalidLiveNewAidIncmAmtDiffNextScore != null" >
        unvalid_live_new_aid_incm_amt_diff_next_score = #{unvalidLiveNewAidIncmAmtDiffNextScore,jdbcType=INTEGER},
      </if>
      <if test="unvalidLiveNewAidIncmAmtDiffTrgtDiff != null" >
        unvalid_live_new_aid_incm_amt_diff_trgt_diff = #{unvalidLiveNewAidIncmAmtDiffTrgtDiff,jdbcType=DOUBLE},
      </if>
      <if test="unvalidLiveNewAidIncmAmtRateDiff != null" >
        unvalid_live_new_aid_incm_amt_rate_diff = #{unvalidLiveNewAidIncmAmtRateDiff,jdbcType=DOUBLE},
      </if>
      <if test="unvalidLiveNewAidIncmAmtRateDiffScore != null" >
        unvalid_live_new_aid_incm_amt_rate_diff_score = #{unvalidLiveNewAidIncmAmtRateDiffScore,jdbcType=INTEGER},
      </if>
      <if test="unvalidLiveNewAidIncmAmtRateDiffNextScore != null" >
        unvalid_live_new_aid_incm_amt_rate_diff_next_score = #{unvalidLiveNewAidIncmAmtRateDiffNextScore,jdbcType=INTEGER},
      </if>
      <if test="unvalidLiveNewAidIncmAmtRateDiffTrgtDiff != null" >
        unvalid_live_new_aid_incm_amt_rate_diff_trgt_diff = #{unvalidLiveNewAidIncmAmtRateDiffTrgtDiff,jdbcType=DOUBLE},
      </if>
      <if test="guildHealthPoint != null" >
        guild_health_point = #{guildHealthPoint,jdbcType=INTEGER},
      </if>
      <if test="guildHealthPointScore != null" >
        guild_health_point_score = #{guildHealthPointScore,jdbcType=INTEGER},
      </if>
      <if test="guildHealthPointNextScore != null" >
        guild_health_point_next_score = #{guildHealthPointNextScore,jdbcType=INTEGER},
      </if>
      <if test="guildHealthPointTrgtDiff != null" >
        guild_health_point_trgt_diff = #{guildHealthPointTrgtDiff,jdbcType=INTEGER},
      </if>
      <if test="dt != null" >
        dt = #{dt,jdbcType=DATE},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.yyzone.guildrank.db.gen.model.YyDmEntityGuildCmpHealthAnalysisExptDi" >
    update yy_dm_entity_guild_cmp_health_analysis_expt_di
    set guild_cmp_ownr_id = #{guildCmpOwnrId,jdbcType=BIGINT},
      new_aid_rat_3_4_aid_num = #{newAidRat34AidNum,jdbcType=BIGINT},
      new_aid_rat_3_4_aid_num_rn = #{newAidRat34AidNumRn,jdbcType=BIGINT},
      new_aid_rat_3_4_aid_num_rn_score = #{newAidRat34AidNumRnScore,jdbcType=INTEGER},
      new_aid_rat_3_4_aid_num_rn_next_score = #{newAidRat34AidNumRnNextScore,jdbcType=INTEGER},
      new_aid_rat_3_4_aid_num_trgt_diff = #{newAidRat34AidNumTrgtDiff,jdbcType=BIGINT},
      new_aid_rat_3_4_aid_num_diff = #{newAidRat34AidNumDiff,jdbcType=BIGINT},
      new_aid_rat_3_4_aid_num_diff_score = #{newAidRat34AidNumDiffScore,jdbcType=INTEGER},
      new_aid_rat_3_4_aid_num_diff_next_score = #{newAidRat34AidNumDiffNextScore,jdbcType=INTEGER},
      new_aid_rat_3_4_aid_num_diff_trgt_diff = #{newAidRat34AidNumDiffTrgtDiff,jdbcType=BIGINT},
      upgrd_high_aid_num_diff = #{upgrdHighAidNumDiff,jdbcType=INTEGER},
      upgrd_high_aid_num_diff_score = #{upgrdHighAidNumDiffScore,jdbcType=INTEGER},
      upgrd_high_aid_num_diff_next_score = #{upgrdHighAidNumDiffNextScore,jdbcType=INTEGER},
      upgrd_high_aid_num_diff_trgt_diff = #{upgrdHighAidNumDiffTrgtDiff,jdbcType=INTEGER},
      upgrd_waist_aid_num_diff = #{upgrdWaistAidNumDiff,jdbcType=INTEGER},
      upgrd_waist_aid_num_diff_score = #{upgrdWaistAidNumDiffScore,jdbcType=INTEGER},
      upgrd_waist_aid_num_diff_next_score = #{upgrdWaistAidNumDiffNextScore,jdbcType=INTEGER},
      upgrd_waist_aid_num_diff_trgt_diff = #{upgrdWaistAidNumDiffTrgtDiff,jdbcType=INTEGER},
      avg_high_valid_live_aid_num = #{avgHighValidLiveAidNum,jdbcType=DOUBLE},
      avg_high_valid_live_aid_num_rn = #{avgHighValidLiveAidNumRn,jdbcType=BIGINT},
      avg_high_valid_live_aid_num_rn_score = #{avgHighValidLiveAidNumRnScore,jdbcType=INTEGER},
      avg_high_valid_live_aid_num_rn_next_score = #{avgHighValidLiveAidNumRnNextScore,jdbcType=INTEGER},
      avg_high_valid_live_aid_num_trgt_diff = #{avgHighValidLiveAidNumTrgtDiff,jdbcType=DOUBLE},
      avg_high_valid_live_aid_num_diff = #{avgHighValidLiveAidNumDiff,jdbcType=DOUBLE},
      avg_high_valid_live_aid_num_diff_score = #{avgHighValidLiveAidNumDiffScore,jdbcType=INTEGER},
      avg_high_valid_live_aid_num_diff_next_score = #{avgHighValidLiveAidNumDiffNextScore,jdbcType=INTEGER},
      avg_high_valid_live_aid_num_diff_trgt_diff = #{avgHighValidLiveAidNumDiffTrgtDiff,jdbcType=DOUBLE},
      avg_waist_valid_live_aid_num = #{avgWaistValidLiveAidNum,jdbcType=DOUBLE},
      avg_waist_valid_live_aid_num_rn = #{avgWaistValidLiveAidNumRn,jdbcType=BIGINT},
      avg_waist_valid_live_aid_num_rn_score = #{avgWaistValidLiveAidNumRnScore,jdbcType=INTEGER},
      avg_waist_valid_live_aid_num_rn_next_score = #{avgWaistValidLiveAidNumRnNextScore,jdbcType=INTEGER},
      avg_waist_valid_live_aid_num_trgt_diff = #{avgWaistValidLiveAidNumTrgtDiff,jdbcType=DOUBLE},
      avg_waist_valid_live_aid_num_diff = #{avgWaistValidLiveAidNumDiff,jdbcType=DOUBLE},
      avg_waist_valid_live_aid_num_diff_score = #{avgWaistValidLiveAidNumDiffScore,jdbcType=INTEGER},
      avg_waist_valid_live_aid_num_diff_next_score = #{avgWaistValidLiveAidNumDiffNextScore,jdbcType=INTEGER},
      avg_waist_valid_live_aid_num_diff_trgt_diff = #{avgWaistValidLiveAidNumDiffTrgtDiff,jdbcType=DOUBLE},
      avg_nto_valid_live_aid_rate = #{avgNtoValidLiveAidRate,jdbcType=DOUBLE},
      avg_nto_valid_live_aid_rate_rn = #{avgNtoValidLiveAidRateRn,jdbcType=BIGINT},
      avg_nto_valid_live_aid_rate_rn_score = #{avgNtoValidLiveAidRateRnScore,jdbcType=INTEGER},
      avg_nto_valid_live_aid_rate_rn_next_score = #{avgNtoValidLiveAidRateRnNextScore,jdbcType=INTEGER},
      avg_nto_valid_live_aid_rate_trgt_diff = #{avgNtoValidLiveAidRateTrgtDiff,jdbcType=DOUBLE},
      avg_stock_valid_live_aid_rate = #{avgStockValidLiveAidRate,jdbcType=DOUBLE},
      avg_stock_valid_live_aid_rate_rn = #{avgStockValidLiveAidRateRn,jdbcType=BIGINT},
      avg_stock_valid_live_aid_rate_rn_score = #{avgStockValidLiveAidRateRnScore,jdbcType=INTEGER},
      avg_stock_valid_live_aid_rate_rn_next_score = #{avgStockValidLiveAidRateRnNextScore,jdbcType=INTEGER},
      avg_stock_valid_live_aid_rate_trgt_diff = #{avgStockValidLiveAidRateTrgtDiff,jdbcType=DOUBLE},
      new_auth_golden_aid_num = #{newAuthGoldenAidNum,jdbcType=BIGINT},
      new_auth_golden_aid_num_rn = #{newAuthGoldenAidNumRn,jdbcType=BIGINT},
      new_auth_golden_aid_num_rn_score = #{newAuthGoldenAidNumRnScore,jdbcType=INTEGER},
      new_auth_golden_aid_num_rn_next_score = #{newAuthGoldenAidNumRnNextScore,jdbcType=INTEGER},
      new_auth_golden_aid_num_trgt_diff = #{newAuthGoldenAidNumTrgtDiff,jdbcType=BIGINT},
      valid_live_new_aid_incm_amt = #{validLiveNewAidIncmAmt,jdbcType=DOUBLE},
      valid_live_new_aid_incm_amt_rn = #{validLiveNewAidIncmAmtRn,jdbcType=BIGINT},
      valid_live_new_aid_incm_amt_rn_score = #{validLiveNewAidIncmAmtRnScore,jdbcType=INTEGER},
      valid_live_new_aid_incm_amt_rn_next_score = #{validLiveNewAidIncmAmtRnNextScore,jdbcType=INTEGER},
      valid_live_new_aid_incm_amt_trgt_diff = #{validLiveNewAidIncmAmtTrgtDiff,jdbcType=DOUBLE},
      valid_live_new_aid_incm_amt_diff = #{validLiveNewAidIncmAmtDiff,jdbcType=DOUBLE},
      valid_live_new_aid_incm_amt_diff_score = #{validLiveNewAidIncmAmtDiffScore,jdbcType=INTEGER},
      valid_live_new_aid_incm_amt_diff_next_score = #{validLiveNewAidIncmAmtDiffNextScore,jdbcType=INTEGER},
      valid_live_new_aid_incm_amt_diff_trgt_diff = #{validLiveNewAidIncmAmtDiffTrgtDiff,jdbcType=DOUBLE},
      unvalid_live_new_aid_incm_amt = #{unvalidLiveNewAidIncmAmt,jdbcType=DOUBLE},
      unvalid_live_new_aid_incm_amt_rn = #{unvalidLiveNewAidIncmAmtRn,jdbcType=BIGINT},
      unvalid_live_new_aid_incm_amt_rn_score = #{unvalidLiveNewAidIncmAmtRnScore,jdbcType=INTEGER},
      unvalid_live_new_aid_incm_amt_rn_next_score = #{unvalidLiveNewAidIncmAmtRnNextScore,jdbcType=INTEGER},
      unvalid_live_new_aid_incm_amt_trgt_diff = #{unvalidLiveNewAidIncmAmtTrgtDiff,jdbcType=DOUBLE},
      unvalid_live_new_aid_incm_amt_diff = #{unvalidLiveNewAidIncmAmtDiff,jdbcType=DOUBLE},
      unvalid_live_new_aid_incm_amt_diff_score = #{unvalidLiveNewAidIncmAmtDiffScore,jdbcType=INTEGER},
      unvalid_live_new_aid_incm_amt_diff_next_score = #{unvalidLiveNewAidIncmAmtDiffNextScore,jdbcType=INTEGER},
      unvalid_live_new_aid_incm_amt_diff_trgt_diff = #{unvalidLiveNewAidIncmAmtDiffTrgtDiff,jdbcType=DOUBLE},
      unvalid_live_new_aid_incm_amt_rate_diff = #{unvalidLiveNewAidIncmAmtRateDiff,jdbcType=DOUBLE},
      unvalid_live_new_aid_incm_amt_rate_diff_score = #{unvalidLiveNewAidIncmAmtRateDiffScore,jdbcType=INTEGER},
      unvalid_live_new_aid_incm_amt_rate_diff_next_score = #{unvalidLiveNewAidIncmAmtRateDiffNextScore,jdbcType=INTEGER},
      unvalid_live_new_aid_incm_amt_rate_diff_trgt_diff = #{unvalidLiveNewAidIncmAmtRateDiffTrgtDiff,jdbcType=DOUBLE},
      guild_health_point = #{guildHealthPoint,jdbcType=INTEGER},
      guild_health_point_score = #{guildHealthPointScore,jdbcType=INTEGER},
      guild_health_point_next_score = #{guildHealthPointNextScore,jdbcType=INTEGER},
      guild_health_point_trgt_diff = #{guildHealthPointTrgtDiff,jdbcType=INTEGER},
      dt = #{dt,jdbcType=DATE}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>