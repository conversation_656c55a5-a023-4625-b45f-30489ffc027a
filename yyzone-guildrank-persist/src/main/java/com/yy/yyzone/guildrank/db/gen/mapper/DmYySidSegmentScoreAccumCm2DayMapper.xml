<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yy.yyzone.guildrank.db.gen.mapper.DmYySidSegmentScoreAccumCm2DayMapper" >
  <resultMap id="BaseResultMap" type="com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreAccumCm2Day" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="sid_ownerid" property="sidOwnerid" jdbcType="BIGINT" />
    <result column="ps_n" property="psN" jdbcType="DOUBLE" />
    <result column="ps_n_all_rn" property="psNAllRn" jdbcType="INTEGER" />
    <result column="kpi_name" property="kpiName" jdbcType="VARCHAR" />
    <result column="dt" property="dt" jdbcType="VARCHAR" />
    <result column="grade" property="grade" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, sid_ownerid, ps_n, ps_n_all_rn, kpi_name, dt, grade
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreAccumCm2DayExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from dm_yy_sid_segment_score_accum_cm_2_day
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit > 0" >
      limit ${limit}
    </if>
    <if test="offset > 0" >
      offset ${offset}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from dm_yy_sid_segment_score_accum_cm_2_day
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from dm_yy_sid_segment_score_accum_cm_2_day
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreAccumCm2DayExample" >
    delete from dm_yy_sid_segment_score_accum_cm_2_day
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreAccumCm2Day" useGeneratedKeys="true" keyProperty="id" >
    insert into dm_yy_sid_segment_score_accum_cm_2_day (sid_ownerid, ps_n, ps_n_all_rn, 
      kpi_name, dt, grade)
    values (#{sidOwnerid,jdbcType=BIGINT}, #{psN,jdbcType=DOUBLE}, #{psNAllRn,jdbcType=INTEGER}, 
      #{kpiName,jdbcType=VARCHAR}, #{dt,jdbcType=VARCHAR}, #{grade,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreAccumCm2Day" useGeneratedKeys="true" keyProperty="id" >
    insert into dm_yy_sid_segment_score_accum_cm_2_day
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="sidOwnerid != null" >
        sid_ownerid,
      </if>
      <if test="psN != null" >
        ps_n,
      </if>
      <if test="psNAllRn != null" >
        ps_n_all_rn,
      </if>
      <if test="kpiName != null" >
        kpi_name,
      </if>
      <if test="dt != null" >
        dt,
      </if>
      <if test="grade != null" >
        grade,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="sidOwnerid != null" >
        #{sidOwnerid,jdbcType=BIGINT},
      </if>
      <if test="psN != null" >
        #{psN,jdbcType=DOUBLE},
      </if>
      <if test="psNAllRn != null" >
        #{psNAllRn,jdbcType=INTEGER},
      </if>
      <if test="kpiName != null" >
        #{kpiName,jdbcType=VARCHAR},
      </if>
      <if test="dt != null" >
        #{dt,jdbcType=VARCHAR},
      </if>
      <if test="grade != null" >
        #{grade,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreAccumCm2DayExample" resultType="java.lang.Integer" >
    select count(*) from dm_yy_sid_segment_score_accum_cm_2_day
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update dm_yy_sid_segment_score_accum_cm_2_day
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.sidOwnerid != null" >
        sid_ownerid = #{record.sidOwnerid,jdbcType=BIGINT},
      </if>
      <if test="record.psN != null" >
        ps_n = #{record.psN,jdbcType=DOUBLE},
      </if>
      <if test="record.psNAllRn != null" >
        ps_n_all_rn = #{record.psNAllRn,jdbcType=INTEGER},
      </if>
      <if test="record.kpiName != null" >
        kpi_name = #{record.kpiName,jdbcType=VARCHAR},
      </if>
      <if test="record.dt != null" >
        dt = #{record.dt,jdbcType=VARCHAR},
      </if>
      <if test="record.grade != null" >
        grade = #{record.grade,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update dm_yy_sid_segment_score_accum_cm_2_day
    set id = #{record.id,jdbcType=BIGINT},
      sid_ownerid = #{record.sidOwnerid,jdbcType=BIGINT},
      ps_n = #{record.psN,jdbcType=DOUBLE},
      ps_n_all_rn = #{record.psNAllRn,jdbcType=INTEGER},
      kpi_name = #{record.kpiName,jdbcType=VARCHAR},
      dt = #{record.dt,jdbcType=VARCHAR},
      grade = #{record.grade,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreAccumCm2Day" >
    update dm_yy_sid_segment_score_accum_cm_2_day
    <set >
      <if test="sidOwnerid != null" >
        sid_ownerid = #{sidOwnerid,jdbcType=BIGINT},
      </if>
      <if test="psN != null" >
        ps_n = #{psN,jdbcType=DOUBLE},
      </if>
      <if test="psNAllRn != null" >
        ps_n_all_rn = #{psNAllRn,jdbcType=INTEGER},
      </if>
      <if test="kpiName != null" >
        kpi_name = #{kpiName,jdbcType=VARCHAR},
      </if>
      <if test="dt != null" >
        dt = #{dt,jdbcType=VARCHAR},
      </if>
      <if test="grade != null" >
        grade = #{grade,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreAccumCm2Day" >
    update dm_yy_sid_segment_score_accum_cm_2_day
    set sid_ownerid = #{sidOwnerid,jdbcType=BIGINT},
      ps_n = #{psN,jdbcType=DOUBLE},
      ps_n_all_rn = #{psNAllRn,jdbcType=INTEGER},
      kpi_name = #{kpiName,jdbcType=VARCHAR},
      dt = #{dt,jdbcType=VARCHAR},
      grade = #{grade,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>