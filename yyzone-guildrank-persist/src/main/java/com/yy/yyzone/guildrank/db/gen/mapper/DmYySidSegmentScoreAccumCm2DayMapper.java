package com.yy.yyzone.guildrank.db.gen.mapper;

import com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreAccumCm2Day;
import com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreAccumCm2DayExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DmYySidSegmentScoreAccumCm2DayMapper {
    int countByExample(DmYySidSegmentScoreAccumCm2DayExample example);

    int deleteByExample(DmYySidSegmentScoreAccumCm2DayExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DmYySidSegmentScoreAccumCm2Day record);

    int insertSelective(DmYySidSegmentScoreAccumCm2Day record);

    List<DmYySidSegmentScoreAccumCm2Day> selectByExample(DmYySidSegmentScoreAccumCm2DayExample example);

    DmYySidSegmentScoreAccumCm2Day selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DmYySidSegmentScoreAccumCm2Day record, @Param("example") DmYySidSegmentScoreAccumCm2DayExample example);

    int updateByExample(@Param("record") DmYySidSegmentScoreAccumCm2Day record, @Param("example") DmYySidSegmentScoreAccumCm2DayExample example);

    int updateByPrimaryKeySelective(DmYySidSegmentScoreAccumCm2Day record);

    int updateByPrimaryKey(DmYySidSegmentScoreAccumCm2Day record);
}