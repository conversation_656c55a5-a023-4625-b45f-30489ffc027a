package com.yy.yyzone.guildrank.db.gen.mapper;

import com.yy.yyzone.guildrank.db.gen.model.GuildrankConfigLog;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankConfigLogExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface GuildrankConfigLogMapper {
    int countByExample(GuildrankConfigLogExample example);

    int deleteByExample(GuildrankConfigLogExample example);

    int deleteByPrimaryKey(Long id);

    int insert(GuildrankConfigLog record);

    int insertSelective(GuildrankConfigLog record);

    List<GuildrankConfigLog> selectByExampleWithBLOBs(GuildrankConfigLogExample example);

    List<GuildrankConfigLog> selectByExample(GuildrankConfigLogExample example);

    GuildrankConfigLog selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") GuildrankConfigLog record, @Param("example") GuildrankConfigLogExample example);

    int updateByExampleWithBLOBs(@Param("record") GuildrankConfigLog record, @Param("example") GuildrankConfigLogExample example);

    int updateByExample(@Param("record") GuildrankConfigLog record, @Param("example") GuildrankConfigLogExample example);

    int updateByPrimaryKeySelective(GuildrankConfigLog record);

    int updateByPrimaryKeyWithBLOBs(GuildrankConfigLog record);

    int updateByPrimaryKey(GuildrankConfigLog record);
}