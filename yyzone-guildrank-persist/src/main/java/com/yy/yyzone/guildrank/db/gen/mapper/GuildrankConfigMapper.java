package com.yy.yyzone.guildrank.db.gen.mapper;

import com.yy.yyzone.guildrank.db.gen.model.GuildrankConfig;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankConfigExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface GuildrankConfigMapper {
    int countByExample(GuildrankConfigExample example);

    int deleteByExample(GuildrankConfigExample example);

    int deleteByPrimaryKey(Long id);

    int insert(GuildrankConfig record);

    int insertSelective(GuildrankConfig record);

    List<GuildrankConfig> selectByExampleWithBLOBs(GuildrankConfigExample example);

    List<GuildrankConfig> selectByExample(GuildrankConfigExample example);

    GuildrankConfig selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") GuildrankConfig record, @Param("example") GuildrankConfigExample example);

    int updateByExampleWithBLOBs(@Param("record") GuildrankConfig record, @Param("example") GuildrankConfigExample example);

    int updateByExample(@Param("record") GuildrankConfig record, @Param("example") GuildrankConfigExample example);

    int updateByPrimaryKeySelective(GuildrankConfig record);

    int updateByPrimaryKeyWithBLOBs(GuildrankConfig record);

    int updateByPrimaryKey(GuildrankConfig record);
}