<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yy.yyzone.guildrank.db.gen.mapper.GuildrankRenewArtistCountMapper" >
  <resultMap id="BaseResultMap" type="com.yy.yyzone.guildrank.db.gen.model.GuildrankRenewArtistCount" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="data_month" property="dataMonth" jdbcType="DATE" />
    <result column="main_guild_uid" property="mainGuildUid" jdbcType="BIGINT" />
    <result column="renew_count" property="renewCount" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, data_month, main_guild_uid, renew_count
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankRenewArtistCountExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from guildrank_renew_artist_count
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit > 0" >
      limit ${limit}
    </if>
    <if test="offset > 0" >
      offset ${offset}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from guildrank_renew_artist_count
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from guildrank_renew_artist_count
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankRenewArtistCountExample" >
    delete from guildrank_renew_artist_count
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankRenewArtistCount" useGeneratedKeys="true" keyProperty="id" >
    insert into guildrank_renew_artist_count (data_month, main_guild_uid, renew_count
      )
    values (#{dataMonth,jdbcType=DATE}, #{mainGuildUid,jdbcType=BIGINT}, #{renewCount,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankRenewArtistCount" useGeneratedKeys="true" keyProperty="id" >
    insert into guildrank_renew_artist_count
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="dataMonth != null" >
        data_month,
      </if>
      <if test="mainGuildUid != null" >
        main_guild_uid,
      </if>
      <if test="renewCount != null" >
        renew_count,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="dataMonth != null" >
        #{dataMonth,jdbcType=DATE},
      </if>
      <if test="mainGuildUid != null" >
        #{mainGuildUid,jdbcType=BIGINT},
      </if>
      <if test="renewCount != null" >
        #{renewCount,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankRenewArtistCountExample" resultType="java.lang.Integer" >
    select count(*) from guildrank_renew_artist_count
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update guildrank_renew_artist_count
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.dataMonth != null" >
        data_month = #{record.dataMonth,jdbcType=DATE},
      </if>
      <if test="record.mainGuildUid != null" >
        main_guild_uid = #{record.mainGuildUid,jdbcType=BIGINT},
      </if>
      <if test="record.renewCount != null" >
        renew_count = #{record.renewCount,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update guildrank_renew_artist_count
    set id = #{record.id,jdbcType=BIGINT},
      data_month = #{record.dataMonth,jdbcType=DATE},
      main_guild_uid = #{record.mainGuildUid,jdbcType=BIGINT},
      renew_count = #{record.renewCount,jdbcType=INTEGER}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankRenewArtistCount" >
    update guildrank_renew_artist_count
    <set >
      <if test="dataMonth != null" >
        data_month = #{dataMonth,jdbcType=DATE},
      </if>
      <if test="mainGuildUid != null" >
        main_guild_uid = #{mainGuildUid,jdbcType=BIGINT},
      </if>
      <if test="renewCount != null" >
        renew_count = #{renewCount,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.yyzone.guildrank.db.gen.model.GuildrankRenewArtistCount" >
    update guildrank_renew_artist_count
    set data_month = #{dataMonth,jdbcType=DATE},
      main_guild_uid = #{mainGuildUid,jdbcType=BIGINT},
      renew_count = #{renewCount,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>