<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yy.yyzone.guildrank.db.custom.mapper.BigdaSyncDetailExtMapper">
    <select id="selectLatestDt" resultType="java.util.Map">
        select table_name,MAX(dt) dt from bigda_sync_detail where status=#{status} and table_name in
        <foreach item="item" index="index" collection="tbls" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by table_name;
    </select>
    <select id="selectDts" resultType="java.util.Date">
        select distinct dt from bigda_sync_detail where status=#{status} and table_name=#{tbl}
        <if test="minDt!=null">
            and dt>#{minDt}
        </if>
        <if test="maxDt!=null">
            and dt&lt;#{maxDt}
        </if>
        order by dt
    </select>
</mapper>