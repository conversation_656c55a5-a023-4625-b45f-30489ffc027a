<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yy.yyzone.guildrank.db.custom.mapper.BiExtMapper" >
    <resultMap id="GuildCmpAvgScore" type="com.yy.yyzone.guildrank.db.custom.model.GuildCmpAvgScore">
        <result column="recru_score" property="recruScore" jdbcType="DECIMAL" />
        <result column="hatch_score" property="hatchScore" jdbcType="DECIMAL" />
        <result column="ret_score" property="retScore" jdbcType="DECIMAL" />
        <result column="rvnu_score" property="rvnuScore" jdbcType="DECIMAL" />
        <result column="com_score" property="comScore" jdbcType="DECIMAL" />
        <result column="level" property="level" jdbcType="INTEGER" />
    </resultMap>

    <select id="selectDataCmpLevel" resultType="com.yy.yyzone.guildrank.db.custom.model.KV">
        select guild_cmp_ownr_id K,data_cmp_star_lvl V from yy_dm_entity_guild_cmp_health_analysis_di
        where dt=#{dt,jdbcType=DATE}
    </select>

    <select id="selectCmpStarLevel" resultType="com.yy.yyzone.guildrank.db.custom.model.KV">
        select guild_cmp_ownr_id K,cmp_star_lvl V from yy_dm_entity_guild_cmp_health_analysis_di
        where dt=#{dt,jdbcType=DATE}
    </select>

    <select id="selectLevelFromHisotry" resultType="com.yy.yyzone.guildrank.db.custom.model.KV">
        select guild_uid K,`level` V from guildrank_history where `month`=#{month,jdbcType=DATE}
        <if test="guildUids!=null and guildUids.size>0">
            and guild_uid in
            <foreach item="item" index="index" collection="guildUids"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <insert id="insertBatch">
        insert into yy_dm_entity_guild_cmp_health_analysis_di(guild_cmp_ownr_id, new_aid_rat_3_4_aid_num,
        new_aid_rat_3_4_aid_num_rn, new_aid_rat_3_4_aid_num_diff, upgrd_high_aid_num, upgrd_high_aid_num_diff,
        upgrd_waist_aid_num, upgrd_waist_aid_num_diff, avg_high_valid_live_aid_num, avg_high_valid_live_aid_num_rn,
        avg_high_valid_live_aid_num_diff, avg_waist_valid_live_aid_num, avg_waist_valid_live_aid_num_rn,
        avg_waist_valid_live_aid_num_diff, avg_nto_valid_live_aid_rate, avg_nto_valid_live_aid_rate_rn,
        avg_stock_valid_live_aid_rate, avg_stock_valid_live_aid_rate_rn, new_auth_golden_aid_num,
        new_auth_golden_aid_num_rn, ext_golden_aid_num, actv_prod_pay_amt, prod_pay_amt, actv_prod_pay_amt_rate,
        valid_live_new_aid_prod_pay_amt, valid_live_new_aid_prod_pay_amt_rn, valid_live_new_aid_prod_pay_amt_diff,
        unvalid_live_new_aid_prod_pay_amt, unvalid_live_new_aid_prod_pay_amt_rn, unvalid_live_new_aid_prod_pay_amt_diff,
        unvalid_live_new_aid_prod_pay_amt_rate, total_unvalid_live_new_aid_prod_pay_amt_rate,
        unvalid_live_new_aid_prod_pay_amt_rate_diff, actv_incm_amt, incm_amt, actv_incm_amt_rate,
        valid_live_new_aid_incm_amt, valid_live_new_aid_incm_amt_rn, valid_live_new_aid_incm_amt_diff,
        unvalid_live_new_aid_incm_amt, unvalid_live_new_aid_incm_amt_rn, unvalid_live_new_aid_incm_amt_diff,
        unvalid_live_new_aid_incm_amt_rate, total_unvalid_live_new_aid_incm_amt_rate,
        unvalid_live_new_aid_incm_amt_rate_diff, guild_health_point, new_aid_rat_3_4_aid_num_rn_score,
        new_aid_rat_3_4_aid_num_diff_score, upgrd_high_aid_num_diff_score, upgrd_waist_aid_num_diff_score,
        avg_high_valid_live_aid_num_rn_score, avg_waist_valid_live_aid_num_rn_score,
        avg_high_valid_live_aid_num_diff_score, avg_waist_valid_live_aid_num_diff_score,
        avg_nto_valid_live_aid_rate_rn_score, avg_stock_valid_live_aid_rate_rn_score, new_auth_golden_aid_num_rn_score,
        valid_live_new_aid_prod_pay_amt_rn_score, unvalid_live_new_aid_prod_pay_amt_rn_score,
        valid_live_new_aid_prod_pay_amt_diff_score, unvalid_live_new_aid_prod_pay_amt_diff_score,
        unvalid_live_new_aid_prod_pay_amt_rate_diff_score, valid_live_new_aid_incm_amt_rn_score,
        unvalid_live_new_aid_incm_amt_rn_score, valid_live_new_aid_incm_amt_diff_score,
        unvalid_live_new_aid_incm_amt_diff_score, unvalid_live_new_aid_incm_amt_rate_diff_score,
        guild_health_point_score, recru_score, hatch_score, ret_score, rvnu_score, com_score, data_total_score,
        data_cmp_star_lvl, cmp_star_lvl, new_aid_rat_3_4_aid_num_score, upgrd_high_aid_num_score, upgrd_waist_aid_num_score,
        ext_golden_aid_num_score, actv_prod_pay_amt_rate_score, actv_incm_amt_rate_score, task_total_score,
        new_aid_rat_3_4_aid_num_itg, upgrd_high_aid_num_itg, upgrd_waist_aid_num_itg, ext_golden_aid_num_itg,
        actv_prod_pay_amt_rate_itg, actv_incm_amt_rate_itg, task_total_itg, total_score, dt,
        avg_new_aid_rat_3_4_aid_num_3m, new_aid_rat_3_aid_num, new_aid_rat_4_aid_num, avg_new_aid_rat_3_4_aid_num_3m_aim,
        avg_new_aid_rat_3_4_aid_num_3m_score, avg_upgrd_high_waist_aid_num_3m, avg_upgrd_high_waist_aid_num_3m_aim, avg_upgrd_high_waist_aid_num_3m_score,
        new_aid_incm_com_rate, new_aid_incm_com_rate_aim, new_aid_incm_com_rate_score, guild_task_incm_lvl, guild_task_incm_lvl_aim, guild_task_incm_lvl_score,
        accum_incm_amt_diff, accum_incm_amt_aim, accum_incm_amt_socre,guild_cmp_name)
        values
        <foreach collection="list" item="i" separator=",">
            (#{i.guildCmpOwnrId},#{i.newAidRat34AidNum},#{i.newAidRat34AidNumRn},#{i.newAidRat34AidNumDiff},
            #{i.upgrdHighAidNum},#{i.upgrdHighAidNumDiff},#{i.upgrdWaistAidNum},#{i.upgrdWaistAidNumDiff},
            #{i.avgHighValidLiveAidNum},#{i.avgHighValidLiveAidNumRn},#{i.avgHighValidLiveAidNumDiff},
            #{i.avgWaistValidLiveAidNum},#{i.avgWaistValidLiveAidNumRn},#{i.avgWaistValidLiveAidNumDiff},
            #{i.avgNtoValidLiveAidRate},#{i.avgNtoValidLiveAidRateRn},#{i.avgStockValidLiveAidRate},
            #{i.avgStockValidLiveAidRateRn},#{i.newAuthGoldenAidNum},#{i.newAuthGoldenAidNumRn},#{i.extGoldenAidNum},
            #{i.actvProdPayAmt},#{i.prodPayAmt},#{i.actvProdPayAmtRate},#{i.validLiveNewAidProdPayAmt},
            #{i.validLiveNewAidProdPayAmtRn},#{i.validLiveNewAidProdPayAmtDiff},#{i.unvalidLiveNewAidProdPayAmt},
            #{i.unvalidLiveNewAidProdPayAmtRn},#{i.unvalidLiveNewAidProdPayAmtDiff},
            #{i.unvalidLiveNewAidProdPayAmtRate},#{i.totalUnvalidLiveNewAidProdPayAmtRate},
            #{i.unvalidLiveNewAidProdPayAmtRateDiff},#{i.actvIncmAmt},#{i.incmAmt},#{i.actvIncmAmtRate},
            #{i.validLiveNewAidIncmAmt},#{i.validLiveNewAidIncmAmtRn},#{i.validLiveNewAidIncmAmtDiff},
            #{i.unvalidLiveNewAidIncmAmt},#{i.unvalidLiveNewAidIncmAmtRn},#{i.unvalidLiveNewAidIncmAmtDiff},
            #{i.unvalidLiveNewAidIncmAmtRate},#{i.totalUnvalidLiveNewAidIncmAmtRate},
            #{i.unvalidLiveNewAidIncmAmtRateDiff},#{i.guildHealthPoint},#{i.newAidRat34AidNumRnScore},
            #{i.newAidRat34AidNumDiffScore},#{i.upgrdHighAidNumDiffScore},#{i.upgrdWaistAidNumDiffScore},
            #{i.avgHighValidLiveAidNumRnScore},#{i.avgWaistValidLiveAidNumRnScore},#{i.avgHighValidLiveAidNumDiffScore},
            #{i.avgWaistValidLiveAidNumDiffScore},#{i.avgNtoValidLiveAidRateRnScore},
            #{i.avgStockValidLiveAidRateRnScore},#{i.newAuthGoldenAidNumRnScore},#{i.validLiveNewAidProdPayAmtRnScore},
            #{i.unvalidLiveNewAidProdPayAmtRnScore},#{i.validLiveNewAidProdPayAmtDiffScore},
            #{i.unvalidLiveNewAidProdPayAmtDiffScore},#{i.unvalidLiveNewAidProdPayAmtRateDiffScore},
            #{i.validLiveNewAidIncmAmtRnScore},#{i.unvalidLiveNewAidIncmAmtRnScore},
            #{i.validLiveNewAidIncmAmtDiffScore},#{i.unvalidLiveNewAidIncmAmtDiffScore},
            #{i.unvalidLiveNewAidIncmAmtRateDiffScore},#{i.guildHealthPointScore},#{i.recruScore},#{i.hatchScore},
            #{i.retScore},#{i.rvnuScore},#{i.comScore},#{i.dataTotalScore},#{i.dataCmpStarLvl},#{i.cmpStarLvl},#{i.newAidRat34AidNumScore},
            #{i.upgrdHighAidNumScore},#{i.upgrdWaistAidNumScore},#{i.extGoldenAidNumScore},#{i.actvProdPayAmtRateScore},
            #{i.actvIncmAmtRateScore},#{i.taskTotalScore},#{i.newAidRat34AidNumItg},#{i.upgrdHighAidNumItg},
            #{i.upgrdWaistAidNumItg},#{i.extGoldenAidNumItg},#{i.actvProdPayAmtRateItg},#{i.actvIncmAmtRateItg},
            #{i.taskTotalItg},#{i.totalScore},#{i.dt},#{i.avgNewAidRat34AidNum3m},#{i.newAidRat3AidNum},#{i.newAidRat4AidNum},#{i.avgNewAidRat34AidNum3mAim},
            #{i.avgNewAidRat34AidNum3mScore},#{i.avgUpgrdHighWaistAidNum3m},#{i.avgUpgrdHighWaistAidNum3mAim},#{i.avgUpgrdHighWaistAidNum3mScore},#{i.newAidIncmComRate},
            #{i.newAidIncmComRateAim},#{i.newAidIncmComRateScore},#{i.guildTaskIncmLvl},#{i.guildTaskIncmLvlAim},#{i.guildTaskIncmLvlScore},#{i.accumIncmAmtDiff},#{i.accumIncmAmtAim}
            ,#{i.accumIncmAmtSocre},#{i.guildCmpName})
        </foreach>
    </insert>

    <insert id="insertPoints">
        insert ignore into guildrank_point_detail(uid, `point`, `type`, type_serial, status, due_dt, remark, dt, create_time, update_time)
        values
        <foreach collection="list" item="i" separator=",">
            ( #{i.uid}, #{i.point}, #{i.type}, #{i.typeSerial}, #{i.status}, #{i.dueDt}, #{i.remark}, #{i.dt},
            #{i.createTime}, #{i.updateTime})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatchHistory">
        insert into guildrank_history(`month`, main_guild_uid, guild_uid, `level`, `type`, update_time) values
        <foreach collection="list" item="i" separator=",">
            (#{i.month},#{i.mainGuildUid},#{i.guildUid},#{i.level},#{i.type},#{i.updateTime})
        </foreach>
        on duplicate key update main_guild_uid=values(main_guild_uid),level=values(level),type=values(type),update_time=values(update_time);
    </insert>

    <select id="selectDt" resultType="java.util.Date">
        select distinct dt from ${tbl}
    </select>

    <delete id="deleteByDt">
        delete from ${tbl} where dt=#{dt,jdbcType=DATE} limit ${limit}
    </delete>

    <insert id="insertSyncDetail">
        insert ignore into bigda_sync_detail(dt, table_name, status, sync_time, update_time)
        select distinct dt,'data_out.yy_dm_entity_guild_cmp_health_analysis_di',0,now(),now() from yy_dm_entity_guild_cmp_health_analysis_di_tmp
    </insert>

    <select id="selectLevelCount" resultType="com.yy.yyzone.guildrank.db.custom.model.KV">
        select data_cmp_star_lvl K,count(*) V from ${tbl} where dt=#{dt,jdbcType=DATE}
        group by data_cmp_star_lvl
    </select>

    <select id="selectGuildCmpAvgScoreByUid" resultMap="GuildCmpAvgScore">
        select recru_score,hatch_score,ret_score,rvnu_score,com_score,data_cmp_star_lvl level
        from yy_dm_entity_guild_cmp_health_analysis_di where dt=#{dt,jdbcType=DATE} and guild_cmp_ownr_id=#{uid,jdbcType=BIGINT}
    </select>

    <select id="selectGuildCmpAvgScoreByLvl" resultMap="GuildCmpAvgScore">
        select avg(recru_score)recru_score,avg(hatch_score)hatch_score,avg(ret_score)ret_score,avg(rvnu_score)rvnu_score,avg(com_score)com_score
        from yy_dm_entity_guild_cmp_health_analysis_di where dt=#{dt,jdbcType=DATE} and data_cmp_star_lvl=#{lvl,jdbcType=INTEGER}
    </select>
</mapper>