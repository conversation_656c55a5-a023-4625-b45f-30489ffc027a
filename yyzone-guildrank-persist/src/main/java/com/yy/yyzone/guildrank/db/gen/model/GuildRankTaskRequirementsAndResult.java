package com.yy.yyzone.guildrank.db.gen.model;

import java.util.Date;

public class GuildRankTaskRequirementsAndResult {
    /** id */
    private Long id;

    /** 主体uid */
    private Long guildCmpOwnrId;
    /**
     * 公司名
     */
    private String guildCmpName;

    /** 日期 */
    private Date dt;

    /** 公会近3个自然月月均新签3&4星主播数，保留两位小数后四舍五入取整 */
    private Double avgNewAidRat3Or4AidNum3m;

    /** 新签3星主播数，当月3星主播签约数量 */
    private Long newAidRat3AidNum;

    /** 新签4星主播数，当月4星主播签约数量 */
    private Long newAidRat4AidNum;

    /** 线上绑定3&4星主播任务要求 */
    private Integer avgNewAidRat3Or4AidNum3mAim;

    /** 线上绑定3&4星主播任务得分 */
    private Integer avgNewAidRat3Or4AidNum3mScore;

    /** 公会近3个自然月月均新增头&腰部主播数，保留两位小数后四舍五入取整 */
    private Double avgUpgrdHighWaistAidNum3m;

    /** 新增头&腰部主播任务要求 */
    private Integer avgUpgrdHighWaistAidNum3mAim;

    /** 新增头&腰部主播任务得分 */
    private Integer avgUpgrdHighWaistAidNum3mScore;

    /** 自然月新主播蓝钻任务完成率，保留整数 */
    private Double newAidIncmComRate;

    /** 新主播蓝钻任务完成率要求 */
    private Double newAidIncmComRateAim;

    /** 新主播蓝钻任务得分 */
    private Integer newAidIncmComRateScore;

    /** 公会蓝钻任务完成档位 */
    private Integer guildTaskIncmLvl;

    /** 公会蓝钻任务目标档位 */
    private Integer guildTaskIncmLvlAim;

    /** 公会蓝钻任务得分 */
    private Integer guildTaskIncmLvlScore;

    /** 自然月月累计蓝钻diff 单位元*/
    private Long accumIncmAmtDiff;

    /** 自然月月累计蓝钻任务要求 单位蓝钻 */
    private Long accumIncmAmtAim;

    /** 自然月月累计蓝钻任务得分 */
    private Integer accumIncmAmtSocre;

    /**
     * 月累积蓝钻收入
     */
    private Double incmAmt;
    /**
     * 当月新增头部主播数
     */
    private Integer upgrdHighAidNum;
    /**
     * 当月新增腰部主播数
     */
    private Integer upgrdWaistAidNum;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getGuildCmpOwnrId() {
        return guildCmpOwnrId;
    }

    public void setGuildCmpOwnrId(Long guildCmpOwnrId) {
        this.guildCmpOwnrId = guildCmpOwnrId;
    }

    public Date getDt() {
        return dt;
    }

    public void setDt(Date dt) {
        this.dt = dt;
    }

    public Double getAvgNewAidRat3Or4AidNum3m() {
        return avgNewAidRat3Or4AidNum3m;
    }

    public void setAvgNewAidRat3Or4AidNum3m(Double avgNewAidRat3Or4AidNum3m) {
        this.avgNewAidRat3Or4AidNum3m = avgNewAidRat3Or4AidNum3m;
    }

    public Long getNewAidRat3AidNum() {
        return newAidRat3AidNum;
    }

    public void setNewAidRat3AidNum(Long newAidRat3AidNum) {
        this.newAidRat3AidNum = newAidRat3AidNum;
    }

    public Long getNewAidRat4AidNum() {
        return newAidRat4AidNum;
    }

    public void setNewAidRat4AidNum(Long newAidRat4AidNum) {
        this.newAidRat4AidNum = newAidRat4AidNum;
    }

    public Integer getAvgNewAidRat3Or4AidNum3mAim() {
        return avgNewAidRat3Or4AidNum3mAim;
    }

    public void setAvgNewAidRat3Or4AidNum3mAim(Integer avgNewAidRat3Or4AidNum3mAim) {
        this.avgNewAidRat3Or4AidNum3mAim = avgNewAidRat3Or4AidNum3mAim;
    }

    public Integer getAvgNewAidRat3Or4AidNum3mScore() {
        return avgNewAidRat3Or4AidNum3mScore;
    }

    public void setAvgNewAidRat3Or4AidNum3mScore(Integer avgNewAidRat3Or4AidNum3mScore) {
        this.avgNewAidRat3Or4AidNum3mScore = avgNewAidRat3Or4AidNum3mScore;
    }

    public Double getAvgUpgrdHighWaistAidNum3m() {
        return avgUpgrdHighWaistAidNum3m;
    }

    public void setAvgUpgrdHighWaistAidNum3m(Double avgUpgrdHighWaistAidNum3m) {
        this.avgUpgrdHighWaistAidNum3m = avgUpgrdHighWaistAidNum3m;
    }

    public Integer getAvgUpgrdHighWaistAidNum3mAim() {
        return avgUpgrdHighWaistAidNum3mAim;
    }

    public void setAvgUpgrdHighWaistAidNum3mAim(Integer avgUpgrdHighWaistAidNum3mAim) {
        this.avgUpgrdHighWaistAidNum3mAim = avgUpgrdHighWaistAidNum3mAim;
    }

    public Integer getAvgUpgrdHighWaistAidNum3mScore() {
        return avgUpgrdHighWaistAidNum3mScore;
    }

    public void setAvgUpgrdHighWaistAidNum3mScore(Integer avgUpgrdHighWaistAidNum3mScore) {
        this.avgUpgrdHighWaistAidNum3mScore = avgUpgrdHighWaistAidNum3mScore;
    }

    public Double getNewAidIncmComRate() {
        return newAidIncmComRate;
    }

    public void setNewAidIncmComRate(Double newAidIncmComRate) {
        this.newAidIncmComRate = newAidIncmComRate;
    }

    public Double getNewAidIncmComRateAim() {
        return newAidIncmComRateAim;
    }

    public void setNewAidIncmComRateAim(Double newAidIncmComRateAim) {
        this.newAidIncmComRateAim = newAidIncmComRateAim;
    }

    public Integer getNewAidIncmComRateScore() {
        return newAidIncmComRateScore;
    }

    public void setNewAidIncmComRateScore(Integer newAidIncmComRateScore) {
        this.newAidIncmComRateScore = newAidIncmComRateScore;
    }

    public Integer getGuildTaskIncmLvl() {
        return guildTaskIncmLvl;
    }

    public void setGuildTaskIncmLvl(Integer guildTaskIncmLvl) {
        this.guildTaskIncmLvl = guildTaskIncmLvl;
    }

    public Integer getGuildTaskIncmLvlAim() {
        return guildTaskIncmLvlAim;
    }

    public void setGuildTaskIncmLvlAim(Integer guildTaskIncmLvlAim) {
        this.guildTaskIncmLvlAim = guildTaskIncmLvlAim;
    }

    public Integer getGuildTaskIncmLvlScore() {
        return guildTaskIncmLvlScore;
    }

    public void setGuildTaskIncmLvlScore(Integer guildTaskIncmLvlScore) {
        this.guildTaskIncmLvlScore = guildTaskIncmLvlScore;
    }

    public Long getAccumIncmAmtDiff() {
        return accumIncmAmtDiff;
    }

    public void setAccumIncmAmtDiff(Long accumIncmAmtDiff) {
        this.accumIncmAmtDiff = accumIncmAmtDiff;
    }

    public Long getAccumIncmAmtAim() {
        return accumIncmAmtAim;
    }

    public Long getAccumIncmAmtAimForBlueDiamondToKw(){
        return accumIncmAmtAim == null ? null : getAccumIncmAmtAim() / 10000000L;
    }

    public void setAccumIncmAmtAim(Long accumIncmAmtAim) {
        this.accumIncmAmtAim = accumIncmAmtAim;
    }

    public Integer getAccumIncmAmtSocre() {
        return accumIncmAmtSocre;
    }

    public void setAccumIncmAmtSocre(Integer accumIncmAmtSocre) {
        this.accumIncmAmtSocre = accumIncmAmtSocre;
    }

    public Double getIncmAmt() {
        return incmAmt;
    }

    public Long getIncmAmtForBlueDiamond() {
        return incmAmt == null ? null : (long)(incmAmt * 1000L);
    }

    public void setIncmAmt(Double incmAmt) {
        this.incmAmt = incmAmt;
    }

    public Integer getUpgrdHighAidNum() {
        return upgrdHighAidNum;
    }

    public void setUpgrdHighAidNum(Integer upgrdHighAidNum) {
        this.upgrdHighAidNum = upgrdHighAidNum;
    }

    public Integer getUpgrdWaistAidNum() {
        return upgrdWaistAidNum;
    }

    public void setUpgrdWaistAidNum(Integer upgrdWaistAidNum) {
        this.upgrdWaistAidNum = upgrdWaistAidNum;
    }

    public String getGuildCmpName() {
        return guildCmpName;
    }

    public void setGuildCmpName(String guildCmpName) {
        this.guildCmpName = guildCmpName;
    }

    @Override
    public String toString() {
        return "GuildRankTaskRequirementsAndResult{" +
                "id=" + id +
                ", guildCmpOwnrId=" + guildCmpOwnrId +
                ", guildCmpName='" + guildCmpName + '\'' +
                ", dt=" + dt +
                ", avgNewAidRat3Or4AidNum3m=" + avgNewAidRat3Or4AidNum3m +
                ", newAidRat3AidNum=" + newAidRat3AidNum +
                ", newAidRat4AidNum=" + newAidRat4AidNum +
                ", avgNewAidRat3Or4AidNum3mAim=" + avgNewAidRat3Or4AidNum3mAim +
                ", avgNewAidRat3Or4AidNum3mScore=" + avgNewAidRat3Or4AidNum3mScore +
                ", avgUpgrdHighWaistAidNum3m=" + avgUpgrdHighWaistAidNum3m +
                ", avgUpgrdHighWaistAidNum3mAim=" + avgUpgrdHighWaistAidNum3mAim +
                ", avgUpgrdHighWaistAidNum3mScore=" + avgUpgrdHighWaistAidNum3mScore +
                ", newAidIncmComRate=" + newAidIncmComRate +
                ", newAidIncmComRateAim=" + newAidIncmComRateAim +
                ", newAidIncmComRateScore=" + newAidIncmComRateScore +
                ", guildTaskIncmLvl=" + guildTaskIncmLvl +
                ", guildTaskIncmLvlAim=" + guildTaskIncmLvlAim +
                ", guildTaskIncmLvlScore=" + guildTaskIncmLvlScore +
                ", accumIncmAmtDiff=" + accumIncmAmtDiff +
                ", accumIncmAmtAim=" + accumIncmAmtAim +
                ", accumIncmAmtSocre=" + accumIncmAmtSocre +
                ", incmAmt=" + incmAmt +
                ", upgrdHighAidNum=" + upgrdHighAidNum +
                ", upgrdWaistAidNum=" + upgrdWaistAidNum +
                '}';
    }
}