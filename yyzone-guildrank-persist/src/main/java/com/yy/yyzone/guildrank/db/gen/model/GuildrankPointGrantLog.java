package com.yy.yyzone.guildrank.db.gen.model;

import java.util.Date;

public class GuildrankPointGrantLog {
    /**
     * id，自增主键
     */
    private Long id;

    /**
     * guildrank_point_detail.id
     */
    private Long detailId;

    /**
     * uid
     */
    private Long uid;

    /**
     * 积分
     */
    private Integer point;

    /**
     * 请求订单号
     */
    private String reqOrderNo;

    /**
     * 响应result
     */
    private Integer rspResult;

    /**
     * 响应错误提示
     */
    private String rspErrMsg;

    /**
     * 响应扩展信息
     */
    private String rspExt;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 获取id，自增主键
     * @return id id，自增主键
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置id，自增主键
     * @param id id，自增主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取guildrank_point_detail.id
     * @return detail_id guildrank_point_detail.id
     */
    public Long getDetailId() {
        return detailId;
    }

    /**
     * 设置guildrank_point_detail.id
     * @param detailId guildrank_point_detail.id
     */
    public void setDetailId(Long detailId) {
        this.detailId = detailId;
    }

    /**
     * 获取uid
     * @return uid uid
     */
    public Long getUid() {
        return uid;
    }

    /**
     * 设置uid
     * @param uid uid
     */
    public void setUid(Long uid) {
        this.uid = uid;
    }

    /**
     * 获取积分
     * @return point 积分
     */
    public Integer getPoint() {
        return point;
    }

    /**
     * 设置积分
     * @param point 积分
     */
    public void setPoint(Integer point) {
        this.point = point;
    }

    /**
     * 获取请求订单号
     * @return req_order_no 请求订单号
     */
    public String getReqOrderNo() {
        return reqOrderNo;
    }

    /**
     * 设置请求订单号
     * @param reqOrderNo 请求订单号
     */
    public void setReqOrderNo(String reqOrderNo) {
        this.reqOrderNo = reqOrderNo;
    }

    /**
     * 获取响应result
     * @return rsp_result 响应result
     */
    public Integer getRspResult() {
        return rspResult;
    }

    /**
     * 设置响应result
     * @param rspResult 响应result
     */
    public void setRspResult(Integer rspResult) {
        this.rspResult = rspResult;
    }

    /**
     * 获取响应错误提示
     * @return rsp_err_msg 响应错误提示
     */
    public String getRspErrMsg() {
        return rspErrMsg;
    }

    /**
     * 设置响应错误提示
     * @param rspErrMsg 响应错误提示
     */
    public void setRspErrMsg(String rspErrMsg) {
        this.rspErrMsg = rspErrMsg;
    }

    /**
     * 获取响应扩展信息
     * @return rsp_ext 响应扩展信息
     */
    public String getRspExt() {
        return rspExt;
    }

    /**
     * 设置响应扩展信息
     * @param rspExt 响应扩展信息
     */
    public void setRspExt(String rspExt) {
        this.rspExt = rspExt;
    }

    /**
     * 获取创建时间
     * @return create_time 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", detailId=").append(detailId);
        sb.append(", uid=").append(uid);
        sb.append(", point=").append(point);
        sb.append(", reqOrderNo=").append(reqOrderNo);
        sb.append(", rspResult=").append(rspResult);
        sb.append(", rspErrMsg=").append(rspErrMsg);
        sb.append(", rspExt=").append(rspExt);
        sb.append(", createTime=").append(createTime);
        sb.append("]");
        return sb.toString();
    }
}