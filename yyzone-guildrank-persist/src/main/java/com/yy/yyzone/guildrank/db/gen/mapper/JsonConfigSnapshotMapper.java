package com.yy.yyzone.guildrank.db.gen.mapper;

import com.yy.yyzone.guildrank.db.gen.model.JsonConfigSnapshot;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface JsonConfigSnapshotMapper {

    @Insert({
        "<script>",
        "INSERT INTO guild_rank_json_config_snapshot (dt, config_name, config_id) VALUES ",
        "<foreach collection='list' item='item' separator=','>",
        "(#{item.dt}, #{item.configName}, #{item.configId})",
        "</foreach>",
        "ON DUPLICATE KEY UPDATE config_id = VALUES(config_id)",
        "</script>"
    })
    void batchInsert(@Param("list") List<JsonConfigSnapshot> list);

    @Select("SELECT dt, config_name as configName, config_id as configId FROM guild_rank_json_config_snapshot WHERE dt = #{dt} AND config_name = #{configName}")
    JsonConfigSnapshot selectByDtAndConfigName(@Param("dt") Integer dt, @Param("configName") String configName);
}