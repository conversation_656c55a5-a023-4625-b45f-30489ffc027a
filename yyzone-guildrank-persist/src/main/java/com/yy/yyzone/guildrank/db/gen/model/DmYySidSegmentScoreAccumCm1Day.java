package com.yy.yyzone.guildrank.db.gen.model;

public class DmYySidSegmentScoreAccumCm1Day {
    /**
     * 
     */
    private Long id;

    /**
     * 公会owuid
     */
    private Long sidOwnerid;

    /**
     * 汇总得分
     */
    private Double psAll;

    /**
     * 上月段位
     */
    private String grade;

    /**
     * 汇总得分按上月段位排名
     */
    private Integer gradeRn;

    /**
     * 日期
     */
    private String dt;

    /**
     * 	公会owyyid
     */
    private Long sidOwyyid;

    /**
     * 公会总分值
     */
    private Double psAllWeight;

    /**
     * 总加权百分比
     */
    private Double weightAb;

    /**
     * 累计新签主播数
     */
    private Long newUvCm;

    /**
     * 累计新签主播数加权百分比
     */
    private Double weightA;

    /**
     * 累计新增活跃主播数
     */
    private Long hActUv;

    /**
     * 累计新增活跃主播数加权百分比
     */
    private Double weightB;

    /**
     * 获取
     * @return id 
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置
     * @param id 
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取公会owuid
     * @return sid_ownerid 公会owuid
     */
    public Long getSidOwnerid() {
        return sidOwnerid;
    }

    /**
     * 设置公会owuid
     * @param sidOwnerid 公会owuid
     */
    public void setSidOwnerid(Long sidOwnerid) {
        this.sidOwnerid = sidOwnerid;
    }

    /**
     * 获取汇总得分
     * @return ps_all 汇总得分
     */
    public Double getPsAll() {
        return psAll;
    }

    /**
     * 设置汇总得分
     * @param psAll 汇总得分
     */
    public void setPsAll(Double psAll) {
        this.psAll = psAll;
    }

    /**
     * 获取上月段位
     * @return grade 上月段位
     */
    public String getGrade() {
        return grade;
    }

    /**
     * 设置上月段位
     * @param grade 上月段位
     */
    public void setGrade(String grade) {
        this.grade = grade;
    }

    /**
     * 获取汇总得分按上月段位排名
     * @return grade_rn 汇总得分按上月段位排名
     */
    public Integer getGradeRn() {
        return gradeRn;
    }

    /**
     * 设置汇总得分按上月段位排名
     * @param gradeRn 汇总得分按上月段位排名
     */
    public void setGradeRn(Integer gradeRn) {
        this.gradeRn = gradeRn;
    }

    /**
     * 获取日期
     * @return dt 日期
     */
    public String getDt() {
        return dt;
    }

    /**
     * 设置日期
     * @param dt 日期
     */
    public void setDt(String dt) {
        this.dt = dt;
    }

    /**
     * 获取	公会owyyid
     * @return sid_owyyid 	公会owyyid
     */
    public Long getSidOwyyid() {
        return sidOwyyid;
    }

    /**
     * 设置	公会owyyid
     * @param sidOwyyid 	公会owyyid
     */
    public void setSidOwyyid(Long sidOwyyid) {
        this.sidOwyyid = sidOwyyid;
    }

    /**
     * 获取公会总分值
     * @return ps_all_weight 公会总分值
     */
    public Double getPsAllWeight() {
        return psAllWeight;
    }

    /**
     * 设置公会总分值
     * @param psAllWeight 公会总分值
     */
    public void setPsAllWeight(Double psAllWeight) {
        this.psAllWeight = psAllWeight;
    }

    /**
     * 获取总加权百分比
     * @return weight_ab 总加权百分比
     */
    public Double getWeightAb() {
        return weightAb;
    }

    /**
     * 设置总加权百分比
     * @param weightAb 总加权百分比
     */
    public void setWeightAb(Double weightAb) {
        this.weightAb = weightAb;
    }

    /**
     * 获取累计新签主播数
     * @return new_uv_cm 累计新签主播数
     */
    public Long getNewUvCm() {
        return newUvCm;
    }

    /**
     * 设置累计新签主播数
     * @param newUvCm 累计新签主播数
     */
    public void setNewUvCm(Long newUvCm) {
        this.newUvCm = newUvCm;
    }

    /**
     * 获取累计新签主播数加权百分比
     * @return weight_a 累计新签主播数加权百分比
     */
    public Double getWeightA() {
        return weightA;
    }

    /**
     * 设置累计新签主播数加权百分比
     * @param weightA 累计新签主播数加权百分比
     */
    public void setWeightA(Double weightA) {
        this.weightA = weightA;
    }

    /**
     * 获取累计新增活跃主播数
     * @return h_act_uv 累计新增活跃主播数
     */
    public Long gethActUv() {
        return hActUv;
    }

    /**
     * 设置累计新增活跃主播数
     * @param hActUv 累计新增活跃主播数
     */
    public void sethActUv(Long hActUv) {
        this.hActUv = hActUv;
    }

    /**
     * 获取累计新增活跃主播数加权百分比
     * @return weight_b 累计新增活跃主播数加权百分比
     */
    public Double getWeightB() {
        return weightB;
    }

    /**
     * 设置累计新增活跃主播数加权百分比
     * @param weightB 累计新增活跃主播数加权百分比
     */
    public void setWeightB(Double weightB) {
        this.weightB = weightB;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", sidOwnerid=").append(sidOwnerid);
        sb.append(", psAll=").append(psAll);
        sb.append(", grade=").append(grade);
        sb.append(", gradeRn=").append(gradeRn);
        sb.append(", dt=").append(dt);
        sb.append(", sidOwyyid=").append(sidOwyyid);
        sb.append(", psAllWeight=").append(psAllWeight);
        sb.append(", weightAb=").append(weightAb);
        sb.append(", newUvCm=").append(newUvCm);
        sb.append(", weightA=").append(weightA);
        sb.append(", hActUv=").append(hActUv);
        sb.append(", weightB=").append(weightB);
        sb.append("]");
        return sb.toString();
    }
}