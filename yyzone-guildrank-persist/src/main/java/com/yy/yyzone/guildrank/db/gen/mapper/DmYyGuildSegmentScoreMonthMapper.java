package com.yy.yyzone.guildrank.db.gen.mapper;

import com.yy.yyzone.guildrank.db.gen.model.DmYyGuildSegmentScoreMonth;
import com.yy.yyzone.guildrank.db.gen.model.DmYyGuildSegmentScoreMonthExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DmYyGuildSegmentScoreMonthMapper {
    int countByExample(DmYyGuildSegmentScoreMonthExample example);

    int deleteByExample(DmYyGuildSegmentScoreMonthExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DmYyGuildSegmentScoreMonth record);

    int insertSelective(DmYyGuildSegmentScoreMonth record);

    List<DmYyGuildSegmentScoreMonth> selectByExample(DmYyGuildSegmentScoreMonthExample example);

    DmYyGuildSegmentScoreMonth selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DmYyGuildSegmentScoreMonth record, @Param("example") DmYyGuildSegmentScoreMonthExample example);

    int updateByExample(@Param("record") DmYyGuildSegmentScoreMonth record, @Param("example") DmYyGuildSegmentScoreMonthExample example);

    int updateByPrimaryKeySelective(DmYyGuildSegmentScoreMonth record);

    int updateByPrimaryKey(DmYyGuildSegmentScoreMonth record);
}