package com.yy.yyzone.guildrank.db.gen.mapper;

import com.yy.yyzone.guildrank.db.gen.model.YyDmEntityGuildCmpHealthAnalysisDi;
import com.yy.yyzone.guildrank.db.gen.model.YyDmEntityGuildCmpHealthAnalysisDiExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface YyDmEntityGuildCmpHealthAnalysisDiMapper {
    int countByExample(YyDmEntityGuildCmpHealthAnalysisDiExample example);

    int deleteByExample(YyDmEntityGuildCmpHealthAnalysisDiExample example);

    int deleteByPrimaryKey(Long id);

    int insert(YyDmEntityGuildCmpHealthAnalysisDi record);

    int insertSelective(YyDmEntityGuildCmpHealthAnalysisDi record);

    List<YyDmEntityGuildCmpHealthAnalysisDi> selectByExample(YyDmEntityGuildCmpHealthAnalysisDiExample example);

    YyDmEntityGuildCmpHealthAnalysisDi selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") YyDmEntityGuildCmpHealthAnalysisDi record, @Param("example") YyDmEntityGuildCmpHealthAnalysisDiExample example);

    int updateByExample(@Param("record") YyDmEntityGuildCmpHealthAnalysisDi record, @Param("example") YyDmEntityGuildCmpHealthAnalysisDiExample example);

    int updateByPrimaryKeySelective(YyDmEntityGuildCmpHealthAnalysisDi record);

    int updateByPrimaryKey(YyDmEntityGuildCmpHealthAnalysisDi record);
}