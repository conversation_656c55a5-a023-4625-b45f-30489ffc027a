package com.yy.yyzone.guildrank.db.gen.model;

import java.util.Date;

public class GuildRankPlatformActivityTaskResult {
    private Integer dt;
    private Long uid;
    private Long yy;
    private String guildName;
    private String springHeadlineTaskDetail;
    private String springHeadlineCompletion;
    private Integer springHeadlineScore;
    private String summerHeadlineTaskDetail;
    private String summerHeadlineCompletion;
    private Integer summerHeadlineScore;
    private String guildRaceS1TaskDetail;
    private String guildRaceS1Completion;
    private Integer guildRaceS1Score;
    private String guildRaceS2TaskDetail;
    private String guildRaceS2Completion;
    private Integer guildRaceS2Score;
    private String guildRaceMainTaskDetail;
    private String guildRaceMainCompletion;
    private Integer guildRaceMainScore;
    private String personalRaceMainTaskDetail;
    private String personalRaceMainCompletion;
    private Integer personalRaceMainScore;
    private String remark;
    private String operator;
    private Date createTime;

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getDt() {
        return dt;
    }

    public void setDt(Integer dt) {
        this.dt = dt;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Long getYy() {
        return yy;
    }

    public void setYy(Long yy) {
        this.yy = yy;
    }

    public String getGuildName() {
        return guildName;
    }

    public void setGuildName(String guildName) {
        this.guildName = guildName;
    }

    public String getSpringHeadlineTaskDetail() {
        return springHeadlineTaskDetail;
    }

    public void setSpringHeadlineTaskDetail(String springHeadlineTaskDetail) {
        this.springHeadlineTaskDetail = springHeadlineTaskDetail;
    }

    public String getSpringHeadlineCompletion() {
        return springHeadlineCompletion;
    }

    public void setSpringHeadlineCompletion(String springHeadlineCompletion) {
        this.springHeadlineCompletion = springHeadlineCompletion;
    }

    public Integer getSpringHeadlineScore() {
        return springHeadlineScore;
    }

    public void setSpringHeadlineScore(Integer springHeadlineScore) {
        this.springHeadlineScore = springHeadlineScore;
    }

    public String getSummerHeadlineTaskDetail() {
        return summerHeadlineTaskDetail;
    }

    public void setSummerHeadlineTaskDetail(String summerHeadlineTaskDetail) {
        this.summerHeadlineTaskDetail = summerHeadlineTaskDetail;
    }

    public String getSummerHeadlineCompletion() {
        return summerHeadlineCompletion;
    }

    public void setSummerHeadlineCompletion(String summerHeadlineCompletion) {
        this.summerHeadlineCompletion = summerHeadlineCompletion;
    }

    public Integer getSummerHeadlineScore() {
        return summerHeadlineScore;
    }

    public void setSummerHeadlineScore(Integer summerHeadlineScore) {
        this.summerHeadlineScore = summerHeadlineScore;
    }

    public String getGuildRaceS1TaskDetail() {
        return guildRaceS1TaskDetail;
    }

    public void setGuildRaceS1TaskDetail(String guildRaceS1TaskDetail) {
        this.guildRaceS1TaskDetail = guildRaceS1TaskDetail;
    }

    public String getGuildRaceS1Completion() {
        return guildRaceS1Completion;
    }

    public void setGuildRaceS1Completion(String guildRaceS1Completion) {
        this.guildRaceS1Completion = guildRaceS1Completion;
    }

    public Integer getGuildRaceS1Score() {
        return guildRaceS1Score;
    }

    public void setGuildRaceS1Score(Integer guildRaceS1Score) {
        this.guildRaceS1Score = guildRaceS1Score;
    }

    public String getGuildRaceS2TaskDetail() {
        return guildRaceS2TaskDetail;
    }

    public void setGuildRaceS2TaskDetail(String guildRaceS2TaskDetail) {
        this.guildRaceS2TaskDetail = guildRaceS2TaskDetail;
    }

    public String getGuildRaceS2Completion() {
        return guildRaceS2Completion;
    }

    public void setGuildRaceS2Completion(String guildRaceS2Completion) {
        this.guildRaceS2Completion = guildRaceS2Completion;
    }


    public Integer getGuildRaceS2Score() {
        return guildRaceS2Score;
    }

    public void setGuildRaceS2Score(Integer guildRaceS2Score) {
        this.guildRaceS2Score = guildRaceS2Score;
    }

    public String getGuildRaceMainTaskDetail() {
        return guildRaceMainTaskDetail;
    }

    public void setGuildRaceMainTaskDetail(String guildRaceMainTaskDetail) {
        this.guildRaceMainTaskDetail = guildRaceMainTaskDetail;
    }

    public String getGuildRaceMainCompletion() {
        return guildRaceMainCompletion;
    }

    public void setGuildRaceMainCompletion(String guildRaceMainCompletion) {
        this.guildRaceMainCompletion = guildRaceMainCompletion;
    }

    public Integer getGuildRaceMainScore() {
        return guildRaceMainScore;
    }

    public void setGuildRaceMainScore(Integer guildRaceMainScore) {
        this.guildRaceMainScore = guildRaceMainScore;
    }

    public String getPersonalRaceMainTaskDetail() {
        return personalRaceMainTaskDetail;
    }

    public void setPersonalRaceMainTaskDetail(String personalRaceMainTaskDetail) {
        this.personalRaceMainTaskDetail = personalRaceMainTaskDetail;
    }

    public String getPersonalRaceMainCompletion() {
        return personalRaceMainCompletion;
    }

    public void setPersonalRaceMainCompletion(String personalRaceMainCompletion) {
        this.personalRaceMainCompletion = personalRaceMainCompletion;
    }

    public Integer getPersonalRaceMainScore() {
        return personalRaceMainScore;
    }

    public void setPersonalRaceMainScore(Integer personalRaceMainScore) {
        this.personalRaceMainScore = personalRaceMainScore;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "GuildRankPlatformActivityTaskResult{" +
                "dt=" + dt +
                ", uid=" + uid +
                ", yy=" + yy +
                ", guildName='" + guildName + '\'' +
                ", springHeadlineTaskDetail='" + springHeadlineTaskDetail + '\'' +
                ", springHeadlineCompletion='" + springHeadlineCompletion + '\'' +
                ", springHeadlineScore=" + springHeadlineScore +
                ", summerHeadlineTaskDetail='" + summerHeadlineTaskDetail + '\'' +
                ", summerHeadlineCompletion='" + summerHeadlineCompletion + '\'' +
                ", summerHeadlineScore=" + summerHeadlineScore +
                ", guildRaceS1TaskDetail='" + guildRaceS1TaskDetail + '\'' +
                ", guildRaceS1Completion='" + guildRaceS1Completion + '\'' +
                ", guildRaceS1Score=" + guildRaceS1Score +
                ", guildRaceS2TaskDetail='" + guildRaceS2TaskDetail + '\'' +
                ", guildRaceS2Completion='" + guildRaceS2Completion + '\'' +
                ", guildRaceS2Score=" + guildRaceS2Score +
                ", guildRaceMainTaskDetail='" + guildRaceMainTaskDetail + '\'' +
                ", guildRaceMainCompletion='" + guildRaceMainCompletion + '\'' +
                ", guildRaceMainScore=" + guildRaceMainScore +
                ", personalRaceMainTaskDetail='" + personalRaceMainTaskDetail + '\'' +
                ", personalRaceMainCompletion='" + personalRaceMainCompletion + '\'' +
                ", personalRaceMainScore=" + personalRaceMainScore +
                ", remark='" + remark + '\'' +
                ", operator='" + operator + '\'' +
                ", createTime=" + createTime +
                '}';
    }
}