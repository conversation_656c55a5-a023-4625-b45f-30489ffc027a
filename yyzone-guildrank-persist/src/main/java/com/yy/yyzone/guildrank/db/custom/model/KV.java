package com.yy.yyzone.guildrank.db.custom.model;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/3/8
 *
 */
public class KV<K, V> {
    private K k;
    private V v;

    public K getK() {
        return this.k;
    }

    public void setK(final K k) {
        this.k = k;
    }

    public V getV() {
        return this.v;
    }

    public void setV(final V v) {
        this.v = v;
    }

    public static <K, V> KV<K, V> of(K k, V v) {
        KV kv = new KV();
        kv.setK(k);
        kv.setV(v);
        return kv;
    }

    public static <K, V> V get(List<KV<K, V>> kvs, K k, V defaultVal) {
        if (kvs != null) {
            for (KV<K, V> kv : kvs) {
                if (kv.getK().equals(k)) {
                    return kv.getV() == null ? defaultVal : kv.getV();
                }
            }
        }

        return defaultVal;
    }

    public static <K, V> Map<K, V> toMap(List<KV<K, V>> kvs) {
        if (kvs == null || kvs.size() == 0) {
            return Collections.emptyMap();
        }

        return kvs.stream().collect(Collectors.toMap(l -> l.getK(), l -> l.getV(), (o, n) -> o));
    }
}