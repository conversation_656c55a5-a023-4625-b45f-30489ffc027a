package com.yy.yyzone.guildrank.db.gen.mapper;

import com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreMonth;
import com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreMonthExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DmYySidSegmentScoreMonthMapper {
    int countByExample(DmYySidSegmentScoreMonthExample example);

    int deleteByExample(DmYySidSegmentScoreMonthExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DmYySidSegmentScoreMonth record);

    int insertSelective(DmYySidSegmentScoreMonth record);

    List<DmYySidSegmentScoreMonth> selectByExample(DmYySidSegmentScoreMonthExample example);

    DmYySidSegmentScoreMonth selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DmYySidSegmentScoreMonth record, @Param("example") DmYySidSegmentScoreMonthExample example);

    int updateByExample(@Param("record") DmYySidSegmentScoreMonth record, @Param("example") DmYySidSegmentScoreMonthExample example);

    int updateByPrimaryKeySelective(DmYySidSegmentScoreMonth record);

    int updateByPrimaryKey(DmYySidSegmentScoreMonth record);
}