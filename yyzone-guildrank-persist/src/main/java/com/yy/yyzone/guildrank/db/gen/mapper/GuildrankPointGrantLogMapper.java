package com.yy.yyzone.guildrank.db.gen.mapper;

import com.yy.yyzone.guildrank.db.gen.model.GuildrankPointGrantLog;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankPointGrantLogExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface GuildrankPointGrantLogMapper {
    int countByExample(GuildrankPointGrantLogExample example);

    int deleteByExample(GuildrankPointGrantLogExample example);

    int deleteByPrimaryKey(Long id);

    int insert(GuildrankPointGrantLog record);

    int insertSelective(GuildrankPointGrantLog record);

    List<GuildrankPointGrantLog> selectByExample(GuildrankPointGrantLogExample example);

    GuildrankPointGrantLog selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") GuildrankPointGrantLog record, @Param("example") GuildrankPointGrantLogExample example);

    int updateByExample(@Param("record") GuildrankPointGrantLog record, @Param("example") GuildrankPointGrantLogExample example);

    int updateByPrimaryKeySelective(GuildrankPointGrantLog record);

    int updateByPrimaryKey(GuildrankPointGrantLog record);
}