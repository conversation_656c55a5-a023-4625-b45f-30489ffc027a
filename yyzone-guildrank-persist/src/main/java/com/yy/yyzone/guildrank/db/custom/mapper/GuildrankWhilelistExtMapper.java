package com.yy.yyzone.guildrank.db.custom.mapper;

import com.yy.yyzone.guildrank.db.custom.model.KV;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankWhilelist;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankWhilelistExample;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface GuildrankWhilelistExtMapper {
    /**
     * 批量插入
     * @param list
     * @return
     */
    int insertBatch(@Param("list") List<GuildrankWhilelist> list);

    List<KV<Long, Integer>> selectRank(@Param("month") String month);
}