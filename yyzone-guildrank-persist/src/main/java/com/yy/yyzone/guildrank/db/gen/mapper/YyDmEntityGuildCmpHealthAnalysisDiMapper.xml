<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yy.yyzone.guildrank.db.gen.mapper.YyDmEntityGuildCmpHealthAnalysisDiMapper" >
  <resultMap id="BaseResultMap" type="com.yy.yyzone.guildrank.db.gen.model.YyDmEntityGuildCmpHealthAnalysisDi" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="guild_cmp_ownr_id" property="guildCmpOwnrId" jdbcType="BIGINT" />
    <result column="new_aid_rat_3_4_aid_num" property="newAidRat34AidNum" jdbcType="BIGINT" />
    <result column="new_aid_rat_3_4_aid_num_rn" property="newAidRat34AidNumRn" jdbcType="BIGINT" />
    <result column="new_aid_rat_3_4_aid_num_diff" property="newAidRat34AidNumDiff" jdbcType="BIGINT" />
    <result column="upgrd_high_aid_num" property="upgrdHighAidNum" jdbcType="BIGINT" />
    <result column="upgrd_high_aid_num_diff" property="upgrdHighAidNumDiff" jdbcType="BIGINT" />
    <result column="upgrd_waist_aid_num" property="upgrdWaistAidNum" jdbcType="BIGINT" />
    <result column="upgrd_waist_aid_num_diff" property="upgrdWaistAidNumDiff" jdbcType="BIGINT" />
    <result column="avg_high_valid_live_aid_num" property="avgHighValidLiveAidNum" jdbcType="DOUBLE" />
    <result column="avg_high_valid_live_aid_num_rn" property="avgHighValidLiveAidNumRn" jdbcType="BIGINT" />
    <result column="avg_high_valid_live_aid_num_diff" property="avgHighValidLiveAidNumDiff" jdbcType="DOUBLE" />
    <result column="avg_waist_valid_live_aid_num" property="avgWaistValidLiveAidNum" jdbcType="DOUBLE" />
    <result column="avg_waist_valid_live_aid_num_rn" property="avgWaistValidLiveAidNumRn" jdbcType="BIGINT" />
    <result column="avg_waist_valid_live_aid_num_diff" property="avgWaistValidLiveAidNumDiff" jdbcType="DOUBLE" />
    <result column="avg_nto_valid_live_aid_rate" property="avgNtoValidLiveAidRate" jdbcType="DOUBLE" />
    <result column="avg_nto_valid_live_aid_rate_rn" property="avgNtoValidLiveAidRateRn" jdbcType="BIGINT" />
    <result column="avg_stock_valid_live_aid_rate" property="avgStockValidLiveAidRate" jdbcType="DOUBLE" />
    <result column="avg_stock_valid_live_aid_rate_rn" property="avgStockValidLiveAidRateRn" jdbcType="BIGINT" />
    <result column="new_auth_golden_aid_num" property="newAuthGoldenAidNum" jdbcType="BIGINT" />
    <result column="new_auth_golden_aid_num_rn" property="newAuthGoldenAidNumRn" jdbcType="BIGINT" />
    <result column="ext_golden_aid_num" property="extGoldenAidNum" jdbcType="BIGINT" />
    <result column="actv_prod_pay_amt" property="actvProdPayAmt" jdbcType="DOUBLE" />
    <result column="prod_pay_amt" property="prodPayAmt" jdbcType="DOUBLE" />
    <result column="actv_prod_pay_amt_rate" property="actvProdPayAmtRate" jdbcType="DOUBLE" />
    <result column="valid_live_new_aid_prod_pay_amt" property="validLiveNewAidProdPayAmt" jdbcType="DOUBLE" />
    <result column="valid_live_new_aid_prod_pay_amt_rn" property="validLiveNewAidProdPayAmtRn" jdbcType="BIGINT" />
    <result column="valid_live_new_aid_prod_pay_amt_diff" property="validLiveNewAidProdPayAmtDiff" jdbcType="DOUBLE" />
    <result column="unvalid_live_new_aid_prod_pay_amt" property="unvalidLiveNewAidProdPayAmt" jdbcType="DOUBLE" />
    <result column="unvalid_live_new_aid_prod_pay_amt_rn" property="unvalidLiveNewAidProdPayAmtRn" jdbcType="BIGINT" />
    <result column="unvalid_live_new_aid_prod_pay_amt_diff" property="unvalidLiveNewAidProdPayAmtDiff" jdbcType="DOUBLE" />
    <result column="unvalid_live_new_aid_prod_pay_amt_rate" property="unvalidLiveNewAidProdPayAmtRate" jdbcType="DOUBLE" />
    <result column="total_unvalid_live_new_aid_prod_pay_amt_rate" property="totalUnvalidLiveNewAidProdPayAmtRate" jdbcType="DOUBLE" />
    <result column="unvalid_live_new_aid_prod_pay_amt_rate_diff" property="unvalidLiveNewAidProdPayAmtRateDiff" jdbcType="DOUBLE" />
    <result column="actv_incm_amt" property="actvIncmAmt" jdbcType="DOUBLE" />
    <result column="incm_amt" property="incmAmt" jdbcType="DOUBLE" />
    <result column="actv_incm_amt_rate" property="actvIncmAmtRate" jdbcType="DOUBLE" />
    <result column="valid_live_new_aid_incm_amt" property="validLiveNewAidIncmAmt" jdbcType="DOUBLE" />
    <result column="valid_live_new_aid_incm_amt_rn" property="validLiveNewAidIncmAmtRn" jdbcType="BIGINT" />
    <result column="valid_live_new_aid_incm_amt_diff" property="validLiveNewAidIncmAmtDiff" jdbcType="DOUBLE" />
    <result column="unvalid_live_new_aid_incm_amt" property="unvalidLiveNewAidIncmAmt" jdbcType="DOUBLE" />
    <result column="unvalid_live_new_aid_incm_amt_rn" property="unvalidLiveNewAidIncmAmtRn" jdbcType="BIGINT" />
    <result column="unvalid_live_new_aid_incm_amt_diff" property="unvalidLiveNewAidIncmAmtDiff" jdbcType="DOUBLE" />
    <result column="unvalid_live_new_aid_incm_amt_rate" property="unvalidLiveNewAidIncmAmtRate" jdbcType="DOUBLE" />
    <result column="total_unvalid_live_new_aid_incm_amt_rate" property="totalUnvalidLiveNewAidIncmAmtRate" jdbcType="DOUBLE" />
    <result column="unvalid_live_new_aid_incm_amt_rate_diff" property="unvalidLiveNewAidIncmAmtRateDiff" jdbcType="DOUBLE" />
    <result column="guild_health_point" property="guildHealthPoint" jdbcType="DOUBLE" />
    <result column="new_aid_rat_3_4_aid_num_rn_score" property="newAidRat34AidNumRnScore" jdbcType="INTEGER" />
    <result column="new_aid_rat_3_4_aid_num_diff_score" property="newAidRat34AidNumDiffScore" jdbcType="INTEGER" />
    <result column="upgrd_high_aid_num_diff_score" property="upgrdHighAidNumDiffScore" jdbcType="INTEGER" />
    <result column="upgrd_waist_aid_num_diff_score" property="upgrdWaistAidNumDiffScore" jdbcType="INTEGER" />
    <result column="avg_high_valid_live_aid_num_rn_score" property="avgHighValidLiveAidNumRnScore" jdbcType="INTEGER" />
    <result column="avg_waist_valid_live_aid_num_rn_score" property="avgWaistValidLiveAidNumRnScore" jdbcType="INTEGER" />
    <result column="avg_high_valid_live_aid_num_diff_score" property="avgHighValidLiveAidNumDiffScore" jdbcType="INTEGER" />
    <result column="avg_waist_valid_live_aid_num_diff_score" property="avgWaistValidLiveAidNumDiffScore" jdbcType="INTEGER" />
    <result column="avg_nto_valid_live_aid_rate_rn_score" property="avgNtoValidLiveAidRateRnScore" jdbcType="INTEGER" />
    <result column="avg_stock_valid_live_aid_rate_rn_score" property="avgStockValidLiveAidRateRnScore" jdbcType="INTEGER" />
    <result column="new_auth_golden_aid_num_rn_score" property="newAuthGoldenAidNumRnScore" jdbcType="INTEGER" />
    <result column="valid_live_new_aid_prod_pay_amt_rn_score" property="validLiveNewAidProdPayAmtRnScore" jdbcType="INTEGER" />
    <result column="unvalid_live_new_aid_prod_pay_amt_rn_score" property="unvalidLiveNewAidProdPayAmtRnScore" jdbcType="INTEGER" />
    <result column="valid_live_new_aid_prod_pay_amt_diff_score" property="validLiveNewAidProdPayAmtDiffScore" jdbcType="INTEGER" />
    <result column="unvalid_live_new_aid_prod_pay_amt_diff_score" property="unvalidLiveNewAidProdPayAmtDiffScore" jdbcType="INTEGER" />
    <result column="unvalid_live_new_aid_prod_pay_amt_rate_diff_score" property="unvalidLiveNewAidProdPayAmtRateDiffScore" jdbcType="INTEGER" />
    <result column="valid_live_new_aid_incm_amt_rn_score" property="validLiveNewAidIncmAmtRnScore" jdbcType="INTEGER" />
    <result column="unvalid_live_new_aid_incm_amt_rn_score" property="unvalidLiveNewAidIncmAmtRnScore" jdbcType="INTEGER" />
    <result column="valid_live_new_aid_incm_amt_diff_score" property="validLiveNewAidIncmAmtDiffScore" jdbcType="INTEGER" />
    <result column="unvalid_live_new_aid_incm_amt_diff_score" property="unvalidLiveNewAidIncmAmtDiffScore" jdbcType="INTEGER" />
    <result column="unvalid_live_new_aid_incm_amt_rate_diff_score" property="unvalidLiveNewAidIncmAmtRateDiffScore" jdbcType="INTEGER" />
    <result column="guild_health_point_score" property="guildHealthPointScore" jdbcType="INTEGER" />
    <result column="recru_score" property="recruScore" jdbcType="INTEGER" />
    <result column="hatch_score" property="hatchScore" jdbcType="INTEGER" />
    <result column="ret_score" property="retScore" jdbcType="INTEGER" />
    <result column="rvnu_score" property="rvnuScore" jdbcType="INTEGER" />
    <result column="com_score" property="comScore" jdbcType="INTEGER" />
    <result column="data_total_score" property="dataTotalScore" jdbcType="INTEGER" />
    <result column="data_cmp_star_lvl" property="dataCmpStarLvl" jdbcType="INTEGER" />
    <result column="cmp_star_lvl" property="cmpStarLvl" jdbcType="INTEGER" />
    <result column="new_aid_rat_3_4_aid_num_score" property="newAidRat34AidNumScore" jdbcType="INTEGER" />
    <result column="upgrd_high_aid_num_score" property="upgrdHighAidNumScore" jdbcType="INTEGER" />
    <result column="upgrd_waist_aid_num_score" property="upgrdWaistAidNumScore" jdbcType="INTEGER" />
    <result column="ext_golden_aid_num_score" property="extGoldenAidNumScore" jdbcType="INTEGER" />
    <result column="actv_prod_pay_amt_rate_score" property="actvProdPayAmtRateScore" jdbcType="INTEGER" />
    <result column="actv_incm_amt_rate_score" property="actvIncmAmtRateScore" jdbcType="INTEGER" />
    <result column="task_total_score" property="taskTotalScore" jdbcType="INTEGER" />
    <result column="new_aid_rat_3_4_aid_num_itg" property="newAidRat34AidNumItg" jdbcType="INTEGER" />
    <result column="upgrd_high_aid_num_itg" property="upgrdHighAidNumItg" jdbcType="INTEGER" />
    <result column="upgrd_waist_aid_num_itg" property="upgrdWaistAidNumItg" jdbcType="INTEGER" />
    <result column="ext_golden_aid_num_itg" property="extGoldenAidNumItg" jdbcType="INTEGER" />
    <result column="actv_prod_pay_amt_rate_itg" property="actvProdPayAmtRateItg" jdbcType="INTEGER" />
    <result column="actv_incm_amt_rate_itg" property="actvIncmAmtRateItg" jdbcType="INTEGER" />
    <result column="task_total_itg" property="taskTotalItg" jdbcType="INTEGER" />
    <result column="total_score" property="totalScore" jdbcType="INTEGER" />
    <result column="dt" property="dt" jdbcType="DATE" />
    <result column="avg_new_aid_rat_3_4_aid_num_3m" property="avgNewAidRat34AidNum3m" jdbcType="DECIMAL" />
    <result column="new_aid_rat_3_aid_num" property="newAidRat3AidNum" jdbcType="BIGINT" />
    <result column="new_aid_rat_4_aid_num" property="newAidRat4AidNum" jdbcType="BIGINT" />
    <result column="avg_new_aid_rat_3_4_aid_num_3m_aim" property="avgNewAidRat34AidNum3mAim" jdbcType="INTEGER" />
    <result column="avg_new_aid_rat_3_4_aid_num_3m_score" property="avgNewAidRat34AidNum3mScore" jdbcType="INTEGER" />
    <result column="avg_upgrd_high_waist_aid_num_3m" property="avgUpgrdHighWaistAidNum3m" jdbcType="DECIMAL" />
    <result column="avg_upgrd_high_waist_aid_num_3m_aim" property="avgUpgrdHighWaistAidNum3mAim" jdbcType="INTEGER" />
    <result column="avg_upgrd_high_waist_aid_num_3m_score" property="avgUpgrdHighWaistAidNum3mScore" jdbcType="INTEGER" />
    <result column="new_aid_incm_com_rate" property="newAidIncmComRate" jdbcType="DECIMAL" />
    <result column="new_aid_incm_com_rate_aim" property="newAidIncmComRateAim" jdbcType="DECIMAL" />
    <result column="new_aid_incm_com_rate_score" property="newAidIncmComRateScore" jdbcType="INTEGER" />
    <result column="guild_task_incm_lvl" property="guildTaskIncmLvl" jdbcType="INTEGER" />
    <result column="guild_task_incm_lvl_aim" property="guildTaskIncmLvlAim" jdbcType="INTEGER" />
    <result column="guild_task_incm_lvl_score" property="guildTaskIncmLvlScore" jdbcType="INTEGER" />
    <result column="accum_incm_amt_diff" property="accumIncmAmtDiff" jdbcType="BIGINT" />
    <result column="accum_incm_amt_aim" property="accumIncmAmtAim" jdbcType="BIGINT" />
    <result column="accum_incm_amt_socre" property="accumIncmAmtSocre" jdbcType="INTEGER" />
    <result column="guild_cmp_name" property="guildCmpName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, guild_cmp_ownr_id, new_aid_rat_3_4_aid_num, new_aid_rat_3_4_aid_num_rn, new_aid_rat_3_4_aid_num_diff, 
    upgrd_high_aid_num, upgrd_high_aid_num_diff, upgrd_waist_aid_num, upgrd_waist_aid_num_diff, 
    avg_high_valid_live_aid_num, avg_high_valid_live_aid_num_rn, avg_high_valid_live_aid_num_diff, 
    avg_waist_valid_live_aid_num, avg_waist_valid_live_aid_num_rn, avg_waist_valid_live_aid_num_diff, 
    avg_nto_valid_live_aid_rate, avg_nto_valid_live_aid_rate_rn, avg_stock_valid_live_aid_rate, 
    avg_stock_valid_live_aid_rate_rn, new_auth_golden_aid_num, new_auth_golden_aid_num_rn, 
    ext_golden_aid_num, actv_prod_pay_amt, prod_pay_amt, actv_prod_pay_amt_rate, valid_live_new_aid_prod_pay_amt, 
    valid_live_new_aid_prod_pay_amt_rn, valid_live_new_aid_prod_pay_amt_diff, unvalid_live_new_aid_prod_pay_amt, 
    unvalid_live_new_aid_prod_pay_amt_rn, unvalid_live_new_aid_prod_pay_amt_diff, unvalid_live_new_aid_prod_pay_amt_rate, 
    total_unvalid_live_new_aid_prod_pay_amt_rate, unvalid_live_new_aid_prod_pay_amt_rate_diff, 
    actv_incm_amt, incm_amt, actv_incm_amt_rate, valid_live_new_aid_incm_amt, valid_live_new_aid_incm_amt_rn, 
    valid_live_new_aid_incm_amt_diff, unvalid_live_new_aid_incm_amt, unvalid_live_new_aid_incm_amt_rn, 
    unvalid_live_new_aid_incm_amt_diff, unvalid_live_new_aid_incm_amt_rate, total_unvalid_live_new_aid_incm_amt_rate, 
    unvalid_live_new_aid_incm_amt_rate_diff, guild_health_point, new_aid_rat_3_4_aid_num_rn_score, 
    new_aid_rat_3_4_aid_num_diff_score, upgrd_high_aid_num_diff_score, upgrd_waist_aid_num_diff_score, 
    avg_high_valid_live_aid_num_rn_score, avg_waist_valid_live_aid_num_rn_score, avg_high_valid_live_aid_num_diff_score, 
    avg_waist_valid_live_aid_num_diff_score, avg_nto_valid_live_aid_rate_rn_score, avg_stock_valid_live_aid_rate_rn_score, 
    new_auth_golden_aid_num_rn_score, valid_live_new_aid_prod_pay_amt_rn_score, unvalid_live_new_aid_prod_pay_amt_rn_score, 
    valid_live_new_aid_prod_pay_amt_diff_score, unvalid_live_new_aid_prod_pay_amt_diff_score, 
    unvalid_live_new_aid_prod_pay_amt_rate_diff_score, valid_live_new_aid_incm_amt_rn_score, 
    unvalid_live_new_aid_incm_amt_rn_score, valid_live_new_aid_incm_amt_diff_score, unvalid_live_new_aid_incm_amt_diff_score, 
    unvalid_live_new_aid_incm_amt_rate_diff_score, guild_health_point_score, recru_score, 
    hatch_score, ret_score, rvnu_score, com_score, data_total_score, data_cmp_star_lvl, 
    cmp_star_lvl, new_aid_rat_3_4_aid_num_score, upgrd_high_aid_num_score, upgrd_waist_aid_num_score, 
    ext_golden_aid_num_score, actv_prod_pay_amt_rate_score, actv_incm_amt_rate_score, 
    task_total_score, new_aid_rat_3_4_aid_num_itg, upgrd_high_aid_num_itg, upgrd_waist_aid_num_itg, 
    ext_golden_aid_num_itg, actv_prod_pay_amt_rate_itg, actv_incm_amt_rate_itg, task_total_itg, 
    total_score, dt, avg_new_aid_rat_3_4_aid_num_3m, new_aid_rat_3_aid_num, new_aid_rat_4_aid_num, 
    avg_new_aid_rat_3_4_aid_num_3m_aim, avg_new_aid_rat_3_4_aid_num_3m_score, avg_upgrd_high_waist_aid_num_3m, 
    avg_upgrd_high_waist_aid_num_3m_aim, avg_upgrd_high_waist_aid_num_3m_score, new_aid_incm_com_rate, 
    new_aid_incm_com_rate_aim, new_aid_incm_com_rate_score, guild_task_incm_lvl, guild_task_incm_lvl_aim, 
    guild_task_incm_lvl_score, accum_incm_amt_diff, accum_incm_amt_aim, accum_incm_amt_socre, 
    guild_cmp_name
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.yy.yyzone.guildrank.db.gen.model.YyDmEntityGuildCmpHealthAnalysisDiExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from yy_dm_entity_guild_cmp_health_analysis_di
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit > 0" >
      limit ${limit}
    </if>
    <if test="offset > 0" >
      offset ${offset}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from yy_dm_entity_guild_cmp_health_analysis_di
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from yy_dm_entity_guild_cmp_health_analysis_di
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.yy.yyzone.guildrank.db.gen.model.YyDmEntityGuildCmpHealthAnalysisDiExample" >
    delete from yy_dm_entity_guild_cmp_health_analysis_di
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.yy.yyzone.guildrank.db.gen.model.YyDmEntityGuildCmpHealthAnalysisDi" useGeneratedKeys="true" keyProperty="id" >
    insert into yy_dm_entity_guild_cmp_health_analysis_di (guild_cmp_ownr_id, new_aid_rat_3_4_aid_num, 
      new_aid_rat_3_4_aid_num_rn, new_aid_rat_3_4_aid_num_diff, 
      upgrd_high_aid_num, upgrd_high_aid_num_diff, upgrd_waist_aid_num, 
      upgrd_waist_aid_num_diff, avg_high_valid_live_aid_num, 
      avg_high_valid_live_aid_num_rn, avg_high_valid_live_aid_num_diff, 
      avg_waist_valid_live_aid_num, avg_waist_valid_live_aid_num_rn, 
      avg_waist_valid_live_aid_num_diff, avg_nto_valid_live_aid_rate, 
      avg_nto_valid_live_aid_rate_rn, avg_stock_valid_live_aid_rate, 
      avg_stock_valid_live_aid_rate_rn, new_auth_golden_aid_num, 
      new_auth_golden_aid_num_rn, ext_golden_aid_num, actv_prod_pay_amt, 
      prod_pay_amt, actv_prod_pay_amt_rate, valid_live_new_aid_prod_pay_amt, 
      valid_live_new_aid_prod_pay_amt_rn, valid_live_new_aid_prod_pay_amt_diff, 
      unvalid_live_new_aid_prod_pay_amt, unvalid_live_new_aid_prod_pay_amt_rn, 
      unvalid_live_new_aid_prod_pay_amt_diff, unvalid_live_new_aid_prod_pay_amt_rate, 
      total_unvalid_live_new_aid_prod_pay_amt_rate, unvalid_live_new_aid_prod_pay_amt_rate_diff, 
      actv_incm_amt, incm_amt, actv_incm_amt_rate, 
      valid_live_new_aid_incm_amt, valid_live_new_aid_incm_amt_rn, 
      valid_live_new_aid_incm_amt_diff, unvalid_live_new_aid_incm_amt, 
      unvalid_live_new_aid_incm_amt_rn, unvalid_live_new_aid_incm_amt_diff, 
      unvalid_live_new_aid_incm_amt_rate, total_unvalid_live_new_aid_incm_amt_rate, 
      unvalid_live_new_aid_incm_amt_rate_diff, guild_health_point, 
      new_aid_rat_3_4_aid_num_rn_score, new_aid_rat_3_4_aid_num_diff_score, 
      upgrd_high_aid_num_diff_score, upgrd_waist_aid_num_diff_score, 
      avg_high_valid_live_aid_num_rn_score, avg_waist_valid_live_aid_num_rn_score, 
      avg_high_valid_live_aid_num_diff_score, avg_waist_valid_live_aid_num_diff_score, 
      avg_nto_valid_live_aid_rate_rn_score, avg_stock_valid_live_aid_rate_rn_score, 
      new_auth_golden_aid_num_rn_score, valid_live_new_aid_prod_pay_amt_rn_score, 
      unvalid_live_new_aid_prod_pay_amt_rn_score, valid_live_new_aid_prod_pay_amt_diff_score, 
      unvalid_live_new_aid_prod_pay_amt_diff_score, unvalid_live_new_aid_prod_pay_amt_rate_diff_score, 
      valid_live_new_aid_incm_amt_rn_score, unvalid_live_new_aid_incm_amt_rn_score, 
      valid_live_new_aid_incm_amt_diff_score, unvalid_live_new_aid_incm_amt_diff_score, 
      unvalid_live_new_aid_incm_amt_rate_diff_score, guild_health_point_score, 
      recru_score, hatch_score, ret_score, 
      rvnu_score, com_score, data_total_score, 
      data_cmp_star_lvl, cmp_star_lvl, new_aid_rat_3_4_aid_num_score, 
      upgrd_high_aid_num_score, upgrd_waist_aid_num_score, 
      ext_golden_aid_num_score, actv_prod_pay_amt_rate_score, 
      actv_incm_amt_rate_score, task_total_score, new_aid_rat_3_4_aid_num_itg, 
      upgrd_high_aid_num_itg, upgrd_waist_aid_num_itg, 
      ext_golden_aid_num_itg, actv_prod_pay_amt_rate_itg, 
      actv_incm_amt_rate_itg, task_total_itg, total_score, 
      dt, avg_new_aid_rat_3_4_aid_num_3m, new_aid_rat_3_aid_num, 
      new_aid_rat_4_aid_num, avg_new_aid_rat_3_4_aid_num_3m_aim, 
      avg_new_aid_rat_3_4_aid_num_3m_score, avg_upgrd_high_waist_aid_num_3m, 
      avg_upgrd_high_waist_aid_num_3m_aim, avg_upgrd_high_waist_aid_num_3m_score, 
      new_aid_incm_com_rate, new_aid_incm_com_rate_aim, 
      new_aid_incm_com_rate_score, guild_task_incm_lvl, 
      guild_task_incm_lvl_aim, guild_task_incm_lvl_score, 
      accum_incm_amt_diff, accum_incm_amt_aim, accum_incm_amt_socre, 
      guild_cmp_name)
    values (#{guildCmpOwnrId,jdbcType=BIGINT}, #{newAidRat34AidNum,jdbcType=BIGINT}, 
      #{newAidRat34AidNumRn,jdbcType=BIGINT}, #{newAidRat34AidNumDiff,jdbcType=BIGINT}, 
      #{upgrdHighAidNum,jdbcType=BIGINT}, #{upgrdHighAidNumDiff,jdbcType=BIGINT}, #{upgrdWaistAidNum,jdbcType=BIGINT}, 
      #{upgrdWaistAidNumDiff,jdbcType=BIGINT}, #{avgHighValidLiveAidNum,jdbcType=DOUBLE}, 
      #{avgHighValidLiveAidNumRn,jdbcType=BIGINT}, #{avgHighValidLiveAidNumDiff,jdbcType=DOUBLE}, 
      #{avgWaistValidLiveAidNum,jdbcType=DOUBLE}, #{avgWaistValidLiveAidNumRn,jdbcType=BIGINT}, 
      #{avgWaistValidLiveAidNumDiff,jdbcType=DOUBLE}, #{avgNtoValidLiveAidRate,jdbcType=DOUBLE}, 
      #{avgNtoValidLiveAidRateRn,jdbcType=BIGINT}, #{avgStockValidLiveAidRate,jdbcType=DOUBLE}, 
      #{avgStockValidLiveAidRateRn,jdbcType=BIGINT}, #{newAuthGoldenAidNum,jdbcType=BIGINT}, 
      #{newAuthGoldenAidNumRn,jdbcType=BIGINT}, #{extGoldenAidNum,jdbcType=BIGINT}, #{actvProdPayAmt,jdbcType=DOUBLE}, 
      #{prodPayAmt,jdbcType=DOUBLE}, #{actvProdPayAmtRate,jdbcType=DOUBLE}, #{validLiveNewAidProdPayAmt,jdbcType=DOUBLE}, 
      #{validLiveNewAidProdPayAmtRn,jdbcType=BIGINT}, #{validLiveNewAidProdPayAmtDiff,jdbcType=DOUBLE}, 
      #{unvalidLiveNewAidProdPayAmt,jdbcType=DOUBLE}, #{unvalidLiveNewAidProdPayAmtRn,jdbcType=BIGINT}, 
      #{unvalidLiveNewAidProdPayAmtDiff,jdbcType=DOUBLE}, #{unvalidLiveNewAidProdPayAmtRate,jdbcType=DOUBLE}, 
      #{totalUnvalidLiveNewAidProdPayAmtRate,jdbcType=DOUBLE}, #{unvalidLiveNewAidProdPayAmtRateDiff,jdbcType=DOUBLE}, 
      #{actvIncmAmt,jdbcType=DOUBLE}, #{incmAmt,jdbcType=DOUBLE}, #{actvIncmAmtRate,jdbcType=DOUBLE}, 
      #{validLiveNewAidIncmAmt,jdbcType=DOUBLE}, #{validLiveNewAidIncmAmtRn,jdbcType=BIGINT}, 
      #{validLiveNewAidIncmAmtDiff,jdbcType=DOUBLE}, #{unvalidLiveNewAidIncmAmt,jdbcType=DOUBLE}, 
      #{unvalidLiveNewAidIncmAmtRn,jdbcType=BIGINT}, #{unvalidLiveNewAidIncmAmtDiff,jdbcType=DOUBLE}, 
      #{unvalidLiveNewAidIncmAmtRate,jdbcType=DOUBLE}, #{totalUnvalidLiveNewAidIncmAmtRate,jdbcType=DOUBLE}, 
      #{unvalidLiveNewAidIncmAmtRateDiff,jdbcType=DOUBLE}, #{guildHealthPoint,jdbcType=DOUBLE}, 
      #{newAidRat34AidNumRnScore,jdbcType=INTEGER}, #{newAidRat34AidNumDiffScore,jdbcType=INTEGER}, 
      #{upgrdHighAidNumDiffScore,jdbcType=INTEGER}, #{upgrdWaistAidNumDiffScore,jdbcType=INTEGER}, 
      #{avgHighValidLiveAidNumRnScore,jdbcType=INTEGER}, #{avgWaistValidLiveAidNumRnScore,jdbcType=INTEGER}, 
      #{avgHighValidLiveAidNumDiffScore,jdbcType=INTEGER}, #{avgWaistValidLiveAidNumDiffScore,jdbcType=INTEGER}, 
      #{avgNtoValidLiveAidRateRnScore,jdbcType=INTEGER}, #{avgStockValidLiveAidRateRnScore,jdbcType=INTEGER}, 
      #{newAuthGoldenAidNumRnScore,jdbcType=INTEGER}, #{validLiveNewAidProdPayAmtRnScore,jdbcType=INTEGER}, 
      #{unvalidLiveNewAidProdPayAmtRnScore,jdbcType=INTEGER}, #{validLiveNewAidProdPayAmtDiffScore,jdbcType=INTEGER}, 
      #{unvalidLiveNewAidProdPayAmtDiffScore,jdbcType=INTEGER}, #{unvalidLiveNewAidProdPayAmtRateDiffScore,jdbcType=INTEGER}, 
      #{validLiveNewAidIncmAmtRnScore,jdbcType=INTEGER}, #{unvalidLiveNewAidIncmAmtRnScore,jdbcType=INTEGER}, 
      #{validLiveNewAidIncmAmtDiffScore,jdbcType=INTEGER}, #{unvalidLiveNewAidIncmAmtDiffScore,jdbcType=INTEGER}, 
      #{unvalidLiveNewAidIncmAmtRateDiffScore,jdbcType=INTEGER}, #{guildHealthPointScore,jdbcType=INTEGER}, 
      #{recruScore,jdbcType=INTEGER}, #{hatchScore,jdbcType=INTEGER}, #{retScore,jdbcType=INTEGER}, 
      #{rvnuScore,jdbcType=INTEGER}, #{comScore,jdbcType=INTEGER}, #{dataTotalScore,jdbcType=INTEGER}, 
      #{dataCmpStarLvl,jdbcType=INTEGER}, #{cmpStarLvl,jdbcType=INTEGER}, #{newAidRat34AidNumScore,jdbcType=INTEGER}, 
      #{upgrdHighAidNumScore,jdbcType=INTEGER}, #{upgrdWaistAidNumScore,jdbcType=INTEGER}, 
      #{extGoldenAidNumScore,jdbcType=INTEGER}, #{actvProdPayAmtRateScore,jdbcType=INTEGER}, 
      #{actvIncmAmtRateScore,jdbcType=INTEGER}, #{taskTotalScore,jdbcType=INTEGER}, #{newAidRat34AidNumItg,jdbcType=INTEGER}, 
      #{upgrdHighAidNumItg,jdbcType=INTEGER}, #{upgrdWaistAidNumItg,jdbcType=INTEGER}, 
      #{extGoldenAidNumItg,jdbcType=INTEGER}, #{actvProdPayAmtRateItg,jdbcType=INTEGER}, 
      #{actvIncmAmtRateItg,jdbcType=INTEGER}, #{taskTotalItg,jdbcType=INTEGER}, #{totalScore,jdbcType=INTEGER}, 
      #{dt,jdbcType=DATE}, #{avgNewAidRat34AidNum3m,jdbcType=DECIMAL}, #{newAidRat3AidNum,jdbcType=BIGINT}, 
      #{newAidRat4AidNum,jdbcType=BIGINT}, #{avgNewAidRat34AidNum3mAim,jdbcType=INTEGER}, 
      #{avgNewAidRat34AidNum3mScore,jdbcType=INTEGER}, #{avgUpgrdHighWaistAidNum3m,jdbcType=DECIMAL}, 
      #{avgUpgrdHighWaistAidNum3mAim,jdbcType=INTEGER}, #{avgUpgrdHighWaistAidNum3mScore,jdbcType=INTEGER}, 
      #{newAidIncmComRate,jdbcType=DECIMAL}, #{newAidIncmComRateAim,jdbcType=DECIMAL}, 
      #{newAidIncmComRateScore,jdbcType=INTEGER}, #{guildTaskIncmLvl,jdbcType=INTEGER}, 
      #{guildTaskIncmLvlAim,jdbcType=INTEGER}, #{guildTaskIncmLvlScore,jdbcType=INTEGER}, 
      #{accumIncmAmtDiff,jdbcType=BIGINT}, #{accumIncmAmtAim,jdbcType=BIGINT}, #{accumIncmAmtSocre,jdbcType=INTEGER}, 
      #{guildCmpName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.yy.yyzone.guildrank.db.gen.model.YyDmEntityGuildCmpHealthAnalysisDi" useGeneratedKeys="true" keyProperty="id" >
    insert into yy_dm_entity_guild_cmp_health_analysis_di
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="guildCmpOwnrId != null" >
        guild_cmp_ownr_id,
      </if>
      <if test="newAidRat34AidNum != null" >
        new_aid_rat_3_4_aid_num,
      </if>
      <if test="newAidRat34AidNumRn != null" >
        new_aid_rat_3_4_aid_num_rn,
      </if>
      <if test="newAidRat34AidNumDiff != null" >
        new_aid_rat_3_4_aid_num_diff,
      </if>
      <if test="upgrdHighAidNum != null" >
        upgrd_high_aid_num,
      </if>
      <if test="upgrdHighAidNumDiff != null" >
        upgrd_high_aid_num_diff,
      </if>
      <if test="upgrdWaistAidNum != null" >
        upgrd_waist_aid_num,
      </if>
      <if test="upgrdWaistAidNumDiff != null" >
        upgrd_waist_aid_num_diff,
      </if>
      <if test="avgHighValidLiveAidNum != null" >
        avg_high_valid_live_aid_num,
      </if>
      <if test="avgHighValidLiveAidNumRn != null" >
        avg_high_valid_live_aid_num_rn,
      </if>
      <if test="avgHighValidLiveAidNumDiff != null" >
        avg_high_valid_live_aid_num_diff,
      </if>
      <if test="avgWaistValidLiveAidNum != null" >
        avg_waist_valid_live_aid_num,
      </if>
      <if test="avgWaistValidLiveAidNumRn != null" >
        avg_waist_valid_live_aid_num_rn,
      </if>
      <if test="avgWaistValidLiveAidNumDiff != null" >
        avg_waist_valid_live_aid_num_diff,
      </if>
      <if test="avgNtoValidLiveAidRate != null" >
        avg_nto_valid_live_aid_rate,
      </if>
      <if test="avgNtoValidLiveAidRateRn != null" >
        avg_nto_valid_live_aid_rate_rn,
      </if>
      <if test="avgStockValidLiveAidRate != null" >
        avg_stock_valid_live_aid_rate,
      </if>
      <if test="avgStockValidLiveAidRateRn != null" >
        avg_stock_valid_live_aid_rate_rn,
      </if>
      <if test="newAuthGoldenAidNum != null" >
        new_auth_golden_aid_num,
      </if>
      <if test="newAuthGoldenAidNumRn != null" >
        new_auth_golden_aid_num_rn,
      </if>
      <if test="extGoldenAidNum != null" >
        ext_golden_aid_num,
      </if>
      <if test="actvProdPayAmt != null" >
        actv_prod_pay_amt,
      </if>
      <if test="prodPayAmt != null" >
        prod_pay_amt,
      </if>
      <if test="actvProdPayAmtRate != null" >
        actv_prod_pay_amt_rate,
      </if>
      <if test="validLiveNewAidProdPayAmt != null" >
        valid_live_new_aid_prod_pay_amt,
      </if>
      <if test="validLiveNewAidProdPayAmtRn != null" >
        valid_live_new_aid_prod_pay_amt_rn,
      </if>
      <if test="validLiveNewAidProdPayAmtDiff != null" >
        valid_live_new_aid_prod_pay_amt_diff,
      </if>
      <if test="unvalidLiveNewAidProdPayAmt != null" >
        unvalid_live_new_aid_prod_pay_amt,
      </if>
      <if test="unvalidLiveNewAidProdPayAmtRn != null" >
        unvalid_live_new_aid_prod_pay_amt_rn,
      </if>
      <if test="unvalidLiveNewAidProdPayAmtDiff != null" >
        unvalid_live_new_aid_prod_pay_amt_diff,
      </if>
      <if test="unvalidLiveNewAidProdPayAmtRate != null" >
        unvalid_live_new_aid_prod_pay_amt_rate,
      </if>
      <if test="totalUnvalidLiveNewAidProdPayAmtRate != null" >
        total_unvalid_live_new_aid_prod_pay_amt_rate,
      </if>
      <if test="unvalidLiveNewAidProdPayAmtRateDiff != null" >
        unvalid_live_new_aid_prod_pay_amt_rate_diff,
      </if>
      <if test="actvIncmAmt != null" >
        actv_incm_amt,
      </if>
      <if test="incmAmt != null" >
        incm_amt,
      </if>
      <if test="actvIncmAmtRate != null" >
        actv_incm_amt_rate,
      </if>
      <if test="validLiveNewAidIncmAmt != null" >
        valid_live_new_aid_incm_amt,
      </if>
      <if test="validLiveNewAidIncmAmtRn != null" >
        valid_live_new_aid_incm_amt_rn,
      </if>
      <if test="validLiveNewAidIncmAmtDiff != null" >
        valid_live_new_aid_incm_amt_diff,
      </if>
      <if test="unvalidLiveNewAidIncmAmt != null" >
        unvalid_live_new_aid_incm_amt,
      </if>
      <if test="unvalidLiveNewAidIncmAmtRn != null" >
        unvalid_live_new_aid_incm_amt_rn,
      </if>
      <if test="unvalidLiveNewAidIncmAmtDiff != null" >
        unvalid_live_new_aid_incm_amt_diff,
      </if>
      <if test="unvalidLiveNewAidIncmAmtRate != null" >
        unvalid_live_new_aid_incm_amt_rate,
      </if>
      <if test="totalUnvalidLiveNewAidIncmAmtRate != null" >
        total_unvalid_live_new_aid_incm_amt_rate,
      </if>
      <if test="unvalidLiveNewAidIncmAmtRateDiff != null" >
        unvalid_live_new_aid_incm_amt_rate_diff,
      </if>
      <if test="guildHealthPoint != null" >
        guild_health_point,
      </if>
      <if test="newAidRat34AidNumRnScore != null" >
        new_aid_rat_3_4_aid_num_rn_score,
      </if>
      <if test="newAidRat34AidNumDiffScore != null" >
        new_aid_rat_3_4_aid_num_diff_score,
      </if>
      <if test="upgrdHighAidNumDiffScore != null" >
        upgrd_high_aid_num_diff_score,
      </if>
      <if test="upgrdWaistAidNumDiffScore != null" >
        upgrd_waist_aid_num_diff_score,
      </if>
      <if test="avgHighValidLiveAidNumRnScore != null" >
        avg_high_valid_live_aid_num_rn_score,
      </if>
      <if test="avgWaistValidLiveAidNumRnScore != null" >
        avg_waist_valid_live_aid_num_rn_score,
      </if>
      <if test="avgHighValidLiveAidNumDiffScore != null" >
        avg_high_valid_live_aid_num_diff_score,
      </if>
      <if test="avgWaistValidLiveAidNumDiffScore != null" >
        avg_waist_valid_live_aid_num_diff_score,
      </if>
      <if test="avgNtoValidLiveAidRateRnScore != null" >
        avg_nto_valid_live_aid_rate_rn_score,
      </if>
      <if test="avgStockValidLiveAidRateRnScore != null" >
        avg_stock_valid_live_aid_rate_rn_score,
      </if>
      <if test="newAuthGoldenAidNumRnScore != null" >
        new_auth_golden_aid_num_rn_score,
      </if>
      <if test="validLiveNewAidProdPayAmtRnScore != null" >
        valid_live_new_aid_prod_pay_amt_rn_score,
      </if>
      <if test="unvalidLiveNewAidProdPayAmtRnScore != null" >
        unvalid_live_new_aid_prod_pay_amt_rn_score,
      </if>
      <if test="validLiveNewAidProdPayAmtDiffScore != null" >
        valid_live_new_aid_prod_pay_amt_diff_score,
      </if>
      <if test="unvalidLiveNewAidProdPayAmtDiffScore != null" >
        unvalid_live_new_aid_prod_pay_amt_diff_score,
      </if>
      <if test="unvalidLiveNewAidProdPayAmtRateDiffScore != null" >
        unvalid_live_new_aid_prod_pay_amt_rate_diff_score,
      </if>
      <if test="validLiveNewAidIncmAmtRnScore != null" >
        valid_live_new_aid_incm_amt_rn_score,
      </if>
      <if test="unvalidLiveNewAidIncmAmtRnScore != null" >
        unvalid_live_new_aid_incm_amt_rn_score,
      </if>
      <if test="validLiveNewAidIncmAmtDiffScore != null" >
        valid_live_new_aid_incm_amt_diff_score,
      </if>
      <if test="unvalidLiveNewAidIncmAmtDiffScore != null" >
        unvalid_live_new_aid_incm_amt_diff_score,
      </if>
      <if test="unvalidLiveNewAidIncmAmtRateDiffScore != null" >
        unvalid_live_new_aid_incm_amt_rate_diff_score,
      </if>
      <if test="guildHealthPointScore != null" >
        guild_health_point_score,
      </if>
      <if test="recruScore != null" >
        recru_score,
      </if>
      <if test="hatchScore != null" >
        hatch_score,
      </if>
      <if test="retScore != null" >
        ret_score,
      </if>
      <if test="rvnuScore != null" >
        rvnu_score,
      </if>
      <if test="comScore != null" >
        com_score,
      </if>
      <if test="dataTotalScore != null" >
        data_total_score,
      </if>
      <if test="dataCmpStarLvl != null" >
        data_cmp_star_lvl,
      </if>
      <if test="cmpStarLvl != null" >
        cmp_star_lvl,
      </if>
      <if test="newAidRat34AidNumScore != null" >
        new_aid_rat_3_4_aid_num_score,
      </if>
      <if test="upgrdHighAidNumScore != null" >
        upgrd_high_aid_num_score,
      </if>
      <if test="upgrdWaistAidNumScore != null" >
        upgrd_waist_aid_num_score,
      </if>
      <if test="extGoldenAidNumScore != null" >
        ext_golden_aid_num_score,
      </if>
      <if test="actvProdPayAmtRateScore != null" >
        actv_prod_pay_amt_rate_score,
      </if>
      <if test="actvIncmAmtRateScore != null" >
        actv_incm_amt_rate_score,
      </if>
      <if test="taskTotalScore != null" >
        task_total_score,
      </if>
      <if test="newAidRat34AidNumItg != null" >
        new_aid_rat_3_4_aid_num_itg,
      </if>
      <if test="upgrdHighAidNumItg != null" >
        upgrd_high_aid_num_itg,
      </if>
      <if test="upgrdWaistAidNumItg != null" >
        upgrd_waist_aid_num_itg,
      </if>
      <if test="extGoldenAidNumItg != null" >
        ext_golden_aid_num_itg,
      </if>
      <if test="actvProdPayAmtRateItg != null" >
        actv_prod_pay_amt_rate_itg,
      </if>
      <if test="actvIncmAmtRateItg != null" >
        actv_incm_amt_rate_itg,
      </if>
      <if test="taskTotalItg != null" >
        task_total_itg,
      </if>
      <if test="totalScore != null" >
        total_score,
      </if>
      <if test="dt != null" >
        dt,
      </if>
      <if test="avgNewAidRat34AidNum3m != null" >
        avg_new_aid_rat_3_4_aid_num_3m,
      </if>
      <if test="newAidRat3AidNum != null" >
        new_aid_rat_3_aid_num,
      </if>
      <if test="newAidRat4AidNum != null" >
        new_aid_rat_4_aid_num,
      </if>
      <if test="avgNewAidRat34AidNum3mAim != null" >
        avg_new_aid_rat_3_4_aid_num_3m_aim,
      </if>
      <if test="avgNewAidRat34AidNum3mScore != null" >
        avg_new_aid_rat_3_4_aid_num_3m_score,
      </if>
      <if test="avgUpgrdHighWaistAidNum3m != null" >
        avg_upgrd_high_waist_aid_num_3m,
      </if>
      <if test="avgUpgrdHighWaistAidNum3mAim != null" >
        avg_upgrd_high_waist_aid_num_3m_aim,
      </if>
      <if test="avgUpgrdHighWaistAidNum3mScore != null" >
        avg_upgrd_high_waist_aid_num_3m_score,
      </if>
      <if test="newAidIncmComRate != null" >
        new_aid_incm_com_rate,
      </if>
      <if test="newAidIncmComRateAim != null" >
        new_aid_incm_com_rate_aim,
      </if>
      <if test="newAidIncmComRateScore != null" >
        new_aid_incm_com_rate_score,
      </if>
      <if test="guildTaskIncmLvl != null" >
        guild_task_incm_lvl,
      </if>
      <if test="guildTaskIncmLvlAim != null" >
        guild_task_incm_lvl_aim,
      </if>
      <if test="guildTaskIncmLvlScore != null" >
        guild_task_incm_lvl_score,
      </if>
      <if test="accumIncmAmtDiff != null" >
        accum_incm_amt_diff,
      </if>
      <if test="accumIncmAmtAim != null" >
        accum_incm_amt_aim,
      </if>
      <if test="accumIncmAmtSocre != null" >
        accum_incm_amt_socre,
      </if>
      <if test="guildCmpName != null" >
        guild_cmp_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="guildCmpOwnrId != null" >
        #{guildCmpOwnrId,jdbcType=BIGINT},
      </if>
      <if test="newAidRat34AidNum != null" >
        #{newAidRat34AidNum,jdbcType=BIGINT},
      </if>
      <if test="newAidRat34AidNumRn != null" >
        #{newAidRat34AidNumRn,jdbcType=BIGINT},
      </if>
      <if test="newAidRat34AidNumDiff != null" >
        #{newAidRat34AidNumDiff,jdbcType=BIGINT},
      </if>
      <if test="upgrdHighAidNum != null" >
        #{upgrdHighAidNum,jdbcType=BIGINT},
      </if>
      <if test="upgrdHighAidNumDiff != null" >
        #{upgrdHighAidNumDiff,jdbcType=BIGINT},
      </if>
      <if test="upgrdWaistAidNum != null" >
        #{upgrdWaistAidNum,jdbcType=BIGINT},
      </if>
      <if test="upgrdWaistAidNumDiff != null" >
        #{upgrdWaistAidNumDiff,jdbcType=BIGINT},
      </if>
      <if test="avgHighValidLiveAidNum != null" >
        #{avgHighValidLiveAidNum,jdbcType=DOUBLE},
      </if>
      <if test="avgHighValidLiveAidNumRn != null" >
        #{avgHighValidLiveAidNumRn,jdbcType=BIGINT},
      </if>
      <if test="avgHighValidLiveAidNumDiff != null" >
        #{avgHighValidLiveAidNumDiff,jdbcType=DOUBLE},
      </if>
      <if test="avgWaistValidLiveAidNum != null" >
        #{avgWaistValidLiveAidNum,jdbcType=DOUBLE},
      </if>
      <if test="avgWaistValidLiveAidNumRn != null" >
        #{avgWaistValidLiveAidNumRn,jdbcType=BIGINT},
      </if>
      <if test="avgWaistValidLiveAidNumDiff != null" >
        #{avgWaistValidLiveAidNumDiff,jdbcType=DOUBLE},
      </if>
      <if test="avgNtoValidLiveAidRate != null" >
        #{avgNtoValidLiveAidRate,jdbcType=DOUBLE},
      </if>
      <if test="avgNtoValidLiveAidRateRn != null" >
        #{avgNtoValidLiveAidRateRn,jdbcType=BIGINT},
      </if>
      <if test="avgStockValidLiveAidRate != null" >
        #{avgStockValidLiveAidRate,jdbcType=DOUBLE},
      </if>
      <if test="avgStockValidLiveAidRateRn != null" >
        #{avgStockValidLiveAidRateRn,jdbcType=BIGINT},
      </if>
      <if test="newAuthGoldenAidNum != null" >
        #{newAuthGoldenAidNum,jdbcType=BIGINT},
      </if>
      <if test="newAuthGoldenAidNumRn != null" >
        #{newAuthGoldenAidNumRn,jdbcType=BIGINT},
      </if>
      <if test="extGoldenAidNum != null" >
        #{extGoldenAidNum,jdbcType=BIGINT},
      </if>
      <if test="actvProdPayAmt != null" >
        #{actvProdPayAmt,jdbcType=DOUBLE},
      </if>
      <if test="prodPayAmt != null" >
        #{prodPayAmt,jdbcType=DOUBLE},
      </if>
      <if test="actvProdPayAmtRate != null" >
        #{actvProdPayAmtRate,jdbcType=DOUBLE},
      </if>
      <if test="validLiveNewAidProdPayAmt != null" >
        #{validLiveNewAidProdPayAmt,jdbcType=DOUBLE},
      </if>
      <if test="validLiveNewAidProdPayAmtRn != null" >
        #{validLiveNewAidProdPayAmtRn,jdbcType=BIGINT},
      </if>
      <if test="validLiveNewAidProdPayAmtDiff != null" >
        #{validLiveNewAidProdPayAmtDiff,jdbcType=DOUBLE},
      </if>
      <if test="unvalidLiveNewAidProdPayAmt != null" >
        #{unvalidLiveNewAidProdPayAmt,jdbcType=DOUBLE},
      </if>
      <if test="unvalidLiveNewAidProdPayAmtRn != null" >
        #{unvalidLiveNewAidProdPayAmtRn,jdbcType=BIGINT},
      </if>
      <if test="unvalidLiveNewAidProdPayAmtDiff != null" >
        #{unvalidLiveNewAidProdPayAmtDiff,jdbcType=DOUBLE},
      </if>
      <if test="unvalidLiveNewAidProdPayAmtRate != null" >
        #{unvalidLiveNewAidProdPayAmtRate,jdbcType=DOUBLE},
      </if>
      <if test="totalUnvalidLiveNewAidProdPayAmtRate != null" >
        #{totalUnvalidLiveNewAidProdPayAmtRate,jdbcType=DOUBLE},
      </if>
      <if test="unvalidLiveNewAidProdPayAmtRateDiff != null" >
        #{unvalidLiveNewAidProdPayAmtRateDiff,jdbcType=DOUBLE},
      </if>
      <if test="actvIncmAmt != null" >
        #{actvIncmAmt,jdbcType=DOUBLE},
      </if>
      <if test="incmAmt != null" >
        #{incmAmt,jdbcType=DOUBLE},
      </if>
      <if test="actvIncmAmtRate != null" >
        #{actvIncmAmtRate,jdbcType=DOUBLE},
      </if>
      <if test="validLiveNewAidIncmAmt != null" >
        #{validLiveNewAidIncmAmt,jdbcType=DOUBLE},
      </if>
      <if test="validLiveNewAidIncmAmtRn != null" >
        #{validLiveNewAidIncmAmtRn,jdbcType=BIGINT},
      </if>
      <if test="validLiveNewAidIncmAmtDiff != null" >
        #{validLiveNewAidIncmAmtDiff,jdbcType=DOUBLE},
      </if>
      <if test="unvalidLiveNewAidIncmAmt != null" >
        #{unvalidLiveNewAidIncmAmt,jdbcType=DOUBLE},
      </if>
      <if test="unvalidLiveNewAidIncmAmtRn != null" >
        #{unvalidLiveNewAidIncmAmtRn,jdbcType=BIGINT},
      </if>
      <if test="unvalidLiveNewAidIncmAmtDiff != null" >
        #{unvalidLiveNewAidIncmAmtDiff,jdbcType=DOUBLE},
      </if>
      <if test="unvalidLiveNewAidIncmAmtRate != null" >
        #{unvalidLiveNewAidIncmAmtRate,jdbcType=DOUBLE},
      </if>
      <if test="totalUnvalidLiveNewAidIncmAmtRate != null" >
        #{totalUnvalidLiveNewAidIncmAmtRate,jdbcType=DOUBLE},
      </if>
      <if test="unvalidLiveNewAidIncmAmtRateDiff != null" >
        #{unvalidLiveNewAidIncmAmtRateDiff,jdbcType=DOUBLE},
      </if>
      <if test="guildHealthPoint != null" >
        #{guildHealthPoint,jdbcType=DOUBLE},
      </if>
      <if test="newAidRat34AidNumRnScore != null" >
        #{newAidRat34AidNumRnScore,jdbcType=INTEGER},
      </if>
      <if test="newAidRat34AidNumDiffScore != null" >
        #{newAidRat34AidNumDiffScore,jdbcType=INTEGER},
      </if>
      <if test="upgrdHighAidNumDiffScore != null" >
        #{upgrdHighAidNumDiffScore,jdbcType=INTEGER},
      </if>
      <if test="upgrdWaistAidNumDiffScore != null" >
        #{upgrdWaistAidNumDiffScore,jdbcType=INTEGER},
      </if>
      <if test="avgHighValidLiveAidNumRnScore != null" >
        #{avgHighValidLiveAidNumRnScore,jdbcType=INTEGER},
      </if>
      <if test="avgWaistValidLiveAidNumRnScore != null" >
        #{avgWaistValidLiveAidNumRnScore,jdbcType=INTEGER},
      </if>
      <if test="avgHighValidLiveAidNumDiffScore != null" >
        #{avgHighValidLiveAidNumDiffScore,jdbcType=INTEGER},
      </if>
      <if test="avgWaistValidLiveAidNumDiffScore != null" >
        #{avgWaistValidLiveAidNumDiffScore,jdbcType=INTEGER},
      </if>
      <if test="avgNtoValidLiveAidRateRnScore != null" >
        #{avgNtoValidLiveAidRateRnScore,jdbcType=INTEGER},
      </if>
      <if test="avgStockValidLiveAidRateRnScore != null" >
        #{avgStockValidLiveAidRateRnScore,jdbcType=INTEGER},
      </if>
      <if test="newAuthGoldenAidNumRnScore != null" >
        #{newAuthGoldenAidNumRnScore,jdbcType=INTEGER},
      </if>
      <if test="validLiveNewAidProdPayAmtRnScore != null" >
        #{validLiveNewAidProdPayAmtRnScore,jdbcType=INTEGER},
      </if>
      <if test="unvalidLiveNewAidProdPayAmtRnScore != null" >
        #{unvalidLiveNewAidProdPayAmtRnScore,jdbcType=INTEGER},
      </if>
      <if test="validLiveNewAidProdPayAmtDiffScore != null" >
        #{validLiveNewAidProdPayAmtDiffScore,jdbcType=INTEGER},
      </if>
      <if test="unvalidLiveNewAidProdPayAmtDiffScore != null" >
        #{unvalidLiveNewAidProdPayAmtDiffScore,jdbcType=INTEGER},
      </if>
      <if test="unvalidLiveNewAidProdPayAmtRateDiffScore != null" >
        #{unvalidLiveNewAidProdPayAmtRateDiffScore,jdbcType=INTEGER},
      </if>
      <if test="validLiveNewAidIncmAmtRnScore != null" >
        #{validLiveNewAidIncmAmtRnScore,jdbcType=INTEGER},
      </if>
      <if test="unvalidLiveNewAidIncmAmtRnScore != null" >
        #{unvalidLiveNewAidIncmAmtRnScore,jdbcType=INTEGER},
      </if>
      <if test="validLiveNewAidIncmAmtDiffScore != null" >
        #{validLiveNewAidIncmAmtDiffScore,jdbcType=INTEGER},
      </if>
      <if test="unvalidLiveNewAidIncmAmtDiffScore != null" >
        #{unvalidLiveNewAidIncmAmtDiffScore,jdbcType=INTEGER},
      </if>
      <if test="unvalidLiveNewAidIncmAmtRateDiffScore != null" >
        #{unvalidLiveNewAidIncmAmtRateDiffScore,jdbcType=INTEGER},
      </if>
      <if test="guildHealthPointScore != null" >
        #{guildHealthPointScore,jdbcType=INTEGER},
      </if>
      <if test="recruScore != null" >
        #{recruScore,jdbcType=INTEGER},
      </if>
      <if test="hatchScore != null" >
        #{hatchScore,jdbcType=INTEGER},
      </if>
      <if test="retScore != null" >
        #{retScore,jdbcType=INTEGER},
      </if>
      <if test="rvnuScore != null" >
        #{rvnuScore,jdbcType=INTEGER},
      </if>
      <if test="comScore != null" >
        #{comScore,jdbcType=INTEGER},
      </if>
      <if test="dataTotalScore != null" >
        #{dataTotalScore,jdbcType=INTEGER},
      </if>
      <if test="dataCmpStarLvl != null" >
        #{dataCmpStarLvl,jdbcType=INTEGER},
      </if>
      <if test="cmpStarLvl != null" >
        #{cmpStarLvl,jdbcType=INTEGER},
      </if>
      <if test="newAidRat34AidNumScore != null" >
        #{newAidRat34AidNumScore,jdbcType=INTEGER},
      </if>
      <if test="upgrdHighAidNumScore != null" >
        #{upgrdHighAidNumScore,jdbcType=INTEGER},
      </if>
      <if test="upgrdWaistAidNumScore != null" >
        #{upgrdWaistAidNumScore,jdbcType=INTEGER},
      </if>
      <if test="extGoldenAidNumScore != null" >
        #{extGoldenAidNumScore,jdbcType=INTEGER},
      </if>
      <if test="actvProdPayAmtRateScore != null" >
        #{actvProdPayAmtRateScore,jdbcType=INTEGER},
      </if>
      <if test="actvIncmAmtRateScore != null" >
        #{actvIncmAmtRateScore,jdbcType=INTEGER},
      </if>
      <if test="taskTotalScore != null" >
        #{taskTotalScore,jdbcType=INTEGER},
      </if>
      <if test="newAidRat34AidNumItg != null" >
        #{newAidRat34AidNumItg,jdbcType=INTEGER},
      </if>
      <if test="upgrdHighAidNumItg != null" >
        #{upgrdHighAidNumItg,jdbcType=INTEGER},
      </if>
      <if test="upgrdWaistAidNumItg != null" >
        #{upgrdWaistAidNumItg,jdbcType=INTEGER},
      </if>
      <if test="extGoldenAidNumItg != null" >
        #{extGoldenAidNumItg,jdbcType=INTEGER},
      </if>
      <if test="actvProdPayAmtRateItg != null" >
        #{actvProdPayAmtRateItg,jdbcType=INTEGER},
      </if>
      <if test="actvIncmAmtRateItg != null" >
        #{actvIncmAmtRateItg,jdbcType=INTEGER},
      </if>
      <if test="taskTotalItg != null" >
        #{taskTotalItg,jdbcType=INTEGER},
      </if>
      <if test="totalScore != null" >
        #{totalScore,jdbcType=INTEGER},
      </if>
      <if test="dt != null" >
        #{dt,jdbcType=DATE},
      </if>
      <if test="avgNewAidRat34AidNum3m != null" >
        #{avgNewAidRat34AidNum3m,jdbcType=DECIMAL},
      </if>
      <if test="newAidRat3AidNum != null" >
        #{newAidRat3AidNum,jdbcType=BIGINT},
      </if>
      <if test="newAidRat4AidNum != null" >
        #{newAidRat4AidNum,jdbcType=BIGINT},
      </if>
      <if test="avgNewAidRat34AidNum3mAim != null" >
        #{avgNewAidRat34AidNum3mAim,jdbcType=INTEGER},
      </if>
      <if test="avgNewAidRat34AidNum3mScore != null" >
        #{avgNewAidRat34AidNum3mScore,jdbcType=INTEGER},
      </if>
      <if test="avgUpgrdHighWaistAidNum3m != null" >
        #{avgUpgrdHighWaistAidNum3m,jdbcType=DECIMAL},
      </if>
      <if test="avgUpgrdHighWaistAidNum3mAim != null" >
        #{avgUpgrdHighWaistAidNum3mAim,jdbcType=INTEGER},
      </if>
      <if test="avgUpgrdHighWaistAidNum3mScore != null" >
        #{avgUpgrdHighWaistAidNum3mScore,jdbcType=INTEGER},
      </if>
      <if test="newAidIncmComRate != null" >
        #{newAidIncmComRate,jdbcType=DECIMAL},
      </if>
      <if test="newAidIncmComRateAim != null" >
        #{newAidIncmComRateAim,jdbcType=DECIMAL},
      </if>
      <if test="newAidIncmComRateScore != null" >
        #{newAidIncmComRateScore,jdbcType=INTEGER},
      </if>
      <if test="guildTaskIncmLvl != null" >
        #{guildTaskIncmLvl,jdbcType=INTEGER},
      </if>
      <if test="guildTaskIncmLvlAim != null" >
        #{guildTaskIncmLvlAim,jdbcType=INTEGER},
      </if>
      <if test="guildTaskIncmLvlScore != null" >
        #{guildTaskIncmLvlScore,jdbcType=INTEGER},
      </if>
      <if test="accumIncmAmtDiff != null" >
        #{accumIncmAmtDiff,jdbcType=BIGINT},
      </if>
      <if test="accumIncmAmtAim != null" >
        #{accumIncmAmtAim,jdbcType=BIGINT},
      </if>
      <if test="accumIncmAmtSocre != null" >
        #{accumIncmAmtSocre,jdbcType=INTEGER},
      </if>
      <if test="guildCmpName != null" >
        #{guildCmpName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.yy.yyzone.guildrank.db.gen.model.YyDmEntityGuildCmpHealthAnalysisDiExample" resultType="java.lang.Integer" >
    select count(*) from yy_dm_entity_guild_cmp_health_analysis_di
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update yy_dm_entity_guild_cmp_health_analysis_di
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.guildCmpOwnrId != null" >
        guild_cmp_ownr_id = #{record.guildCmpOwnrId,jdbcType=BIGINT},
      </if>
      <if test="record.newAidRat34AidNum != null" >
        new_aid_rat_3_4_aid_num = #{record.newAidRat34AidNum,jdbcType=BIGINT},
      </if>
      <if test="record.newAidRat34AidNumRn != null" >
        new_aid_rat_3_4_aid_num_rn = #{record.newAidRat34AidNumRn,jdbcType=BIGINT},
      </if>
      <if test="record.newAidRat34AidNumDiff != null" >
        new_aid_rat_3_4_aid_num_diff = #{record.newAidRat34AidNumDiff,jdbcType=BIGINT},
      </if>
      <if test="record.upgrdHighAidNum != null" >
        upgrd_high_aid_num = #{record.upgrdHighAidNum,jdbcType=BIGINT},
      </if>
      <if test="record.upgrdHighAidNumDiff != null" >
        upgrd_high_aid_num_diff = #{record.upgrdHighAidNumDiff,jdbcType=BIGINT},
      </if>
      <if test="record.upgrdWaistAidNum != null" >
        upgrd_waist_aid_num = #{record.upgrdWaistAidNum,jdbcType=BIGINT},
      </if>
      <if test="record.upgrdWaistAidNumDiff != null" >
        upgrd_waist_aid_num_diff = #{record.upgrdWaistAidNumDiff,jdbcType=BIGINT},
      </if>
      <if test="record.avgHighValidLiveAidNum != null" >
        avg_high_valid_live_aid_num = #{record.avgHighValidLiveAidNum,jdbcType=DOUBLE},
      </if>
      <if test="record.avgHighValidLiveAidNumRn != null" >
        avg_high_valid_live_aid_num_rn = #{record.avgHighValidLiveAidNumRn,jdbcType=BIGINT},
      </if>
      <if test="record.avgHighValidLiveAidNumDiff != null" >
        avg_high_valid_live_aid_num_diff = #{record.avgHighValidLiveAidNumDiff,jdbcType=DOUBLE},
      </if>
      <if test="record.avgWaistValidLiveAidNum != null" >
        avg_waist_valid_live_aid_num = #{record.avgWaistValidLiveAidNum,jdbcType=DOUBLE},
      </if>
      <if test="record.avgWaistValidLiveAidNumRn != null" >
        avg_waist_valid_live_aid_num_rn = #{record.avgWaistValidLiveAidNumRn,jdbcType=BIGINT},
      </if>
      <if test="record.avgWaistValidLiveAidNumDiff != null" >
        avg_waist_valid_live_aid_num_diff = #{record.avgWaistValidLiveAidNumDiff,jdbcType=DOUBLE},
      </if>
      <if test="record.avgNtoValidLiveAidRate != null" >
        avg_nto_valid_live_aid_rate = #{record.avgNtoValidLiveAidRate,jdbcType=DOUBLE},
      </if>
      <if test="record.avgNtoValidLiveAidRateRn != null" >
        avg_nto_valid_live_aid_rate_rn = #{record.avgNtoValidLiveAidRateRn,jdbcType=BIGINT},
      </if>
      <if test="record.avgStockValidLiveAidRate != null" >
        avg_stock_valid_live_aid_rate = #{record.avgStockValidLiveAidRate,jdbcType=DOUBLE},
      </if>
      <if test="record.avgStockValidLiveAidRateRn != null" >
        avg_stock_valid_live_aid_rate_rn = #{record.avgStockValidLiveAidRateRn,jdbcType=BIGINT},
      </if>
      <if test="record.newAuthGoldenAidNum != null" >
        new_auth_golden_aid_num = #{record.newAuthGoldenAidNum,jdbcType=BIGINT},
      </if>
      <if test="record.newAuthGoldenAidNumRn != null" >
        new_auth_golden_aid_num_rn = #{record.newAuthGoldenAidNumRn,jdbcType=BIGINT},
      </if>
      <if test="record.extGoldenAidNum != null" >
        ext_golden_aid_num = #{record.extGoldenAidNum,jdbcType=BIGINT},
      </if>
      <if test="record.actvProdPayAmt != null" >
        actv_prod_pay_amt = #{record.actvProdPayAmt,jdbcType=DOUBLE},
      </if>
      <if test="record.prodPayAmt != null" >
        prod_pay_amt = #{record.prodPayAmt,jdbcType=DOUBLE},
      </if>
      <if test="record.actvProdPayAmtRate != null" >
        actv_prod_pay_amt_rate = #{record.actvProdPayAmtRate,jdbcType=DOUBLE},
      </if>
      <if test="record.validLiveNewAidProdPayAmt != null" >
        valid_live_new_aid_prod_pay_amt = #{record.validLiveNewAidProdPayAmt,jdbcType=DOUBLE},
      </if>
      <if test="record.validLiveNewAidProdPayAmtRn != null" >
        valid_live_new_aid_prod_pay_amt_rn = #{record.validLiveNewAidProdPayAmtRn,jdbcType=BIGINT},
      </if>
      <if test="record.validLiveNewAidProdPayAmtDiff != null" >
        valid_live_new_aid_prod_pay_amt_diff = #{record.validLiveNewAidProdPayAmtDiff,jdbcType=DOUBLE},
      </if>
      <if test="record.unvalidLiveNewAidProdPayAmt != null" >
        unvalid_live_new_aid_prod_pay_amt = #{record.unvalidLiveNewAidProdPayAmt,jdbcType=DOUBLE},
      </if>
      <if test="record.unvalidLiveNewAidProdPayAmtRn != null" >
        unvalid_live_new_aid_prod_pay_amt_rn = #{record.unvalidLiveNewAidProdPayAmtRn,jdbcType=BIGINT},
      </if>
      <if test="record.unvalidLiveNewAidProdPayAmtDiff != null" >
        unvalid_live_new_aid_prod_pay_amt_diff = #{record.unvalidLiveNewAidProdPayAmtDiff,jdbcType=DOUBLE},
      </if>
      <if test="record.unvalidLiveNewAidProdPayAmtRate != null" >
        unvalid_live_new_aid_prod_pay_amt_rate = #{record.unvalidLiveNewAidProdPayAmtRate,jdbcType=DOUBLE},
      </if>
      <if test="record.totalUnvalidLiveNewAidProdPayAmtRate != null" >
        total_unvalid_live_new_aid_prod_pay_amt_rate = #{record.totalUnvalidLiveNewAidProdPayAmtRate,jdbcType=DOUBLE},
      </if>
      <if test="record.unvalidLiveNewAidProdPayAmtRateDiff != null" >
        unvalid_live_new_aid_prod_pay_amt_rate_diff = #{record.unvalidLiveNewAidProdPayAmtRateDiff,jdbcType=DOUBLE},
      </if>
      <if test="record.actvIncmAmt != null" >
        actv_incm_amt = #{record.actvIncmAmt,jdbcType=DOUBLE},
      </if>
      <if test="record.incmAmt != null" >
        incm_amt = #{record.incmAmt,jdbcType=DOUBLE},
      </if>
      <if test="record.actvIncmAmtRate != null" >
        actv_incm_amt_rate = #{record.actvIncmAmtRate,jdbcType=DOUBLE},
      </if>
      <if test="record.validLiveNewAidIncmAmt != null" >
        valid_live_new_aid_incm_amt = #{record.validLiveNewAidIncmAmt,jdbcType=DOUBLE},
      </if>
      <if test="record.validLiveNewAidIncmAmtRn != null" >
        valid_live_new_aid_incm_amt_rn = #{record.validLiveNewAidIncmAmtRn,jdbcType=BIGINT},
      </if>
      <if test="record.validLiveNewAidIncmAmtDiff != null" >
        valid_live_new_aid_incm_amt_diff = #{record.validLiveNewAidIncmAmtDiff,jdbcType=DOUBLE},
      </if>
      <if test="record.unvalidLiveNewAidIncmAmt != null" >
        unvalid_live_new_aid_incm_amt = #{record.unvalidLiveNewAidIncmAmt,jdbcType=DOUBLE},
      </if>
      <if test="record.unvalidLiveNewAidIncmAmtRn != null" >
        unvalid_live_new_aid_incm_amt_rn = #{record.unvalidLiveNewAidIncmAmtRn,jdbcType=BIGINT},
      </if>
      <if test="record.unvalidLiveNewAidIncmAmtDiff != null" >
        unvalid_live_new_aid_incm_amt_diff = #{record.unvalidLiveNewAidIncmAmtDiff,jdbcType=DOUBLE},
      </if>
      <if test="record.unvalidLiveNewAidIncmAmtRate != null" >
        unvalid_live_new_aid_incm_amt_rate = #{record.unvalidLiveNewAidIncmAmtRate,jdbcType=DOUBLE},
      </if>
      <if test="record.totalUnvalidLiveNewAidIncmAmtRate != null" >
        total_unvalid_live_new_aid_incm_amt_rate = #{record.totalUnvalidLiveNewAidIncmAmtRate,jdbcType=DOUBLE},
      </if>
      <if test="record.unvalidLiveNewAidIncmAmtRateDiff != null" >
        unvalid_live_new_aid_incm_amt_rate_diff = #{record.unvalidLiveNewAidIncmAmtRateDiff,jdbcType=DOUBLE},
      </if>
      <if test="record.guildHealthPoint != null" >
        guild_health_point = #{record.guildHealthPoint,jdbcType=DOUBLE},
      </if>
      <if test="record.newAidRat34AidNumRnScore != null" >
        new_aid_rat_3_4_aid_num_rn_score = #{record.newAidRat34AidNumRnScore,jdbcType=INTEGER},
      </if>
      <if test="record.newAidRat34AidNumDiffScore != null" >
        new_aid_rat_3_4_aid_num_diff_score = #{record.newAidRat34AidNumDiffScore,jdbcType=INTEGER},
      </if>
      <if test="record.upgrdHighAidNumDiffScore != null" >
        upgrd_high_aid_num_diff_score = #{record.upgrdHighAidNumDiffScore,jdbcType=INTEGER},
      </if>
      <if test="record.upgrdWaistAidNumDiffScore != null" >
        upgrd_waist_aid_num_diff_score = #{record.upgrdWaistAidNumDiffScore,jdbcType=INTEGER},
      </if>
      <if test="record.avgHighValidLiveAidNumRnScore != null" >
        avg_high_valid_live_aid_num_rn_score = #{record.avgHighValidLiveAidNumRnScore,jdbcType=INTEGER},
      </if>
      <if test="record.avgWaistValidLiveAidNumRnScore != null" >
        avg_waist_valid_live_aid_num_rn_score = #{record.avgWaistValidLiveAidNumRnScore,jdbcType=INTEGER},
      </if>
      <if test="record.avgHighValidLiveAidNumDiffScore != null" >
        avg_high_valid_live_aid_num_diff_score = #{record.avgHighValidLiveAidNumDiffScore,jdbcType=INTEGER},
      </if>
      <if test="record.avgWaistValidLiveAidNumDiffScore != null" >
        avg_waist_valid_live_aid_num_diff_score = #{record.avgWaistValidLiveAidNumDiffScore,jdbcType=INTEGER},
      </if>
      <if test="record.avgNtoValidLiveAidRateRnScore != null" >
        avg_nto_valid_live_aid_rate_rn_score = #{record.avgNtoValidLiveAidRateRnScore,jdbcType=INTEGER},
      </if>
      <if test="record.avgStockValidLiveAidRateRnScore != null" >
        avg_stock_valid_live_aid_rate_rn_score = #{record.avgStockValidLiveAidRateRnScore,jdbcType=INTEGER},
      </if>
      <if test="record.newAuthGoldenAidNumRnScore != null" >
        new_auth_golden_aid_num_rn_score = #{record.newAuthGoldenAidNumRnScore,jdbcType=INTEGER},
      </if>
      <if test="record.validLiveNewAidProdPayAmtRnScore != null" >
        valid_live_new_aid_prod_pay_amt_rn_score = #{record.validLiveNewAidProdPayAmtRnScore,jdbcType=INTEGER},
      </if>
      <if test="record.unvalidLiveNewAidProdPayAmtRnScore != null" >
        unvalid_live_new_aid_prod_pay_amt_rn_score = #{record.unvalidLiveNewAidProdPayAmtRnScore,jdbcType=INTEGER},
      </if>
      <if test="record.validLiveNewAidProdPayAmtDiffScore != null" >
        valid_live_new_aid_prod_pay_amt_diff_score = #{record.validLiveNewAidProdPayAmtDiffScore,jdbcType=INTEGER},
      </if>
      <if test="record.unvalidLiveNewAidProdPayAmtDiffScore != null" >
        unvalid_live_new_aid_prod_pay_amt_diff_score = #{record.unvalidLiveNewAidProdPayAmtDiffScore,jdbcType=INTEGER},
      </if>
      <if test="record.unvalidLiveNewAidProdPayAmtRateDiffScore != null" >
        unvalid_live_new_aid_prod_pay_amt_rate_diff_score = #{record.unvalidLiveNewAidProdPayAmtRateDiffScore,jdbcType=INTEGER},
      </if>
      <if test="record.validLiveNewAidIncmAmtRnScore != null" >
        valid_live_new_aid_incm_amt_rn_score = #{record.validLiveNewAidIncmAmtRnScore,jdbcType=INTEGER},
      </if>
      <if test="record.unvalidLiveNewAidIncmAmtRnScore != null" >
        unvalid_live_new_aid_incm_amt_rn_score = #{record.unvalidLiveNewAidIncmAmtRnScore,jdbcType=INTEGER},
      </if>
      <if test="record.validLiveNewAidIncmAmtDiffScore != null" >
        valid_live_new_aid_incm_amt_diff_score = #{record.validLiveNewAidIncmAmtDiffScore,jdbcType=INTEGER},
      </if>
      <if test="record.unvalidLiveNewAidIncmAmtDiffScore != null" >
        unvalid_live_new_aid_incm_amt_diff_score = #{record.unvalidLiveNewAidIncmAmtDiffScore,jdbcType=INTEGER},
      </if>
      <if test="record.unvalidLiveNewAidIncmAmtRateDiffScore != null" >
        unvalid_live_new_aid_incm_amt_rate_diff_score = #{record.unvalidLiveNewAidIncmAmtRateDiffScore,jdbcType=INTEGER},
      </if>
      <if test="record.guildHealthPointScore != null" >
        guild_health_point_score = #{record.guildHealthPointScore,jdbcType=INTEGER},
      </if>
      <if test="record.recruScore != null" >
        recru_score = #{record.recruScore,jdbcType=INTEGER},
      </if>
      <if test="record.hatchScore != null" >
        hatch_score = #{record.hatchScore,jdbcType=INTEGER},
      </if>
      <if test="record.retScore != null" >
        ret_score = #{record.retScore,jdbcType=INTEGER},
      </if>
      <if test="record.rvnuScore != null" >
        rvnu_score = #{record.rvnuScore,jdbcType=INTEGER},
      </if>
      <if test="record.comScore != null" >
        com_score = #{record.comScore,jdbcType=INTEGER},
      </if>
      <if test="record.dataTotalScore != null" >
        data_total_score = #{record.dataTotalScore,jdbcType=INTEGER},
      </if>
      <if test="record.dataCmpStarLvl != null" >
        data_cmp_star_lvl = #{record.dataCmpStarLvl,jdbcType=INTEGER},
      </if>
      <if test="record.cmpStarLvl != null" >
        cmp_star_lvl = #{record.cmpStarLvl,jdbcType=INTEGER},
      </if>
      <if test="record.newAidRat34AidNumScore != null" >
        new_aid_rat_3_4_aid_num_score = #{record.newAidRat34AidNumScore,jdbcType=INTEGER},
      </if>
      <if test="record.upgrdHighAidNumScore != null" >
        upgrd_high_aid_num_score = #{record.upgrdHighAidNumScore,jdbcType=INTEGER},
      </if>
      <if test="record.upgrdWaistAidNumScore != null" >
        upgrd_waist_aid_num_score = #{record.upgrdWaistAidNumScore,jdbcType=INTEGER},
      </if>
      <if test="record.extGoldenAidNumScore != null" >
        ext_golden_aid_num_score = #{record.extGoldenAidNumScore,jdbcType=INTEGER},
      </if>
      <if test="record.actvProdPayAmtRateScore != null" >
        actv_prod_pay_amt_rate_score = #{record.actvProdPayAmtRateScore,jdbcType=INTEGER},
      </if>
      <if test="record.actvIncmAmtRateScore != null" >
        actv_incm_amt_rate_score = #{record.actvIncmAmtRateScore,jdbcType=INTEGER},
      </if>
      <if test="record.taskTotalScore != null" >
        task_total_score = #{record.taskTotalScore,jdbcType=INTEGER},
      </if>
      <if test="record.newAidRat34AidNumItg != null" >
        new_aid_rat_3_4_aid_num_itg = #{record.newAidRat34AidNumItg,jdbcType=INTEGER},
      </if>
      <if test="record.upgrdHighAidNumItg != null" >
        upgrd_high_aid_num_itg = #{record.upgrdHighAidNumItg,jdbcType=INTEGER},
      </if>
      <if test="record.upgrdWaistAidNumItg != null" >
        upgrd_waist_aid_num_itg = #{record.upgrdWaistAidNumItg,jdbcType=INTEGER},
      </if>
      <if test="record.extGoldenAidNumItg != null" >
        ext_golden_aid_num_itg = #{record.extGoldenAidNumItg,jdbcType=INTEGER},
      </if>
      <if test="record.actvProdPayAmtRateItg != null" >
        actv_prod_pay_amt_rate_itg = #{record.actvProdPayAmtRateItg,jdbcType=INTEGER},
      </if>
      <if test="record.actvIncmAmtRateItg != null" >
        actv_incm_amt_rate_itg = #{record.actvIncmAmtRateItg,jdbcType=INTEGER},
      </if>
      <if test="record.taskTotalItg != null" >
        task_total_itg = #{record.taskTotalItg,jdbcType=INTEGER},
      </if>
      <if test="record.totalScore != null" >
        total_score = #{record.totalScore,jdbcType=INTEGER},
      </if>
      <if test="record.dt != null" >
        dt = #{record.dt,jdbcType=DATE},
      </if>
      <if test="record.avgNewAidRat34AidNum3m != null" >
        avg_new_aid_rat_3_4_aid_num_3m = #{record.avgNewAidRat34AidNum3m,jdbcType=DECIMAL},
      </if>
      <if test="record.newAidRat3AidNum != null" >
        new_aid_rat_3_aid_num = #{record.newAidRat3AidNum,jdbcType=BIGINT},
      </if>
      <if test="record.newAidRat4AidNum != null" >
        new_aid_rat_4_aid_num = #{record.newAidRat4AidNum,jdbcType=BIGINT},
      </if>
      <if test="record.avgNewAidRat34AidNum3mAim != null" >
        avg_new_aid_rat_3_4_aid_num_3m_aim = #{record.avgNewAidRat34AidNum3mAim,jdbcType=INTEGER},
      </if>
      <if test="record.avgNewAidRat34AidNum3mScore != null" >
        avg_new_aid_rat_3_4_aid_num_3m_score = #{record.avgNewAidRat34AidNum3mScore,jdbcType=INTEGER},
      </if>
      <if test="record.avgUpgrdHighWaistAidNum3m != null" >
        avg_upgrd_high_waist_aid_num_3m = #{record.avgUpgrdHighWaistAidNum3m,jdbcType=DECIMAL},
      </if>
      <if test="record.avgUpgrdHighWaistAidNum3mAim != null" >
        avg_upgrd_high_waist_aid_num_3m_aim = #{record.avgUpgrdHighWaistAidNum3mAim,jdbcType=INTEGER},
      </if>
      <if test="record.avgUpgrdHighWaistAidNum3mScore != null" >
        avg_upgrd_high_waist_aid_num_3m_score = #{record.avgUpgrdHighWaistAidNum3mScore,jdbcType=INTEGER},
      </if>
      <if test="record.newAidIncmComRate != null" >
        new_aid_incm_com_rate = #{record.newAidIncmComRate,jdbcType=DECIMAL},
      </if>
      <if test="record.newAidIncmComRateAim != null" >
        new_aid_incm_com_rate_aim = #{record.newAidIncmComRateAim,jdbcType=DECIMAL},
      </if>
      <if test="record.newAidIncmComRateScore != null" >
        new_aid_incm_com_rate_score = #{record.newAidIncmComRateScore,jdbcType=INTEGER},
      </if>
      <if test="record.guildTaskIncmLvl != null" >
        guild_task_incm_lvl = #{record.guildTaskIncmLvl,jdbcType=INTEGER},
      </if>
      <if test="record.guildTaskIncmLvlAim != null" >
        guild_task_incm_lvl_aim = #{record.guildTaskIncmLvlAim,jdbcType=INTEGER},
      </if>
      <if test="record.guildTaskIncmLvlScore != null" >
        guild_task_incm_lvl_score = #{record.guildTaskIncmLvlScore,jdbcType=INTEGER},
      </if>
      <if test="record.accumIncmAmtDiff != null" >
        accum_incm_amt_diff = #{record.accumIncmAmtDiff,jdbcType=BIGINT},
      </if>
      <if test="record.accumIncmAmtAim != null" >
        accum_incm_amt_aim = #{record.accumIncmAmtAim,jdbcType=BIGINT},
      </if>
      <if test="record.accumIncmAmtSocre != null" >
        accum_incm_amt_socre = #{record.accumIncmAmtSocre,jdbcType=INTEGER},
      </if>
      <if test="record.guildCmpName != null" >
        guild_cmp_name = #{record.guildCmpName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update yy_dm_entity_guild_cmp_health_analysis_di
    set id = #{record.id,jdbcType=BIGINT},
      guild_cmp_ownr_id = #{record.guildCmpOwnrId,jdbcType=BIGINT},
      new_aid_rat_3_4_aid_num = #{record.newAidRat34AidNum,jdbcType=BIGINT},
      new_aid_rat_3_4_aid_num_rn = #{record.newAidRat34AidNumRn,jdbcType=BIGINT},
      new_aid_rat_3_4_aid_num_diff = #{record.newAidRat34AidNumDiff,jdbcType=BIGINT},
      upgrd_high_aid_num = #{record.upgrdHighAidNum,jdbcType=BIGINT},
      upgrd_high_aid_num_diff = #{record.upgrdHighAidNumDiff,jdbcType=BIGINT},
      upgrd_waist_aid_num = #{record.upgrdWaistAidNum,jdbcType=BIGINT},
      upgrd_waist_aid_num_diff = #{record.upgrdWaistAidNumDiff,jdbcType=BIGINT},
      avg_high_valid_live_aid_num = #{record.avgHighValidLiveAidNum,jdbcType=DOUBLE},
      avg_high_valid_live_aid_num_rn = #{record.avgHighValidLiveAidNumRn,jdbcType=BIGINT},
      avg_high_valid_live_aid_num_diff = #{record.avgHighValidLiveAidNumDiff,jdbcType=DOUBLE},
      avg_waist_valid_live_aid_num = #{record.avgWaistValidLiveAidNum,jdbcType=DOUBLE},
      avg_waist_valid_live_aid_num_rn = #{record.avgWaistValidLiveAidNumRn,jdbcType=BIGINT},
      avg_waist_valid_live_aid_num_diff = #{record.avgWaistValidLiveAidNumDiff,jdbcType=DOUBLE},
      avg_nto_valid_live_aid_rate = #{record.avgNtoValidLiveAidRate,jdbcType=DOUBLE},
      avg_nto_valid_live_aid_rate_rn = #{record.avgNtoValidLiveAidRateRn,jdbcType=BIGINT},
      avg_stock_valid_live_aid_rate = #{record.avgStockValidLiveAidRate,jdbcType=DOUBLE},
      avg_stock_valid_live_aid_rate_rn = #{record.avgStockValidLiveAidRateRn,jdbcType=BIGINT},
      new_auth_golden_aid_num = #{record.newAuthGoldenAidNum,jdbcType=BIGINT},
      new_auth_golden_aid_num_rn = #{record.newAuthGoldenAidNumRn,jdbcType=BIGINT},
      ext_golden_aid_num = #{record.extGoldenAidNum,jdbcType=BIGINT},
      actv_prod_pay_amt = #{record.actvProdPayAmt,jdbcType=DOUBLE},
      prod_pay_amt = #{record.prodPayAmt,jdbcType=DOUBLE},
      actv_prod_pay_amt_rate = #{record.actvProdPayAmtRate,jdbcType=DOUBLE},
      valid_live_new_aid_prod_pay_amt = #{record.validLiveNewAidProdPayAmt,jdbcType=DOUBLE},
      valid_live_new_aid_prod_pay_amt_rn = #{record.validLiveNewAidProdPayAmtRn,jdbcType=BIGINT},
      valid_live_new_aid_prod_pay_amt_diff = #{record.validLiveNewAidProdPayAmtDiff,jdbcType=DOUBLE},
      unvalid_live_new_aid_prod_pay_amt = #{record.unvalidLiveNewAidProdPayAmt,jdbcType=DOUBLE},
      unvalid_live_new_aid_prod_pay_amt_rn = #{record.unvalidLiveNewAidProdPayAmtRn,jdbcType=BIGINT},
      unvalid_live_new_aid_prod_pay_amt_diff = #{record.unvalidLiveNewAidProdPayAmtDiff,jdbcType=DOUBLE},
      unvalid_live_new_aid_prod_pay_amt_rate = #{record.unvalidLiveNewAidProdPayAmtRate,jdbcType=DOUBLE},
      total_unvalid_live_new_aid_prod_pay_amt_rate = #{record.totalUnvalidLiveNewAidProdPayAmtRate,jdbcType=DOUBLE},
      unvalid_live_new_aid_prod_pay_amt_rate_diff = #{record.unvalidLiveNewAidProdPayAmtRateDiff,jdbcType=DOUBLE},
      actv_incm_amt = #{record.actvIncmAmt,jdbcType=DOUBLE},
      incm_amt = #{record.incmAmt,jdbcType=DOUBLE},
      actv_incm_amt_rate = #{record.actvIncmAmtRate,jdbcType=DOUBLE},
      valid_live_new_aid_incm_amt = #{record.validLiveNewAidIncmAmt,jdbcType=DOUBLE},
      valid_live_new_aid_incm_amt_rn = #{record.validLiveNewAidIncmAmtRn,jdbcType=BIGINT},
      valid_live_new_aid_incm_amt_diff = #{record.validLiveNewAidIncmAmtDiff,jdbcType=DOUBLE},
      unvalid_live_new_aid_incm_amt = #{record.unvalidLiveNewAidIncmAmt,jdbcType=DOUBLE},
      unvalid_live_new_aid_incm_amt_rn = #{record.unvalidLiveNewAidIncmAmtRn,jdbcType=BIGINT},
      unvalid_live_new_aid_incm_amt_diff = #{record.unvalidLiveNewAidIncmAmtDiff,jdbcType=DOUBLE},
      unvalid_live_new_aid_incm_amt_rate = #{record.unvalidLiveNewAidIncmAmtRate,jdbcType=DOUBLE},
      total_unvalid_live_new_aid_incm_amt_rate = #{record.totalUnvalidLiveNewAidIncmAmtRate,jdbcType=DOUBLE},
      unvalid_live_new_aid_incm_amt_rate_diff = #{record.unvalidLiveNewAidIncmAmtRateDiff,jdbcType=DOUBLE},
      guild_health_point = #{record.guildHealthPoint,jdbcType=DOUBLE},
      new_aid_rat_3_4_aid_num_rn_score = #{record.newAidRat34AidNumRnScore,jdbcType=INTEGER},
      new_aid_rat_3_4_aid_num_diff_score = #{record.newAidRat34AidNumDiffScore,jdbcType=INTEGER},
      upgrd_high_aid_num_diff_score = #{record.upgrdHighAidNumDiffScore,jdbcType=INTEGER},
      upgrd_waist_aid_num_diff_score = #{record.upgrdWaistAidNumDiffScore,jdbcType=INTEGER},
      avg_high_valid_live_aid_num_rn_score = #{record.avgHighValidLiveAidNumRnScore,jdbcType=INTEGER},
      avg_waist_valid_live_aid_num_rn_score = #{record.avgWaistValidLiveAidNumRnScore,jdbcType=INTEGER},
      avg_high_valid_live_aid_num_diff_score = #{record.avgHighValidLiveAidNumDiffScore,jdbcType=INTEGER},
      avg_waist_valid_live_aid_num_diff_score = #{record.avgWaistValidLiveAidNumDiffScore,jdbcType=INTEGER},
      avg_nto_valid_live_aid_rate_rn_score = #{record.avgNtoValidLiveAidRateRnScore,jdbcType=INTEGER},
      avg_stock_valid_live_aid_rate_rn_score = #{record.avgStockValidLiveAidRateRnScore,jdbcType=INTEGER},
      new_auth_golden_aid_num_rn_score = #{record.newAuthGoldenAidNumRnScore,jdbcType=INTEGER},
      valid_live_new_aid_prod_pay_amt_rn_score = #{record.validLiveNewAidProdPayAmtRnScore,jdbcType=INTEGER},
      unvalid_live_new_aid_prod_pay_amt_rn_score = #{record.unvalidLiveNewAidProdPayAmtRnScore,jdbcType=INTEGER},
      valid_live_new_aid_prod_pay_amt_diff_score = #{record.validLiveNewAidProdPayAmtDiffScore,jdbcType=INTEGER},
      unvalid_live_new_aid_prod_pay_amt_diff_score = #{record.unvalidLiveNewAidProdPayAmtDiffScore,jdbcType=INTEGER},
      unvalid_live_new_aid_prod_pay_amt_rate_diff_score = #{record.unvalidLiveNewAidProdPayAmtRateDiffScore,jdbcType=INTEGER},
      valid_live_new_aid_incm_amt_rn_score = #{record.validLiveNewAidIncmAmtRnScore,jdbcType=INTEGER},
      unvalid_live_new_aid_incm_amt_rn_score = #{record.unvalidLiveNewAidIncmAmtRnScore,jdbcType=INTEGER},
      valid_live_new_aid_incm_amt_diff_score = #{record.validLiveNewAidIncmAmtDiffScore,jdbcType=INTEGER},
      unvalid_live_new_aid_incm_amt_diff_score = #{record.unvalidLiveNewAidIncmAmtDiffScore,jdbcType=INTEGER},
      unvalid_live_new_aid_incm_amt_rate_diff_score = #{record.unvalidLiveNewAidIncmAmtRateDiffScore,jdbcType=INTEGER},
      guild_health_point_score = #{record.guildHealthPointScore,jdbcType=INTEGER},
      recru_score = #{record.recruScore,jdbcType=INTEGER},
      hatch_score = #{record.hatchScore,jdbcType=INTEGER},
      ret_score = #{record.retScore,jdbcType=INTEGER},
      rvnu_score = #{record.rvnuScore,jdbcType=INTEGER},
      com_score = #{record.comScore,jdbcType=INTEGER},
      data_total_score = #{record.dataTotalScore,jdbcType=INTEGER},
      data_cmp_star_lvl = #{record.dataCmpStarLvl,jdbcType=INTEGER},
      cmp_star_lvl = #{record.cmpStarLvl,jdbcType=INTEGER},
      new_aid_rat_3_4_aid_num_score = #{record.newAidRat34AidNumScore,jdbcType=INTEGER},
      upgrd_high_aid_num_score = #{record.upgrdHighAidNumScore,jdbcType=INTEGER},
      upgrd_waist_aid_num_score = #{record.upgrdWaistAidNumScore,jdbcType=INTEGER},
      ext_golden_aid_num_score = #{record.extGoldenAidNumScore,jdbcType=INTEGER},
      actv_prod_pay_amt_rate_score = #{record.actvProdPayAmtRateScore,jdbcType=INTEGER},
      actv_incm_amt_rate_score = #{record.actvIncmAmtRateScore,jdbcType=INTEGER},
      task_total_score = #{record.taskTotalScore,jdbcType=INTEGER},
      new_aid_rat_3_4_aid_num_itg = #{record.newAidRat34AidNumItg,jdbcType=INTEGER},
      upgrd_high_aid_num_itg = #{record.upgrdHighAidNumItg,jdbcType=INTEGER},
      upgrd_waist_aid_num_itg = #{record.upgrdWaistAidNumItg,jdbcType=INTEGER},
      ext_golden_aid_num_itg = #{record.extGoldenAidNumItg,jdbcType=INTEGER},
      actv_prod_pay_amt_rate_itg = #{record.actvProdPayAmtRateItg,jdbcType=INTEGER},
      actv_incm_amt_rate_itg = #{record.actvIncmAmtRateItg,jdbcType=INTEGER},
      task_total_itg = #{record.taskTotalItg,jdbcType=INTEGER},
      total_score = #{record.totalScore,jdbcType=INTEGER},
      dt = #{record.dt,jdbcType=DATE},
      avg_new_aid_rat_3_4_aid_num_3m = #{record.avgNewAidRat34AidNum3m,jdbcType=DECIMAL},
      new_aid_rat_3_aid_num = #{record.newAidRat3AidNum,jdbcType=BIGINT},
      new_aid_rat_4_aid_num = #{record.newAidRat4AidNum,jdbcType=BIGINT},
      avg_new_aid_rat_3_4_aid_num_3m_aim = #{record.avgNewAidRat34AidNum3mAim,jdbcType=INTEGER},
      avg_new_aid_rat_3_4_aid_num_3m_score = #{record.avgNewAidRat34AidNum3mScore,jdbcType=INTEGER},
      avg_upgrd_high_waist_aid_num_3m = #{record.avgUpgrdHighWaistAidNum3m,jdbcType=DECIMAL},
      avg_upgrd_high_waist_aid_num_3m_aim = #{record.avgUpgrdHighWaistAidNum3mAim,jdbcType=INTEGER},
      avg_upgrd_high_waist_aid_num_3m_score = #{record.avgUpgrdHighWaistAidNum3mScore,jdbcType=INTEGER},
      new_aid_incm_com_rate = #{record.newAidIncmComRate,jdbcType=DECIMAL},
      new_aid_incm_com_rate_aim = #{record.newAidIncmComRateAim,jdbcType=DECIMAL},
      new_aid_incm_com_rate_score = #{record.newAidIncmComRateScore,jdbcType=INTEGER},
      guild_task_incm_lvl = #{record.guildTaskIncmLvl,jdbcType=INTEGER},
      guild_task_incm_lvl_aim = #{record.guildTaskIncmLvlAim,jdbcType=INTEGER},
      guild_task_incm_lvl_score = #{record.guildTaskIncmLvlScore,jdbcType=INTEGER},
      accum_incm_amt_diff = #{record.accumIncmAmtDiff,jdbcType=BIGINT},
      accum_incm_amt_aim = #{record.accumIncmAmtAim,jdbcType=BIGINT},
      accum_incm_amt_socre = #{record.accumIncmAmtSocre,jdbcType=INTEGER},
      guild_cmp_name = #{record.guildCmpName,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.yyzone.guildrank.db.gen.model.YyDmEntityGuildCmpHealthAnalysisDi" >
    update yy_dm_entity_guild_cmp_health_analysis_di
    <set >
      <if test="guildCmpOwnrId != null" >
        guild_cmp_ownr_id = #{guildCmpOwnrId,jdbcType=BIGINT},
      </if>
      <if test="newAidRat34AidNum != null" >
        new_aid_rat_3_4_aid_num = #{newAidRat34AidNum,jdbcType=BIGINT},
      </if>
      <if test="newAidRat34AidNumRn != null" >
        new_aid_rat_3_4_aid_num_rn = #{newAidRat34AidNumRn,jdbcType=BIGINT},
      </if>
      <if test="newAidRat34AidNumDiff != null" >
        new_aid_rat_3_4_aid_num_diff = #{newAidRat34AidNumDiff,jdbcType=BIGINT},
      </if>
      <if test="upgrdHighAidNum != null" >
        upgrd_high_aid_num = #{upgrdHighAidNum,jdbcType=BIGINT},
      </if>
      <if test="upgrdHighAidNumDiff != null" >
        upgrd_high_aid_num_diff = #{upgrdHighAidNumDiff,jdbcType=BIGINT},
      </if>
      <if test="upgrdWaistAidNum != null" >
        upgrd_waist_aid_num = #{upgrdWaistAidNum,jdbcType=BIGINT},
      </if>
      <if test="upgrdWaistAidNumDiff != null" >
        upgrd_waist_aid_num_diff = #{upgrdWaistAidNumDiff,jdbcType=BIGINT},
      </if>
      <if test="avgHighValidLiveAidNum != null" >
        avg_high_valid_live_aid_num = #{avgHighValidLiveAidNum,jdbcType=DOUBLE},
      </if>
      <if test="avgHighValidLiveAidNumRn != null" >
        avg_high_valid_live_aid_num_rn = #{avgHighValidLiveAidNumRn,jdbcType=BIGINT},
      </if>
      <if test="avgHighValidLiveAidNumDiff != null" >
        avg_high_valid_live_aid_num_diff = #{avgHighValidLiveAidNumDiff,jdbcType=DOUBLE},
      </if>
      <if test="avgWaistValidLiveAidNum != null" >
        avg_waist_valid_live_aid_num = #{avgWaistValidLiveAidNum,jdbcType=DOUBLE},
      </if>
      <if test="avgWaistValidLiveAidNumRn != null" >
        avg_waist_valid_live_aid_num_rn = #{avgWaistValidLiveAidNumRn,jdbcType=BIGINT},
      </if>
      <if test="avgWaistValidLiveAidNumDiff != null" >
        avg_waist_valid_live_aid_num_diff = #{avgWaistValidLiveAidNumDiff,jdbcType=DOUBLE},
      </if>
      <if test="avgNtoValidLiveAidRate != null" >
        avg_nto_valid_live_aid_rate = #{avgNtoValidLiveAidRate,jdbcType=DOUBLE},
      </if>
      <if test="avgNtoValidLiveAidRateRn != null" >
        avg_nto_valid_live_aid_rate_rn = #{avgNtoValidLiveAidRateRn,jdbcType=BIGINT},
      </if>
      <if test="avgStockValidLiveAidRate != null" >
        avg_stock_valid_live_aid_rate = #{avgStockValidLiveAidRate,jdbcType=DOUBLE},
      </if>
      <if test="avgStockValidLiveAidRateRn != null" >
        avg_stock_valid_live_aid_rate_rn = #{avgStockValidLiveAidRateRn,jdbcType=BIGINT},
      </if>
      <if test="newAuthGoldenAidNum != null" >
        new_auth_golden_aid_num = #{newAuthGoldenAidNum,jdbcType=BIGINT},
      </if>
      <if test="newAuthGoldenAidNumRn != null" >
        new_auth_golden_aid_num_rn = #{newAuthGoldenAidNumRn,jdbcType=BIGINT},
      </if>
      <if test="extGoldenAidNum != null" >
        ext_golden_aid_num = #{extGoldenAidNum,jdbcType=BIGINT},
      </if>
      <if test="actvProdPayAmt != null" >
        actv_prod_pay_amt = #{actvProdPayAmt,jdbcType=DOUBLE},
      </if>
      <if test="prodPayAmt != null" >
        prod_pay_amt = #{prodPayAmt,jdbcType=DOUBLE},
      </if>
      <if test="actvProdPayAmtRate != null" >
        actv_prod_pay_amt_rate = #{actvProdPayAmtRate,jdbcType=DOUBLE},
      </if>
      <if test="validLiveNewAidProdPayAmt != null" >
        valid_live_new_aid_prod_pay_amt = #{validLiveNewAidProdPayAmt,jdbcType=DOUBLE},
      </if>
      <if test="validLiveNewAidProdPayAmtRn != null" >
        valid_live_new_aid_prod_pay_amt_rn = #{validLiveNewAidProdPayAmtRn,jdbcType=BIGINT},
      </if>
      <if test="validLiveNewAidProdPayAmtDiff != null" >
        valid_live_new_aid_prod_pay_amt_diff = #{validLiveNewAidProdPayAmtDiff,jdbcType=DOUBLE},
      </if>
      <if test="unvalidLiveNewAidProdPayAmt != null" >
        unvalid_live_new_aid_prod_pay_amt = #{unvalidLiveNewAidProdPayAmt,jdbcType=DOUBLE},
      </if>
      <if test="unvalidLiveNewAidProdPayAmtRn != null" >
        unvalid_live_new_aid_prod_pay_amt_rn = #{unvalidLiveNewAidProdPayAmtRn,jdbcType=BIGINT},
      </if>
      <if test="unvalidLiveNewAidProdPayAmtDiff != null" >
        unvalid_live_new_aid_prod_pay_amt_diff = #{unvalidLiveNewAidProdPayAmtDiff,jdbcType=DOUBLE},
      </if>
      <if test="unvalidLiveNewAidProdPayAmtRate != null" >
        unvalid_live_new_aid_prod_pay_amt_rate = #{unvalidLiveNewAidProdPayAmtRate,jdbcType=DOUBLE},
      </if>
      <if test="totalUnvalidLiveNewAidProdPayAmtRate != null" >
        total_unvalid_live_new_aid_prod_pay_amt_rate = #{totalUnvalidLiveNewAidProdPayAmtRate,jdbcType=DOUBLE},
      </if>
      <if test="unvalidLiveNewAidProdPayAmtRateDiff != null" >
        unvalid_live_new_aid_prod_pay_amt_rate_diff = #{unvalidLiveNewAidProdPayAmtRateDiff,jdbcType=DOUBLE},
      </if>
      <if test="actvIncmAmt != null" >
        actv_incm_amt = #{actvIncmAmt,jdbcType=DOUBLE},
      </if>
      <if test="incmAmt != null" >
        incm_amt = #{incmAmt,jdbcType=DOUBLE},
      </if>
      <if test="actvIncmAmtRate != null" >
        actv_incm_amt_rate = #{actvIncmAmtRate,jdbcType=DOUBLE},
      </if>
      <if test="validLiveNewAidIncmAmt != null" >
        valid_live_new_aid_incm_amt = #{validLiveNewAidIncmAmt,jdbcType=DOUBLE},
      </if>
      <if test="validLiveNewAidIncmAmtRn != null" >
        valid_live_new_aid_incm_amt_rn = #{validLiveNewAidIncmAmtRn,jdbcType=BIGINT},
      </if>
      <if test="validLiveNewAidIncmAmtDiff != null" >
        valid_live_new_aid_incm_amt_diff = #{validLiveNewAidIncmAmtDiff,jdbcType=DOUBLE},
      </if>
      <if test="unvalidLiveNewAidIncmAmt != null" >
        unvalid_live_new_aid_incm_amt = #{unvalidLiveNewAidIncmAmt,jdbcType=DOUBLE},
      </if>
      <if test="unvalidLiveNewAidIncmAmtRn != null" >
        unvalid_live_new_aid_incm_amt_rn = #{unvalidLiveNewAidIncmAmtRn,jdbcType=BIGINT},
      </if>
      <if test="unvalidLiveNewAidIncmAmtDiff != null" >
        unvalid_live_new_aid_incm_amt_diff = #{unvalidLiveNewAidIncmAmtDiff,jdbcType=DOUBLE},
      </if>
      <if test="unvalidLiveNewAidIncmAmtRate != null" >
        unvalid_live_new_aid_incm_amt_rate = #{unvalidLiveNewAidIncmAmtRate,jdbcType=DOUBLE},
      </if>
      <if test="totalUnvalidLiveNewAidIncmAmtRate != null" >
        total_unvalid_live_new_aid_incm_amt_rate = #{totalUnvalidLiveNewAidIncmAmtRate,jdbcType=DOUBLE},
      </if>
      <if test="unvalidLiveNewAidIncmAmtRateDiff != null" >
        unvalid_live_new_aid_incm_amt_rate_diff = #{unvalidLiveNewAidIncmAmtRateDiff,jdbcType=DOUBLE},
      </if>
      <if test="guildHealthPoint != null" >
        guild_health_point = #{guildHealthPoint,jdbcType=DOUBLE},
      </if>
      <if test="newAidRat34AidNumRnScore != null" >
        new_aid_rat_3_4_aid_num_rn_score = #{newAidRat34AidNumRnScore,jdbcType=INTEGER},
      </if>
      <if test="newAidRat34AidNumDiffScore != null" >
        new_aid_rat_3_4_aid_num_diff_score = #{newAidRat34AidNumDiffScore,jdbcType=INTEGER},
      </if>
      <if test="upgrdHighAidNumDiffScore != null" >
        upgrd_high_aid_num_diff_score = #{upgrdHighAidNumDiffScore,jdbcType=INTEGER},
      </if>
      <if test="upgrdWaistAidNumDiffScore != null" >
        upgrd_waist_aid_num_diff_score = #{upgrdWaistAidNumDiffScore,jdbcType=INTEGER},
      </if>
      <if test="avgHighValidLiveAidNumRnScore != null" >
        avg_high_valid_live_aid_num_rn_score = #{avgHighValidLiveAidNumRnScore,jdbcType=INTEGER},
      </if>
      <if test="avgWaistValidLiveAidNumRnScore != null" >
        avg_waist_valid_live_aid_num_rn_score = #{avgWaistValidLiveAidNumRnScore,jdbcType=INTEGER},
      </if>
      <if test="avgHighValidLiveAidNumDiffScore != null" >
        avg_high_valid_live_aid_num_diff_score = #{avgHighValidLiveAidNumDiffScore,jdbcType=INTEGER},
      </if>
      <if test="avgWaistValidLiveAidNumDiffScore != null" >
        avg_waist_valid_live_aid_num_diff_score = #{avgWaistValidLiveAidNumDiffScore,jdbcType=INTEGER},
      </if>
      <if test="avgNtoValidLiveAidRateRnScore != null" >
        avg_nto_valid_live_aid_rate_rn_score = #{avgNtoValidLiveAidRateRnScore,jdbcType=INTEGER},
      </if>
      <if test="avgStockValidLiveAidRateRnScore != null" >
        avg_stock_valid_live_aid_rate_rn_score = #{avgStockValidLiveAidRateRnScore,jdbcType=INTEGER},
      </if>
      <if test="newAuthGoldenAidNumRnScore != null" >
        new_auth_golden_aid_num_rn_score = #{newAuthGoldenAidNumRnScore,jdbcType=INTEGER},
      </if>
      <if test="validLiveNewAidProdPayAmtRnScore != null" >
        valid_live_new_aid_prod_pay_amt_rn_score = #{validLiveNewAidProdPayAmtRnScore,jdbcType=INTEGER},
      </if>
      <if test="unvalidLiveNewAidProdPayAmtRnScore != null" >
        unvalid_live_new_aid_prod_pay_amt_rn_score = #{unvalidLiveNewAidProdPayAmtRnScore,jdbcType=INTEGER},
      </if>
      <if test="validLiveNewAidProdPayAmtDiffScore != null" >
        valid_live_new_aid_prod_pay_amt_diff_score = #{validLiveNewAidProdPayAmtDiffScore,jdbcType=INTEGER},
      </if>
      <if test="unvalidLiveNewAidProdPayAmtDiffScore != null" >
        unvalid_live_new_aid_prod_pay_amt_diff_score = #{unvalidLiveNewAidProdPayAmtDiffScore,jdbcType=INTEGER},
      </if>
      <if test="unvalidLiveNewAidProdPayAmtRateDiffScore != null" >
        unvalid_live_new_aid_prod_pay_amt_rate_diff_score = #{unvalidLiveNewAidProdPayAmtRateDiffScore,jdbcType=INTEGER},
      </if>
      <if test="validLiveNewAidIncmAmtRnScore != null" >
        valid_live_new_aid_incm_amt_rn_score = #{validLiveNewAidIncmAmtRnScore,jdbcType=INTEGER},
      </if>
      <if test="unvalidLiveNewAidIncmAmtRnScore != null" >
        unvalid_live_new_aid_incm_amt_rn_score = #{unvalidLiveNewAidIncmAmtRnScore,jdbcType=INTEGER},
      </if>
      <if test="validLiveNewAidIncmAmtDiffScore != null" >
        valid_live_new_aid_incm_amt_diff_score = #{validLiveNewAidIncmAmtDiffScore,jdbcType=INTEGER},
      </if>
      <if test="unvalidLiveNewAidIncmAmtDiffScore != null" >
        unvalid_live_new_aid_incm_amt_diff_score = #{unvalidLiveNewAidIncmAmtDiffScore,jdbcType=INTEGER},
      </if>
      <if test="unvalidLiveNewAidIncmAmtRateDiffScore != null" >
        unvalid_live_new_aid_incm_amt_rate_diff_score = #{unvalidLiveNewAidIncmAmtRateDiffScore,jdbcType=INTEGER},
      </if>
      <if test="guildHealthPointScore != null" >
        guild_health_point_score = #{guildHealthPointScore,jdbcType=INTEGER},
      </if>
      <if test="recruScore != null" >
        recru_score = #{recruScore,jdbcType=INTEGER},
      </if>
      <if test="hatchScore != null" >
        hatch_score = #{hatchScore,jdbcType=INTEGER},
      </if>
      <if test="retScore != null" >
        ret_score = #{retScore,jdbcType=INTEGER},
      </if>
      <if test="rvnuScore != null" >
        rvnu_score = #{rvnuScore,jdbcType=INTEGER},
      </if>
      <if test="comScore != null" >
        com_score = #{comScore,jdbcType=INTEGER},
      </if>
      <if test="dataTotalScore != null" >
        data_total_score = #{dataTotalScore,jdbcType=INTEGER},
      </if>
      <if test="dataCmpStarLvl != null" >
        data_cmp_star_lvl = #{dataCmpStarLvl,jdbcType=INTEGER},
      </if>
      <if test="cmpStarLvl != null" >
        cmp_star_lvl = #{cmpStarLvl,jdbcType=INTEGER},
      </if>
      <if test="newAidRat34AidNumScore != null" >
        new_aid_rat_3_4_aid_num_score = #{newAidRat34AidNumScore,jdbcType=INTEGER},
      </if>
      <if test="upgrdHighAidNumScore != null" >
        upgrd_high_aid_num_score = #{upgrdHighAidNumScore,jdbcType=INTEGER},
      </if>
      <if test="upgrdWaistAidNumScore != null" >
        upgrd_waist_aid_num_score = #{upgrdWaistAidNumScore,jdbcType=INTEGER},
      </if>
      <if test="extGoldenAidNumScore != null" >
        ext_golden_aid_num_score = #{extGoldenAidNumScore,jdbcType=INTEGER},
      </if>
      <if test="actvProdPayAmtRateScore != null" >
        actv_prod_pay_amt_rate_score = #{actvProdPayAmtRateScore,jdbcType=INTEGER},
      </if>
      <if test="actvIncmAmtRateScore != null" >
        actv_incm_amt_rate_score = #{actvIncmAmtRateScore,jdbcType=INTEGER},
      </if>
      <if test="taskTotalScore != null" >
        task_total_score = #{taskTotalScore,jdbcType=INTEGER},
      </if>
      <if test="newAidRat34AidNumItg != null" >
        new_aid_rat_3_4_aid_num_itg = #{newAidRat34AidNumItg,jdbcType=INTEGER},
      </if>
      <if test="upgrdHighAidNumItg != null" >
        upgrd_high_aid_num_itg = #{upgrdHighAidNumItg,jdbcType=INTEGER},
      </if>
      <if test="upgrdWaistAidNumItg != null" >
        upgrd_waist_aid_num_itg = #{upgrdWaistAidNumItg,jdbcType=INTEGER},
      </if>
      <if test="extGoldenAidNumItg != null" >
        ext_golden_aid_num_itg = #{extGoldenAidNumItg,jdbcType=INTEGER},
      </if>
      <if test="actvProdPayAmtRateItg != null" >
        actv_prod_pay_amt_rate_itg = #{actvProdPayAmtRateItg,jdbcType=INTEGER},
      </if>
      <if test="actvIncmAmtRateItg != null" >
        actv_incm_amt_rate_itg = #{actvIncmAmtRateItg,jdbcType=INTEGER},
      </if>
      <if test="taskTotalItg != null" >
        task_total_itg = #{taskTotalItg,jdbcType=INTEGER},
      </if>
      <if test="totalScore != null" >
        total_score = #{totalScore,jdbcType=INTEGER},
      </if>
      <if test="dt != null" >
        dt = #{dt,jdbcType=DATE},
      </if>
      <if test="avgNewAidRat34AidNum3m != null" >
        avg_new_aid_rat_3_4_aid_num_3m = #{avgNewAidRat34AidNum3m,jdbcType=DECIMAL},
      </if>
      <if test="newAidRat3AidNum != null" >
        new_aid_rat_3_aid_num = #{newAidRat3AidNum,jdbcType=BIGINT},
      </if>
      <if test="newAidRat4AidNum != null" >
        new_aid_rat_4_aid_num = #{newAidRat4AidNum,jdbcType=BIGINT},
      </if>
      <if test="avgNewAidRat34AidNum3mAim != null" >
        avg_new_aid_rat_3_4_aid_num_3m_aim = #{avgNewAidRat34AidNum3mAim,jdbcType=INTEGER},
      </if>
      <if test="avgNewAidRat34AidNum3mScore != null" >
        avg_new_aid_rat_3_4_aid_num_3m_score = #{avgNewAidRat34AidNum3mScore,jdbcType=INTEGER},
      </if>
      <if test="avgUpgrdHighWaistAidNum3m != null" >
        avg_upgrd_high_waist_aid_num_3m = #{avgUpgrdHighWaistAidNum3m,jdbcType=DECIMAL},
      </if>
      <if test="avgUpgrdHighWaistAidNum3mAim != null" >
        avg_upgrd_high_waist_aid_num_3m_aim = #{avgUpgrdHighWaistAidNum3mAim,jdbcType=INTEGER},
      </if>
      <if test="avgUpgrdHighWaistAidNum3mScore != null" >
        avg_upgrd_high_waist_aid_num_3m_score = #{avgUpgrdHighWaistAidNum3mScore,jdbcType=INTEGER},
      </if>
      <if test="newAidIncmComRate != null" >
        new_aid_incm_com_rate = #{newAidIncmComRate,jdbcType=DECIMAL},
      </if>
      <if test="newAidIncmComRateAim != null" >
        new_aid_incm_com_rate_aim = #{newAidIncmComRateAim,jdbcType=DECIMAL},
      </if>
      <if test="newAidIncmComRateScore != null" >
        new_aid_incm_com_rate_score = #{newAidIncmComRateScore,jdbcType=INTEGER},
      </if>
      <if test="guildTaskIncmLvl != null" >
        guild_task_incm_lvl = #{guildTaskIncmLvl,jdbcType=INTEGER},
      </if>
      <if test="guildTaskIncmLvlAim != null" >
        guild_task_incm_lvl_aim = #{guildTaskIncmLvlAim,jdbcType=INTEGER},
      </if>
      <if test="guildTaskIncmLvlScore != null" >
        guild_task_incm_lvl_score = #{guildTaskIncmLvlScore,jdbcType=INTEGER},
      </if>
      <if test="accumIncmAmtDiff != null" >
        accum_incm_amt_diff = #{accumIncmAmtDiff,jdbcType=BIGINT},
      </if>
      <if test="accumIncmAmtAim != null" >
        accum_incm_amt_aim = #{accumIncmAmtAim,jdbcType=BIGINT},
      </if>
      <if test="accumIncmAmtSocre != null" >
        accum_incm_amt_socre = #{accumIncmAmtSocre,jdbcType=INTEGER},
      </if>
      <if test="guildCmpName != null" >
        guild_cmp_name = #{guildCmpName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.yyzone.guildrank.db.gen.model.YyDmEntityGuildCmpHealthAnalysisDi" >
    update yy_dm_entity_guild_cmp_health_analysis_di
    set guild_cmp_ownr_id = #{guildCmpOwnrId,jdbcType=BIGINT},
      new_aid_rat_3_4_aid_num = #{newAidRat34AidNum,jdbcType=BIGINT},
      new_aid_rat_3_4_aid_num_rn = #{newAidRat34AidNumRn,jdbcType=BIGINT},
      new_aid_rat_3_4_aid_num_diff = #{newAidRat34AidNumDiff,jdbcType=BIGINT},
      upgrd_high_aid_num = #{upgrdHighAidNum,jdbcType=BIGINT},
      upgrd_high_aid_num_diff = #{upgrdHighAidNumDiff,jdbcType=BIGINT},
      upgrd_waist_aid_num = #{upgrdWaistAidNum,jdbcType=BIGINT},
      upgrd_waist_aid_num_diff = #{upgrdWaistAidNumDiff,jdbcType=BIGINT},
      avg_high_valid_live_aid_num = #{avgHighValidLiveAidNum,jdbcType=DOUBLE},
      avg_high_valid_live_aid_num_rn = #{avgHighValidLiveAidNumRn,jdbcType=BIGINT},
      avg_high_valid_live_aid_num_diff = #{avgHighValidLiveAidNumDiff,jdbcType=DOUBLE},
      avg_waist_valid_live_aid_num = #{avgWaistValidLiveAidNum,jdbcType=DOUBLE},
      avg_waist_valid_live_aid_num_rn = #{avgWaistValidLiveAidNumRn,jdbcType=BIGINT},
      avg_waist_valid_live_aid_num_diff = #{avgWaistValidLiveAidNumDiff,jdbcType=DOUBLE},
      avg_nto_valid_live_aid_rate = #{avgNtoValidLiveAidRate,jdbcType=DOUBLE},
      avg_nto_valid_live_aid_rate_rn = #{avgNtoValidLiveAidRateRn,jdbcType=BIGINT},
      avg_stock_valid_live_aid_rate = #{avgStockValidLiveAidRate,jdbcType=DOUBLE},
      avg_stock_valid_live_aid_rate_rn = #{avgStockValidLiveAidRateRn,jdbcType=BIGINT},
      new_auth_golden_aid_num = #{newAuthGoldenAidNum,jdbcType=BIGINT},
      new_auth_golden_aid_num_rn = #{newAuthGoldenAidNumRn,jdbcType=BIGINT},
      ext_golden_aid_num = #{extGoldenAidNum,jdbcType=BIGINT},
      actv_prod_pay_amt = #{actvProdPayAmt,jdbcType=DOUBLE},
      prod_pay_amt = #{prodPayAmt,jdbcType=DOUBLE},
      actv_prod_pay_amt_rate = #{actvProdPayAmtRate,jdbcType=DOUBLE},
      valid_live_new_aid_prod_pay_amt = #{validLiveNewAidProdPayAmt,jdbcType=DOUBLE},
      valid_live_new_aid_prod_pay_amt_rn = #{validLiveNewAidProdPayAmtRn,jdbcType=BIGINT},
      valid_live_new_aid_prod_pay_amt_diff = #{validLiveNewAidProdPayAmtDiff,jdbcType=DOUBLE},
      unvalid_live_new_aid_prod_pay_amt = #{unvalidLiveNewAidProdPayAmt,jdbcType=DOUBLE},
      unvalid_live_new_aid_prod_pay_amt_rn = #{unvalidLiveNewAidProdPayAmtRn,jdbcType=BIGINT},
      unvalid_live_new_aid_prod_pay_amt_diff = #{unvalidLiveNewAidProdPayAmtDiff,jdbcType=DOUBLE},
      unvalid_live_new_aid_prod_pay_amt_rate = #{unvalidLiveNewAidProdPayAmtRate,jdbcType=DOUBLE},
      total_unvalid_live_new_aid_prod_pay_amt_rate = #{totalUnvalidLiveNewAidProdPayAmtRate,jdbcType=DOUBLE},
      unvalid_live_new_aid_prod_pay_amt_rate_diff = #{unvalidLiveNewAidProdPayAmtRateDiff,jdbcType=DOUBLE},
      actv_incm_amt = #{actvIncmAmt,jdbcType=DOUBLE},
      incm_amt = #{incmAmt,jdbcType=DOUBLE},
      actv_incm_amt_rate = #{actvIncmAmtRate,jdbcType=DOUBLE},
      valid_live_new_aid_incm_amt = #{validLiveNewAidIncmAmt,jdbcType=DOUBLE},
      valid_live_new_aid_incm_amt_rn = #{validLiveNewAidIncmAmtRn,jdbcType=BIGINT},
      valid_live_new_aid_incm_amt_diff = #{validLiveNewAidIncmAmtDiff,jdbcType=DOUBLE},
      unvalid_live_new_aid_incm_amt = #{unvalidLiveNewAidIncmAmt,jdbcType=DOUBLE},
      unvalid_live_new_aid_incm_amt_rn = #{unvalidLiveNewAidIncmAmtRn,jdbcType=BIGINT},
      unvalid_live_new_aid_incm_amt_diff = #{unvalidLiveNewAidIncmAmtDiff,jdbcType=DOUBLE},
      unvalid_live_new_aid_incm_amt_rate = #{unvalidLiveNewAidIncmAmtRate,jdbcType=DOUBLE},
      total_unvalid_live_new_aid_incm_amt_rate = #{totalUnvalidLiveNewAidIncmAmtRate,jdbcType=DOUBLE},
      unvalid_live_new_aid_incm_amt_rate_diff = #{unvalidLiveNewAidIncmAmtRateDiff,jdbcType=DOUBLE},
      guild_health_point = #{guildHealthPoint,jdbcType=DOUBLE},
      new_aid_rat_3_4_aid_num_rn_score = #{newAidRat34AidNumRnScore,jdbcType=INTEGER},
      new_aid_rat_3_4_aid_num_diff_score = #{newAidRat34AidNumDiffScore,jdbcType=INTEGER},
      upgrd_high_aid_num_diff_score = #{upgrdHighAidNumDiffScore,jdbcType=INTEGER},
      upgrd_waist_aid_num_diff_score = #{upgrdWaistAidNumDiffScore,jdbcType=INTEGER},
      avg_high_valid_live_aid_num_rn_score = #{avgHighValidLiveAidNumRnScore,jdbcType=INTEGER},
      avg_waist_valid_live_aid_num_rn_score = #{avgWaistValidLiveAidNumRnScore,jdbcType=INTEGER},
      avg_high_valid_live_aid_num_diff_score = #{avgHighValidLiveAidNumDiffScore,jdbcType=INTEGER},
      avg_waist_valid_live_aid_num_diff_score = #{avgWaistValidLiveAidNumDiffScore,jdbcType=INTEGER},
      avg_nto_valid_live_aid_rate_rn_score = #{avgNtoValidLiveAidRateRnScore,jdbcType=INTEGER},
      avg_stock_valid_live_aid_rate_rn_score = #{avgStockValidLiveAidRateRnScore,jdbcType=INTEGER},
      new_auth_golden_aid_num_rn_score = #{newAuthGoldenAidNumRnScore,jdbcType=INTEGER},
      valid_live_new_aid_prod_pay_amt_rn_score = #{validLiveNewAidProdPayAmtRnScore,jdbcType=INTEGER},
      unvalid_live_new_aid_prod_pay_amt_rn_score = #{unvalidLiveNewAidProdPayAmtRnScore,jdbcType=INTEGER},
      valid_live_new_aid_prod_pay_amt_diff_score = #{validLiveNewAidProdPayAmtDiffScore,jdbcType=INTEGER},
      unvalid_live_new_aid_prod_pay_amt_diff_score = #{unvalidLiveNewAidProdPayAmtDiffScore,jdbcType=INTEGER},
      unvalid_live_new_aid_prod_pay_amt_rate_diff_score = #{unvalidLiveNewAidProdPayAmtRateDiffScore,jdbcType=INTEGER},
      valid_live_new_aid_incm_amt_rn_score = #{validLiveNewAidIncmAmtRnScore,jdbcType=INTEGER},
      unvalid_live_new_aid_incm_amt_rn_score = #{unvalidLiveNewAidIncmAmtRnScore,jdbcType=INTEGER},
      valid_live_new_aid_incm_amt_diff_score = #{validLiveNewAidIncmAmtDiffScore,jdbcType=INTEGER},
      unvalid_live_new_aid_incm_amt_diff_score = #{unvalidLiveNewAidIncmAmtDiffScore,jdbcType=INTEGER},
      unvalid_live_new_aid_incm_amt_rate_diff_score = #{unvalidLiveNewAidIncmAmtRateDiffScore,jdbcType=INTEGER},
      guild_health_point_score = #{guildHealthPointScore,jdbcType=INTEGER},
      recru_score = #{recruScore,jdbcType=INTEGER},
      hatch_score = #{hatchScore,jdbcType=INTEGER},
      ret_score = #{retScore,jdbcType=INTEGER},
      rvnu_score = #{rvnuScore,jdbcType=INTEGER},
      com_score = #{comScore,jdbcType=INTEGER},
      data_total_score = #{dataTotalScore,jdbcType=INTEGER},
      data_cmp_star_lvl = #{dataCmpStarLvl,jdbcType=INTEGER},
      cmp_star_lvl = #{cmpStarLvl,jdbcType=INTEGER},
      new_aid_rat_3_4_aid_num_score = #{newAidRat34AidNumScore,jdbcType=INTEGER},
      upgrd_high_aid_num_score = #{upgrdHighAidNumScore,jdbcType=INTEGER},
      upgrd_waist_aid_num_score = #{upgrdWaistAidNumScore,jdbcType=INTEGER},
      ext_golden_aid_num_score = #{extGoldenAidNumScore,jdbcType=INTEGER},
      actv_prod_pay_amt_rate_score = #{actvProdPayAmtRateScore,jdbcType=INTEGER},
      actv_incm_amt_rate_score = #{actvIncmAmtRateScore,jdbcType=INTEGER},
      task_total_score = #{taskTotalScore,jdbcType=INTEGER},
      new_aid_rat_3_4_aid_num_itg = #{newAidRat34AidNumItg,jdbcType=INTEGER},
      upgrd_high_aid_num_itg = #{upgrdHighAidNumItg,jdbcType=INTEGER},
      upgrd_waist_aid_num_itg = #{upgrdWaistAidNumItg,jdbcType=INTEGER},
      ext_golden_aid_num_itg = #{extGoldenAidNumItg,jdbcType=INTEGER},
      actv_prod_pay_amt_rate_itg = #{actvProdPayAmtRateItg,jdbcType=INTEGER},
      actv_incm_amt_rate_itg = #{actvIncmAmtRateItg,jdbcType=INTEGER},
      task_total_itg = #{taskTotalItg,jdbcType=INTEGER},
      total_score = #{totalScore,jdbcType=INTEGER},
      dt = #{dt,jdbcType=DATE},
      avg_new_aid_rat_3_4_aid_num_3m = #{avgNewAidRat34AidNum3m,jdbcType=DECIMAL},
      new_aid_rat_3_aid_num = #{newAidRat3AidNum,jdbcType=BIGINT},
      new_aid_rat_4_aid_num = #{newAidRat4AidNum,jdbcType=BIGINT},
      avg_new_aid_rat_3_4_aid_num_3m_aim = #{avgNewAidRat34AidNum3mAim,jdbcType=INTEGER},
      avg_new_aid_rat_3_4_aid_num_3m_score = #{avgNewAidRat34AidNum3mScore,jdbcType=INTEGER},
      avg_upgrd_high_waist_aid_num_3m = #{avgUpgrdHighWaistAidNum3m,jdbcType=DECIMAL},
      avg_upgrd_high_waist_aid_num_3m_aim = #{avgUpgrdHighWaistAidNum3mAim,jdbcType=INTEGER},
      avg_upgrd_high_waist_aid_num_3m_score = #{avgUpgrdHighWaistAidNum3mScore,jdbcType=INTEGER},
      new_aid_incm_com_rate = #{newAidIncmComRate,jdbcType=DECIMAL},
      new_aid_incm_com_rate_aim = #{newAidIncmComRateAim,jdbcType=DECIMAL},
      new_aid_incm_com_rate_score = #{newAidIncmComRateScore,jdbcType=INTEGER},
      guild_task_incm_lvl = #{guildTaskIncmLvl,jdbcType=INTEGER},
      guild_task_incm_lvl_aim = #{guildTaskIncmLvlAim,jdbcType=INTEGER},
      guild_task_incm_lvl_score = #{guildTaskIncmLvlScore,jdbcType=INTEGER},
      accum_incm_amt_diff = #{accumIncmAmtDiff,jdbcType=BIGINT},
      accum_incm_amt_aim = #{accumIncmAmtAim,jdbcType=BIGINT},
      accum_incm_amt_socre = #{accumIncmAmtSocre,jdbcType=INTEGER},
      guild_cmp_name = #{guildCmpName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>