<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yy.yyzone.guildrank.db.custom.mapper.DmYyGuildSegmentScoreMonthExtMapper" >
    <!-- 批量插入 -->
    <insert id="insertBatch">
        insert into dm_yy_guild_segment_score_month (sid_ownerid, sid_owyyid, month_diamond,
        month_diamond_rr, valid_live_uv, valid_live_uv_rr,
        acu300_uv, acu300_uv_rr, acu50_300_uv,
        acu50_300_uv_rr, acu10_50_uv, acu10_50_uv_rr,
        acu10_uv, acu10_uv_rr, break_a_uv,
        break_b_uv, break_c_uv, break_e_uv,
        live_uv, old_break_uv, break_uv_pp,
        break_uv_pp_rr, ps_s_all, ps_s_all_rr,
        ps_s_month_diamond, spr_month_diamond, ps_s_valid_live_uv,
        spr_valid_live_uv, ps_acu, ps_s_acu,
        spr_acu, ps_s_live_uv, ps_s_break_uv_pp,
        spr_break_uv_pp, dt, avg_high_aid_num,
        avg_high_aid_num_rr, avg_waist_aid_num, avg_waist_aid_num_rr,
        avg_tail_aid_num, avg_tail_aid_num_rr, aid_value_score,
        aid_value_score_star_lvl, aid_value_score_sprint_value,
        is_mars_guild, mars_sid_ownerid)
        values
        <foreach collection="list" item="i" separator=",">
        (#{i.sidOwnerid,jdbcType=BIGINT}, #{i.sidOwyyid,jdbcType=BIGINT}, #{i.monthDiamond,jdbcType=DOUBLE},
        #{i.monthDiamondRr,jdbcType=DOUBLE}, #{i.validLiveUv,jdbcType=DOUBLE}, #{i.validLiveUvRr,jdbcType=DOUBLE},
        #{i.acu300Uv,jdbcType=DOUBLE}, #{i.acu300UvRr,jdbcType=DOUBLE}, #{i.acu50300Uv,jdbcType=DOUBLE},
        #{i.acu50300UvRr,jdbcType=DOUBLE}, #{i.acu1050Uv,jdbcType=DOUBLE}, #{i.acu1050UvRr,jdbcType=DOUBLE},
        #{i.acu10Uv,jdbcType=DOUBLE}, #{i.acu10UvRr,jdbcType=DOUBLE}, #{i.breakAUv,jdbcType=BIGINT},
        #{i.breakBUv,jdbcType=BIGINT}, #{i.breakCUv,jdbcType=BIGINT}, #{i.breakEUv,jdbcType=BIGINT},
        #{i.liveUv,jdbcType=BIGINT}, #{i.oldBreakUv,jdbcType=BIGINT}, #{i.breakUvPp,jdbcType=DOUBLE},
        #{i.breakUvPpRr,jdbcType=DOUBLE}, #{i.psSAll,jdbcType=INTEGER}, #{i.psSAllRr,jdbcType=INTEGER},
        #{i.psSMonthDiamond,jdbcType=INTEGER}, #{i.sprMonthDiamond,jdbcType=DOUBLE}, #{i.psSValidLiveUv,jdbcType=INTEGER},
        #{i.sprValidLiveUv,jdbcType=DOUBLE}, #{i.psAcu,jdbcType=DOUBLE}, #{i.psSAcu,jdbcType=INTEGER},
        #{i.sprAcu,jdbcType=DOUBLE}, #{i.psSLiveUv,jdbcType=INTEGER}, #{i.psSBreakUvPp,jdbcType=INTEGER},
        #{i.sprBreakUvPp,jdbcType=DOUBLE}, #{i.dt,jdbcType=VARCHAR}, #{i.avgHighAidNum,jdbcType=DOUBLE},
        #{i.avgHighAidNumRr,jdbcType=DOUBLE}, #{i.avgWaistAidNum,jdbcType=DOUBLE}, #{i.avgWaistAidNumRr,jdbcType=DOUBLE},
        #{i.avgTailAidNum,jdbcType=DOUBLE}, #{i.avgTailAidNumRr,jdbcType=DOUBLE}, #{i.aidValueScore,jdbcType=DOUBLE},
        #{i.aidValueScoreStarLvl,jdbcType=INTEGER}, #{i.aidValueScoreSprintValue,jdbcType=DOUBLE},
        #{i.isMarsGuild,jdbcType=INTEGER}, #{i.marsSidOwnerid,jdbcType=BIGINT})
        </foreach>
    </insert>
</mapper>