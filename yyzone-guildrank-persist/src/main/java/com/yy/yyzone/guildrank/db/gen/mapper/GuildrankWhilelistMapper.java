package com.yy.yyzone.guildrank.db.gen.mapper;

import com.yy.yyzone.guildrank.db.gen.model.GuildrankWhilelist;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankWhilelistExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface GuildrankWhilelistMapper {
    int countByExample(GuildrankWhilelistExample example);

    int deleteByExample(GuildrankWhilelistExample example);

    int deleteByPrimaryKey(Long id);

    int insert(GuildrankWhilelist record);

    int insertSelective(GuildrankWhilelist record);

    List<GuildrankWhilelist> selectByExample(GuildrankWhilelistExample example);

    GuildrankWhilelist selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") GuildrankWhilelist record, @Param("example") GuildrankWhilelistExample example);

    int updateByExample(@Param("record") GuildrankWhilelist record, @Param("example") GuildrankWhilelistExample example);

    int updateByPrimaryKeySelective(GuildrankWhilelist record);

    int updateByPrimaryKey(GuildrankWhilelist record);
}