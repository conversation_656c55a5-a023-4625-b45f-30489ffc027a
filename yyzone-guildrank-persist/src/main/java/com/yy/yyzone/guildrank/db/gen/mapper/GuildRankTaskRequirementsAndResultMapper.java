package com.yy.yyzone.guildrank.db.gen.mapper;

import com.yy.yyzone.guildrank.db.gen.model.GuildRankTaskRequirementsAndResult;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface GuildRankTaskRequirementsAndResultMapper {

    @Select("SELECT id AS id, guild_cmp_ownr_id AS guildCmpOwnrId, dt AS dt, guild_cmp_name AS guildCmpName, " +
            "avg_new_aid_rat_3_4_aid_num_3m AS avgNewAidRat3Or4AidNum3m, " +
            "new_aid_rat_3_aid_num AS newAidRat3AidNum, " +
            "new_aid_rat_4_aid_num AS newAidRat4AidNum, " +
            "avg_new_aid_rat_3_4_aid_num_3m_aim AS avgNewAidRat3Or4AidNum3mAim, " +
            "avg_new_aid_rat_3_4_aid_num_3m_score AS avgNewAidRat3Or4AidNum3mScore, " +
            "avg_upgrd_high_waist_aid_num_3m AS avgUpgrdHighWaistAidNum3m, " +
            "avg_upgrd_high_waist_aid_num_3m_aim AS avgUpgrdHighWaistAidNum3mAim, " +
            "avg_upgrd_high_waist_aid_num_3m_score AS avgUpgrdHighWaistAidNum3mScore, " +
            "new_aid_incm_com_rate AS newAidIncmComRate, " +
            "new_aid_incm_com_rate_aim AS newAidIncmComRateAim, " +
            "new_aid_incm_com_rate_score AS newAidIncmComRateScore, " +
            "guild_task_incm_lvl AS guildTaskIncmLvl, " +
            "guild_task_incm_lvl_aim AS guildTaskIncmLvlAim, " +
            "guild_task_incm_lvl_score AS guildTaskIncmLvlScore, " +
            "accum_incm_amt_diff AS accumIncmAmtDiff, " +
            "accum_incm_amt_aim AS accumIncmAmtAim, " +
            "incm_amt AS incmAmt, " +
            "upgrd_high_aid_num AS upgrdHighAidNum, " +
            "upgrd_waist_aid_num AS upgrdWaistAidNum, " +
            "accum_incm_amt_socre AS accumIncmAmtSocre " +
            "FROM yy_dm_entity_guild_cmp_health_analysis_di " +
            "WHERE dt = #{dt} AND guild_cmp_ownr_id = #{guildCmpOwnrId}")
    GuildRankTaskRequirementsAndResult selectByDtAndUid(@Param("dt") Date dt, @Param("guildCmpOwnrId") Long uid);

    @Select("<script>" +
            "SELECT id AS id, guild_cmp_ownr_id AS guildCmpOwnrId, dt AS dt, guild_cmp_name AS guildCmpName, " +
            "avg_new_aid_rat_3_4_aid_num_3m AS avgNewAidRat3Or4AidNum3m, " +
            "new_aid_rat_3_aid_num AS newAidRat3AidNum, " +
            "new_aid_rat_4_aid_num AS newAidRat4AidNum, " +
            "avg_new_aid_rat_3_4_aid_num_3m_aim AS avgNewAidRat3Or4AidNum3mAim, " +
            "avg_new_aid_rat_3_4_aid_num_3m_score AS avgNewAidRat3Or4AidNum3mScore, " +
            "avg_upgrd_high_waist_aid_num_3m AS avgUpgrdHighWaistAidNum3m, " +
            "avg_upgrd_high_waist_aid_num_3m_aim AS avgUpgrdHighWaistAidNum3mAim, " +
            "avg_upgrd_high_waist_aid_num_3m_score AS avgUpgrdHighWaistAidNum3mScore, " +
            "new_aid_incm_com_rate AS newAidIncmComRate, " +
            "new_aid_incm_com_rate_aim AS newAidIncmComRateAim, " +
            "new_aid_incm_com_rate_score AS newAidIncmComRateScore, " +
            "guild_task_incm_lvl AS guildTaskIncmLvl, " +
            "guild_task_incm_lvl_aim AS guildTaskIncmLvlAim, " +
            "guild_task_incm_lvl_score AS guildTaskIncmLvlScore, " +
            "accum_incm_amt_diff AS accumIncmAmtDiff, " +
            "accum_incm_amt_aim AS accumIncmAmtAim, " +
            "incm_amt AS incmAmt, " +
            "upgrd_high_aid_num AS upgrdHighAidNum, " +
            "upgrd_waist_aid_num AS upgrdWaistAidNum, " +
            "accum_incm_amt_socre AS accumIncmAmtSocre " +
            "FROM yy_dm_entity_guild_cmp_health_analysis_di " +
            "WHERE dt = #{dt} AND guild_cmp_ownr_id IN " +
            "<foreach collection='uids' item='uid' open='(' separator=',' close=')'>" +
            "#{uid}" +
            "</foreach>" +
            "</script>")
    List<GuildRankTaskRequirementsAndResult> selectByDtAndUids(@Param("dt") Date dt, @Param("uids") Set<Long> uids);
}