package com.yy.yyzone.guildrank.db.gen.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import javax.annotation.Generated;

@Generated("yy_dm_entity_guild_cmp_health_analysis_di_tmp")
public class YyDmEntityGuildCmpHealthAnalysisDiTmpExample {
    /**
     * yy_dm_entity_guild_cmp_health_analysis_di_tmp
     */
    protected String orderByClause;

    /**
     * yy_dm_entity_guild_cmp_health_analysis_di_tmp
     */
    protected boolean distinct;

    /**
     * yy_dm_entity_guild_cmp_health_analysis_di_tmp
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public YyDmEntityGuildCmpHealthAnalysisDiTmpExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    /**
     * yy_dm_entity_guild_cmp_health_analysis_di_tmp null
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGuildCmpOwnrIdIsNull() {
            addCriterion("guild_cmp_ownr_id is null");
            return (Criteria) this;
        }

        public Criteria andGuildCmpOwnrIdIsNotNull() {
            addCriterion("guild_cmp_ownr_id is not null");
            return (Criteria) this;
        }

        public Criteria andGuildCmpOwnrIdEqualTo(Long value) {
            addCriterion("guild_cmp_ownr_id =", value, "guildCmpOwnrId");
            return (Criteria) this;
        }

        public Criteria andGuildCmpOwnrIdNotEqualTo(Long value) {
            addCriterion("guild_cmp_ownr_id <>", value, "guildCmpOwnrId");
            return (Criteria) this;
        }

        public Criteria andGuildCmpOwnrIdGreaterThan(Long value) {
            addCriterion("guild_cmp_ownr_id >", value, "guildCmpOwnrId");
            return (Criteria) this;
        }

        public Criteria andGuildCmpOwnrIdGreaterThanOrEqualTo(Long value) {
            addCriterion("guild_cmp_ownr_id >=", value, "guildCmpOwnrId");
            return (Criteria) this;
        }

        public Criteria andGuildCmpOwnrIdLessThan(Long value) {
            addCriterion("guild_cmp_ownr_id <", value, "guildCmpOwnrId");
            return (Criteria) this;
        }

        public Criteria andGuildCmpOwnrIdLessThanOrEqualTo(Long value) {
            addCriterion("guild_cmp_ownr_id <=", value, "guildCmpOwnrId");
            return (Criteria) this;
        }

        public Criteria andGuildCmpOwnrIdIn(List<Long> values) {
            addCriterion("guild_cmp_ownr_id in", values, "guildCmpOwnrId");
            return (Criteria) this;
        }

        public Criteria andGuildCmpOwnrIdNotIn(List<Long> values) {
            addCriterion("guild_cmp_ownr_id not in", values, "guildCmpOwnrId");
            return (Criteria) this;
        }

        public Criteria andGuildCmpOwnrIdBetween(Long value1, Long value2) {
            addCriterion("guild_cmp_ownr_id between", value1, value2, "guildCmpOwnrId");
            return (Criteria) this;
        }

        public Criteria andGuildCmpOwnrIdNotBetween(Long value1, Long value2) {
            addCriterion("guild_cmp_ownr_id not between", value1, value2, "guildCmpOwnrId");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumIsNull() {
            addCriterion("new_aid_rat_3_4_aid_num is null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumIsNotNull() {
            addCriterion("new_aid_rat_3_4_aid_num is not null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumEqualTo(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num =", value, "newAidRat34AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumNotEqualTo(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num <>", value, "newAidRat34AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumGreaterThan(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num >", value, "newAidRat34AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumGreaterThanOrEqualTo(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num >=", value, "newAidRat34AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumLessThan(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num <", value, "newAidRat34AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumLessThanOrEqualTo(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num <=", value, "newAidRat34AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumIn(List<Long> values) {
            addCriterion("new_aid_rat_3_4_aid_num in", values, "newAidRat34AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumNotIn(List<Long> values) {
            addCriterion("new_aid_rat_3_4_aid_num not in", values, "newAidRat34AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumBetween(Long value1, Long value2) {
            addCriterion("new_aid_rat_3_4_aid_num between", value1, value2, "newAidRat34AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumNotBetween(Long value1, Long value2) {
            addCriterion("new_aid_rat_3_4_aid_num not between", value1, value2, "newAidRat34AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnIsNull() {
            addCriterion("new_aid_rat_3_4_aid_num_rn is null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnIsNotNull() {
            addCriterion("new_aid_rat_3_4_aid_num_rn is not null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnEqualTo(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_rn =", value, "newAidRat34AidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnNotEqualTo(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_rn <>", value, "newAidRat34AidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnGreaterThan(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_rn >", value, "newAidRat34AidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnGreaterThanOrEqualTo(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_rn >=", value, "newAidRat34AidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnLessThan(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_rn <", value, "newAidRat34AidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnLessThanOrEqualTo(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_rn <=", value, "newAidRat34AidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnIn(List<Long> values) {
            addCriterion("new_aid_rat_3_4_aid_num_rn in", values, "newAidRat34AidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnNotIn(List<Long> values) {
            addCriterion("new_aid_rat_3_4_aid_num_rn not in", values, "newAidRat34AidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnBetween(Long value1, Long value2) {
            addCriterion("new_aid_rat_3_4_aid_num_rn between", value1, value2, "newAidRat34AidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnNotBetween(Long value1, Long value2) {
            addCriterion("new_aid_rat_3_4_aid_num_rn not between", value1, value2, "newAidRat34AidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffIsNull() {
            addCriterion("new_aid_rat_3_4_aid_num_diff is null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffIsNotNull() {
            addCriterion("new_aid_rat_3_4_aid_num_diff is not null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffEqualTo(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff =", value, "newAidRat34AidNumDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffNotEqualTo(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff <>", value, "newAidRat34AidNumDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffGreaterThan(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff >", value, "newAidRat34AidNumDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffGreaterThanOrEqualTo(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff >=", value, "newAidRat34AidNumDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffLessThan(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff <", value, "newAidRat34AidNumDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffLessThanOrEqualTo(Long value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff <=", value, "newAidRat34AidNumDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffIn(List<Long> values) {
            addCriterion("new_aid_rat_3_4_aid_num_diff in", values, "newAidRat34AidNumDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffNotIn(List<Long> values) {
            addCriterion("new_aid_rat_3_4_aid_num_diff not in", values, "newAidRat34AidNumDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffBetween(Long value1, Long value2) {
            addCriterion("new_aid_rat_3_4_aid_num_diff between", value1, value2, "newAidRat34AidNumDiff");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffNotBetween(Long value1, Long value2) {
            addCriterion("new_aid_rat_3_4_aid_num_diff not between", value1, value2, "newAidRat34AidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumIsNull() {
            addCriterion("upgrd_high_aid_num is null");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumIsNotNull() {
            addCriterion("upgrd_high_aid_num is not null");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumEqualTo(Long value) {
            addCriterion("upgrd_high_aid_num =", value, "upgrdHighAidNum");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumNotEqualTo(Long value) {
            addCriterion("upgrd_high_aid_num <>", value, "upgrdHighAidNum");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumGreaterThan(Long value) {
            addCriterion("upgrd_high_aid_num >", value, "upgrdHighAidNum");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumGreaterThanOrEqualTo(Long value) {
            addCriterion("upgrd_high_aid_num >=", value, "upgrdHighAidNum");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumLessThan(Long value) {
            addCriterion("upgrd_high_aid_num <", value, "upgrdHighAidNum");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumLessThanOrEqualTo(Long value) {
            addCriterion("upgrd_high_aid_num <=", value, "upgrdHighAidNum");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumIn(List<Long> values) {
            addCriterion("upgrd_high_aid_num in", values, "upgrdHighAidNum");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumNotIn(List<Long> values) {
            addCriterion("upgrd_high_aid_num not in", values, "upgrdHighAidNum");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumBetween(Long value1, Long value2) {
            addCriterion("upgrd_high_aid_num between", value1, value2, "upgrdHighAidNum");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumNotBetween(Long value1, Long value2) {
            addCriterion("upgrd_high_aid_num not between", value1, value2, "upgrdHighAidNum");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffIsNull() {
            addCriterion("upgrd_high_aid_num_diff is null");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffIsNotNull() {
            addCriterion("upgrd_high_aid_num_diff is not null");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffEqualTo(Long value) {
            addCriterion("upgrd_high_aid_num_diff =", value, "upgrdHighAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffNotEqualTo(Long value) {
            addCriterion("upgrd_high_aid_num_diff <>", value, "upgrdHighAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffGreaterThan(Long value) {
            addCriterion("upgrd_high_aid_num_diff >", value, "upgrdHighAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffGreaterThanOrEqualTo(Long value) {
            addCriterion("upgrd_high_aid_num_diff >=", value, "upgrdHighAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffLessThan(Long value) {
            addCriterion("upgrd_high_aid_num_diff <", value, "upgrdHighAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffLessThanOrEqualTo(Long value) {
            addCriterion("upgrd_high_aid_num_diff <=", value, "upgrdHighAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffIn(List<Long> values) {
            addCriterion("upgrd_high_aid_num_diff in", values, "upgrdHighAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffNotIn(List<Long> values) {
            addCriterion("upgrd_high_aid_num_diff not in", values, "upgrdHighAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffBetween(Long value1, Long value2) {
            addCriterion("upgrd_high_aid_num_diff between", value1, value2, "upgrdHighAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffNotBetween(Long value1, Long value2) {
            addCriterion("upgrd_high_aid_num_diff not between", value1, value2, "upgrdHighAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumIsNull() {
            addCriterion("upgrd_waist_aid_num is null");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumIsNotNull() {
            addCriterion("upgrd_waist_aid_num is not null");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumEqualTo(Long value) {
            addCriterion("upgrd_waist_aid_num =", value, "upgrdWaistAidNum");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumNotEqualTo(Long value) {
            addCriterion("upgrd_waist_aid_num <>", value, "upgrdWaistAidNum");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumGreaterThan(Long value) {
            addCriterion("upgrd_waist_aid_num >", value, "upgrdWaistAidNum");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumGreaterThanOrEqualTo(Long value) {
            addCriterion("upgrd_waist_aid_num >=", value, "upgrdWaistAidNum");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumLessThan(Long value) {
            addCriterion("upgrd_waist_aid_num <", value, "upgrdWaistAidNum");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumLessThanOrEqualTo(Long value) {
            addCriterion("upgrd_waist_aid_num <=", value, "upgrdWaistAidNum");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumIn(List<Long> values) {
            addCriterion("upgrd_waist_aid_num in", values, "upgrdWaistAidNum");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumNotIn(List<Long> values) {
            addCriterion("upgrd_waist_aid_num not in", values, "upgrdWaistAidNum");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumBetween(Long value1, Long value2) {
            addCriterion("upgrd_waist_aid_num between", value1, value2, "upgrdWaistAidNum");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumNotBetween(Long value1, Long value2) {
            addCriterion("upgrd_waist_aid_num not between", value1, value2, "upgrdWaistAidNum");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffIsNull() {
            addCriterion("upgrd_waist_aid_num_diff is null");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffIsNotNull() {
            addCriterion("upgrd_waist_aid_num_diff is not null");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffEqualTo(Long value) {
            addCriterion("upgrd_waist_aid_num_diff =", value, "upgrdWaistAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffNotEqualTo(Long value) {
            addCriterion("upgrd_waist_aid_num_diff <>", value, "upgrdWaistAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffGreaterThan(Long value) {
            addCriterion("upgrd_waist_aid_num_diff >", value, "upgrdWaistAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffGreaterThanOrEqualTo(Long value) {
            addCriterion("upgrd_waist_aid_num_diff >=", value, "upgrdWaistAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffLessThan(Long value) {
            addCriterion("upgrd_waist_aid_num_diff <", value, "upgrdWaistAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffLessThanOrEqualTo(Long value) {
            addCriterion("upgrd_waist_aid_num_diff <=", value, "upgrdWaistAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffIn(List<Long> values) {
            addCriterion("upgrd_waist_aid_num_diff in", values, "upgrdWaistAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffNotIn(List<Long> values) {
            addCriterion("upgrd_waist_aid_num_diff not in", values, "upgrdWaistAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffBetween(Long value1, Long value2) {
            addCriterion("upgrd_waist_aid_num_diff between", value1, value2, "upgrdWaistAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffNotBetween(Long value1, Long value2) {
            addCriterion("upgrd_waist_aid_num_diff not between", value1, value2, "upgrdWaistAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumIsNull() {
            addCriterion("avg_high_valid_live_aid_num is null");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumIsNotNull() {
            addCriterion("avg_high_valid_live_aid_num is not null");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumEqualTo(Double value) {
            addCriterion("avg_high_valid_live_aid_num =", value, "avgHighValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumNotEqualTo(Double value) {
            addCriterion("avg_high_valid_live_aid_num <>", value, "avgHighValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumGreaterThan(Double value) {
            addCriterion("avg_high_valid_live_aid_num >", value, "avgHighValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumGreaterThanOrEqualTo(Double value) {
            addCriterion("avg_high_valid_live_aid_num >=", value, "avgHighValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumLessThan(Double value) {
            addCriterion("avg_high_valid_live_aid_num <", value, "avgHighValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumLessThanOrEqualTo(Double value) {
            addCriterion("avg_high_valid_live_aid_num <=", value, "avgHighValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumIn(List<Double> values) {
            addCriterion("avg_high_valid_live_aid_num in", values, "avgHighValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumNotIn(List<Double> values) {
            addCriterion("avg_high_valid_live_aid_num not in", values, "avgHighValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumBetween(Double value1, Double value2) {
            addCriterion("avg_high_valid_live_aid_num between", value1, value2, "avgHighValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumNotBetween(Double value1, Double value2) {
            addCriterion("avg_high_valid_live_aid_num not between", value1, value2, "avgHighValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnIsNull() {
            addCriterion("avg_high_valid_live_aid_num_rn is null");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnIsNotNull() {
            addCriterion("avg_high_valid_live_aid_num_rn is not null");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnEqualTo(Long value) {
            addCriterion("avg_high_valid_live_aid_num_rn =", value, "avgHighValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnNotEqualTo(Long value) {
            addCriterion("avg_high_valid_live_aid_num_rn <>", value, "avgHighValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnGreaterThan(Long value) {
            addCriterion("avg_high_valid_live_aid_num_rn >", value, "avgHighValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnGreaterThanOrEqualTo(Long value) {
            addCriterion("avg_high_valid_live_aid_num_rn >=", value, "avgHighValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnLessThan(Long value) {
            addCriterion("avg_high_valid_live_aid_num_rn <", value, "avgHighValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnLessThanOrEqualTo(Long value) {
            addCriterion("avg_high_valid_live_aid_num_rn <=", value, "avgHighValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnIn(List<Long> values) {
            addCriterion("avg_high_valid_live_aid_num_rn in", values, "avgHighValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnNotIn(List<Long> values) {
            addCriterion("avg_high_valid_live_aid_num_rn not in", values, "avgHighValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnBetween(Long value1, Long value2) {
            addCriterion("avg_high_valid_live_aid_num_rn between", value1, value2, "avgHighValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnNotBetween(Long value1, Long value2) {
            addCriterion("avg_high_valid_live_aid_num_rn not between", value1, value2, "avgHighValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffIsNull() {
            addCriterion("avg_high_valid_live_aid_num_diff is null");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffIsNotNull() {
            addCriterion("avg_high_valid_live_aid_num_diff is not null");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffEqualTo(Double value) {
            addCriterion("avg_high_valid_live_aid_num_diff =", value, "avgHighValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffNotEqualTo(Double value) {
            addCriterion("avg_high_valid_live_aid_num_diff <>", value, "avgHighValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffGreaterThan(Double value) {
            addCriterion("avg_high_valid_live_aid_num_diff >", value, "avgHighValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffGreaterThanOrEqualTo(Double value) {
            addCriterion("avg_high_valid_live_aid_num_diff >=", value, "avgHighValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffLessThan(Double value) {
            addCriterion("avg_high_valid_live_aid_num_diff <", value, "avgHighValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffLessThanOrEqualTo(Double value) {
            addCriterion("avg_high_valid_live_aid_num_diff <=", value, "avgHighValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffIn(List<Double> values) {
            addCriterion("avg_high_valid_live_aid_num_diff in", values, "avgHighValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffNotIn(List<Double> values) {
            addCriterion("avg_high_valid_live_aid_num_diff not in", values, "avgHighValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffBetween(Double value1, Double value2) {
            addCriterion("avg_high_valid_live_aid_num_diff between", value1, value2, "avgHighValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffNotBetween(Double value1, Double value2) {
            addCriterion("avg_high_valid_live_aid_num_diff not between", value1, value2, "avgHighValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumIsNull() {
            addCriterion("avg_waist_valid_live_aid_num is null");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumIsNotNull() {
            addCriterion("avg_waist_valid_live_aid_num is not null");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumEqualTo(Double value) {
            addCriterion("avg_waist_valid_live_aid_num =", value, "avgWaistValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumNotEqualTo(Double value) {
            addCriterion("avg_waist_valid_live_aid_num <>", value, "avgWaistValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumGreaterThan(Double value) {
            addCriterion("avg_waist_valid_live_aid_num >", value, "avgWaistValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumGreaterThanOrEqualTo(Double value) {
            addCriterion("avg_waist_valid_live_aid_num >=", value, "avgWaistValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumLessThan(Double value) {
            addCriterion("avg_waist_valid_live_aid_num <", value, "avgWaistValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumLessThanOrEqualTo(Double value) {
            addCriterion("avg_waist_valid_live_aid_num <=", value, "avgWaistValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumIn(List<Double> values) {
            addCriterion("avg_waist_valid_live_aid_num in", values, "avgWaistValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumNotIn(List<Double> values) {
            addCriterion("avg_waist_valid_live_aid_num not in", values, "avgWaistValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumBetween(Double value1, Double value2) {
            addCriterion("avg_waist_valid_live_aid_num between", value1, value2, "avgWaistValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumNotBetween(Double value1, Double value2) {
            addCriterion("avg_waist_valid_live_aid_num not between", value1, value2, "avgWaistValidLiveAidNum");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnIsNull() {
            addCriterion("avg_waist_valid_live_aid_num_rn is null");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnIsNotNull() {
            addCriterion("avg_waist_valid_live_aid_num_rn is not null");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnEqualTo(Long value) {
            addCriterion("avg_waist_valid_live_aid_num_rn =", value, "avgWaistValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnNotEqualTo(Long value) {
            addCriterion("avg_waist_valid_live_aid_num_rn <>", value, "avgWaistValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnGreaterThan(Long value) {
            addCriterion("avg_waist_valid_live_aid_num_rn >", value, "avgWaistValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnGreaterThanOrEqualTo(Long value) {
            addCriterion("avg_waist_valid_live_aid_num_rn >=", value, "avgWaistValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnLessThan(Long value) {
            addCriterion("avg_waist_valid_live_aid_num_rn <", value, "avgWaistValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnLessThanOrEqualTo(Long value) {
            addCriterion("avg_waist_valid_live_aid_num_rn <=", value, "avgWaistValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnIn(List<Long> values) {
            addCriterion("avg_waist_valid_live_aid_num_rn in", values, "avgWaistValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnNotIn(List<Long> values) {
            addCriterion("avg_waist_valid_live_aid_num_rn not in", values, "avgWaistValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnBetween(Long value1, Long value2) {
            addCriterion("avg_waist_valid_live_aid_num_rn between", value1, value2, "avgWaistValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnNotBetween(Long value1, Long value2) {
            addCriterion("avg_waist_valid_live_aid_num_rn not between", value1, value2, "avgWaistValidLiveAidNumRn");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffIsNull() {
            addCriterion("avg_waist_valid_live_aid_num_diff is null");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffIsNotNull() {
            addCriterion("avg_waist_valid_live_aid_num_diff is not null");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffEqualTo(Double value) {
            addCriterion("avg_waist_valid_live_aid_num_diff =", value, "avgWaistValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffNotEqualTo(Double value) {
            addCriterion("avg_waist_valid_live_aid_num_diff <>", value, "avgWaistValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffGreaterThan(Double value) {
            addCriterion("avg_waist_valid_live_aid_num_diff >", value, "avgWaistValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffGreaterThanOrEqualTo(Double value) {
            addCriterion("avg_waist_valid_live_aid_num_diff >=", value, "avgWaistValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffLessThan(Double value) {
            addCriterion("avg_waist_valid_live_aid_num_diff <", value, "avgWaistValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffLessThanOrEqualTo(Double value) {
            addCriterion("avg_waist_valid_live_aid_num_diff <=", value, "avgWaistValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffIn(List<Double> values) {
            addCriterion("avg_waist_valid_live_aid_num_diff in", values, "avgWaistValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffNotIn(List<Double> values) {
            addCriterion("avg_waist_valid_live_aid_num_diff not in", values, "avgWaistValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffBetween(Double value1, Double value2) {
            addCriterion("avg_waist_valid_live_aid_num_diff between", value1, value2, "avgWaistValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffNotBetween(Double value1, Double value2) {
            addCriterion("avg_waist_valid_live_aid_num_diff not between", value1, value2, "avgWaistValidLiveAidNumDiff");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateIsNull() {
            addCriterion("avg_nto_valid_live_aid_rate is null");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateIsNotNull() {
            addCriterion("avg_nto_valid_live_aid_rate is not null");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateEqualTo(Double value) {
            addCriterion("avg_nto_valid_live_aid_rate =", value, "avgNtoValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateNotEqualTo(Double value) {
            addCriterion("avg_nto_valid_live_aid_rate <>", value, "avgNtoValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateGreaterThan(Double value) {
            addCriterion("avg_nto_valid_live_aid_rate >", value, "avgNtoValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateGreaterThanOrEqualTo(Double value) {
            addCriterion("avg_nto_valid_live_aid_rate >=", value, "avgNtoValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateLessThan(Double value) {
            addCriterion("avg_nto_valid_live_aid_rate <", value, "avgNtoValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateLessThanOrEqualTo(Double value) {
            addCriterion("avg_nto_valid_live_aid_rate <=", value, "avgNtoValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateIn(List<Double> values) {
            addCriterion("avg_nto_valid_live_aid_rate in", values, "avgNtoValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateNotIn(List<Double> values) {
            addCriterion("avg_nto_valid_live_aid_rate not in", values, "avgNtoValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateBetween(Double value1, Double value2) {
            addCriterion("avg_nto_valid_live_aid_rate between", value1, value2, "avgNtoValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateNotBetween(Double value1, Double value2) {
            addCriterion("avg_nto_valid_live_aid_rate not between", value1, value2, "avgNtoValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnIsNull() {
            addCriterion("avg_nto_valid_live_aid_rate_rn is null");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnIsNotNull() {
            addCriterion("avg_nto_valid_live_aid_rate_rn is not null");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnEqualTo(Long value) {
            addCriterion("avg_nto_valid_live_aid_rate_rn =", value, "avgNtoValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnNotEqualTo(Long value) {
            addCriterion("avg_nto_valid_live_aid_rate_rn <>", value, "avgNtoValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnGreaterThan(Long value) {
            addCriterion("avg_nto_valid_live_aid_rate_rn >", value, "avgNtoValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnGreaterThanOrEqualTo(Long value) {
            addCriterion("avg_nto_valid_live_aid_rate_rn >=", value, "avgNtoValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnLessThan(Long value) {
            addCriterion("avg_nto_valid_live_aid_rate_rn <", value, "avgNtoValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnLessThanOrEqualTo(Long value) {
            addCriterion("avg_nto_valid_live_aid_rate_rn <=", value, "avgNtoValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnIn(List<Long> values) {
            addCriterion("avg_nto_valid_live_aid_rate_rn in", values, "avgNtoValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnNotIn(List<Long> values) {
            addCriterion("avg_nto_valid_live_aid_rate_rn not in", values, "avgNtoValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnBetween(Long value1, Long value2) {
            addCriterion("avg_nto_valid_live_aid_rate_rn between", value1, value2, "avgNtoValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnNotBetween(Long value1, Long value2) {
            addCriterion("avg_nto_valid_live_aid_rate_rn not between", value1, value2, "avgNtoValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateIsNull() {
            addCriterion("avg_stock_valid_live_aid_rate is null");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateIsNotNull() {
            addCriterion("avg_stock_valid_live_aid_rate is not null");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateEqualTo(Double value) {
            addCriterion("avg_stock_valid_live_aid_rate =", value, "avgStockValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateNotEqualTo(Double value) {
            addCriterion("avg_stock_valid_live_aid_rate <>", value, "avgStockValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateGreaterThan(Double value) {
            addCriterion("avg_stock_valid_live_aid_rate >", value, "avgStockValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateGreaterThanOrEqualTo(Double value) {
            addCriterion("avg_stock_valid_live_aid_rate >=", value, "avgStockValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateLessThan(Double value) {
            addCriterion("avg_stock_valid_live_aid_rate <", value, "avgStockValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateLessThanOrEqualTo(Double value) {
            addCriterion("avg_stock_valid_live_aid_rate <=", value, "avgStockValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateIn(List<Double> values) {
            addCriterion("avg_stock_valid_live_aid_rate in", values, "avgStockValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateNotIn(List<Double> values) {
            addCriterion("avg_stock_valid_live_aid_rate not in", values, "avgStockValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateBetween(Double value1, Double value2) {
            addCriterion("avg_stock_valid_live_aid_rate between", value1, value2, "avgStockValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateNotBetween(Double value1, Double value2) {
            addCriterion("avg_stock_valid_live_aid_rate not between", value1, value2, "avgStockValidLiveAidRate");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnIsNull() {
            addCriterion("avg_stock_valid_live_aid_rate_rn is null");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnIsNotNull() {
            addCriterion("avg_stock_valid_live_aid_rate_rn is not null");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnEqualTo(Long value) {
            addCriterion("avg_stock_valid_live_aid_rate_rn =", value, "avgStockValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnNotEqualTo(Long value) {
            addCriterion("avg_stock_valid_live_aid_rate_rn <>", value, "avgStockValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnGreaterThan(Long value) {
            addCriterion("avg_stock_valid_live_aid_rate_rn >", value, "avgStockValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnGreaterThanOrEqualTo(Long value) {
            addCriterion("avg_stock_valid_live_aid_rate_rn >=", value, "avgStockValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnLessThan(Long value) {
            addCriterion("avg_stock_valid_live_aid_rate_rn <", value, "avgStockValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnLessThanOrEqualTo(Long value) {
            addCriterion("avg_stock_valid_live_aid_rate_rn <=", value, "avgStockValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnIn(List<Long> values) {
            addCriterion("avg_stock_valid_live_aid_rate_rn in", values, "avgStockValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnNotIn(List<Long> values) {
            addCriterion("avg_stock_valid_live_aid_rate_rn not in", values, "avgStockValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnBetween(Long value1, Long value2) {
            addCriterion("avg_stock_valid_live_aid_rate_rn between", value1, value2, "avgStockValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnNotBetween(Long value1, Long value2) {
            addCriterion("avg_stock_valid_live_aid_rate_rn not between", value1, value2, "avgStockValidLiveAidRateRn");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumIsNull() {
            addCriterion("new_auth_golden_aid_num is null");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumIsNotNull() {
            addCriterion("new_auth_golden_aid_num is not null");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumEqualTo(Long value) {
            addCriterion("new_auth_golden_aid_num =", value, "newAuthGoldenAidNum");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumNotEqualTo(Long value) {
            addCriterion("new_auth_golden_aid_num <>", value, "newAuthGoldenAidNum");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumGreaterThan(Long value) {
            addCriterion("new_auth_golden_aid_num >", value, "newAuthGoldenAidNum");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumGreaterThanOrEqualTo(Long value) {
            addCriterion("new_auth_golden_aid_num >=", value, "newAuthGoldenAidNum");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumLessThan(Long value) {
            addCriterion("new_auth_golden_aid_num <", value, "newAuthGoldenAidNum");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumLessThanOrEqualTo(Long value) {
            addCriterion("new_auth_golden_aid_num <=", value, "newAuthGoldenAidNum");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumIn(List<Long> values) {
            addCriterion("new_auth_golden_aid_num in", values, "newAuthGoldenAidNum");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumNotIn(List<Long> values) {
            addCriterion("new_auth_golden_aid_num not in", values, "newAuthGoldenAidNum");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumBetween(Long value1, Long value2) {
            addCriterion("new_auth_golden_aid_num between", value1, value2, "newAuthGoldenAidNum");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumNotBetween(Long value1, Long value2) {
            addCriterion("new_auth_golden_aid_num not between", value1, value2, "newAuthGoldenAidNum");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnIsNull() {
            addCriterion("new_auth_golden_aid_num_rn is null");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnIsNotNull() {
            addCriterion("new_auth_golden_aid_num_rn is not null");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnEqualTo(Long value) {
            addCriterion("new_auth_golden_aid_num_rn =", value, "newAuthGoldenAidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnNotEqualTo(Long value) {
            addCriterion("new_auth_golden_aid_num_rn <>", value, "newAuthGoldenAidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnGreaterThan(Long value) {
            addCriterion("new_auth_golden_aid_num_rn >", value, "newAuthGoldenAidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnGreaterThanOrEqualTo(Long value) {
            addCriterion("new_auth_golden_aid_num_rn >=", value, "newAuthGoldenAidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnLessThan(Long value) {
            addCriterion("new_auth_golden_aid_num_rn <", value, "newAuthGoldenAidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnLessThanOrEqualTo(Long value) {
            addCriterion("new_auth_golden_aid_num_rn <=", value, "newAuthGoldenAidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnIn(List<Long> values) {
            addCriterion("new_auth_golden_aid_num_rn in", values, "newAuthGoldenAidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnNotIn(List<Long> values) {
            addCriterion("new_auth_golden_aid_num_rn not in", values, "newAuthGoldenAidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnBetween(Long value1, Long value2) {
            addCriterion("new_auth_golden_aid_num_rn between", value1, value2, "newAuthGoldenAidNumRn");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnNotBetween(Long value1, Long value2) {
            addCriterion("new_auth_golden_aid_num_rn not between", value1, value2, "newAuthGoldenAidNumRn");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumIsNull() {
            addCriterion("ext_golden_aid_num is null");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumIsNotNull() {
            addCriterion("ext_golden_aid_num is not null");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumEqualTo(Long value) {
            addCriterion("ext_golden_aid_num =", value, "extGoldenAidNum");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumNotEqualTo(Long value) {
            addCriterion("ext_golden_aid_num <>", value, "extGoldenAidNum");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumGreaterThan(Long value) {
            addCriterion("ext_golden_aid_num >", value, "extGoldenAidNum");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumGreaterThanOrEqualTo(Long value) {
            addCriterion("ext_golden_aid_num >=", value, "extGoldenAidNum");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumLessThan(Long value) {
            addCriterion("ext_golden_aid_num <", value, "extGoldenAidNum");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumLessThanOrEqualTo(Long value) {
            addCriterion("ext_golden_aid_num <=", value, "extGoldenAidNum");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumIn(List<Long> values) {
            addCriterion("ext_golden_aid_num in", values, "extGoldenAidNum");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumNotIn(List<Long> values) {
            addCriterion("ext_golden_aid_num not in", values, "extGoldenAidNum");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumBetween(Long value1, Long value2) {
            addCriterion("ext_golden_aid_num between", value1, value2, "extGoldenAidNum");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumNotBetween(Long value1, Long value2) {
            addCriterion("ext_golden_aid_num not between", value1, value2, "extGoldenAidNum");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtIsNull() {
            addCriterion("actv_prod_pay_amt is null");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtIsNotNull() {
            addCriterion("actv_prod_pay_amt is not null");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtEqualTo(Double value) {
            addCriterion("actv_prod_pay_amt =", value, "actvProdPayAmt");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtNotEqualTo(Double value) {
            addCriterion("actv_prod_pay_amt <>", value, "actvProdPayAmt");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtGreaterThan(Double value) {
            addCriterion("actv_prod_pay_amt >", value, "actvProdPayAmt");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtGreaterThanOrEqualTo(Double value) {
            addCriterion("actv_prod_pay_amt >=", value, "actvProdPayAmt");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtLessThan(Double value) {
            addCriterion("actv_prod_pay_amt <", value, "actvProdPayAmt");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtLessThanOrEqualTo(Double value) {
            addCriterion("actv_prod_pay_amt <=", value, "actvProdPayAmt");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtIn(List<Double> values) {
            addCriterion("actv_prod_pay_amt in", values, "actvProdPayAmt");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtNotIn(List<Double> values) {
            addCriterion("actv_prod_pay_amt not in", values, "actvProdPayAmt");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtBetween(Double value1, Double value2) {
            addCriterion("actv_prod_pay_amt between", value1, value2, "actvProdPayAmt");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtNotBetween(Double value1, Double value2) {
            addCriterion("actv_prod_pay_amt not between", value1, value2, "actvProdPayAmt");
            return (Criteria) this;
        }

        public Criteria andProdPayAmtIsNull() {
            addCriterion("prod_pay_amt is null");
            return (Criteria) this;
        }

        public Criteria andProdPayAmtIsNotNull() {
            addCriterion("prod_pay_amt is not null");
            return (Criteria) this;
        }

        public Criteria andProdPayAmtEqualTo(Double value) {
            addCriterion("prod_pay_amt =", value, "prodPayAmt");
            return (Criteria) this;
        }

        public Criteria andProdPayAmtNotEqualTo(Double value) {
            addCriterion("prod_pay_amt <>", value, "prodPayAmt");
            return (Criteria) this;
        }

        public Criteria andProdPayAmtGreaterThan(Double value) {
            addCriterion("prod_pay_amt >", value, "prodPayAmt");
            return (Criteria) this;
        }

        public Criteria andProdPayAmtGreaterThanOrEqualTo(Double value) {
            addCriterion("prod_pay_amt >=", value, "prodPayAmt");
            return (Criteria) this;
        }

        public Criteria andProdPayAmtLessThan(Double value) {
            addCriterion("prod_pay_amt <", value, "prodPayAmt");
            return (Criteria) this;
        }

        public Criteria andProdPayAmtLessThanOrEqualTo(Double value) {
            addCriterion("prod_pay_amt <=", value, "prodPayAmt");
            return (Criteria) this;
        }

        public Criteria andProdPayAmtIn(List<Double> values) {
            addCriterion("prod_pay_amt in", values, "prodPayAmt");
            return (Criteria) this;
        }

        public Criteria andProdPayAmtNotIn(List<Double> values) {
            addCriterion("prod_pay_amt not in", values, "prodPayAmt");
            return (Criteria) this;
        }

        public Criteria andProdPayAmtBetween(Double value1, Double value2) {
            addCriterion("prod_pay_amt between", value1, value2, "prodPayAmt");
            return (Criteria) this;
        }

        public Criteria andProdPayAmtNotBetween(Double value1, Double value2) {
            addCriterion("prod_pay_amt not between", value1, value2, "prodPayAmt");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateIsNull() {
            addCriterion("actv_prod_pay_amt_rate is null");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateIsNotNull() {
            addCriterion("actv_prod_pay_amt_rate is not null");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateEqualTo(Double value) {
            addCriterion("actv_prod_pay_amt_rate =", value, "actvProdPayAmtRate");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateNotEqualTo(Double value) {
            addCriterion("actv_prod_pay_amt_rate <>", value, "actvProdPayAmtRate");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateGreaterThan(Double value) {
            addCriterion("actv_prod_pay_amt_rate >", value, "actvProdPayAmtRate");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateGreaterThanOrEqualTo(Double value) {
            addCriterion("actv_prod_pay_amt_rate >=", value, "actvProdPayAmtRate");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateLessThan(Double value) {
            addCriterion("actv_prod_pay_amt_rate <", value, "actvProdPayAmtRate");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateLessThanOrEqualTo(Double value) {
            addCriterion("actv_prod_pay_amt_rate <=", value, "actvProdPayAmtRate");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateIn(List<Double> values) {
            addCriterion("actv_prod_pay_amt_rate in", values, "actvProdPayAmtRate");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateNotIn(List<Double> values) {
            addCriterion("actv_prod_pay_amt_rate not in", values, "actvProdPayAmtRate");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateBetween(Double value1, Double value2) {
            addCriterion("actv_prod_pay_amt_rate between", value1, value2, "actvProdPayAmtRate");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateNotBetween(Double value1, Double value2) {
            addCriterion("actv_prod_pay_amt_rate not between", value1, value2, "actvProdPayAmtRate");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtIsNull() {
            addCriterion("valid_live_new_aid_prod_pay_amt is null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtIsNotNull() {
            addCriterion("valid_live_new_aid_prod_pay_amt is not null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtEqualTo(Double value) {
            addCriterion("valid_live_new_aid_prod_pay_amt =", value, "validLiveNewAidProdPayAmt");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtNotEqualTo(Double value) {
            addCriterion("valid_live_new_aid_prod_pay_amt <>", value, "validLiveNewAidProdPayAmt");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtGreaterThan(Double value) {
            addCriterion("valid_live_new_aid_prod_pay_amt >", value, "validLiveNewAidProdPayAmt");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtGreaterThanOrEqualTo(Double value) {
            addCriterion("valid_live_new_aid_prod_pay_amt >=", value, "validLiveNewAidProdPayAmt");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtLessThan(Double value) {
            addCriterion("valid_live_new_aid_prod_pay_amt <", value, "validLiveNewAidProdPayAmt");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtLessThanOrEqualTo(Double value) {
            addCriterion("valid_live_new_aid_prod_pay_amt <=", value, "validLiveNewAidProdPayAmt");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtIn(List<Double> values) {
            addCriterion("valid_live_new_aid_prod_pay_amt in", values, "validLiveNewAidProdPayAmt");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtNotIn(List<Double> values) {
            addCriterion("valid_live_new_aid_prod_pay_amt not in", values, "validLiveNewAidProdPayAmt");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtBetween(Double value1, Double value2) {
            addCriterion("valid_live_new_aid_prod_pay_amt between", value1, value2, "validLiveNewAidProdPayAmt");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtNotBetween(Double value1, Double value2) {
            addCriterion("valid_live_new_aid_prod_pay_amt not between", value1, value2, "validLiveNewAidProdPayAmt");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtRnIsNull() {
            addCriterion("valid_live_new_aid_prod_pay_amt_rn is null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtRnIsNotNull() {
            addCriterion("valid_live_new_aid_prod_pay_amt_rn is not null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtRnEqualTo(Long value) {
            addCriterion("valid_live_new_aid_prod_pay_amt_rn =", value, "validLiveNewAidProdPayAmtRn");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtRnNotEqualTo(Long value) {
            addCriterion("valid_live_new_aid_prod_pay_amt_rn <>", value, "validLiveNewAidProdPayAmtRn");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtRnGreaterThan(Long value) {
            addCriterion("valid_live_new_aid_prod_pay_amt_rn >", value, "validLiveNewAidProdPayAmtRn");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtRnGreaterThanOrEqualTo(Long value) {
            addCriterion("valid_live_new_aid_prod_pay_amt_rn >=", value, "validLiveNewAidProdPayAmtRn");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtRnLessThan(Long value) {
            addCriterion("valid_live_new_aid_prod_pay_amt_rn <", value, "validLiveNewAidProdPayAmtRn");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtRnLessThanOrEqualTo(Long value) {
            addCriterion("valid_live_new_aid_prod_pay_amt_rn <=", value, "validLiveNewAidProdPayAmtRn");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtRnIn(List<Long> values) {
            addCriterion("valid_live_new_aid_prod_pay_amt_rn in", values, "validLiveNewAidProdPayAmtRn");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtRnNotIn(List<Long> values) {
            addCriterion("valid_live_new_aid_prod_pay_amt_rn not in", values, "validLiveNewAidProdPayAmtRn");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtRnBetween(Long value1, Long value2) {
            addCriterion("valid_live_new_aid_prod_pay_amt_rn between", value1, value2, "validLiveNewAidProdPayAmtRn");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtRnNotBetween(Long value1, Long value2) {
            addCriterion("valid_live_new_aid_prod_pay_amt_rn not between", value1, value2, "validLiveNewAidProdPayAmtRn");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtDiffIsNull() {
            addCriterion("valid_live_new_aid_prod_pay_amt_diff is null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtDiffIsNotNull() {
            addCriterion("valid_live_new_aid_prod_pay_amt_diff is not null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtDiffEqualTo(Double value) {
            addCriterion("valid_live_new_aid_prod_pay_amt_diff =", value, "validLiveNewAidProdPayAmtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtDiffNotEqualTo(Double value) {
            addCriterion("valid_live_new_aid_prod_pay_amt_diff <>", value, "validLiveNewAidProdPayAmtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtDiffGreaterThan(Double value) {
            addCriterion("valid_live_new_aid_prod_pay_amt_diff >", value, "validLiveNewAidProdPayAmtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtDiffGreaterThanOrEqualTo(Double value) {
            addCriterion("valid_live_new_aid_prod_pay_amt_diff >=", value, "validLiveNewAidProdPayAmtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtDiffLessThan(Double value) {
            addCriterion("valid_live_new_aid_prod_pay_amt_diff <", value, "validLiveNewAidProdPayAmtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtDiffLessThanOrEqualTo(Double value) {
            addCriterion("valid_live_new_aid_prod_pay_amt_diff <=", value, "validLiveNewAidProdPayAmtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtDiffIn(List<Double> values) {
            addCriterion("valid_live_new_aid_prod_pay_amt_diff in", values, "validLiveNewAidProdPayAmtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtDiffNotIn(List<Double> values) {
            addCriterion("valid_live_new_aid_prod_pay_amt_diff not in", values, "validLiveNewAidProdPayAmtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtDiffBetween(Double value1, Double value2) {
            addCriterion("valid_live_new_aid_prod_pay_amt_diff between", value1, value2, "validLiveNewAidProdPayAmtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtDiffNotBetween(Double value1, Double value2) {
            addCriterion("valid_live_new_aid_prod_pay_amt_diff not between", value1, value2, "validLiveNewAidProdPayAmtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtIsNull() {
            addCriterion("unvalid_live_new_aid_prod_pay_amt is null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtIsNotNull() {
            addCriterion("unvalid_live_new_aid_prod_pay_amt is not null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt =", value, "unvalidLiveNewAidProdPayAmt");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtNotEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt <>", value, "unvalidLiveNewAidProdPayAmt");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtGreaterThan(Double value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt >", value, "unvalidLiveNewAidProdPayAmt");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtGreaterThanOrEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt >=", value, "unvalidLiveNewAidProdPayAmt");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtLessThan(Double value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt <", value, "unvalidLiveNewAidProdPayAmt");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtLessThanOrEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt <=", value, "unvalidLiveNewAidProdPayAmt");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtIn(List<Double> values) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt in", values, "unvalidLiveNewAidProdPayAmt");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtNotIn(List<Double> values) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt not in", values, "unvalidLiveNewAidProdPayAmt");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtBetween(Double value1, Double value2) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt between", value1, value2, "unvalidLiveNewAidProdPayAmt");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtNotBetween(Double value1, Double value2) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt not between", value1, value2, "unvalidLiveNewAidProdPayAmt");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRnIsNull() {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rn is null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRnIsNotNull() {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rn is not null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRnEqualTo(Long value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rn =", value, "unvalidLiveNewAidProdPayAmtRn");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRnNotEqualTo(Long value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rn <>", value, "unvalidLiveNewAidProdPayAmtRn");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRnGreaterThan(Long value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rn >", value, "unvalidLiveNewAidProdPayAmtRn");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRnGreaterThanOrEqualTo(Long value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rn >=", value, "unvalidLiveNewAidProdPayAmtRn");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRnLessThan(Long value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rn <", value, "unvalidLiveNewAidProdPayAmtRn");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRnLessThanOrEqualTo(Long value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rn <=", value, "unvalidLiveNewAidProdPayAmtRn");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRnIn(List<Long> values) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rn in", values, "unvalidLiveNewAidProdPayAmtRn");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRnNotIn(List<Long> values) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rn not in", values, "unvalidLiveNewAidProdPayAmtRn");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRnBetween(Long value1, Long value2) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rn between", value1, value2, "unvalidLiveNewAidProdPayAmtRn");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRnNotBetween(Long value1, Long value2) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rn not between", value1, value2, "unvalidLiveNewAidProdPayAmtRn");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtDiffIsNull() {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_diff is null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtDiffIsNotNull() {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_diff is not null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtDiffEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_diff =", value, "unvalidLiveNewAidProdPayAmtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtDiffNotEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_diff <>", value, "unvalidLiveNewAidProdPayAmtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtDiffGreaterThan(Double value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_diff >", value, "unvalidLiveNewAidProdPayAmtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtDiffGreaterThanOrEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_diff >=", value, "unvalidLiveNewAidProdPayAmtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtDiffLessThan(Double value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_diff <", value, "unvalidLiveNewAidProdPayAmtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtDiffLessThanOrEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_diff <=", value, "unvalidLiveNewAidProdPayAmtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtDiffIn(List<Double> values) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_diff in", values, "unvalidLiveNewAidProdPayAmtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtDiffNotIn(List<Double> values) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_diff not in", values, "unvalidLiveNewAidProdPayAmtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtDiffBetween(Double value1, Double value2) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_diff between", value1, value2, "unvalidLiveNewAidProdPayAmtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtDiffNotBetween(Double value1, Double value2) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_diff not between", value1, value2, "unvalidLiveNewAidProdPayAmtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateIsNull() {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate is null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateIsNotNull() {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate is not null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate =", value, "unvalidLiveNewAidProdPayAmtRate");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateNotEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate <>", value, "unvalidLiveNewAidProdPayAmtRate");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateGreaterThan(Double value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate >", value, "unvalidLiveNewAidProdPayAmtRate");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateGreaterThanOrEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate >=", value, "unvalidLiveNewAidProdPayAmtRate");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateLessThan(Double value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate <", value, "unvalidLiveNewAidProdPayAmtRate");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateLessThanOrEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate <=", value, "unvalidLiveNewAidProdPayAmtRate");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateIn(List<Double> values) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate in", values, "unvalidLiveNewAidProdPayAmtRate");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateNotIn(List<Double> values) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate not in", values, "unvalidLiveNewAidProdPayAmtRate");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateBetween(Double value1, Double value2) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate between", value1, value2, "unvalidLiveNewAidProdPayAmtRate");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateNotBetween(Double value1, Double value2) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate not between", value1, value2, "unvalidLiveNewAidProdPayAmtRate");
            return (Criteria) this;
        }

        public Criteria andTotalUnvalidLiveNewAidProdPayAmtRateIsNull() {
            addCriterion("total_unvalid_live_new_aid_prod_pay_amt_rate is null");
            return (Criteria) this;
        }

        public Criteria andTotalUnvalidLiveNewAidProdPayAmtRateIsNotNull() {
            addCriterion("total_unvalid_live_new_aid_prod_pay_amt_rate is not null");
            return (Criteria) this;
        }

        public Criteria andTotalUnvalidLiveNewAidProdPayAmtRateEqualTo(Double value) {
            addCriterion("total_unvalid_live_new_aid_prod_pay_amt_rate =", value, "totalUnvalidLiveNewAidProdPayAmtRate");
            return (Criteria) this;
        }

        public Criteria andTotalUnvalidLiveNewAidProdPayAmtRateNotEqualTo(Double value) {
            addCriterion("total_unvalid_live_new_aid_prod_pay_amt_rate <>", value, "totalUnvalidLiveNewAidProdPayAmtRate");
            return (Criteria) this;
        }

        public Criteria andTotalUnvalidLiveNewAidProdPayAmtRateGreaterThan(Double value) {
            addCriterion("total_unvalid_live_new_aid_prod_pay_amt_rate >", value, "totalUnvalidLiveNewAidProdPayAmtRate");
            return (Criteria) this;
        }

        public Criteria andTotalUnvalidLiveNewAidProdPayAmtRateGreaterThanOrEqualTo(Double value) {
            addCriterion("total_unvalid_live_new_aid_prod_pay_amt_rate >=", value, "totalUnvalidLiveNewAidProdPayAmtRate");
            return (Criteria) this;
        }

        public Criteria andTotalUnvalidLiveNewAidProdPayAmtRateLessThan(Double value) {
            addCriterion("total_unvalid_live_new_aid_prod_pay_amt_rate <", value, "totalUnvalidLiveNewAidProdPayAmtRate");
            return (Criteria) this;
        }

        public Criteria andTotalUnvalidLiveNewAidProdPayAmtRateLessThanOrEqualTo(Double value) {
            addCriterion("total_unvalid_live_new_aid_prod_pay_amt_rate <=", value, "totalUnvalidLiveNewAidProdPayAmtRate");
            return (Criteria) this;
        }

        public Criteria andTotalUnvalidLiveNewAidProdPayAmtRateIn(List<Double> values) {
            addCriterion("total_unvalid_live_new_aid_prod_pay_amt_rate in", values, "totalUnvalidLiveNewAidProdPayAmtRate");
            return (Criteria) this;
        }

        public Criteria andTotalUnvalidLiveNewAidProdPayAmtRateNotIn(List<Double> values) {
            addCriterion("total_unvalid_live_new_aid_prod_pay_amt_rate not in", values, "totalUnvalidLiveNewAidProdPayAmtRate");
            return (Criteria) this;
        }

        public Criteria andTotalUnvalidLiveNewAidProdPayAmtRateBetween(Double value1, Double value2) {
            addCriterion("total_unvalid_live_new_aid_prod_pay_amt_rate between", value1, value2, "totalUnvalidLiveNewAidProdPayAmtRate");
            return (Criteria) this;
        }

        public Criteria andTotalUnvalidLiveNewAidProdPayAmtRateNotBetween(Double value1, Double value2) {
            addCriterion("total_unvalid_live_new_aid_prod_pay_amt_rate not between", value1, value2, "totalUnvalidLiveNewAidProdPayAmtRate");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateDiffIsNull() {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate_diff is null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateDiffIsNotNull() {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate_diff is not null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateDiffEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate_diff =", value, "unvalidLiveNewAidProdPayAmtRateDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateDiffNotEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate_diff <>", value, "unvalidLiveNewAidProdPayAmtRateDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateDiffGreaterThan(Double value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate_diff >", value, "unvalidLiveNewAidProdPayAmtRateDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateDiffGreaterThanOrEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate_diff >=", value, "unvalidLiveNewAidProdPayAmtRateDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateDiffLessThan(Double value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate_diff <", value, "unvalidLiveNewAidProdPayAmtRateDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateDiffLessThanOrEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate_diff <=", value, "unvalidLiveNewAidProdPayAmtRateDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateDiffIn(List<Double> values) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate_diff in", values, "unvalidLiveNewAidProdPayAmtRateDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateDiffNotIn(List<Double> values) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate_diff not in", values, "unvalidLiveNewAidProdPayAmtRateDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateDiffBetween(Double value1, Double value2) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate_diff between", value1, value2, "unvalidLiveNewAidProdPayAmtRateDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateDiffNotBetween(Double value1, Double value2) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate_diff not between", value1, value2, "unvalidLiveNewAidProdPayAmtRateDiff");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtIsNull() {
            addCriterion("actv_incm_amt is null");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtIsNotNull() {
            addCriterion("actv_incm_amt is not null");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtEqualTo(Double value) {
            addCriterion("actv_incm_amt =", value, "actvIncmAmt");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtNotEqualTo(Double value) {
            addCriterion("actv_incm_amt <>", value, "actvIncmAmt");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtGreaterThan(Double value) {
            addCriterion("actv_incm_amt >", value, "actvIncmAmt");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtGreaterThanOrEqualTo(Double value) {
            addCriterion("actv_incm_amt >=", value, "actvIncmAmt");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtLessThan(Double value) {
            addCriterion("actv_incm_amt <", value, "actvIncmAmt");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtLessThanOrEqualTo(Double value) {
            addCriterion("actv_incm_amt <=", value, "actvIncmAmt");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtIn(List<Double> values) {
            addCriterion("actv_incm_amt in", values, "actvIncmAmt");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtNotIn(List<Double> values) {
            addCriterion("actv_incm_amt not in", values, "actvIncmAmt");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtBetween(Double value1, Double value2) {
            addCriterion("actv_incm_amt between", value1, value2, "actvIncmAmt");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtNotBetween(Double value1, Double value2) {
            addCriterion("actv_incm_amt not between", value1, value2, "actvIncmAmt");
            return (Criteria) this;
        }

        public Criteria andIncmAmtIsNull() {
            addCriterion("incm_amt is null");
            return (Criteria) this;
        }

        public Criteria andIncmAmtIsNotNull() {
            addCriterion("incm_amt is not null");
            return (Criteria) this;
        }

        public Criteria andIncmAmtEqualTo(Double value) {
            addCriterion("incm_amt =", value, "incmAmt");
            return (Criteria) this;
        }

        public Criteria andIncmAmtNotEqualTo(Double value) {
            addCriterion("incm_amt <>", value, "incmAmt");
            return (Criteria) this;
        }

        public Criteria andIncmAmtGreaterThan(Double value) {
            addCriterion("incm_amt >", value, "incmAmt");
            return (Criteria) this;
        }

        public Criteria andIncmAmtGreaterThanOrEqualTo(Double value) {
            addCriterion("incm_amt >=", value, "incmAmt");
            return (Criteria) this;
        }

        public Criteria andIncmAmtLessThan(Double value) {
            addCriterion("incm_amt <", value, "incmAmt");
            return (Criteria) this;
        }

        public Criteria andIncmAmtLessThanOrEqualTo(Double value) {
            addCriterion("incm_amt <=", value, "incmAmt");
            return (Criteria) this;
        }

        public Criteria andIncmAmtIn(List<Double> values) {
            addCriterion("incm_amt in", values, "incmAmt");
            return (Criteria) this;
        }

        public Criteria andIncmAmtNotIn(List<Double> values) {
            addCriterion("incm_amt not in", values, "incmAmt");
            return (Criteria) this;
        }

        public Criteria andIncmAmtBetween(Double value1, Double value2) {
            addCriterion("incm_amt between", value1, value2, "incmAmt");
            return (Criteria) this;
        }

        public Criteria andIncmAmtNotBetween(Double value1, Double value2) {
            addCriterion("incm_amt not between", value1, value2, "incmAmt");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateIsNull() {
            addCriterion("actv_incm_amt_rate is null");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateIsNotNull() {
            addCriterion("actv_incm_amt_rate is not null");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateEqualTo(Double value) {
            addCriterion("actv_incm_amt_rate =", value, "actvIncmAmtRate");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateNotEqualTo(Double value) {
            addCriterion("actv_incm_amt_rate <>", value, "actvIncmAmtRate");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateGreaterThan(Double value) {
            addCriterion("actv_incm_amt_rate >", value, "actvIncmAmtRate");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateGreaterThanOrEqualTo(Double value) {
            addCriterion("actv_incm_amt_rate >=", value, "actvIncmAmtRate");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateLessThan(Double value) {
            addCriterion("actv_incm_amt_rate <", value, "actvIncmAmtRate");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateLessThanOrEqualTo(Double value) {
            addCriterion("actv_incm_amt_rate <=", value, "actvIncmAmtRate");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateIn(List<Double> values) {
            addCriterion("actv_incm_amt_rate in", values, "actvIncmAmtRate");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateNotIn(List<Double> values) {
            addCriterion("actv_incm_amt_rate not in", values, "actvIncmAmtRate");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateBetween(Double value1, Double value2) {
            addCriterion("actv_incm_amt_rate between", value1, value2, "actvIncmAmtRate");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateNotBetween(Double value1, Double value2) {
            addCriterion("actv_incm_amt_rate not between", value1, value2, "actvIncmAmtRate");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtIsNull() {
            addCriterion("valid_live_new_aid_incm_amt is null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtIsNotNull() {
            addCriterion("valid_live_new_aid_incm_amt is not null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtEqualTo(Double value) {
            addCriterion("valid_live_new_aid_incm_amt =", value, "validLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtNotEqualTo(Double value) {
            addCriterion("valid_live_new_aid_incm_amt <>", value, "validLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtGreaterThan(Double value) {
            addCriterion("valid_live_new_aid_incm_amt >", value, "validLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtGreaterThanOrEqualTo(Double value) {
            addCriterion("valid_live_new_aid_incm_amt >=", value, "validLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtLessThan(Double value) {
            addCriterion("valid_live_new_aid_incm_amt <", value, "validLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtLessThanOrEqualTo(Double value) {
            addCriterion("valid_live_new_aid_incm_amt <=", value, "validLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtIn(List<Double> values) {
            addCriterion("valid_live_new_aid_incm_amt in", values, "validLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtNotIn(List<Double> values) {
            addCriterion("valid_live_new_aid_incm_amt not in", values, "validLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtBetween(Double value1, Double value2) {
            addCriterion("valid_live_new_aid_incm_amt between", value1, value2, "validLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtNotBetween(Double value1, Double value2) {
            addCriterion("valid_live_new_aid_incm_amt not between", value1, value2, "validLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnIsNull() {
            addCriterion("valid_live_new_aid_incm_amt_rn is null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnIsNotNull() {
            addCriterion("valid_live_new_aid_incm_amt_rn is not null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnEqualTo(Long value) {
            addCriterion("valid_live_new_aid_incm_amt_rn =", value, "validLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnNotEqualTo(Long value) {
            addCriterion("valid_live_new_aid_incm_amt_rn <>", value, "validLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnGreaterThan(Long value) {
            addCriterion("valid_live_new_aid_incm_amt_rn >", value, "validLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnGreaterThanOrEqualTo(Long value) {
            addCriterion("valid_live_new_aid_incm_amt_rn >=", value, "validLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnLessThan(Long value) {
            addCriterion("valid_live_new_aid_incm_amt_rn <", value, "validLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnLessThanOrEqualTo(Long value) {
            addCriterion("valid_live_new_aid_incm_amt_rn <=", value, "validLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnIn(List<Long> values) {
            addCriterion("valid_live_new_aid_incm_amt_rn in", values, "validLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnNotIn(List<Long> values) {
            addCriterion("valid_live_new_aid_incm_amt_rn not in", values, "validLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnBetween(Long value1, Long value2) {
            addCriterion("valid_live_new_aid_incm_amt_rn between", value1, value2, "validLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnNotBetween(Long value1, Long value2) {
            addCriterion("valid_live_new_aid_incm_amt_rn not between", value1, value2, "validLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffIsNull() {
            addCriterion("valid_live_new_aid_incm_amt_diff is null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffIsNotNull() {
            addCriterion("valid_live_new_aid_incm_amt_diff is not null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffEqualTo(Double value) {
            addCriterion("valid_live_new_aid_incm_amt_diff =", value, "validLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffNotEqualTo(Double value) {
            addCriterion("valid_live_new_aid_incm_amt_diff <>", value, "validLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffGreaterThan(Double value) {
            addCriterion("valid_live_new_aid_incm_amt_diff >", value, "validLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffGreaterThanOrEqualTo(Double value) {
            addCriterion("valid_live_new_aid_incm_amt_diff >=", value, "validLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffLessThan(Double value) {
            addCriterion("valid_live_new_aid_incm_amt_diff <", value, "validLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffLessThanOrEqualTo(Double value) {
            addCriterion("valid_live_new_aid_incm_amt_diff <=", value, "validLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffIn(List<Double> values) {
            addCriterion("valid_live_new_aid_incm_amt_diff in", values, "validLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffNotIn(List<Double> values) {
            addCriterion("valid_live_new_aid_incm_amt_diff not in", values, "validLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffBetween(Double value1, Double value2) {
            addCriterion("valid_live_new_aid_incm_amt_diff between", value1, value2, "validLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffNotBetween(Double value1, Double value2) {
            addCriterion("valid_live_new_aid_incm_amt_diff not between", value1, value2, "validLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtIsNull() {
            addCriterion("unvalid_live_new_aid_incm_amt is null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtIsNotNull() {
            addCriterion("unvalid_live_new_aid_incm_amt is not null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt =", value, "unvalidLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtNotEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt <>", value, "unvalidLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtGreaterThan(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt >", value, "unvalidLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtGreaterThanOrEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt >=", value, "unvalidLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtLessThan(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt <", value, "unvalidLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtLessThanOrEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt <=", value, "unvalidLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtIn(List<Double> values) {
            addCriterion("unvalid_live_new_aid_incm_amt in", values, "unvalidLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtNotIn(List<Double> values) {
            addCriterion("unvalid_live_new_aid_incm_amt not in", values, "unvalidLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtBetween(Double value1, Double value2) {
            addCriterion("unvalid_live_new_aid_incm_amt between", value1, value2, "unvalidLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtNotBetween(Double value1, Double value2) {
            addCriterion("unvalid_live_new_aid_incm_amt not between", value1, value2, "unvalidLiveNewAidIncmAmt");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnIsNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_rn is null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnIsNotNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_rn is not null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnEqualTo(Long value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn =", value, "unvalidLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnNotEqualTo(Long value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn <>", value, "unvalidLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnGreaterThan(Long value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn >", value, "unvalidLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnGreaterThanOrEqualTo(Long value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn >=", value, "unvalidLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnLessThan(Long value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn <", value, "unvalidLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnLessThanOrEqualTo(Long value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn <=", value, "unvalidLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnIn(List<Long> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn in", values, "unvalidLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnNotIn(List<Long> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn not in", values, "unvalidLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnBetween(Long value1, Long value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn between", value1, value2, "unvalidLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnNotBetween(Long value1, Long value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn not between", value1, value2, "unvalidLiveNewAidIncmAmtRn");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffIsNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_diff is null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffIsNotNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_diff is not null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff =", value, "unvalidLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffNotEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff <>", value, "unvalidLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffGreaterThan(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff >", value, "unvalidLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffGreaterThanOrEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff >=", value, "unvalidLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffLessThan(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff <", value, "unvalidLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffLessThanOrEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff <=", value, "unvalidLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffIn(List<Double> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff in", values, "unvalidLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffNotIn(List<Double> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff not in", values, "unvalidLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffBetween(Double value1, Double value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff between", value1, value2, "unvalidLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffNotBetween(Double value1, Double value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff not between", value1, value2, "unvalidLiveNewAidIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateIsNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_rate is null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateIsNotNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_rate is not null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate =", value, "unvalidLiveNewAidIncmAmtRate");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateNotEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate <>", value, "unvalidLiveNewAidIncmAmtRate");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateGreaterThan(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate >", value, "unvalidLiveNewAidIncmAmtRate");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateGreaterThanOrEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate >=", value, "unvalidLiveNewAidIncmAmtRate");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateLessThan(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate <", value, "unvalidLiveNewAidIncmAmtRate");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateLessThanOrEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate <=", value, "unvalidLiveNewAidIncmAmtRate");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateIn(List<Double> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate in", values, "unvalidLiveNewAidIncmAmtRate");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateNotIn(List<Double> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate not in", values, "unvalidLiveNewAidIncmAmtRate");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateBetween(Double value1, Double value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate between", value1, value2, "unvalidLiveNewAidIncmAmtRate");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateNotBetween(Double value1, Double value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate not between", value1, value2, "unvalidLiveNewAidIncmAmtRate");
            return (Criteria) this;
        }

        public Criteria andTotalUnvalidLiveNewAidIncmAmtRateIsNull() {
            addCriterion("total_unvalid_live_new_aid_incm_amt_rate is null");
            return (Criteria) this;
        }

        public Criteria andTotalUnvalidLiveNewAidIncmAmtRateIsNotNull() {
            addCriterion("total_unvalid_live_new_aid_incm_amt_rate is not null");
            return (Criteria) this;
        }

        public Criteria andTotalUnvalidLiveNewAidIncmAmtRateEqualTo(Double value) {
            addCriterion("total_unvalid_live_new_aid_incm_amt_rate =", value, "totalUnvalidLiveNewAidIncmAmtRate");
            return (Criteria) this;
        }

        public Criteria andTotalUnvalidLiveNewAidIncmAmtRateNotEqualTo(Double value) {
            addCriterion("total_unvalid_live_new_aid_incm_amt_rate <>", value, "totalUnvalidLiveNewAidIncmAmtRate");
            return (Criteria) this;
        }

        public Criteria andTotalUnvalidLiveNewAidIncmAmtRateGreaterThan(Double value) {
            addCriterion("total_unvalid_live_new_aid_incm_amt_rate >", value, "totalUnvalidLiveNewAidIncmAmtRate");
            return (Criteria) this;
        }

        public Criteria andTotalUnvalidLiveNewAidIncmAmtRateGreaterThanOrEqualTo(Double value) {
            addCriterion("total_unvalid_live_new_aid_incm_amt_rate >=", value, "totalUnvalidLiveNewAidIncmAmtRate");
            return (Criteria) this;
        }

        public Criteria andTotalUnvalidLiveNewAidIncmAmtRateLessThan(Double value) {
            addCriterion("total_unvalid_live_new_aid_incm_amt_rate <", value, "totalUnvalidLiveNewAidIncmAmtRate");
            return (Criteria) this;
        }

        public Criteria andTotalUnvalidLiveNewAidIncmAmtRateLessThanOrEqualTo(Double value) {
            addCriterion("total_unvalid_live_new_aid_incm_amt_rate <=", value, "totalUnvalidLiveNewAidIncmAmtRate");
            return (Criteria) this;
        }

        public Criteria andTotalUnvalidLiveNewAidIncmAmtRateIn(List<Double> values) {
            addCriterion("total_unvalid_live_new_aid_incm_amt_rate in", values, "totalUnvalidLiveNewAidIncmAmtRate");
            return (Criteria) this;
        }

        public Criteria andTotalUnvalidLiveNewAidIncmAmtRateNotIn(List<Double> values) {
            addCriterion("total_unvalid_live_new_aid_incm_amt_rate not in", values, "totalUnvalidLiveNewAidIncmAmtRate");
            return (Criteria) this;
        }

        public Criteria andTotalUnvalidLiveNewAidIncmAmtRateBetween(Double value1, Double value2) {
            addCriterion("total_unvalid_live_new_aid_incm_amt_rate between", value1, value2, "totalUnvalidLiveNewAidIncmAmtRate");
            return (Criteria) this;
        }

        public Criteria andTotalUnvalidLiveNewAidIncmAmtRateNotBetween(Double value1, Double value2) {
            addCriterion("total_unvalid_live_new_aid_incm_amt_rate not between", value1, value2, "totalUnvalidLiveNewAidIncmAmtRate");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffIsNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff is null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffIsNotNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff is not null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff =", value, "unvalidLiveNewAidIncmAmtRateDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffNotEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff <>", value, "unvalidLiveNewAidIncmAmtRateDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffGreaterThan(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff >", value, "unvalidLiveNewAidIncmAmtRateDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffGreaterThanOrEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff >=", value, "unvalidLiveNewAidIncmAmtRateDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffLessThan(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff <", value, "unvalidLiveNewAidIncmAmtRateDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffLessThanOrEqualTo(Double value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff <=", value, "unvalidLiveNewAidIncmAmtRateDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffIn(List<Double> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff in", values, "unvalidLiveNewAidIncmAmtRateDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffNotIn(List<Double> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff not in", values, "unvalidLiveNewAidIncmAmtRateDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffBetween(Double value1, Double value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff between", value1, value2, "unvalidLiveNewAidIncmAmtRateDiff");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffNotBetween(Double value1, Double value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff not between", value1, value2, "unvalidLiveNewAidIncmAmtRateDiff");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointIsNull() {
            addCriterion("guild_health_point is null");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointIsNotNull() {
            addCriterion("guild_health_point is not null");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointEqualTo(Double value) {
            addCriterion("guild_health_point =", value, "guildHealthPoint");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointNotEqualTo(Double value) {
            addCriterion("guild_health_point <>", value, "guildHealthPoint");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointGreaterThan(Double value) {
            addCriterion("guild_health_point >", value, "guildHealthPoint");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointGreaterThanOrEqualTo(Double value) {
            addCriterion("guild_health_point >=", value, "guildHealthPoint");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointLessThan(Double value) {
            addCriterion("guild_health_point <", value, "guildHealthPoint");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointLessThanOrEqualTo(Double value) {
            addCriterion("guild_health_point <=", value, "guildHealthPoint");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointIn(List<Double> values) {
            addCriterion("guild_health_point in", values, "guildHealthPoint");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointNotIn(List<Double> values) {
            addCriterion("guild_health_point not in", values, "guildHealthPoint");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointBetween(Double value1, Double value2) {
            addCriterion("guild_health_point between", value1, value2, "guildHealthPoint");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointNotBetween(Double value1, Double value2) {
            addCriterion("guild_health_point not between", value1, value2, "guildHealthPoint");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnScoreIsNull() {
            addCriterion("new_aid_rat_3_4_aid_num_rn_score is null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnScoreIsNotNull() {
            addCriterion("new_aid_rat_3_4_aid_num_rn_score is not null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnScoreEqualTo(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_rn_score =", value, "newAidRat34AidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnScoreNotEqualTo(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_rn_score <>", value, "newAidRat34AidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnScoreGreaterThan(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_rn_score >", value, "newAidRat34AidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_rn_score >=", value, "newAidRat34AidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnScoreLessThan(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_rn_score <", value, "newAidRat34AidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnScoreLessThanOrEqualTo(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_rn_score <=", value, "newAidRat34AidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnScoreIn(List<Integer> values) {
            addCriterion("new_aid_rat_3_4_aid_num_rn_score in", values, "newAidRat34AidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnScoreNotIn(List<Integer> values) {
            addCriterion("new_aid_rat_3_4_aid_num_rn_score not in", values, "newAidRat34AidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnScoreBetween(Integer value1, Integer value2) {
            addCriterion("new_aid_rat_3_4_aid_num_rn_score between", value1, value2, "newAidRat34AidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumRnScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("new_aid_rat_3_4_aid_num_rn_score not between", value1, value2, "newAidRat34AidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffScoreIsNull() {
            addCriterion("new_aid_rat_3_4_aid_num_diff_score is null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffScoreIsNotNull() {
            addCriterion("new_aid_rat_3_4_aid_num_diff_score is not null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffScoreEqualTo(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_score =", value, "newAidRat34AidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffScoreNotEqualTo(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_score <>", value, "newAidRat34AidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffScoreGreaterThan(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_score >", value, "newAidRat34AidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_score >=", value, "newAidRat34AidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffScoreLessThan(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_score <", value, "newAidRat34AidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffScoreLessThanOrEqualTo(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_score <=", value, "newAidRat34AidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffScoreIn(List<Integer> values) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_score in", values, "newAidRat34AidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffScoreNotIn(List<Integer> values) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_score not in", values, "newAidRat34AidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffScoreBetween(Integer value1, Integer value2) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_score between", value1, value2, "newAidRat34AidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumDiffScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("new_aid_rat_3_4_aid_num_diff_score not between", value1, value2, "newAidRat34AidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffScoreIsNull() {
            addCriterion("upgrd_high_aid_num_diff_score is null");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffScoreIsNotNull() {
            addCriterion("upgrd_high_aid_num_diff_score is not null");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffScoreEqualTo(Integer value) {
            addCriterion("upgrd_high_aid_num_diff_score =", value, "upgrdHighAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffScoreNotEqualTo(Integer value) {
            addCriterion("upgrd_high_aid_num_diff_score <>", value, "upgrdHighAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffScoreGreaterThan(Integer value) {
            addCriterion("upgrd_high_aid_num_diff_score >", value, "upgrdHighAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("upgrd_high_aid_num_diff_score >=", value, "upgrdHighAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffScoreLessThan(Integer value) {
            addCriterion("upgrd_high_aid_num_diff_score <", value, "upgrdHighAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffScoreLessThanOrEqualTo(Integer value) {
            addCriterion("upgrd_high_aid_num_diff_score <=", value, "upgrdHighAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffScoreIn(List<Integer> values) {
            addCriterion("upgrd_high_aid_num_diff_score in", values, "upgrdHighAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffScoreNotIn(List<Integer> values) {
            addCriterion("upgrd_high_aid_num_diff_score not in", values, "upgrdHighAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffScoreBetween(Integer value1, Integer value2) {
            addCriterion("upgrd_high_aid_num_diff_score between", value1, value2, "upgrdHighAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumDiffScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("upgrd_high_aid_num_diff_score not between", value1, value2, "upgrdHighAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffScoreIsNull() {
            addCriterion("upgrd_waist_aid_num_diff_score is null");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffScoreIsNotNull() {
            addCriterion("upgrd_waist_aid_num_diff_score is not null");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffScoreEqualTo(Integer value) {
            addCriterion("upgrd_waist_aid_num_diff_score =", value, "upgrdWaistAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffScoreNotEqualTo(Integer value) {
            addCriterion("upgrd_waist_aid_num_diff_score <>", value, "upgrdWaistAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffScoreGreaterThan(Integer value) {
            addCriterion("upgrd_waist_aid_num_diff_score >", value, "upgrdWaistAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("upgrd_waist_aid_num_diff_score >=", value, "upgrdWaistAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffScoreLessThan(Integer value) {
            addCriterion("upgrd_waist_aid_num_diff_score <", value, "upgrdWaistAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffScoreLessThanOrEqualTo(Integer value) {
            addCriterion("upgrd_waist_aid_num_diff_score <=", value, "upgrdWaistAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffScoreIn(List<Integer> values) {
            addCriterion("upgrd_waist_aid_num_diff_score in", values, "upgrdWaistAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffScoreNotIn(List<Integer> values) {
            addCriterion("upgrd_waist_aid_num_diff_score not in", values, "upgrdWaistAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffScoreBetween(Integer value1, Integer value2) {
            addCriterion("upgrd_waist_aid_num_diff_score between", value1, value2, "upgrdWaistAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumDiffScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("upgrd_waist_aid_num_diff_score not between", value1, value2, "upgrdWaistAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnScoreIsNull() {
            addCriterion("avg_high_valid_live_aid_num_rn_score is null");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnScoreIsNotNull() {
            addCriterion("avg_high_valid_live_aid_num_rn_score is not null");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnScoreEqualTo(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_rn_score =", value, "avgHighValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnScoreNotEqualTo(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_rn_score <>", value, "avgHighValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnScoreGreaterThan(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_rn_score >", value, "avgHighValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_rn_score >=", value, "avgHighValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnScoreLessThan(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_rn_score <", value, "avgHighValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnScoreLessThanOrEqualTo(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_rn_score <=", value, "avgHighValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnScoreIn(List<Integer> values) {
            addCriterion("avg_high_valid_live_aid_num_rn_score in", values, "avgHighValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnScoreNotIn(List<Integer> values) {
            addCriterion("avg_high_valid_live_aid_num_rn_score not in", values, "avgHighValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnScoreBetween(Integer value1, Integer value2) {
            addCriterion("avg_high_valid_live_aid_num_rn_score between", value1, value2, "avgHighValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumRnScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("avg_high_valid_live_aid_num_rn_score not between", value1, value2, "avgHighValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnScoreIsNull() {
            addCriterion("avg_waist_valid_live_aid_num_rn_score is null");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnScoreIsNotNull() {
            addCriterion("avg_waist_valid_live_aid_num_rn_score is not null");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnScoreEqualTo(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_rn_score =", value, "avgWaistValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnScoreNotEqualTo(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_rn_score <>", value, "avgWaistValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnScoreGreaterThan(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_rn_score >", value, "avgWaistValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_rn_score >=", value, "avgWaistValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnScoreLessThan(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_rn_score <", value, "avgWaistValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnScoreLessThanOrEqualTo(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_rn_score <=", value, "avgWaistValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnScoreIn(List<Integer> values) {
            addCriterion("avg_waist_valid_live_aid_num_rn_score in", values, "avgWaistValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnScoreNotIn(List<Integer> values) {
            addCriterion("avg_waist_valid_live_aid_num_rn_score not in", values, "avgWaistValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnScoreBetween(Integer value1, Integer value2) {
            addCriterion("avg_waist_valid_live_aid_num_rn_score between", value1, value2, "avgWaistValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumRnScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("avg_waist_valid_live_aid_num_rn_score not between", value1, value2, "avgWaistValidLiveAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffScoreIsNull() {
            addCriterion("avg_high_valid_live_aid_num_diff_score is null");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffScoreIsNotNull() {
            addCriterion("avg_high_valid_live_aid_num_diff_score is not null");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffScoreEqualTo(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_diff_score =", value, "avgHighValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffScoreNotEqualTo(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_diff_score <>", value, "avgHighValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffScoreGreaterThan(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_diff_score >", value, "avgHighValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_diff_score >=", value, "avgHighValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffScoreLessThan(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_diff_score <", value, "avgHighValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffScoreLessThanOrEqualTo(Integer value) {
            addCriterion("avg_high_valid_live_aid_num_diff_score <=", value, "avgHighValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffScoreIn(List<Integer> values) {
            addCriterion("avg_high_valid_live_aid_num_diff_score in", values, "avgHighValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffScoreNotIn(List<Integer> values) {
            addCriterion("avg_high_valid_live_aid_num_diff_score not in", values, "avgHighValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffScoreBetween(Integer value1, Integer value2) {
            addCriterion("avg_high_valid_live_aid_num_diff_score between", value1, value2, "avgHighValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgHighValidLiveAidNumDiffScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("avg_high_valid_live_aid_num_diff_score not between", value1, value2, "avgHighValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffScoreIsNull() {
            addCriterion("avg_waist_valid_live_aid_num_diff_score is null");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffScoreIsNotNull() {
            addCriterion("avg_waist_valid_live_aid_num_diff_score is not null");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffScoreEqualTo(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_diff_score =", value, "avgWaistValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffScoreNotEqualTo(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_diff_score <>", value, "avgWaistValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffScoreGreaterThan(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_diff_score >", value, "avgWaistValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_diff_score >=", value, "avgWaistValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffScoreLessThan(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_diff_score <", value, "avgWaistValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffScoreLessThanOrEqualTo(Integer value) {
            addCriterion("avg_waist_valid_live_aid_num_diff_score <=", value, "avgWaistValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffScoreIn(List<Integer> values) {
            addCriterion("avg_waist_valid_live_aid_num_diff_score in", values, "avgWaistValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffScoreNotIn(List<Integer> values) {
            addCriterion("avg_waist_valid_live_aid_num_diff_score not in", values, "avgWaistValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffScoreBetween(Integer value1, Integer value2) {
            addCriterion("avg_waist_valid_live_aid_num_diff_score between", value1, value2, "avgWaistValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgWaistValidLiveAidNumDiffScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("avg_waist_valid_live_aid_num_diff_score not between", value1, value2, "avgWaistValidLiveAidNumDiffScore");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnScoreIsNull() {
            addCriterion("avg_nto_valid_live_aid_rate_rn_score is null");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnScoreIsNotNull() {
            addCriterion("avg_nto_valid_live_aid_rate_rn_score is not null");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnScoreEqualTo(Integer value) {
            addCriterion("avg_nto_valid_live_aid_rate_rn_score =", value, "avgNtoValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnScoreNotEqualTo(Integer value) {
            addCriterion("avg_nto_valid_live_aid_rate_rn_score <>", value, "avgNtoValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnScoreGreaterThan(Integer value) {
            addCriterion("avg_nto_valid_live_aid_rate_rn_score >", value, "avgNtoValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("avg_nto_valid_live_aid_rate_rn_score >=", value, "avgNtoValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnScoreLessThan(Integer value) {
            addCriterion("avg_nto_valid_live_aid_rate_rn_score <", value, "avgNtoValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnScoreLessThanOrEqualTo(Integer value) {
            addCriterion("avg_nto_valid_live_aid_rate_rn_score <=", value, "avgNtoValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnScoreIn(List<Integer> values) {
            addCriterion("avg_nto_valid_live_aid_rate_rn_score in", values, "avgNtoValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnScoreNotIn(List<Integer> values) {
            addCriterion("avg_nto_valid_live_aid_rate_rn_score not in", values, "avgNtoValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnScoreBetween(Integer value1, Integer value2) {
            addCriterion("avg_nto_valid_live_aid_rate_rn_score between", value1, value2, "avgNtoValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgNtoValidLiveAidRateRnScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("avg_nto_valid_live_aid_rate_rn_score not between", value1, value2, "avgNtoValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnScoreIsNull() {
            addCriterion("avg_stock_valid_live_aid_rate_rn_score is null");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnScoreIsNotNull() {
            addCriterion("avg_stock_valid_live_aid_rate_rn_score is not null");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnScoreEqualTo(Integer value) {
            addCriterion("avg_stock_valid_live_aid_rate_rn_score =", value, "avgStockValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnScoreNotEqualTo(Integer value) {
            addCriterion("avg_stock_valid_live_aid_rate_rn_score <>", value, "avgStockValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnScoreGreaterThan(Integer value) {
            addCriterion("avg_stock_valid_live_aid_rate_rn_score >", value, "avgStockValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("avg_stock_valid_live_aid_rate_rn_score >=", value, "avgStockValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnScoreLessThan(Integer value) {
            addCriterion("avg_stock_valid_live_aid_rate_rn_score <", value, "avgStockValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnScoreLessThanOrEqualTo(Integer value) {
            addCriterion("avg_stock_valid_live_aid_rate_rn_score <=", value, "avgStockValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnScoreIn(List<Integer> values) {
            addCriterion("avg_stock_valid_live_aid_rate_rn_score in", values, "avgStockValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnScoreNotIn(List<Integer> values) {
            addCriterion("avg_stock_valid_live_aid_rate_rn_score not in", values, "avgStockValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnScoreBetween(Integer value1, Integer value2) {
            addCriterion("avg_stock_valid_live_aid_rate_rn_score between", value1, value2, "avgStockValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andAvgStockValidLiveAidRateRnScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("avg_stock_valid_live_aid_rate_rn_score not between", value1, value2, "avgStockValidLiveAidRateRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnScoreIsNull() {
            addCriterion("new_auth_golden_aid_num_rn_score is null");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnScoreIsNotNull() {
            addCriterion("new_auth_golden_aid_num_rn_score is not null");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnScoreEqualTo(Integer value) {
            addCriterion("new_auth_golden_aid_num_rn_score =", value, "newAuthGoldenAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnScoreNotEqualTo(Integer value) {
            addCriterion("new_auth_golden_aid_num_rn_score <>", value, "newAuthGoldenAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnScoreGreaterThan(Integer value) {
            addCriterion("new_auth_golden_aid_num_rn_score >", value, "newAuthGoldenAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("new_auth_golden_aid_num_rn_score >=", value, "newAuthGoldenAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnScoreLessThan(Integer value) {
            addCriterion("new_auth_golden_aid_num_rn_score <", value, "newAuthGoldenAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnScoreLessThanOrEqualTo(Integer value) {
            addCriterion("new_auth_golden_aid_num_rn_score <=", value, "newAuthGoldenAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnScoreIn(List<Integer> values) {
            addCriterion("new_auth_golden_aid_num_rn_score in", values, "newAuthGoldenAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnScoreNotIn(List<Integer> values) {
            addCriterion("new_auth_golden_aid_num_rn_score not in", values, "newAuthGoldenAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnScoreBetween(Integer value1, Integer value2) {
            addCriterion("new_auth_golden_aid_num_rn_score between", value1, value2, "newAuthGoldenAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andNewAuthGoldenAidNumRnScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("new_auth_golden_aid_num_rn_score not between", value1, value2, "newAuthGoldenAidNumRnScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtRnScoreIsNull() {
            addCriterion("valid_live_new_aid_prod_pay_amt_rn_score is null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtRnScoreIsNotNull() {
            addCriterion("valid_live_new_aid_prod_pay_amt_rn_score is not null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtRnScoreEqualTo(Integer value) {
            addCriterion("valid_live_new_aid_prod_pay_amt_rn_score =", value, "validLiveNewAidProdPayAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtRnScoreNotEqualTo(Integer value) {
            addCriterion("valid_live_new_aid_prod_pay_amt_rn_score <>", value, "validLiveNewAidProdPayAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtRnScoreGreaterThan(Integer value) {
            addCriterion("valid_live_new_aid_prod_pay_amt_rn_score >", value, "validLiveNewAidProdPayAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtRnScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("valid_live_new_aid_prod_pay_amt_rn_score >=", value, "validLiveNewAidProdPayAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtRnScoreLessThan(Integer value) {
            addCriterion("valid_live_new_aid_prod_pay_amt_rn_score <", value, "validLiveNewAidProdPayAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtRnScoreLessThanOrEqualTo(Integer value) {
            addCriterion("valid_live_new_aid_prod_pay_amt_rn_score <=", value, "validLiveNewAidProdPayAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtRnScoreIn(List<Integer> values) {
            addCriterion("valid_live_new_aid_prod_pay_amt_rn_score in", values, "validLiveNewAidProdPayAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtRnScoreNotIn(List<Integer> values) {
            addCriterion("valid_live_new_aid_prod_pay_amt_rn_score not in", values, "validLiveNewAidProdPayAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtRnScoreBetween(Integer value1, Integer value2) {
            addCriterion("valid_live_new_aid_prod_pay_amt_rn_score between", value1, value2, "validLiveNewAidProdPayAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtRnScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("valid_live_new_aid_prod_pay_amt_rn_score not between", value1, value2, "validLiveNewAidProdPayAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRnScoreIsNull() {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rn_score is null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRnScoreIsNotNull() {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rn_score is not null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRnScoreEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rn_score =", value, "unvalidLiveNewAidProdPayAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRnScoreNotEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rn_score <>", value, "unvalidLiveNewAidProdPayAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRnScoreGreaterThan(Integer value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rn_score >", value, "unvalidLiveNewAidProdPayAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRnScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rn_score >=", value, "unvalidLiveNewAidProdPayAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRnScoreLessThan(Integer value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rn_score <", value, "unvalidLiveNewAidProdPayAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRnScoreLessThanOrEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rn_score <=", value, "unvalidLiveNewAidProdPayAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRnScoreIn(List<Integer> values) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rn_score in", values, "unvalidLiveNewAidProdPayAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRnScoreNotIn(List<Integer> values) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rn_score not in", values, "unvalidLiveNewAidProdPayAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRnScoreBetween(Integer value1, Integer value2) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rn_score between", value1, value2, "unvalidLiveNewAidProdPayAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRnScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rn_score not between", value1, value2, "unvalidLiveNewAidProdPayAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtDiffScoreIsNull() {
            addCriterion("valid_live_new_aid_prod_pay_amt_diff_score is null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtDiffScoreIsNotNull() {
            addCriterion("valid_live_new_aid_prod_pay_amt_diff_score is not null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtDiffScoreEqualTo(Integer value) {
            addCriterion("valid_live_new_aid_prod_pay_amt_diff_score =", value, "validLiveNewAidProdPayAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtDiffScoreNotEqualTo(Integer value) {
            addCriterion("valid_live_new_aid_prod_pay_amt_diff_score <>", value, "validLiveNewAidProdPayAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtDiffScoreGreaterThan(Integer value) {
            addCriterion("valid_live_new_aid_prod_pay_amt_diff_score >", value, "validLiveNewAidProdPayAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtDiffScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("valid_live_new_aid_prod_pay_amt_diff_score >=", value, "validLiveNewAidProdPayAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtDiffScoreLessThan(Integer value) {
            addCriterion("valid_live_new_aid_prod_pay_amt_diff_score <", value, "validLiveNewAidProdPayAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtDiffScoreLessThanOrEqualTo(Integer value) {
            addCriterion("valid_live_new_aid_prod_pay_amt_diff_score <=", value, "validLiveNewAidProdPayAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtDiffScoreIn(List<Integer> values) {
            addCriterion("valid_live_new_aid_prod_pay_amt_diff_score in", values, "validLiveNewAidProdPayAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtDiffScoreNotIn(List<Integer> values) {
            addCriterion("valid_live_new_aid_prod_pay_amt_diff_score not in", values, "validLiveNewAidProdPayAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtDiffScoreBetween(Integer value1, Integer value2) {
            addCriterion("valid_live_new_aid_prod_pay_amt_diff_score between", value1, value2, "validLiveNewAidProdPayAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidProdPayAmtDiffScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("valid_live_new_aid_prod_pay_amt_diff_score not between", value1, value2, "validLiveNewAidProdPayAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtDiffScoreIsNull() {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_diff_score is null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtDiffScoreIsNotNull() {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_diff_score is not null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtDiffScoreEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_diff_score =", value, "unvalidLiveNewAidProdPayAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtDiffScoreNotEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_diff_score <>", value, "unvalidLiveNewAidProdPayAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtDiffScoreGreaterThan(Integer value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_diff_score >", value, "unvalidLiveNewAidProdPayAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtDiffScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_diff_score >=", value, "unvalidLiveNewAidProdPayAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtDiffScoreLessThan(Integer value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_diff_score <", value, "unvalidLiveNewAidProdPayAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtDiffScoreLessThanOrEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_diff_score <=", value, "unvalidLiveNewAidProdPayAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtDiffScoreIn(List<Integer> values) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_diff_score in", values, "unvalidLiveNewAidProdPayAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtDiffScoreNotIn(List<Integer> values) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_diff_score not in", values, "unvalidLiveNewAidProdPayAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtDiffScoreBetween(Integer value1, Integer value2) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_diff_score between", value1, value2, "unvalidLiveNewAidProdPayAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtDiffScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_diff_score not between", value1, value2, "unvalidLiveNewAidProdPayAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateDiffScoreIsNull() {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate_diff_score is null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateDiffScoreIsNotNull() {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate_diff_score is not null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateDiffScoreEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate_diff_score =", value, "unvalidLiveNewAidProdPayAmtRateDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateDiffScoreNotEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate_diff_score <>", value, "unvalidLiveNewAidProdPayAmtRateDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateDiffScoreGreaterThan(Integer value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate_diff_score >", value, "unvalidLiveNewAidProdPayAmtRateDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateDiffScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate_diff_score >=", value, "unvalidLiveNewAidProdPayAmtRateDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateDiffScoreLessThan(Integer value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate_diff_score <", value, "unvalidLiveNewAidProdPayAmtRateDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateDiffScoreLessThanOrEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate_diff_score <=", value, "unvalidLiveNewAidProdPayAmtRateDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateDiffScoreIn(List<Integer> values) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate_diff_score in", values, "unvalidLiveNewAidProdPayAmtRateDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateDiffScoreNotIn(List<Integer> values) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate_diff_score not in", values, "unvalidLiveNewAidProdPayAmtRateDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateDiffScoreBetween(Integer value1, Integer value2) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate_diff_score between", value1, value2, "unvalidLiveNewAidProdPayAmtRateDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidProdPayAmtRateDiffScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("unvalid_live_new_aid_prod_pay_amt_rate_diff_score not between", value1, value2, "unvalidLiveNewAidProdPayAmtRateDiffScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnScoreIsNull() {
            addCriterion("valid_live_new_aid_incm_amt_rn_score is null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnScoreIsNotNull() {
            addCriterion("valid_live_new_aid_incm_amt_rn_score is not null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnScoreEqualTo(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_rn_score =", value, "validLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnScoreNotEqualTo(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_rn_score <>", value, "validLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnScoreGreaterThan(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_rn_score >", value, "validLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_rn_score >=", value, "validLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnScoreLessThan(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_rn_score <", value, "validLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnScoreLessThanOrEqualTo(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_rn_score <=", value, "validLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnScoreIn(List<Integer> values) {
            addCriterion("valid_live_new_aid_incm_amt_rn_score in", values, "validLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnScoreNotIn(List<Integer> values) {
            addCriterion("valid_live_new_aid_incm_amt_rn_score not in", values, "validLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnScoreBetween(Integer value1, Integer value2) {
            addCriterion("valid_live_new_aid_incm_amt_rn_score between", value1, value2, "validLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtRnScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("valid_live_new_aid_incm_amt_rn_score not between", value1, value2, "validLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnScoreIsNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_score is null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnScoreIsNotNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_score is not null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnScoreEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_score =", value, "unvalidLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnScoreNotEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_score <>", value, "unvalidLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnScoreGreaterThan(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_score >", value, "unvalidLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_score >=", value, "unvalidLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnScoreLessThan(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_score <", value, "unvalidLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnScoreLessThanOrEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_score <=", value, "unvalidLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnScoreIn(List<Integer> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_score in", values, "unvalidLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnScoreNotIn(List<Integer> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_score not in", values, "unvalidLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnScoreBetween(Integer value1, Integer value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_score between", value1, value2, "unvalidLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRnScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_rn_score not between", value1, value2, "unvalidLiveNewAidIncmAmtRnScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffScoreIsNull() {
            addCriterion("valid_live_new_aid_incm_amt_diff_score is null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffScoreIsNotNull() {
            addCriterion("valid_live_new_aid_incm_amt_diff_score is not null");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffScoreEqualTo(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_diff_score =", value, "validLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffScoreNotEqualTo(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_diff_score <>", value, "validLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffScoreGreaterThan(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_diff_score >", value, "validLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_diff_score >=", value, "validLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffScoreLessThan(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_diff_score <", value, "validLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffScoreLessThanOrEqualTo(Integer value) {
            addCriterion("valid_live_new_aid_incm_amt_diff_score <=", value, "validLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffScoreIn(List<Integer> values) {
            addCriterion("valid_live_new_aid_incm_amt_diff_score in", values, "validLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffScoreNotIn(List<Integer> values) {
            addCriterion("valid_live_new_aid_incm_amt_diff_score not in", values, "validLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffScoreBetween(Integer value1, Integer value2) {
            addCriterion("valid_live_new_aid_incm_amt_diff_score between", value1, value2, "validLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andValidLiveNewAidIncmAmtDiffScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("valid_live_new_aid_incm_amt_diff_score not between", value1, value2, "validLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffScoreIsNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_score is null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffScoreIsNotNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_score is not null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffScoreEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_score =", value, "unvalidLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffScoreNotEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_score <>", value, "unvalidLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffScoreGreaterThan(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_score >", value, "unvalidLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_score >=", value, "unvalidLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffScoreLessThan(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_score <", value, "unvalidLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffScoreLessThanOrEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_score <=", value, "unvalidLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffScoreIn(List<Integer> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_score in", values, "unvalidLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffScoreNotIn(List<Integer> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_score not in", values, "unvalidLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffScoreBetween(Integer value1, Integer value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_score between", value1, value2, "unvalidLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtDiffScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_diff_score not between", value1, value2, "unvalidLiveNewAidIncmAmtDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffScoreIsNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_score is null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffScoreIsNotNull() {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_score is not null");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffScoreEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_score =", value, "unvalidLiveNewAidIncmAmtRateDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffScoreNotEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_score <>", value, "unvalidLiveNewAidIncmAmtRateDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffScoreGreaterThan(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_score >", value, "unvalidLiveNewAidIncmAmtRateDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_score >=", value, "unvalidLiveNewAidIncmAmtRateDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffScoreLessThan(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_score <", value, "unvalidLiveNewAidIncmAmtRateDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffScoreLessThanOrEqualTo(Integer value) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_score <=", value, "unvalidLiveNewAidIncmAmtRateDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffScoreIn(List<Integer> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_score in", values, "unvalidLiveNewAidIncmAmtRateDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffScoreNotIn(List<Integer> values) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_score not in", values, "unvalidLiveNewAidIncmAmtRateDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffScoreBetween(Integer value1, Integer value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_score between", value1, value2, "unvalidLiveNewAidIncmAmtRateDiffScore");
            return (Criteria) this;
        }

        public Criteria andUnvalidLiveNewAidIncmAmtRateDiffScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("unvalid_live_new_aid_incm_amt_rate_diff_score not between", value1, value2, "unvalidLiveNewAidIncmAmtRateDiffScore");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointScoreIsNull() {
            addCriterion("guild_health_point_score is null");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointScoreIsNotNull() {
            addCriterion("guild_health_point_score is not null");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointScoreEqualTo(Integer value) {
            addCriterion("guild_health_point_score =", value, "guildHealthPointScore");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointScoreNotEqualTo(Integer value) {
            addCriterion("guild_health_point_score <>", value, "guildHealthPointScore");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointScoreGreaterThan(Integer value) {
            addCriterion("guild_health_point_score >", value, "guildHealthPointScore");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("guild_health_point_score >=", value, "guildHealthPointScore");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointScoreLessThan(Integer value) {
            addCriterion("guild_health_point_score <", value, "guildHealthPointScore");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointScoreLessThanOrEqualTo(Integer value) {
            addCriterion("guild_health_point_score <=", value, "guildHealthPointScore");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointScoreIn(List<Integer> values) {
            addCriterion("guild_health_point_score in", values, "guildHealthPointScore");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointScoreNotIn(List<Integer> values) {
            addCriterion("guild_health_point_score not in", values, "guildHealthPointScore");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointScoreBetween(Integer value1, Integer value2) {
            addCriterion("guild_health_point_score between", value1, value2, "guildHealthPointScore");
            return (Criteria) this;
        }

        public Criteria andGuildHealthPointScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("guild_health_point_score not between", value1, value2, "guildHealthPointScore");
            return (Criteria) this;
        }

        public Criteria andRecruScoreIsNull() {
            addCriterion("recru_score is null");
            return (Criteria) this;
        }

        public Criteria andRecruScoreIsNotNull() {
            addCriterion("recru_score is not null");
            return (Criteria) this;
        }

        public Criteria andRecruScoreEqualTo(Integer value) {
            addCriterion("recru_score =", value, "recruScore");
            return (Criteria) this;
        }

        public Criteria andRecruScoreNotEqualTo(Integer value) {
            addCriterion("recru_score <>", value, "recruScore");
            return (Criteria) this;
        }

        public Criteria andRecruScoreGreaterThan(Integer value) {
            addCriterion("recru_score >", value, "recruScore");
            return (Criteria) this;
        }

        public Criteria andRecruScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("recru_score >=", value, "recruScore");
            return (Criteria) this;
        }

        public Criteria andRecruScoreLessThan(Integer value) {
            addCriterion("recru_score <", value, "recruScore");
            return (Criteria) this;
        }

        public Criteria andRecruScoreLessThanOrEqualTo(Integer value) {
            addCriterion("recru_score <=", value, "recruScore");
            return (Criteria) this;
        }

        public Criteria andRecruScoreIn(List<Integer> values) {
            addCriterion("recru_score in", values, "recruScore");
            return (Criteria) this;
        }

        public Criteria andRecruScoreNotIn(List<Integer> values) {
            addCriterion("recru_score not in", values, "recruScore");
            return (Criteria) this;
        }

        public Criteria andRecruScoreBetween(Integer value1, Integer value2) {
            addCriterion("recru_score between", value1, value2, "recruScore");
            return (Criteria) this;
        }

        public Criteria andRecruScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("recru_score not between", value1, value2, "recruScore");
            return (Criteria) this;
        }

        public Criteria andHatchScoreIsNull() {
            addCriterion("hatch_score is null");
            return (Criteria) this;
        }

        public Criteria andHatchScoreIsNotNull() {
            addCriterion("hatch_score is not null");
            return (Criteria) this;
        }

        public Criteria andHatchScoreEqualTo(Integer value) {
            addCriterion("hatch_score =", value, "hatchScore");
            return (Criteria) this;
        }

        public Criteria andHatchScoreNotEqualTo(Integer value) {
            addCriterion("hatch_score <>", value, "hatchScore");
            return (Criteria) this;
        }

        public Criteria andHatchScoreGreaterThan(Integer value) {
            addCriterion("hatch_score >", value, "hatchScore");
            return (Criteria) this;
        }

        public Criteria andHatchScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("hatch_score >=", value, "hatchScore");
            return (Criteria) this;
        }

        public Criteria andHatchScoreLessThan(Integer value) {
            addCriterion("hatch_score <", value, "hatchScore");
            return (Criteria) this;
        }

        public Criteria andHatchScoreLessThanOrEqualTo(Integer value) {
            addCriterion("hatch_score <=", value, "hatchScore");
            return (Criteria) this;
        }

        public Criteria andHatchScoreIn(List<Integer> values) {
            addCriterion("hatch_score in", values, "hatchScore");
            return (Criteria) this;
        }

        public Criteria andHatchScoreNotIn(List<Integer> values) {
            addCriterion("hatch_score not in", values, "hatchScore");
            return (Criteria) this;
        }

        public Criteria andHatchScoreBetween(Integer value1, Integer value2) {
            addCriterion("hatch_score between", value1, value2, "hatchScore");
            return (Criteria) this;
        }

        public Criteria andHatchScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("hatch_score not between", value1, value2, "hatchScore");
            return (Criteria) this;
        }

        public Criteria andRetScoreIsNull() {
            addCriterion("ret_score is null");
            return (Criteria) this;
        }

        public Criteria andRetScoreIsNotNull() {
            addCriterion("ret_score is not null");
            return (Criteria) this;
        }

        public Criteria andRetScoreEqualTo(Integer value) {
            addCriterion("ret_score =", value, "retScore");
            return (Criteria) this;
        }

        public Criteria andRetScoreNotEqualTo(Integer value) {
            addCriterion("ret_score <>", value, "retScore");
            return (Criteria) this;
        }

        public Criteria andRetScoreGreaterThan(Integer value) {
            addCriterion("ret_score >", value, "retScore");
            return (Criteria) this;
        }

        public Criteria andRetScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("ret_score >=", value, "retScore");
            return (Criteria) this;
        }

        public Criteria andRetScoreLessThan(Integer value) {
            addCriterion("ret_score <", value, "retScore");
            return (Criteria) this;
        }

        public Criteria andRetScoreLessThanOrEqualTo(Integer value) {
            addCriterion("ret_score <=", value, "retScore");
            return (Criteria) this;
        }

        public Criteria andRetScoreIn(List<Integer> values) {
            addCriterion("ret_score in", values, "retScore");
            return (Criteria) this;
        }

        public Criteria andRetScoreNotIn(List<Integer> values) {
            addCriterion("ret_score not in", values, "retScore");
            return (Criteria) this;
        }

        public Criteria andRetScoreBetween(Integer value1, Integer value2) {
            addCriterion("ret_score between", value1, value2, "retScore");
            return (Criteria) this;
        }

        public Criteria andRetScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("ret_score not between", value1, value2, "retScore");
            return (Criteria) this;
        }

        public Criteria andRvnuScoreIsNull() {
            addCriterion("rvnu_score is null");
            return (Criteria) this;
        }

        public Criteria andRvnuScoreIsNotNull() {
            addCriterion("rvnu_score is not null");
            return (Criteria) this;
        }

        public Criteria andRvnuScoreEqualTo(Integer value) {
            addCriterion("rvnu_score =", value, "rvnuScore");
            return (Criteria) this;
        }

        public Criteria andRvnuScoreNotEqualTo(Integer value) {
            addCriterion("rvnu_score <>", value, "rvnuScore");
            return (Criteria) this;
        }

        public Criteria andRvnuScoreGreaterThan(Integer value) {
            addCriterion("rvnu_score >", value, "rvnuScore");
            return (Criteria) this;
        }

        public Criteria andRvnuScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("rvnu_score >=", value, "rvnuScore");
            return (Criteria) this;
        }

        public Criteria andRvnuScoreLessThan(Integer value) {
            addCriterion("rvnu_score <", value, "rvnuScore");
            return (Criteria) this;
        }

        public Criteria andRvnuScoreLessThanOrEqualTo(Integer value) {
            addCriterion("rvnu_score <=", value, "rvnuScore");
            return (Criteria) this;
        }

        public Criteria andRvnuScoreIn(List<Integer> values) {
            addCriterion("rvnu_score in", values, "rvnuScore");
            return (Criteria) this;
        }

        public Criteria andRvnuScoreNotIn(List<Integer> values) {
            addCriterion("rvnu_score not in", values, "rvnuScore");
            return (Criteria) this;
        }

        public Criteria andRvnuScoreBetween(Integer value1, Integer value2) {
            addCriterion("rvnu_score between", value1, value2, "rvnuScore");
            return (Criteria) this;
        }

        public Criteria andRvnuScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("rvnu_score not between", value1, value2, "rvnuScore");
            return (Criteria) this;
        }

        public Criteria andComScoreIsNull() {
            addCriterion("com_score is null");
            return (Criteria) this;
        }

        public Criteria andComScoreIsNotNull() {
            addCriterion("com_score is not null");
            return (Criteria) this;
        }

        public Criteria andComScoreEqualTo(Integer value) {
            addCriterion("com_score =", value, "comScore");
            return (Criteria) this;
        }

        public Criteria andComScoreNotEqualTo(Integer value) {
            addCriterion("com_score <>", value, "comScore");
            return (Criteria) this;
        }

        public Criteria andComScoreGreaterThan(Integer value) {
            addCriterion("com_score >", value, "comScore");
            return (Criteria) this;
        }

        public Criteria andComScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("com_score >=", value, "comScore");
            return (Criteria) this;
        }

        public Criteria andComScoreLessThan(Integer value) {
            addCriterion("com_score <", value, "comScore");
            return (Criteria) this;
        }

        public Criteria andComScoreLessThanOrEqualTo(Integer value) {
            addCriterion("com_score <=", value, "comScore");
            return (Criteria) this;
        }

        public Criteria andComScoreIn(List<Integer> values) {
            addCriterion("com_score in", values, "comScore");
            return (Criteria) this;
        }

        public Criteria andComScoreNotIn(List<Integer> values) {
            addCriterion("com_score not in", values, "comScore");
            return (Criteria) this;
        }

        public Criteria andComScoreBetween(Integer value1, Integer value2) {
            addCriterion("com_score between", value1, value2, "comScore");
            return (Criteria) this;
        }

        public Criteria andComScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("com_score not between", value1, value2, "comScore");
            return (Criteria) this;
        }

        public Criteria andDataTotalScoreIsNull() {
            addCriterion("data_total_score is null");
            return (Criteria) this;
        }

        public Criteria andDataTotalScoreIsNotNull() {
            addCriterion("data_total_score is not null");
            return (Criteria) this;
        }

        public Criteria andDataTotalScoreEqualTo(Integer value) {
            addCriterion("data_total_score =", value, "dataTotalScore");
            return (Criteria) this;
        }

        public Criteria andDataTotalScoreNotEqualTo(Integer value) {
            addCriterion("data_total_score <>", value, "dataTotalScore");
            return (Criteria) this;
        }

        public Criteria andDataTotalScoreGreaterThan(Integer value) {
            addCriterion("data_total_score >", value, "dataTotalScore");
            return (Criteria) this;
        }

        public Criteria andDataTotalScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("data_total_score >=", value, "dataTotalScore");
            return (Criteria) this;
        }

        public Criteria andDataTotalScoreLessThan(Integer value) {
            addCriterion("data_total_score <", value, "dataTotalScore");
            return (Criteria) this;
        }

        public Criteria andDataTotalScoreLessThanOrEqualTo(Integer value) {
            addCriterion("data_total_score <=", value, "dataTotalScore");
            return (Criteria) this;
        }

        public Criteria andDataTotalScoreIn(List<Integer> values) {
            addCriterion("data_total_score in", values, "dataTotalScore");
            return (Criteria) this;
        }

        public Criteria andDataTotalScoreNotIn(List<Integer> values) {
            addCriterion("data_total_score not in", values, "dataTotalScore");
            return (Criteria) this;
        }

        public Criteria andDataTotalScoreBetween(Integer value1, Integer value2) {
            addCriterion("data_total_score between", value1, value2, "dataTotalScore");
            return (Criteria) this;
        }

        public Criteria andDataTotalScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("data_total_score not between", value1, value2, "dataTotalScore");
            return (Criteria) this;
        }

        public Criteria andDataCmpStarLvlIsNull() {
            addCriterion("data_cmp_star_lvl is null");
            return (Criteria) this;
        }

        public Criteria andDataCmpStarLvlIsNotNull() {
            addCriterion("data_cmp_star_lvl is not null");
            return (Criteria) this;
        }

        public Criteria andDataCmpStarLvlEqualTo(Integer value) {
            addCriterion("data_cmp_star_lvl =", value, "dataCmpStarLvl");
            return (Criteria) this;
        }

        public Criteria andDataCmpStarLvlNotEqualTo(Integer value) {
            addCriterion("data_cmp_star_lvl <>", value, "dataCmpStarLvl");
            return (Criteria) this;
        }

        public Criteria andDataCmpStarLvlGreaterThan(Integer value) {
            addCriterion("data_cmp_star_lvl >", value, "dataCmpStarLvl");
            return (Criteria) this;
        }

        public Criteria andDataCmpStarLvlGreaterThanOrEqualTo(Integer value) {
            addCriterion("data_cmp_star_lvl >=", value, "dataCmpStarLvl");
            return (Criteria) this;
        }

        public Criteria andDataCmpStarLvlLessThan(Integer value) {
            addCriterion("data_cmp_star_lvl <", value, "dataCmpStarLvl");
            return (Criteria) this;
        }

        public Criteria andDataCmpStarLvlLessThanOrEqualTo(Integer value) {
            addCriterion("data_cmp_star_lvl <=", value, "dataCmpStarLvl");
            return (Criteria) this;
        }

        public Criteria andDataCmpStarLvlIn(List<Integer> values) {
            addCriterion("data_cmp_star_lvl in", values, "dataCmpStarLvl");
            return (Criteria) this;
        }

        public Criteria andDataCmpStarLvlNotIn(List<Integer> values) {
            addCriterion("data_cmp_star_lvl not in", values, "dataCmpStarLvl");
            return (Criteria) this;
        }

        public Criteria andDataCmpStarLvlBetween(Integer value1, Integer value2) {
            addCriterion("data_cmp_star_lvl between", value1, value2, "dataCmpStarLvl");
            return (Criteria) this;
        }

        public Criteria andDataCmpStarLvlNotBetween(Integer value1, Integer value2) {
            addCriterion("data_cmp_star_lvl not between", value1, value2, "dataCmpStarLvl");
            return (Criteria) this;
        }

        public Criteria andCmpStarLvlIsNull() {
            addCriterion("cmp_star_lvl is null");
            return (Criteria) this;
        }

        public Criteria andCmpStarLvlIsNotNull() {
            addCriterion("cmp_star_lvl is not null");
            return (Criteria) this;
        }

        public Criteria andCmpStarLvlEqualTo(Integer value) {
            addCriterion("cmp_star_lvl =", value, "cmpStarLvl");
            return (Criteria) this;
        }

        public Criteria andCmpStarLvlNotEqualTo(Integer value) {
            addCriterion("cmp_star_lvl <>", value, "cmpStarLvl");
            return (Criteria) this;
        }

        public Criteria andCmpStarLvlGreaterThan(Integer value) {
            addCriterion("cmp_star_lvl >", value, "cmpStarLvl");
            return (Criteria) this;
        }

        public Criteria andCmpStarLvlGreaterThanOrEqualTo(Integer value) {
            addCriterion("cmp_star_lvl >=", value, "cmpStarLvl");
            return (Criteria) this;
        }

        public Criteria andCmpStarLvlLessThan(Integer value) {
            addCriterion("cmp_star_lvl <", value, "cmpStarLvl");
            return (Criteria) this;
        }

        public Criteria andCmpStarLvlLessThanOrEqualTo(Integer value) {
            addCriterion("cmp_star_lvl <=", value, "cmpStarLvl");
            return (Criteria) this;
        }

        public Criteria andCmpStarLvlIn(List<Integer> values) {
            addCriterion("cmp_star_lvl in", values, "cmpStarLvl");
            return (Criteria) this;
        }

        public Criteria andCmpStarLvlNotIn(List<Integer> values) {
            addCriterion("cmp_star_lvl not in", values, "cmpStarLvl");
            return (Criteria) this;
        }

        public Criteria andCmpStarLvlBetween(Integer value1, Integer value2) {
            addCriterion("cmp_star_lvl between", value1, value2, "cmpStarLvl");
            return (Criteria) this;
        }

        public Criteria andCmpStarLvlNotBetween(Integer value1, Integer value2) {
            addCriterion("cmp_star_lvl not between", value1, value2, "cmpStarLvl");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumScoreIsNull() {
            addCriterion("new_aid_rat_3_4_aid_num_score is null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumScoreIsNotNull() {
            addCriterion("new_aid_rat_3_4_aid_num_score is not null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumScoreEqualTo(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_score =", value, "newAidRat34AidNumScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumScoreNotEqualTo(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_score <>", value, "newAidRat34AidNumScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumScoreGreaterThan(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_score >", value, "newAidRat34AidNumScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_score >=", value, "newAidRat34AidNumScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumScoreLessThan(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_score <", value, "newAidRat34AidNumScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumScoreLessThanOrEqualTo(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_score <=", value, "newAidRat34AidNumScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumScoreIn(List<Integer> values) {
            addCriterion("new_aid_rat_3_4_aid_num_score in", values, "newAidRat34AidNumScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumScoreNotIn(List<Integer> values) {
            addCriterion("new_aid_rat_3_4_aid_num_score not in", values, "newAidRat34AidNumScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumScoreBetween(Integer value1, Integer value2) {
            addCriterion("new_aid_rat_3_4_aid_num_score between", value1, value2, "newAidRat34AidNumScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("new_aid_rat_3_4_aid_num_score not between", value1, value2, "newAidRat34AidNumScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumScoreIsNull() {
            addCriterion("upgrd_high_aid_num_score is null");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumScoreIsNotNull() {
            addCriterion("upgrd_high_aid_num_score is not null");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumScoreEqualTo(Integer value) {
            addCriterion("upgrd_high_aid_num_score =", value, "upgrdHighAidNumScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumScoreNotEqualTo(Integer value) {
            addCriterion("upgrd_high_aid_num_score <>", value, "upgrdHighAidNumScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumScoreGreaterThan(Integer value) {
            addCriterion("upgrd_high_aid_num_score >", value, "upgrdHighAidNumScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("upgrd_high_aid_num_score >=", value, "upgrdHighAidNumScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumScoreLessThan(Integer value) {
            addCriterion("upgrd_high_aid_num_score <", value, "upgrdHighAidNumScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumScoreLessThanOrEqualTo(Integer value) {
            addCriterion("upgrd_high_aid_num_score <=", value, "upgrdHighAidNumScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumScoreIn(List<Integer> values) {
            addCriterion("upgrd_high_aid_num_score in", values, "upgrdHighAidNumScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumScoreNotIn(List<Integer> values) {
            addCriterion("upgrd_high_aid_num_score not in", values, "upgrdHighAidNumScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumScoreBetween(Integer value1, Integer value2) {
            addCriterion("upgrd_high_aid_num_score between", value1, value2, "upgrdHighAidNumScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("upgrd_high_aid_num_score not between", value1, value2, "upgrdHighAidNumScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumScoreIsNull() {
            addCriterion("upgrd_waist_aid_num_score is null");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumScoreIsNotNull() {
            addCriterion("upgrd_waist_aid_num_score is not null");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumScoreEqualTo(Integer value) {
            addCriterion("upgrd_waist_aid_num_score =", value, "upgrdWaistAidNumScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumScoreNotEqualTo(Integer value) {
            addCriterion("upgrd_waist_aid_num_score <>", value, "upgrdWaistAidNumScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumScoreGreaterThan(Integer value) {
            addCriterion("upgrd_waist_aid_num_score >", value, "upgrdWaistAidNumScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("upgrd_waist_aid_num_score >=", value, "upgrdWaistAidNumScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumScoreLessThan(Integer value) {
            addCriterion("upgrd_waist_aid_num_score <", value, "upgrdWaistAidNumScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumScoreLessThanOrEqualTo(Integer value) {
            addCriterion("upgrd_waist_aid_num_score <=", value, "upgrdWaistAidNumScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumScoreIn(List<Integer> values) {
            addCriterion("upgrd_waist_aid_num_score in", values, "upgrdWaistAidNumScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumScoreNotIn(List<Integer> values) {
            addCriterion("upgrd_waist_aid_num_score not in", values, "upgrdWaistAidNumScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumScoreBetween(Integer value1, Integer value2) {
            addCriterion("upgrd_waist_aid_num_score between", value1, value2, "upgrdWaistAidNumScore");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("upgrd_waist_aid_num_score not between", value1, value2, "upgrdWaistAidNumScore");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumScoreIsNull() {
            addCriterion("ext_golden_aid_num_score is null");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumScoreIsNotNull() {
            addCriterion("ext_golden_aid_num_score is not null");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumScoreEqualTo(Integer value) {
            addCriterion("ext_golden_aid_num_score =", value, "extGoldenAidNumScore");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumScoreNotEqualTo(Integer value) {
            addCriterion("ext_golden_aid_num_score <>", value, "extGoldenAidNumScore");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumScoreGreaterThan(Integer value) {
            addCriterion("ext_golden_aid_num_score >", value, "extGoldenAidNumScore");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("ext_golden_aid_num_score >=", value, "extGoldenAidNumScore");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumScoreLessThan(Integer value) {
            addCriterion("ext_golden_aid_num_score <", value, "extGoldenAidNumScore");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumScoreLessThanOrEqualTo(Integer value) {
            addCriterion("ext_golden_aid_num_score <=", value, "extGoldenAidNumScore");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumScoreIn(List<Integer> values) {
            addCriterion("ext_golden_aid_num_score in", values, "extGoldenAidNumScore");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumScoreNotIn(List<Integer> values) {
            addCriterion("ext_golden_aid_num_score not in", values, "extGoldenAidNumScore");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumScoreBetween(Integer value1, Integer value2) {
            addCriterion("ext_golden_aid_num_score between", value1, value2, "extGoldenAidNumScore");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("ext_golden_aid_num_score not between", value1, value2, "extGoldenAidNumScore");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateScoreIsNull() {
            addCriterion("actv_prod_pay_amt_rate_score is null");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateScoreIsNotNull() {
            addCriterion("actv_prod_pay_amt_rate_score is not null");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateScoreEqualTo(Integer value) {
            addCriterion("actv_prod_pay_amt_rate_score =", value, "actvProdPayAmtRateScore");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateScoreNotEqualTo(Integer value) {
            addCriterion("actv_prod_pay_amt_rate_score <>", value, "actvProdPayAmtRateScore");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateScoreGreaterThan(Integer value) {
            addCriterion("actv_prod_pay_amt_rate_score >", value, "actvProdPayAmtRateScore");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("actv_prod_pay_amt_rate_score >=", value, "actvProdPayAmtRateScore");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateScoreLessThan(Integer value) {
            addCriterion("actv_prod_pay_amt_rate_score <", value, "actvProdPayAmtRateScore");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateScoreLessThanOrEqualTo(Integer value) {
            addCriterion("actv_prod_pay_amt_rate_score <=", value, "actvProdPayAmtRateScore");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateScoreIn(List<Integer> values) {
            addCriterion("actv_prod_pay_amt_rate_score in", values, "actvProdPayAmtRateScore");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateScoreNotIn(List<Integer> values) {
            addCriterion("actv_prod_pay_amt_rate_score not in", values, "actvProdPayAmtRateScore");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateScoreBetween(Integer value1, Integer value2) {
            addCriterion("actv_prod_pay_amt_rate_score between", value1, value2, "actvProdPayAmtRateScore");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("actv_prod_pay_amt_rate_score not between", value1, value2, "actvProdPayAmtRateScore");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateScoreIsNull() {
            addCriterion("actv_incm_amt_rate_score is null");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateScoreIsNotNull() {
            addCriterion("actv_incm_amt_rate_score is not null");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateScoreEqualTo(Integer value) {
            addCriterion("actv_incm_amt_rate_score =", value, "actvIncmAmtRateScore");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateScoreNotEqualTo(Integer value) {
            addCriterion("actv_incm_amt_rate_score <>", value, "actvIncmAmtRateScore");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateScoreGreaterThan(Integer value) {
            addCriterion("actv_incm_amt_rate_score >", value, "actvIncmAmtRateScore");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("actv_incm_amt_rate_score >=", value, "actvIncmAmtRateScore");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateScoreLessThan(Integer value) {
            addCriterion("actv_incm_amt_rate_score <", value, "actvIncmAmtRateScore");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateScoreLessThanOrEqualTo(Integer value) {
            addCriterion("actv_incm_amt_rate_score <=", value, "actvIncmAmtRateScore");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateScoreIn(List<Integer> values) {
            addCriterion("actv_incm_amt_rate_score in", values, "actvIncmAmtRateScore");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateScoreNotIn(List<Integer> values) {
            addCriterion("actv_incm_amt_rate_score not in", values, "actvIncmAmtRateScore");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateScoreBetween(Integer value1, Integer value2) {
            addCriterion("actv_incm_amt_rate_score between", value1, value2, "actvIncmAmtRateScore");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("actv_incm_amt_rate_score not between", value1, value2, "actvIncmAmtRateScore");
            return (Criteria) this;
        }

        public Criteria andTaskTotalScoreIsNull() {
            addCriterion("task_total_score is null");
            return (Criteria) this;
        }

        public Criteria andTaskTotalScoreIsNotNull() {
            addCriterion("task_total_score is not null");
            return (Criteria) this;
        }

        public Criteria andTaskTotalScoreEqualTo(Integer value) {
            addCriterion("task_total_score =", value, "taskTotalScore");
            return (Criteria) this;
        }

        public Criteria andTaskTotalScoreNotEqualTo(Integer value) {
            addCriterion("task_total_score <>", value, "taskTotalScore");
            return (Criteria) this;
        }

        public Criteria andTaskTotalScoreGreaterThan(Integer value) {
            addCriterion("task_total_score >", value, "taskTotalScore");
            return (Criteria) this;
        }

        public Criteria andTaskTotalScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("task_total_score >=", value, "taskTotalScore");
            return (Criteria) this;
        }

        public Criteria andTaskTotalScoreLessThan(Integer value) {
            addCriterion("task_total_score <", value, "taskTotalScore");
            return (Criteria) this;
        }

        public Criteria andTaskTotalScoreLessThanOrEqualTo(Integer value) {
            addCriterion("task_total_score <=", value, "taskTotalScore");
            return (Criteria) this;
        }

        public Criteria andTaskTotalScoreIn(List<Integer> values) {
            addCriterion("task_total_score in", values, "taskTotalScore");
            return (Criteria) this;
        }

        public Criteria andTaskTotalScoreNotIn(List<Integer> values) {
            addCriterion("task_total_score not in", values, "taskTotalScore");
            return (Criteria) this;
        }

        public Criteria andTaskTotalScoreBetween(Integer value1, Integer value2) {
            addCriterion("task_total_score between", value1, value2, "taskTotalScore");
            return (Criteria) this;
        }

        public Criteria andTaskTotalScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("task_total_score not between", value1, value2, "taskTotalScore");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumItgIsNull() {
            addCriterion("new_aid_rat_3_4_aid_num_itg is null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumItgIsNotNull() {
            addCriterion("new_aid_rat_3_4_aid_num_itg is not null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumItgEqualTo(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_itg =", value, "newAidRat34AidNumItg");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumItgNotEqualTo(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_itg <>", value, "newAidRat34AidNumItg");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumItgGreaterThan(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_itg >", value, "newAidRat34AidNumItg");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumItgGreaterThanOrEqualTo(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_itg >=", value, "newAidRat34AidNumItg");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumItgLessThan(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_itg <", value, "newAidRat34AidNumItg");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumItgLessThanOrEqualTo(Integer value) {
            addCriterion("new_aid_rat_3_4_aid_num_itg <=", value, "newAidRat34AidNumItg");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumItgIn(List<Integer> values) {
            addCriterion("new_aid_rat_3_4_aid_num_itg in", values, "newAidRat34AidNumItg");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumItgNotIn(List<Integer> values) {
            addCriterion("new_aid_rat_3_4_aid_num_itg not in", values, "newAidRat34AidNumItg");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumItgBetween(Integer value1, Integer value2) {
            addCriterion("new_aid_rat_3_4_aid_num_itg between", value1, value2, "newAidRat34AidNumItg");
            return (Criteria) this;
        }

        public Criteria andNewAidRat34AidNumItgNotBetween(Integer value1, Integer value2) {
            addCriterion("new_aid_rat_3_4_aid_num_itg not between", value1, value2, "newAidRat34AidNumItg");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumItgIsNull() {
            addCriterion("upgrd_high_aid_num_itg is null");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumItgIsNotNull() {
            addCriterion("upgrd_high_aid_num_itg is not null");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumItgEqualTo(Integer value) {
            addCriterion("upgrd_high_aid_num_itg =", value, "upgrdHighAidNumItg");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumItgNotEqualTo(Integer value) {
            addCriterion("upgrd_high_aid_num_itg <>", value, "upgrdHighAidNumItg");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumItgGreaterThan(Integer value) {
            addCriterion("upgrd_high_aid_num_itg >", value, "upgrdHighAidNumItg");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumItgGreaterThanOrEqualTo(Integer value) {
            addCriterion("upgrd_high_aid_num_itg >=", value, "upgrdHighAidNumItg");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumItgLessThan(Integer value) {
            addCriterion("upgrd_high_aid_num_itg <", value, "upgrdHighAidNumItg");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumItgLessThanOrEqualTo(Integer value) {
            addCriterion("upgrd_high_aid_num_itg <=", value, "upgrdHighAidNumItg");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumItgIn(List<Integer> values) {
            addCriterion("upgrd_high_aid_num_itg in", values, "upgrdHighAidNumItg");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumItgNotIn(List<Integer> values) {
            addCriterion("upgrd_high_aid_num_itg not in", values, "upgrdHighAidNumItg");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumItgBetween(Integer value1, Integer value2) {
            addCriterion("upgrd_high_aid_num_itg between", value1, value2, "upgrdHighAidNumItg");
            return (Criteria) this;
        }

        public Criteria andUpgrdHighAidNumItgNotBetween(Integer value1, Integer value2) {
            addCriterion("upgrd_high_aid_num_itg not between", value1, value2, "upgrdHighAidNumItg");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumItgIsNull() {
            addCriterion("upgrd_waist_aid_num_itg is null");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumItgIsNotNull() {
            addCriterion("upgrd_waist_aid_num_itg is not null");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumItgEqualTo(Integer value) {
            addCriterion("upgrd_waist_aid_num_itg =", value, "upgrdWaistAidNumItg");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumItgNotEqualTo(Integer value) {
            addCriterion("upgrd_waist_aid_num_itg <>", value, "upgrdWaistAidNumItg");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumItgGreaterThan(Integer value) {
            addCriterion("upgrd_waist_aid_num_itg >", value, "upgrdWaistAidNumItg");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumItgGreaterThanOrEqualTo(Integer value) {
            addCriterion("upgrd_waist_aid_num_itg >=", value, "upgrdWaistAidNumItg");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumItgLessThan(Integer value) {
            addCriterion("upgrd_waist_aid_num_itg <", value, "upgrdWaistAidNumItg");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumItgLessThanOrEqualTo(Integer value) {
            addCriterion("upgrd_waist_aid_num_itg <=", value, "upgrdWaistAidNumItg");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumItgIn(List<Integer> values) {
            addCriterion("upgrd_waist_aid_num_itg in", values, "upgrdWaistAidNumItg");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumItgNotIn(List<Integer> values) {
            addCriterion("upgrd_waist_aid_num_itg not in", values, "upgrdWaistAidNumItg");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumItgBetween(Integer value1, Integer value2) {
            addCriterion("upgrd_waist_aid_num_itg between", value1, value2, "upgrdWaistAidNumItg");
            return (Criteria) this;
        }

        public Criteria andUpgrdWaistAidNumItgNotBetween(Integer value1, Integer value2) {
            addCriterion("upgrd_waist_aid_num_itg not between", value1, value2, "upgrdWaistAidNumItg");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumItgIsNull() {
            addCriterion("ext_golden_aid_num_itg is null");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumItgIsNotNull() {
            addCriterion("ext_golden_aid_num_itg is not null");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumItgEqualTo(Integer value) {
            addCriterion("ext_golden_aid_num_itg =", value, "extGoldenAidNumItg");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumItgNotEqualTo(Integer value) {
            addCriterion("ext_golden_aid_num_itg <>", value, "extGoldenAidNumItg");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumItgGreaterThan(Integer value) {
            addCriterion("ext_golden_aid_num_itg >", value, "extGoldenAidNumItg");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumItgGreaterThanOrEqualTo(Integer value) {
            addCriterion("ext_golden_aid_num_itg >=", value, "extGoldenAidNumItg");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumItgLessThan(Integer value) {
            addCriterion("ext_golden_aid_num_itg <", value, "extGoldenAidNumItg");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumItgLessThanOrEqualTo(Integer value) {
            addCriterion("ext_golden_aid_num_itg <=", value, "extGoldenAidNumItg");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumItgIn(List<Integer> values) {
            addCriterion("ext_golden_aid_num_itg in", values, "extGoldenAidNumItg");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumItgNotIn(List<Integer> values) {
            addCriterion("ext_golden_aid_num_itg not in", values, "extGoldenAidNumItg");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumItgBetween(Integer value1, Integer value2) {
            addCriterion("ext_golden_aid_num_itg between", value1, value2, "extGoldenAidNumItg");
            return (Criteria) this;
        }

        public Criteria andExtGoldenAidNumItgNotBetween(Integer value1, Integer value2) {
            addCriterion("ext_golden_aid_num_itg not between", value1, value2, "extGoldenAidNumItg");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateItgIsNull() {
            addCriterion("actv_prod_pay_amt_rate_itg is null");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateItgIsNotNull() {
            addCriterion("actv_prod_pay_amt_rate_itg is not null");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateItgEqualTo(Integer value) {
            addCriterion("actv_prod_pay_amt_rate_itg =", value, "actvProdPayAmtRateItg");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateItgNotEqualTo(Integer value) {
            addCriterion("actv_prod_pay_amt_rate_itg <>", value, "actvProdPayAmtRateItg");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateItgGreaterThan(Integer value) {
            addCriterion("actv_prod_pay_amt_rate_itg >", value, "actvProdPayAmtRateItg");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateItgGreaterThanOrEqualTo(Integer value) {
            addCriterion("actv_prod_pay_amt_rate_itg >=", value, "actvProdPayAmtRateItg");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateItgLessThan(Integer value) {
            addCriterion("actv_prod_pay_amt_rate_itg <", value, "actvProdPayAmtRateItg");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateItgLessThanOrEqualTo(Integer value) {
            addCriterion("actv_prod_pay_amt_rate_itg <=", value, "actvProdPayAmtRateItg");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateItgIn(List<Integer> values) {
            addCriterion("actv_prod_pay_amt_rate_itg in", values, "actvProdPayAmtRateItg");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateItgNotIn(List<Integer> values) {
            addCriterion("actv_prod_pay_amt_rate_itg not in", values, "actvProdPayAmtRateItg");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateItgBetween(Integer value1, Integer value2) {
            addCriterion("actv_prod_pay_amt_rate_itg between", value1, value2, "actvProdPayAmtRateItg");
            return (Criteria) this;
        }

        public Criteria andActvProdPayAmtRateItgNotBetween(Integer value1, Integer value2) {
            addCriterion("actv_prod_pay_amt_rate_itg not between", value1, value2, "actvProdPayAmtRateItg");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateItgIsNull() {
            addCriterion("actv_incm_amt_rate_itg is null");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateItgIsNotNull() {
            addCriterion("actv_incm_amt_rate_itg is not null");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateItgEqualTo(Integer value) {
            addCriterion("actv_incm_amt_rate_itg =", value, "actvIncmAmtRateItg");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateItgNotEqualTo(Integer value) {
            addCriterion("actv_incm_amt_rate_itg <>", value, "actvIncmAmtRateItg");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateItgGreaterThan(Integer value) {
            addCriterion("actv_incm_amt_rate_itg >", value, "actvIncmAmtRateItg");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateItgGreaterThanOrEqualTo(Integer value) {
            addCriterion("actv_incm_amt_rate_itg >=", value, "actvIncmAmtRateItg");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateItgLessThan(Integer value) {
            addCriterion("actv_incm_amt_rate_itg <", value, "actvIncmAmtRateItg");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateItgLessThanOrEqualTo(Integer value) {
            addCriterion("actv_incm_amt_rate_itg <=", value, "actvIncmAmtRateItg");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateItgIn(List<Integer> values) {
            addCriterion("actv_incm_amt_rate_itg in", values, "actvIncmAmtRateItg");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateItgNotIn(List<Integer> values) {
            addCriterion("actv_incm_amt_rate_itg not in", values, "actvIncmAmtRateItg");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateItgBetween(Integer value1, Integer value2) {
            addCriterion("actv_incm_amt_rate_itg between", value1, value2, "actvIncmAmtRateItg");
            return (Criteria) this;
        }

        public Criteria andActvIncmAmtRateItgNotBetween(Integer value1, Integer value2) {
            addCriterion("actv_incm_amt_rate_itg not between", value1, value2, "actvIncmAmtRateItg");
            return (Criteria) this;
        }

        public Criteria andTaskTotalItgIsNull() {
            addCriterion("task_total_itg is null");
            return (Criteria) this;
        }

        public Criteria andTaskTotalItgIsNotNull() {
            addCriterion("task_total_itg is not null");
            return (Criteria) this;
        }

        public Criteria andTaskTotalItgEqualTo(Integer value) {
            addCriterion("task_total_itg =", value, "taskTotalItg");
            return (Criteria) this;
        }

        public Criteria andTaskTotalItgNotEqualTo(Integer value) {
            addCriterion("task_total_itg <>", value, "taskTotalItg");
            return (Criteria) this;
        }

        public Criteria andTaskTotalItgGreaterThan(Integer value) {
            addCriterion("task_total_itg >", value, "taskTotalItg");
            return (Criteria) this;
        }

        public Criteria andTaskTotalItgGreaterThanOrEqualTo(Integer value) {
            addCriterion("task_total_itg >=", value, "taskTotalItg");
            return (Criteria) this;
        }

        public Criteria andTaskTotalItgLessThan(Integer value) {
            addCriterion("task_total_itg <", value, "taskTotalItg");
            return (Criteria) this;
        }

        public Criteria andTaskTotalItgLessThanOrEqualTo(Integer value) {
            addCriterion("task_total_itg <=", value, "taskTotalItg");
            return (Criteria) this;
        }

        public Criteria andTaskTotalItgIn(List<Integer> values) {
            addCriterion("task_total_itg in", values, "taskTotalItg");
            return (Criteria) this;
        }

        public Criteria andTaskTotalItgNotIn(List<Integer> values) {
            addCriterion("task_total_itg not in", values, "taskTotalItg");
            return (Criteria) this;
        }

        public Criteria andTaskTotalItgBetween(Integer value1, Integer value2) {
            addCriterion("task_total_itg between", value1, value2, "taskTotalItg");
            return (Criteria) this;
        }

        public Criteria andTaskTotalItgNotBetween(Integer value1, Integer value2) {
            addCriterion("task_total_itg not between", value1, value2, "taskTotalItg");
            return (Criteria) this;
        }

        public Criteria andTotalScoreIsNull() {
            addCriterion("total_score is null");
            return (Criteria) this;
        }

        public Criteria andTotalScoreIsNotNull() {
            addCriterion("total_score is not null");
            return (Criteria) this;
        }

        public Criteria andTotalScoreEqualTo(Integer value) {
            addCriterion("total_score =", value, "totalScore");
            return (Criteria) this;
        }

        public Criteria andTotalScoreNotEqualTo(Integer value) {
            addCriterion("total_score <>", value, "totalScore");
            return (Criteria) this;
        }

        public Criteria andTotalScoreGreaterThan(Integer value) {
            addCriterion("total_score >", value, "totalScore");
            return (Criteria) this;
        }

        public Criteria andTotalScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("total_score >=", value, "totalScore");
            return (Criteria) this;
        }

        public Criteria andTotalScoreLessThan(Integer value) {
            addCriterion("total_score <", value, "totalScore");
            return (Criteria) this;
        }

        public Criteria andTotalScoreLessThanOrEqualTo(Integer value) {
            addCriterion("total_score <=", value, "totalScore");
            return (Criteria) this;
        }

        public Criteria andTotalScoreIn(List<Integer> values) {
            addCriterion("total_score in", values, "totalScore");
            return (Criteria) this;
        }

        public Criteria andTotalScoreNotIn(List<Integer> values) {
            addCriterion("total_score not in", values, "totalScore");
            return (Criteria) this;
        }

        public Criteria andTotalScoreBetween(Integer value1, Integer value2) {
            addCriterion("total_score between", value1, value2, "totalScore");
            return (Criteria) this;
        }

        public Criteria andTotalScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("total_score not between", value1, value2, "totalScore");
            return (Criteria) this;
        }

        public Criteria andDtIsNull() {
            addCriterion("dt is null");
            return (Criteria) this;
        }

        public Criteria andDtIsNotNull() {
            addCriterion("dt is not null");
            return (Criteria) this;
        }

        public Criteria andDtEqualTo(Date value) {
            addCriterionForJDBCDate("dt =", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotEqualTo(Date value) {
            addCriterionForJDBCDate("dt <>", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtGreaterThan(Date value) {
            addCriterionForJDBCDate("dt >", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("dt >=", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtLessThan(Date value) {
            addCriterionForJDBCDate("dt <", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("dt <=", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtIn(List<Date> values) {
            addCriterionForJDBCDate("dt in", values, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotIn(List<Date> values) {
            addCriterionForJDBCDate("dt not in", values, "dt");
            return (Criteria) this;
        }

        public Criteria andDtBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("dt between", value1, value2, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("dt not between", value1, value2, "dt");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mIsNull() {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m is null");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mIsNotNull() {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m is not null");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mEqualTo(BigDecimal value) {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m =", value, "avgNewAidRat34AidNum3m");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mNotEqualTo(BigDecimal value) {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m <>", value, "avgNewAidRat34AidNum3m");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mGreaterThan(BigDecimal value) {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m >", value, "avgNewAidRat34AidNum3m");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m >=", value, "avgNewAidRat34AidNum3m");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mLessThan(BigDecimal value) {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m <", value, "avgNewAidRat34AidNum3m");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mLessThanOrEqualTo(BigDecimal value) {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m <=", value, "avgNewAidRat34AidNum3m");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mIn(List<BigDecimal> values) {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m in", values, "avgNewAidRat34AidNum3m");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mNotIn(List<BigDecimal> values) {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m not in", values, "avgNewAidRat34AidNum3m");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m between", value1, value2, "avgNewAidRat34AidNum3m");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m not between", value1, value2, "avgNewAidRat34AidNum3m");
            return (Criteria) this;
        }

        public Criteria andNewAidRat3AidNumIsNull() {
            addCriterion("new_aid_rat_3_aid_num is null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat3AidNumIsNotNull() {
            addCriterion("new_aid_rat_3_aid_num is not null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat3AidNumEqualTo(Long value) {
            addCriterion("new_aid_rat_3_aid_num =", value, "newAidRat3AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat3AidNumNotEqualTo(Long value) {
            addCriterion("new_aid_rat_3_aid_num <>", value, "newAidRat3AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat3AidNumGreaterThan(Long value) {
            addCriterion("new_aid_rat_3_aid_num >", value, "newAidRat3AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat3AidNumGreaterThanOrEqualTo(Long value) {
            addCriterion("new_aid_rat_3_aid_num >=", value, "newAidRat3AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat3AidNumLessThan(Long value) {
            addCriterion("new_aid_rat_3_aid_num <", value, "newAidRat3AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat3AidNumLessThanOrEqualTo(Long value) {
            addCriterion("new_aid_rat_3_aid_num <=", value, "newAidRat3AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat3AidNumIn(List<Long> values) {
            addCriterion("new_aid_rat_3_aid_num in", values, "newAidRat3AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat3AidNumNotIn(List<Long> values) {
            addCriterion("new_aid_rat_3_aid_num not in", values, "newAidRat3AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat3AidNumBetween(Long value1, Long value2) {
            addCriterion("new_aid_rat_3_aid_num between", value1, value2, "newAidRat3AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat3AidNumNotBetween(Long value1, Long value2) {
            addCriterion("new_aid_rat_3_aid_num not between", value1, value2, "newAidRat3AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat4AidNumIsNull() {
            addCriterion("new_aid_rat_4_aid_num is null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat4AidNumIsNotNull() {
            addCriterion("new_aid_rat_4_aid_num is not null");
            return (Criteria) this;
        }

        public Criteria andNewAidRat4AidNumEqualTo(Long value) {
            addCriterion("new_aid_rat_4_aid_num =", value, "newAidRat4AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat4AidNumNotEqualTo(Long value) {
            addCriterion("new_aid_rat_4_aid_num <>", value, "newAidRat4AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat4AidNumGreaterThan(Long value) {
            addCriterion("new_aid_rat_4_aid_num >", value, "newAidRat4AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat4AidNumGreaterThanOrEqualTo(Long value) {
            addCriterion("new_aid_rat_4_aid_num >=", value, "newAidRat4AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat4AidNumLessThan(Long value) {
            addCriterion("new_aid_rat_4_aid_num <", value, "newAidRat4AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat4AidNumLessThanOrEqualTo(Long value) {
            addCriterion("new_aid_rat_4_aid_num <=", value, "newAidRat4AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat4AidNumIn(List<Long> values) {
            addCriterion("new_aid_rat_4_aid_num in", values, "newAidRat4AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat4AidNumNotIn(List<Long> values) {
            addCriterion("new_aid_rat_4_aid_num not in", values, "newAidRat4AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat4AidNumBetween(Long value1, Long value2) {
            addCriterion("new_aid_rat_4_aid_num between", value1, value2, "newAidRat4AidNum");
            return (Criteria) this;
        }

        public Criteria andNewAidRat4AidNumNotBetween(Long value1, Long value2) {
            addCriterion("new_aid_rat_4_aid_num not between", value1, value2, "newAidRat4AidNum");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mAimIsNull() {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m_aim is null");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mAimIsNotNull() {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m_aim is not null");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mAimEqualTo(Integer value) {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m_aim =", value, "avgNewAidRat34AidNum3mAim");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mAimNotEqualTo(Integer value) {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m_aim <>", value, "avgNewAidRat34AidNum3mAim");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mAimGreaterThan(Integer value) {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m_aim >", value, "avgNewAidRat34AidNum3mAim");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mAimGreaterThanOrEqualTo(Integer value) {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m_aim >=", value, "avgNewAidRat34AidNum3mAim");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mAimLessThan(Integer value) {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m_aim <", value, "avgNewAidRat34AidNum3mAim");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mAimLessThanOrEqualTo(Integer value) {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m_aim <=", value, "avgNewAidRat34AidNum3mAim");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mAimIn(List<Integer> values) {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m_aim in", values, "avgNewAidRat34AidNum3mAim");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mAimNotIn(List<Integer> values) {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m_aim not in", values, "avgNewAidRat34AidNum3mAim");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mAimBetween(Integer value1, Integer value2) {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m_aim between", value1, value2, "avgNewAidRat34AidNum3mAim");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mAimNotBetween(Integer value1, Integer value2) {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m_aim not between", value1, value2, "avgNewAidRat34AidNum3mAim");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mScoreIsNull() {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m_score is null");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mScoreIsNotNull() {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m_score is not null");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mScoreEqualTo(Integer value) {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m_score =", value, "avgNewAidRat34AidNum3mScore");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mScoreNotEqualTo(Integer value) {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m_score <>", value, "avgNewAidRat34AidNum3mScore");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mScoreGreaterThan(Integer value) {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m_score >", value, "avgNewAidRat34AidNum3mScore");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m_score >=", value, "avgNewAidRat34AidNum3mScore");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mScoreLessThan(Integer value) {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m_score <", value, "avgNewAidRat34AidNum3mScore");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mScoreLessThanOrEqualTo(Integer value) {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m_score <=", value, "avgNewAidRat34AidNum3mScore");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mScoreIn(List<Integer> values) {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m_score in", values, "avgNewAidRat34AidNum3mScore");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mScoreNotIn(List<Integer> values) {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m_score not in", values, "avgNewAidRat34AidNum3mScore");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mScoreBetween(Integer value1, Integer value2) {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m_score between", value1, value2, "avgNewAidRat34AidNum3mScore");
            return (Criteria) this;
        }

        public Criteria andAvgNewAidRat34AidNum3mScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("avg_new_aid_rat_3_4_aid_num_3m_score not between", value1, value2, "avgNewAidRat34AidNum3mScore");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mIsNull() {
            addCriterion("avg_upgrd_high_waist_aid_num_3m is null");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mIsNotNull() {
            addCriterion("avg_upgrd_high_waist_aid_num_3m is not null");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mEqualTo(BigDecimal value) {
            addCriterion("avg_upgrd_high_waist_aid_num_3m =", value, "avgUpgrdHighWaistAidNum3m");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mNotEqualTo(BigDecimal value) {
            addCriterion("avg_upgrd_high_waist_aid_num_3m <>", value, "avgUpgrdHighWaistAidNum3m");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mGreaterThan(BigDecimal value) {
            addCriterion("avg_upgrd_high_waist_aid_num_3m >", value, "avgUpgrdHighWaistAidNum3m");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("avg_upgrd_high_waist_aid_num_3m >=", value, "avgUpgrdHighWaistAidNum3m");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mLessThan(BigDecimal value) {
            addCriterion("avg_upgrd_high_waist_aid_num_3m <", value, "avgUpgrdHighWaistAidNum3m");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mLessThanOrEqualTo(BigDecimal value) {
            addCriterion("avg_upgrd_high_waist_aid_num_3m <=", value, "avgUpgrdHighWaistAidNum3m");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mIn(List<BigDecimal> values) {
            addCriterion("avg_upgrd_high_waist_aid_num_3m in", values, "avgUpgrdHighWaistAidNum3m");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mNotIn(List<BigDecimal> values) {
            addCriterion("avg_upgrd_high_waist_aid_num_3m not in", values, "avgUpgrdHighWaistAidNum3m");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("avg_upgrd_high_waist_aid_num_3m between", value1, value2, "avgUpgrdHighWaistAidNum3m");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("avg_upgrd_high_waist_aid_num_3m not between", value1, value2, "avgUpgrdHighWaistAidNum3m");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mAimIsNull() {
            addCriterion("avg_upgrd_high_waist_aid_num_3m_aim is null");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mAimIsNotNull() {
            addCriterion("avg_upgrd_high_waist_aid_num_3m_aim is not null");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mAimEqualTo(Integer value) {
            addCriterion("avg_upgrd_high_waist_aid_num_3m_aim =", value, "avgUpgrdHighWaistAidNum3mAim");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mAimNotEqualTo(Integer value) {
            addCriterion("avg_upgrd_high_waist_aid_num_3m_aim <>", value, "avgUpgrdHighWaistAidNum3mAim");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mAimGreaterThan(Integer value) {
            addCriterion("avg_upgrd_high_waist_aid_num_3m_aim >", value, "avgUpgrdHighWaistAidNum3mAim");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mAimGreaterThanOrEqualTo(Integer value) {
            addCriterion("avg_upgrd_high_waist_aid_num_3m_aim >=", value, "avgUpgrdHighWaistAidNum3mAim");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mAimLessThan(Integer value) {
            addCriterion("avg_upgrd_high_waist_aid_num_3m_aim <", value, "avgUpgrdHighWaistAidNum3mAim");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mAimLessThanOrEqualTo(Integer value) {
            addCriterion("avg_upgrd_high_waist_aid_num_3m_aim <=", value, "avgUpgrdHighWaistAidNum3mAim");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mAimIn(List<Integer> values) {
            addCriterion("avg_upgrd_high_waist_aid_num_3m_aim in", values, "avgUpgrdHighWaistAidNum3mAim");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mAimNotIn(List<Integer> values) {
            addCriterion("avg_upgrd_high_waist_aid_num_3m_aim not in", values, "avgUpgrdHighWaistAidNum3mAim");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mAimBetween(Integer value1, Integer value2) {
            addCriterion("avg_upgrd_high_waist_aid_num_3m_aim between", value1, value2, "avgUpgrdHighWaistAidNum3mAim");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mAimNotBetween(Integer value1, Integer value2) {
            addCriterion("avg_upgrd_high_waist_aid_num_3m_aim not between", value1, value2, "avgUpgrdHighWaistAidNum3mAim");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mScoreIsNull() {
            addCriterion("avg_upgrd_high_waist_aid_num_3m_score is null");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mScoreIsNotNull() {
            addCriterion("avg_upgrd_high_waist_aid_num_3m_score is not null");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mScoreEqualTo(Integer value) {
            addCriterion("avg_upgrd_high_waist_aid_num_3m_score =", value, "avgUpgrdHighWaistAidNum3mScore");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mScoreNotEqualTo(Integer value) {
            addCriterion("avg_upgrd_high_waist_aid_num_3m_score <>", value, "avgUpgrdHighWaistAidNum3mScore");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mScoreGreaterThan(Integer value) {
            addCriterion("avg_upgrd_high_waist_aid_num_3m_score >", value, "avgUpgrdHighWaistAidNum3mScore");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("avg_upgrd_high_waist_aid_num_3m_score >=", value, "avgUpgrdHighWaistAidNum3mScore");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mScoreLessThan(Integer value) {
            addCriterion("avg_upgrd_high_waist_aid_num_3m_score <", value, "avgUpgrdHighWaistAidNum3mScore");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mScoreLessThanOrEqualTo(Integer value) {
            addCriterion("avg_upgrd_high_waist_aid_num_3m_score <=", value, "avgUpgrdHighWaistAidNum3mScore");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mScoreIn(List<Integer> values) {
            addCriterion("avg_upgrd_high_waist_aid_num_3m_score in", values, "avgUpgrdHighWaistAidNum3mScore");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mScoreNotIn(List<Integer> values) {
            addCriterion("avg_upgrd_high_waist_aid_num_3m_score not in", values, "avgUpgrdHighWaistAidNum3mScore");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mScoreBetween(Integer value1, Integer value2) {
            addCriterion("avg_upgrd_high_waist_aid_num_3m_score between", value1, value2, "avgUpgrdHighWaistAidNum3mScore");
            return (Criteria) this;
        }

        public Criteria andAvgUpgrdHighWaistAidNum3mScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("avg_upgrd_high_waist_aid_num_3m_score not between", value1, value2, "avgUpgrdHighWaistAidNum3mScore");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateIsNull() {
            addCriterion("new_aid_incm_com_rate is null");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateIsNotNull() {
            addCriterion("new_aid_incm_com_rate is not null");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateEqualTo(BigDecimal value) {
            addCriterion("new_aid_incm_com_rate =", value, "newAidIncmComRate");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateNotEqualTo(BigDecimal value) {
            addCriterion("new_aid_incm_com_rate <>", value, "newAidIncmComRate");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateGreaterThan(BigDecimal value) {
            addCriterion("new_aid_incm_com_rate >", value, "newAidIncmComRate");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("new_aid_incm_com_rate >=", value, "newAidIncmComRate");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateLessThan(BigDecimal value) {
            addCriterion("new_aid_incm_com_rate <", value, "newAidIncmComRate");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("new_aid_incm_com_rate <=", value, "newAidIncmComRate");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateIn(List<BigDecimal> values) {
            addCriterion("new_aid_incm_com_rate in", values, "newAidIncmComRate");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateNotIn(List<BigDecimal> values) {
            addCriterion("new_aid_incm_com_rate not in", values, "newAidIncmComRate");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("new_aid_incm_com_rate between", value1, value2, "newAidIncmComRate");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("new_aid_incm_com_rate not between", value1, value2, "newAidIncmComRate");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateAimIsNull() {
            addCriterion("new_aid_incm_com_rate_aim is null");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateAimIsNotNull() {
            addCriterion("new_aid_incm_com_rate_aim is not null");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateAimEqualTo(BigDecimal value) {
            addCriterion("new_aid_incm_com_rate_aim =", value, "newAidIncmComRateAim");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateAimNotEqualTo(BigDecimal value) {
            addCriterion("new_aid_incm_com_rate_aim <>", value, "newAidIncmComRateAim");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateAimGreaterThan(BigDecimal value) {
            addCriterion("new_aid_incm_com_rate_aim >", value, "newAidIncmComRateAim");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateAimGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("new_aid_incm_com_rate_aim >=", value, "newAidIncmComRateAim");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateAimLessThan(BigDecimal value) {
            addCriterion("new_aid_incm_com_rate_aim <", value, "newAidIncmComRateAim");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateAimLessThanOrEqualTo(BigDecimal value) {
            addCriterion("new_aid_incm_com_rate_aim <=", value, "newAidIncmComRateAim");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateAimIn(List<BigDecimal> values) {
            addCriterion("new_aid_incm_com_rate_aim in", values, "newAidIncmComRateAim");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateAimNotIn(List<BigDecimal> values) {
            addCriterion("new_aid_incm_com_rate_aim not in", values, "newAidIncmComRateAim");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateAimBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("new_aid_incm_com_rate_aim between", value1, value2, "newAidIncmComRateAim");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateAimNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("new_aid_incm_com_rate_aim not between", value1, value2, "newAidIncmComRateAim");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateScoreIsNull() {
            addCriterion("new_aid_incm_com_rate_score is null");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateScoreIsNotNull() {
            addCriterion("new_aid_incm_com_rate_score is not null");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateScoreEqualTo(Integer value) {
            addCriterion("new_aid_incm_com_rate_score =", value, "newAidIncmComRateScore");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateScoreNotEqualTo(Integer value) {
            addCriterion("new_aid_incm_com_rate_score <>", value, "newAidIncmComRateScore");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateScoreGreaterThan(Integer value) {
            addCriterion("new_aid_incm_com_rate_score >", value, "newAidIncmComRateScore");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("new_aid_incm_com_rate_score >=", value, "newAidIncmComRateScore");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateScoreLessThan(Integer value) {
            addCriterion("new_aid_incm_com_rate_score <", value, "newAidIncmComRateScore");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateScoreLessThanOrEqualTo(Integer value) {
            addCriterion("new_aid_incm_com_rate_score <=", value, "newAidIncmComRateScore");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateScoreIn(List<Integer> values) {
            addCriterion("new_aid_incm_com_rate_score in", values, "newAidIncmComRateScore");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateScoreNotIn(List<Integer> values) {
            addCriterion("new_aid_incm_com_rate_score not in", values, "newAidIncmComRateScore");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateScoreBetween(Integer value1, Integer value2) {
            addCriterion("new_aid_incm_com_rate_score between", value1, value2, "newAidIncmComRateScore");
            return (Criteria) this;
        }

        public Criteria andNewAidIncmComRateScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("new_aid_incm_com_rate_score not between", value1, value2, "newAidIncmComRateScore");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlIsNull() {
            addCriterion("guild_task_incm_lvl is null");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlIsNotNull() {
            addCriterion("guild_task_incm_lvl is not null");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlEqualTo(Integer value) {
            addCriterion("guild_task_incm_lvl =", value, "guildTaskIncmLvl");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlNotEqualTo(Integer value) {
            addCriterion("guild_task_incm_lvl <>", value, "guildTaskIncmLvl");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlGreaterThan(Integer value) {
            addCriterion("guild_task_incm_lvl >", value, "guildTaskIncmLvl");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlGreaterThanOrEqualTo(Integer value) {
            addCriterion("guild_task_incm_lvl >=", value, "guildTaskIncmLvl");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlLessThan(Integer value) {
            addCriterion("guild_task_incm_lvl <", value, "guildTaskIncmLvl");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlLessThanOrEqualTo(Integer value) {
            addCriterion("guild_task_incm_lvl <=", value, "guildTaskIncmLvl");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlIn(List<Integer> values) {
            addCriterion("guild_task_incm_lvl in", values, "guildTaskIncmLvl");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlNotIn(List<Integer> values) {
            addCriterion("guild_task_incm_lvl not in", values, "guildTaskIncmLvl");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlBetween(Integer value1, Integer value2) {
            addCriterion("guild_task_incm_lvl between", value1, value2, "guildTaskIncmLvl");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlNotBetween(Integer value1, Integer value2) {
            addCriterion("guild_task_incm_lvl not between", value1, value2, "guildTaskIncmLvl");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlAimIsNull() {
            addCriterion("guild_task_incm_lvl_aim is null");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlAimIsNotNull() {
            addCriterion("guild_task_incm_lvl_aim is not null");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlAimEqualTo(Integer value) {
            addCriterion("guild_task_incm_lvl_aim =", value, "guildTaskIncmLvlAim");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlAimNotEqualTo(Integer value) {
            addCriterion("guild_task_incm_lvl_aim <>", value, "guildTaskIncmLvlAim");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlAimGreaterThan(Integer value) {
            addCriterion("guild_task_incm_lvl_aim >", value, "guildTaskIncmLvlAim");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlAimGreaterThanOrEqualTo(Integer value) {
            addCriterion("guild_task_incm_lvl_aim >=", value, "guildTaskIncmLvlAim");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlAimLessThan(Integer value) {
            addCriterion("guild_task_incm_lvl_aim <", value, "guildTaskIncmLvlAim");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlAimLessThanOrEqualTo(Integer value) {
            addCriterion("guild_task_incm_lvl_aim <=", value, "guildTaskIncmLvlAim");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlAimIn(List<Integer> values) {
            addCriterion("guild_task_incm_lvl_aim in", values, "guildTaskIncmLvlAim");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlAimNotIn(List<Integer> values) {
            addCriterion("guild_task_incm_lvl_aim not in", values, "guildTaskIncmLvlAim");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlAimBetween(Integer value1, Integer value2) {
            addCriterion("guild_task_incm_lvl_aim between", value1, value2, "guildTaskIncmLvlAim");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlAimNotBetween(Integer value1, Integer value2) {
            addCriterion("guild_task_incm_lvl_aim not between", value1, value2, "guildTaskIncmLvlAim");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlScoreIsNull() {
            addCriterion("guild_task_incm_lvl_score is null");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlScoreIsNotNull() {
            addCriterion("guild_task_incm_lvl_score is not null");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlScoreEqualTo(Integer value) {
            addCriterion("guild_task_incm_lvl_score =", value, "guildTaskIncmLvlScore");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlScoreNotEqualTo(Integer value) {
            addCriterion("guild_task_incm_lvl_score <>", value, "guildTaskIncmLvlScore");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlScoreGreaterThan(Integer value) {
            addCriterion("guild_task_incm_lvl_score >", value, "guildTaskIncmLvlScore");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("guild_task_incm_lvl_score >=", value, "guildTaskIncmLvlScore");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlScoreLessThan(Integer value) {
            addCriterion("guild_task_incm_lvl_score <", value, "guildTaskIncmLvlScore");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlScoreLessThanOrEqualTo(Integer value) {
            addCriterion("guild_task_incm_lvl_score <=", value, "guildTaskIncmLvlScore");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlScoreIn(List<Integer> values) {
            addCriterion("guild_task_incm_lvl_score in", values, "guildTaskIncmLvlScore");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlScoreNotIn(List<Integer> values) {
            addCriterion("guild_task_incm_lvl_score not in", values, "guildTaskIncmLvlScore");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlScoreBetween(Integer value1, Integer value2) {
            addCriterion("guild_task_incm_lvl_score between", value1, value2, "guildTaskIncmLvlScore");
            return (Criteria) this;
        }

        public Criteria andGuildTaskIncmLvlScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("guild_task_incm_lvl_score not between", value1, value2, "guildTaskIncmLvlScore");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtDiffIsNull() {
            addCriterion("accum_incm_amt_diff is null");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtDiffIsNotNull() {
            addCriterion("accum_incm_amt_diff is not null");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtDiffEqualTo(Long value) {
            addCriterion("accum_incm_amt_diff =", value, "accumIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtDiffNotEqualTo(Long value) {
            addCriterion("accum_incm_amt_diff <>", value, "accumIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtDiffGreaterThan(Long value) {
            addCriterion("accum_incm_amt_diff >", value, "accumIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtDiffGreaterThanOrEqualTo(Long value) {
            addCriterion("accum_incm_amt_diff >=", value, "accumIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtDiffLessThan(Long value) {
            addCriterion("accum_incm_amt_diff <", value, "accumIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtDiffLessThanOrEqualTo(Long value) {
            addCriterion("accum_incm_amt_diff <=", value, "accumIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtDiffIn(List<Long> values) {
            addCriterion("accum_incm_amt_diff in", values, "accumIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtDiffNotIn(List<Long> values) {
            addCriterion("accum_incm_amt_diff not in", values, "accumIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtDiffBetween(Long value1, Long value2) {
            addCriterion("accum_incm_amt_diff between", value1, value2, "accumIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtDiffNotBetween(Long value1, Long value2) {
            addCriterion("accum_incm_amt_diff not between", value1, value2, "accumIncmAmtDiff");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtAimIsNull() {
            addCriterion("accum_incm_amt_aim is null");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtAimIsNotNull() {
            addCriterion("accum_incm_amt_aim is not null");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtAimEqualTo(Long value) {
            addCriterion("accum_incm_amt_aim =", value, "accumIncmAmtAim");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtAimNotEqualTo(Long value) {
            addCriterion("accum_incm_amt_aim <>", value, "accumIncmAmtAim");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtAimGreaterThan(Long value) {
            addCriterion("accum_incm_amt_aim >", value, "accumIncmAmtAim");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtAimGreaterThanOrEqualTo(Long value) {
            addCriterion("accum_incm_amt_aim >=", value, "accumIncmAmtAim");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtAimLessThan(Long value) {
            addCriterion("accum_incm_amt_aim <", value, "accumIncmAmtAim");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtAimLessThanOrEqualTo(Long value) {
            addCriterion("accum_incm_amt_aim <=", value, "accumIncmAmtAim");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtAimIn(List<Long> values) {
            addCriterion("accum_incm_amt_aim in", values, "accumIncmAmtAim");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtAimNotIn(List<Long> values) {
            addCriterion("accum_incm_amt_aim not in", values, "accumIncmAmtAim");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtAimBetween(Long value1, Long value2) {
            addCriterion("accum_incm_amt_aim between", value1, value2, "accumIncmAmtAim");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtAimNotBetween(Long value1, Long value2) {
            addCriterion("accum_incm_amt_aim not between", value1, value2, "accumIncmAmtAim");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtSocreIsNull() {
            addCriterion("accum_incm_amt_socre is null");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtSocreIsNotNull() {
            addCriterion("accum_incm_amt_socre is not null");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtSocreEqualTo(Integer value) {
            addCriterion("accum_incm_amt_socre =", value, "accumIncmAmtSocre");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtSocreNotEqualTo(Integer value) {
            addCriterion("accum_incm_amt_socre <>", value, "accumIncmAmtSocre");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtSocreGreaterThan(Integer value) {
            addCriterion("accum_incm_amt_socre >", value, "accumIncmAmtSocre");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtSocreGreaterThanOrEqualTo(Integer value) {
            addCriterion("accum_incm_amt_socre >=", value, "accumIncmAmtSocre");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtSocreLessThan(Integer value) {
            addCriterion("accum_incm_amt_socre <", value, "accumIncmAmtSocre");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtSocreLessThanOrEqualTo(Integer value) {
            addCriterion("accum_incm_amt_socre <=", value, "accumIncmAmtSocre");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtSocreIn(List<Integer> values) {
            addCriterion("accum_incm_amt_socre in", values, "accumIncmAmtSocre");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtSocreNotIn(List<Integer> values) {
            addCriterion("accum_incm_amt_socre not in", values, "accumIncmAmtSocre");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtSocreBetween(Integer value1, Integer value2) {
            addCriterion("accum_incm_amt_socre between", value1, value2, "accumIncmAmtSocre");
            return (Criteria) this;
        }

        public Criteria andAccumIncmAmtSocreNotBetween(Integer value1, Integer value2) {
            addCriterion("accum_incm_amt_socre not between", value1, value2, "accumIncmAmtSocre");
            return (Criteria) this;
        }

        public Criteria andGuildCmpNameIsNull() {
            addCriterion("guild_cmp_name is null");
            return (Criteria) this;
        }

        public Criteria andGuildCmpNameIsNotNull() {
            addCriterion("guild_cmp_name is not null");
            return (Criteria) this;
        }

        public Criteria andGuildCmpNameEqualTo(String value) {
            addCriterion("guild_cmp_name =", value, "guildCmpName");
            return (Criteria) this;
        }

        public Criteria andGuildCmpNameNotEqualTo(String value) {
            addCriterion("guild_cmp_name <>", value, "guildCmpName");
            return (Criteria) this;
        }

        public Criteria andGuildCmpNameGreaterThan(String value) {
            addCriterion("guild_cmp_name >", value, "guildCmpName");
            return (Criteria) this;
        }

        public Criteria andGuildCmpNameGreaterThanOrEqualTo(String value) {
            addCriterion("guild_cmp_name >=", value, "guildCmpName");
            return (Criteria) this;
        }

        public Criteria andGuildCmpNameLessThan(String value) {
            addCriterion("guild_cmp_name <", value, "guildCmpName");
            return (Criteria) this;
        }

        public Criteria andGuildCmpNameLessThanOrEqualTo(String value) {
            addCriterion("guild_cmp_name <=", value, "guildCmpName");
            return (Criteria) this;
        }

        public Criteria andGuildCmpNameLike(String value) {
            addCriterion("guild_cmp_name like", value, "guildCmpName");
            return (Criteria) this;
        }

        public Criteria andGuildCmpNameNotLike(String value) {
            addCriterion("guild_cmp_name not like", value, "guildCmpName");
            return (Criteria) this;
        }

        public Criteria andGuildCmpNameIn(List<String> values) {
            addCriterion("guild_cmp_name in", values, "guildCmpName");
            return (Criteria) this;
        }

        public Criteria andGuildCmpNameNotIn(List<String> values) {
            addCriterion("guild_cmp_name not in", values, "guildCmpName");
            return (Criteria) this;
        }

        public Criteria andGuildCmpNameBetween(String value1, String value2) {
            addCriterion("guild_cmp_name between", value1, value2, "guildCmpName");
            return (Criteria) this;
        }

        public Criteria andGuildCmpNameNotBetween(String value1, String value2) {
            addCriterion("guild_cmp_name not between", value1, value2, "guildCmpName");
            return (Criteria) this;
        }
    }

    /**
     * yy_dm_entity_guild_cmp_health_analysis_di_tmp
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * yy_dm_entity_guild_cmp_health_analysis_di_tmp null
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}