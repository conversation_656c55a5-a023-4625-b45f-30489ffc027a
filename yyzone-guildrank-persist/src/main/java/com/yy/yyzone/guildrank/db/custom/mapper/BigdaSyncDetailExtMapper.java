package com.yy.yyzone.guildrank.db.custom.mapper;

import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface BigdaSyncDetailExtMapper {
    /**
     * 获取最近的同步日期
     *
     * @param tbls
     * @return
     */
    List<Map<String, Object>> selectLatestDt(@Param("tbls") Collection<String> tbls, @Param("status") int status);

    /**
     * 查询已同步的日期
     *
     * @param minDt 最小日期（不包括）
     * @return
     */
    List<Date> selectDts(@Param("tbl") String tbl, @Param("minDt") Date minDt, @Param("maxDt") Date maxDt, @Param("status") int status);
}
