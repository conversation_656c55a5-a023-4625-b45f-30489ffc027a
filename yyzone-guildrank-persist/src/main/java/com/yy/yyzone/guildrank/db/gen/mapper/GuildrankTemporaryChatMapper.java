package com.yy.yyzone.guildrank.db.gen.mapper;

import com.yy.yyzone.guildrank.db.gen.model.GuildrankTemporaryChat;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankTemporaryChatExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface GuildrankTemporaryChatMapper {
    int countByExample(GuildrankTemporaryChatExample example);

    int deleteByExample(GuildrankTemporaryChatExample example);

    int deleteByPrimaryKey(Long id);

    int insert(GuildrankTemporaryChat record);

    int insertSelective(GuildrankTemporaryChat record);

    List<GuildrankTemporaryChat> selectByExample(GuildrankTemporaryChatExample example);

    GuildrankTemporaryChat selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") GuildrankTemporaryChat record, @Param("example") GuildrankTemporaryChatExample example);

    int updateByExample(@Param("record") GuildrankTemporaryChat record, @Param("example") GuildrankTemporaryChatExample example);

    int updateByPrimaryKeySelective(GuildrankTemporaryChat record);

    int updateByPrimaryKey(GuildrankTemporaryChat record);
}