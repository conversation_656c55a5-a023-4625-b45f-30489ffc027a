package com.yy.yyzone.guildrank.db.gen.model;

import java.util.Date;

public class GuildrankRenewArtistData {
    /**
     * id
     */
    private Long id;

    /**
     * 数据月1号日期
     */
    private Date dataMonth;

    /**
     * 备注
     */
    private String remark;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updatePassport;

    /**
     * 获取id
     * @return id id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置id
     * @param id id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取数据月1号日期
     * @return data_month 数据月1号日期
     */
    public Date getDataMonth() {
        return dataMonth;
    }

    /**
     * 设置数据月1号日期
     * @param dataMonth 数据月1号日期
     */
    public void setDataMonth(Date dataMonth) {
        this.dataMonth = dataMonth;
    }

    /**
     * 获取备注
     * @return remark 备注
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 设置备注
     * @param remark 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 获取更新时间
     * @return update_time 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取更新人
     * @return update_passport 更新人
     */
    public String getUpdatePassport() {
        return updatePassport;
    }

    /**
     * 设置更新人
     * @param updatePassport 更新人
     */
    public void setUpdatePassport(String updatePassport) {
        this.updatePassport = updatePassport;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", dataMonth=").append(dataMonth);
        sb.append(", remark=").append(remark);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updatePassport=").append(updatePassport);
        sb.append("]");
        return sb.toString();
    }
}