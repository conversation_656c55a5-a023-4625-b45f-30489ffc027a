package com.yy.yyzone.guildrank.db.gen.mapper;

import com.yy.yyzone.guildrank.db.gen.model.GuildrankRenewArtistCount;
import com.yy.yyzone.guildrank.db.gen.model.GuildrankRenewArtistCountExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface GuildrankRenewArtistCountMapper {
    int countByExample(GuildrankRenewArtistCountExample example);

    int deleteByExample(GuildrankRenewArtistCountExample example);

    int deleteByPrimaryKey(Long id);

    int insert(GuildrankRenewArtistCount record);

    int insertSelective(GuildrankRenewArtistCount record);

    List<GuildrankRenewArtistCount> selectByExample(GuildrankRenewArtistCountExample example);

    GuildrankRenewArtistCount selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") GuildrankRenewArtistCount record, @Param("example") GuildrankRenewArtistCountExample example);

    int updateByExample(@Param("record") GuildrankRenewArtistCount record, @Param("example") GuildrankRenewArtistCountExample example);

    int updateByPrimaryKeySelective(GuildrankRenewArtistCount record);

    int updateByPrimaryKey(GuildrankRenewArtistCount record);
}