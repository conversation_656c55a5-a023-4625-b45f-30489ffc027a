package com.yy.yyzone.guildrank.db.gen.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.annotation.Generated;

@Generated("guildrank_config")
public class GuildrankConfigExample {
    /**
     * guildrank_config
     */
    protected String orderByClause;

    /**
     * guildrank_config
     */
    protected boolean distinct;

    /**
     * guildrank_config
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public GuildrankConfigExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    /**
     * guildrank_config null
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreatePassportIsNull() {
            addCriterion("create_passport is null");
            return (Criteria) this;
        }

        public Criteria andCreatePassportIsNotNull() {
            addCriterion("create_passport is not null");
            return (Criteria) this;
        }

        public Criteria andCreatePassportEqualTo(String value) {
            addCriterion("create_passport =", value, "createPassport");
            return (Criteria) this;
        }

        public Criteria andCreatePassportNotEqualTo(String value) {
            addCriterion("create_passport <>", value, "createPassport");
            return (Criteria) this;
        }

        public Criteria andCreatePassportGreaterThan(String value) {
            addCriterion("create_passport >", value, "createPassport");
            return (Criteria) this;
        }

        public Criteria andCreatePassportGreaterThanOrEqualTo(String value) {
            addCriterion("create_passport >=", value, "createPassport");
            return (Criteria) this;
        }

        public Criteria andCreatePassportLessThan(String value) {
            addCriterion("create_passport <", value, "createPassport");
            return (Criteria) this;
        }

        public Criteria andCreatePassportLessThanOrEqualTo(String value) {
            addCriterion("create_passport <=", value, "createPassport");
            return (Criteria) this;
        }

        public Criteria andCreatePassportLike(String value) {
            addCriterion("create_passport like", value, "createPassport");
            return (Criteria) this;
        }

        public Criteria andCreatePassportNotLike(String value) {
            addCriterion("create_passport not like", value, "createPassport");
            return (Criteria) this;
        }

        public Criteria andCreatePassportIn(List<String> values) {
            addCriterion("create_passport in", values, "createPassport");
            return (Criteria) this;
        }

        public Criteria andCreatePassportNotIn(List<String> values) {
            addCriterion("create_passport not in", values, "createPassport");
            return (Criteria) this;
        }

        public Criteria andCreatePassportBetween(String value1, String value2) {
            addCriterion("create_passport between", value1, value2, "createPassport");
            return (Criteria) this;
        }

        public Criteria andCreatePassportNotBetween(String value1, String value2) {
            addCriterion("create_passport not between", value1, value2, "createPassport");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdatePassportIsNull() {
            addCriterion("update_passport is null");
            return (Criteria) this;
        }

        public Criteria andUpdatePassportIsNotNull() {
            addCriterion("update_passport is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatePassportEqualTo(String value) {
            addCriterion("update_passport =", value, "updatePassport");
            return (Criteria) this;
        }

        public Criteria andUpdatePassportNotEqualTo(String value) {
            addCriterion("update_passport <>", value, "updatePassport");
            return (Criteria) this;
        }

        public Criteria andUpdatePassportGreaterThan(String value) {
            addCriterion("update_passport >", value, "updatePassport");
            return (Criteria) this;
        }

        public Criteria andUpdatePassportGreaterThanOrEqualTo(String value) {
            addCriterion("update_passport >=", value, "updatePassport");
            return (Criteria) this;
        }

        public Criteria andUpdatePassportLessThan(String value) {
            addCriterion("update_passport <", value, "updatePassport");
            return (Criteria) this;
        }

        public Criteria andUpdatePassportLessThanOrEqualTo(String value) {
            addCriterion("update_passport <=", value, "updatePassport");
            return (Criteria) this;
        }

        public Criteria andUpdatePassportLike(String value) {
            addCriterion("update_passport like", value, "updatePassport");
            return (Criteria) this;
        }

        public Criteria andUpdatePassportNotLike(String value) {
            addCriterion("update_passport not like", value, "updatePassport");
            return (Criteria) this;
        }

        public Criteria andUpdatePassportIn(List<String> values) {
            addCriterion("update_passport in", values, "updatePassport");
            return (Criteria) this;
        }

        public Criteria andUpdatePassportNotIn(List<String> values) {
            addCriterion("update_passport not in", values, "updatePassport");
            return (Criteria) this;
        }

        public Criteria andUpdatePassportBetween(String value1, String value2) {
            addCriterion("update_passport between", value1, value2, "updatePassport");
            return (Criteria) this;
        }

        public Criteria andUpdatePassportNotBetween(String value1, String value2) {
            addCriterion("update_passport not between", value1, value2, "updatePassport");
            return (Criteria) this;
        }
    }

    /**
     * guildrank_config
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * guildrank_config null
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}