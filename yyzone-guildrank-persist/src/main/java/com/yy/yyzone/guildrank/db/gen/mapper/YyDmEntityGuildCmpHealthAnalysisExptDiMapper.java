package com.yy.yyzone.guildrank.db.gen.mapper;

import com.yy.yyzone.guildrank.db.gen.model.YyDmEntityGuildCmpHealthAnalysisExptDi;
import com.yy.yyzone.guildrank.db.gen.model.YyDmEntityGuildCmpHealthAnalysisExptDiExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface YyDmEntityGuildCmpHealthAnalysisExptDiMapper {
    int countByExample(YyDmEntityGuildCmpHealthAnalysisExptDiExample example);

    int deleteByExample(YyDmEntityGuildCmpHealthAnalysisExptDiExample example);

    int deleteByPrimaryKey(Long id);

    int insert(YyDmEntityGuildCmpHealthAnalysisExptDi record);

    int insertSelective(YyDmEntityGuildCmpHealthAnalysisExptDi record);

    List<YyDmEntityGuildCmpHealthAnalysisExptDi> selectByExample(YyDmEntityGuildCmpHealthAnalysisExptDiExample example);

    YyDmEntityGuildCmpHealthAnalysisExptDi selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") YyDmEntityGuildCmpHealthAnalysisExptDi record, @Param("example") YyDmEntityGuildCmpHealthAnalysisExptDiExample example);

    int updateByExample(@Param("record") YyDmEntityGuildCmpHealthAnalysisExptDi record, @Param("example") YyDmEntityGuildCmpHealthAnalysisExptDiExample example);

    int updateByPrimaryKeySelective(YyDmEntityGuildCmpHealthAnalysisExptDi record);

    int updateByPrimaryKey(YyDmEntityGuildCmpHealthAnalysisExptDi record);
}