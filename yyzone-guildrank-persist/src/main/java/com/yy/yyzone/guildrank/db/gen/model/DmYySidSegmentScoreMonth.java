package com.yy.yyzone.guildrank.db.gen.model;

public class DmYySidSegmentScoreMonth {
    /**
     * id
     */
    private Long id;

    /**
     * 公会uid
     */
    private Long sidOwnerid;

    /**
     * 公会yy
     */
    private Long sidOwyyid;

    /**
     * 公会新主播数
     */
    private Long newUv;

    /**
     * 公会新主播环比上月增长个数，当月数值-上月数值
     */
    private Long newUvRr;

    /**
     * 公会有效开播新主播
     */
    private Long validLiveNewUv;

    /**
     * 公会有效开播新主播环比上月增长个数
     */
    private Long validLiveNewUvRr;

    /**
     * 往上一星公会新主播中位数
     */
    private Double perNewUv;

    /**
     * 往上一星公会有效开播新主播中位数
     */
    private Double perValidLiveNewUv;

    /**
     * 新主播开播率
     */
    private Double newUvRt;

    /**
     * 人气主播（ACU≥10）
     */
    private Long acu10Uv;

    /**
     * 人气主播数（ACU>=10）环比上月增长个数
     */
    private Long acu10UvRr;

    /**
     * 往上一星人气主播（ACU≥10）中位数
     */
    private Double perAcu10Uv;

    /**
     * 低人气主播数（acu[10,50)）
     */
    private Long acu1050Uv;

    /**
     * 中人气主播数（acu[50,300)）
     */
    private Long acu50300Uv;

    /**
     * 高人气主播数（acu[300,）
     */
    private Long acu300Uv;

    /**
     * 高活跃主播
     */
    private Long hActUv;

    /**
     * 高活跃主播环比上月增长个数
     */
    private Long hActUvRr;

    /**
     * 往上一星高活跃主播中位数
     */
    private Double perHActUv;

    /**
     * 公会旗下主播累计蓝钻收入
     */
    private Double monthDiamond;

    /**
     * 公会旗下主播累计蓝钻收入环比上月增长数
     */
    private Double monthDiamondRr;

    /**
     * 往上一星公会旗下主播累计蓝钻收入中位数
     */
    private Double perMonthDiamond;

    /**
     * A类违规数
     */
    private Long breakANum;

    /**
     * B类违规数
     */
    private Long breakBNum;

    /**
     * C类违规数
     */
    private Long breakCNum;

    /**
     * E类违规数
     */
    private Long breakENum;

    /**
     * 开播主播数
     */
    private Long liveUv;

    /**
     * 违规占比
     */
    private Double breakRt;

    /**
     * 违规占比环比上月增长数
     */
    private Double breakRr;

    /**
     * 往前一星违规占比中位数
     */
    private Double perBreak;

    /**
     * 新主播运营能力
     */
    private Double p1;

    /**
     * 新主播运营能力归一分
     */
    private Double ps1;

    /**
     * 新主播运营能力星级
     */
    private Long psS1;

    /**
     * 新主播运营能力雷达图显示
     */
    private String psS1Sh;

    /**
     * 新主播运营能力得分按星级排名
     */
    private Long psS1Rn;

    /**
     * 人气主播
     */
    private Double p2;

    /**
     * 人气主播归一得分
     */
    private Double ps2;

    /**
     * 人气主播星级
     */
    private Long psS2;

    /**
     * 人气主播雷达图显示
     */
    private String psS2Sh;

    /**
     * 人气主播得分按星级排名
     */
    private Long psS2Rn;

    /**
     * 活跃主播
     */
    private Double p3;

    /**
     * 活跃主播归一得分
     */
    private Double ps3;

    /**
     * 活跃主播星级
     */
    private Long psS3;

    /**
     * 活跃主播雷达图显示
     */
    private String psS3Sh;

    /**
     * 	活跃主播得分按星级排名
     */
    private Long psS3Rn;

    /**
     * 营收能力
     */
    private Double p4;

    /**
     * 营收能力归一得分
     */
    private Double ps4;

    /**
     * 营收能力星级
     */
    private Long psS4;

    /**
     * 营收能力雷达图显示
     */
    private String psS4Sh;

    /**
     * 营收能力得分按星级排名
     */
    private Long psS4Rn;

    /**
     * 违规占比
     */
    private Double p5;

    /**
     * 违规占比得分
     */
    private Double ps5;

    /**
     * 违规占比星级
     */
    private Long psS5;

    /**
     * 违规占比雷达图显示
     */
    private String psS5Sh;

    /**
     * 违规占比得分按星级排名
     */
    private Long psS5Rn;

    /**
     * 汇总得分
     */
    private Double psAll;

    /**
     * 最终星级
     */
    private Long psSAllCor;

    /**
     * 环比上月升降星级数
     */
    private String psSAllCorRr;

    /**
     * 违规占比星级
     */
    private Long psSAllRnCor;

    /**
     * 同星级总分排名环比上月升降
     */
    private String psSAllRnCorRr;

    /**
     * 公会新主播数冲刺值
     */
    private Double sprNewUv;

    /**
     * 公会有效开播新主播冲刺值
     */
    private Double sprValidLiveNewUv;

    /**
     * 人气主播数冲刺值
     */
    private Double sprAcu10Uv;

    /**
     * 高活跃主播冲刺值
     */
    private Double sprHActUv;

    /**
     * 公会旗下主播累计蓝钻收入冲刺值
     */
    private Double sprMonthDiamond;

    /**
     * 违规占比冲刺值
     */
    private Double sprP5;

    /**
     * 月份
     */
    private String dt;

    /**
     * 获取id
     * @return id id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置id
     * @param id id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取公会uid
     * @return sid_ownerid 公会uid
     */
    public Long getSidOwnerid() {
        return sidOwnerid;
    }

    /**
     * 设置公会uid
     * @param sidOwnerid 公会uid
     */
    public void setSidOwnerid(Long sidOwnerid) {
        this.sidOwnerid = sidOwnerid;
    }

    /**
     * 获取公会yy
     * @return sid_owyyid 公会yy
     */
    public Long getSidOwyyid() {
        return sidOwyyid;
    }

    /**
     * 设置公会yy
     * @param sidOwyyid 公会yy
     */
    public void setSidOwyyid(Long sidOwyyid) {
        this.sidOwyyid = sidOwyyid;
    }

    /**
     * 获取公会新主播数
     * @return new_uv 公会新主播数
     */
    public Long getNewUv() {
        return newUv;
    }

    /**
     * 设置公会新主播数
     * @param newUv 公会新主播数
     */
    public void setNewUv(Long newUv) {
        this.newUv = newUv;
    }

    /**
     * 获取公会新主播环比上月增长个数，当月数值-上月数值
     * @return new_uv_rr 公会新主播环比上月增长个数，当月数值-上月数值
     */
    public Long getNewUvRr() {
        return newUvRr;
    }

    /**
     * 设置公会新主播环比上月增长个数，当月数值-上月数值
     * @param newUvRr 公会新主播环比上月增长个数，当月数值-上月数值
     */
    public void setNewUvRr(Long newUvRr) {
        this.newUvRr = newUvRr;
    }

    /**
     * 获取公会有效开播新主播
     * @return valid_live_new_uv 公会有效开播新主播
     */
    public Long getValidLiveNewUv() {
        return validLiveNewUv;
    }

    /**
     * 设置公会有效开播新主播
     * @param validLiveNewUv 公会有效开播新主播
     */
    public void setValidLiveNewUv(Long validLiveNewUv) {
        this.validLiveNewUv = validLiveNewUv;
    }

    /**
     * 获取公会有效开播新主播环比上月增长个数
     * @return valid_live_new_uv_rr 公会有效开播新主播环比上月增长个数
     */
    public Long getValidLiveNewUvRr() {
        return validLiveNewUvRr;
    }

    /**
     * 设置公会有效开播新主播环比上月增长个数
     * @param validLiveNewUvRr 公会有效开播新主播环比上月增长个数
     */
    public void setValidLiveNewUvRr(Long validLiveNewUvRr) {
        this.validLiveNewUvRr = validLiveNewUvRr;
    }

    /**
     * 获取往上一星公会新主播中位数
     * @return per_new_uv 往上一星公会新主播中位数
     */
    public Double getPerNewUv() {
        return perNewUv;
    }

    /**
     * 设置往上一星公会新主播中位数
     * @param perNewUv 往上一星公会新主播中位数
     */
    public void setPerNewUv(Double perNewUv) {
        this.perNewUv = perNewUv;
    }

    /**
     * 获取往上一星公会有效开播新主播中位数
     * @return per_valid_live_new_uv 往上一星公会有效开播新主播中位数
     */
    public Double getPerValidLiveNewUv() {
        return perValidLiveNewUv;
    }

    /**
     * 设置往上一星公会有效开播新主播中位数
     * @param perValidLiveNewUv 往上一星公会有效开播新主播中位数
     */
    public void setPerValidLiveNewUv(Double perValidLiveNewUv) {
        this.perValidLiveNewUv = perValidLiveNewUv;
    }

    /**
     * 获取新主播开播率
     * @return new_uv_rt 新主播开播率
     */
    public Double getNewUvRt() {
        return newUvRt;
    }

    /**
     * 设置新主播开播率
     * @param newUvRt 新主播开播率
     */
    public void setNewUvRt(Double newUvRt) {
        this.newUvRt = newUvRt;
    }

    /**
     * 获取人气主播（ACU≥10）
     * @return acu10_uv 人气主播（ACU≥10）
     */
    public Long getAcu10Uv() {
        return acu10Uv;
    }

    /**
     * 设置人气主播（ACU≥10）
     * @param acu10Uv 人气主播（ACU≥10）
     */
    public void setAcu10Uv(Long acu10Uv) {
        this.acu10Uv = acu10Uv;
    }

    /**
     * 获取人气主播数（ACU>=10）环比上月增长个数
     * @return acu10_uv_rr 人气主播数（ACU>=10）环比上月增长个数
     */
    public Long getAcu10UvRr() {
        return acu10UvRr;
    }

    /**
     * 设置人气主播数（ACU>=10）环比上月增长个数
     * @param acu10UvRr 人气主播数（ACU>=10）环比上月增长个数
     */
    public void setAcu10UvRr(Long acu10UvRr) {
        this.acu10UvRr = acu10UvRr;
    }

    /**
     * 获取往上一星人气主播（ACU≥10）中位数
     * @return per_acu10_uv 往上一星人气主播（ACU≥10）中位数
     */
    public Double getPerAcu10Uv() {
        return perAcu10Uv;
    }

    /**
     * 设置往上一星人气主播（ACU≥10）中位数
     * @param perAcu10Uv 往上一星人气主播（ACU≥10）中位数
     */
    public void setPerAcu10Uv(Double perAcu10Uv) {
        this.perAcu10Uv = perAcu10Uv;
    }

    /**
     * 获取低人气主播数（acu[10,50)）
     * @return acu10_50_uv 低人气主播数（acu[10,50)）
     */
    public Long getAcu1050Uv() {
        return acu1050Uv;
    }

    /**
     * 设置低人气主播数（acu[10,50)）
     * @param acu1050Uv 低人气主播数（acu[10,50)）
     */
    public void setAcu1050Uv(Long acu1050Uv) {
        this.acu1050Uv = acu1050Uv;
    }

    /**
     * 获取中人气主播数（acu[50,300)）
     * @return acu50_300_uv 中人气主播数（acu[50,300)）
     */
    public Long getAcu50300Uv() {
        return acu50300Uv;
    }

    /**
     * 设置中人气主播数（acu[50,300)）
     * @param acu50300Uv 中人气主播数（acu[50,300)）
     */
    public void setAcu50300Uv(Long acu50300Uv) {
        this.acu50300Uv = acu50300Uv;
    }

    /**
     * 获取高人气主播数（acu[300,）
     * @return acu300_uv 高人气主播数（acu[300,）
     */
    public Long getAcu300Uv() {
        return acu300Uv;
    }

    /**
     * 设置高人气主播数（acu[300,）
     * @param acu300Uv 高人气主播数（acu[300,）
     */
    public void setAcu300Uv(Long acu300Uv) {
        this.acu300Uv = acu300Uv;
    }

    /**
     * 获取高活跃主播
     * @return h_act_uv 高活跃主播
     */
    public Long gethActUv() {
        return hActUv;
    }

    /**
     * 设置高活跃主播
     * @param hActUv 高活跃主播
     */
    public void sethActUv(Long hActUv) {
        this.hActUv = hActUv;
    }

    /**
     * 获取高活跃主播环比上月增长个数
     * @return h_act_uv_rr 高活跃主播环比上月增长个数
     */
    public Long gethActUvRr() {
        return hActUvRr;
    }

    /**
     * 设置高活跃主播环比上月增长个数
     * @param hActUvRr 高活跃主播环比上月增长个数
     */
    public void sethActUvRr(Long hActUvRr) {
        this.hActUvRr = hActUvRr;
    }

    /**
     * 获取往上一星高活跃主播中位数
     * @return per_h_act_uv 往上一星高活跃主播中位数
     */
    public Double getPerHActUv() {
        return perHActUv;
    }

    /**
     * 设置往上一星高活跃主播中位数
     * @param perHActUv 往上一星高活跃主播中位数
     */
    public void setPerHActUv(Double perHActUv) {
        this.perHActUv = perHActUv;
    }

    /**
     * 获取公会旗下主播累计蓝钻收入
     * @return month_diamond 公会旗下主播累计蓝钻收入
     */
    public Double getMonthDiamond() {
        return monthDiamond;
    }

    /**
     * 设置公会旗下主播累计蓝钻收入
     * @param monthDiamond 公会旗下主播累计蓝钻收入
     */
    public void setMonthDiamond(Double monthDiamond) {
        this.monthDiamond = monthDiamond;
    }

    /**
     * 获取公会旗下主播累计蓝钻收入环比上月增长数
     * @return month_diamond_rr 公会旗下主播累计蓝钻收入环比上月增长数
     */
    public Double getMonthDiamondRr() {
        return monthDiamondRr;
    }

    /**
     * 设置公会旗下主播累计蓝钻收入环比上月增长数
     * @param monthDiamondRr 公会旗下主播累计蓝钻收入环比上月增长数
     */
    public void setMonthDiamondRr(Double monthDiamondRr) {
        this.monthDiamondRr = monthDiamondRr;
    }

    /**
     * 获取往上一星公会旗下主播累计蓝钻收入中位数
     * @return per_month_diamond 往上一星公会旗下主播累计蓝钻收入中位数
     */
    public Double getPerMonthDiamond() {
        return perMonthDiamond;
    }

    /**
     * 设置往上一星公会旗下主播累计蓝钻收入中位数
     * @param perMonthDiamond 往上一星公会旗下主播累计蓝钻收入中位数
     */
    public void setPerMonthDiamond(Double perMonthDiamond) {
        this.perMonthDiamond = perMonthDiamond;
    }

    /**
     * 获取A类违规数
     * @return break_a_num A类违规数
     */
    public Long getBreakANum() {
        return breakANum;
    }

    /**
     * 设置A类违规数
     * @param breakANum A类违规数
     */
    public void setBreakANum(Long breakANum) {
        this.breakANum = breakANum;
    }

    /**
     * 获取B类违规数
     * @return break_b_num B类违规数
     */
    public Long getBreakBNum() {
        return breakBNum;
    }

    /**
     * 设置B类违规数
     * @param breakBNum B类违规数
     */
    public void setBreakBNum(Long breakBNum) {
        this.breakBNum = breakBNum;
    }

    /**
     * 获取C类违规数
     * @return break_c_num C类违规数
     */
    public Long getBreakCNum() {
        return breakCNum;
    }

    /**
     * 设置C类违规数
     * @param breakCNum C类违规数
     */
    public void setBreakCNum(Long breakCNum) {
        this.breakCNum = breakCNum;
    }

    /**
     * 获取E类违规数
     * @return break_e_num E类违规数
     */
    public Long getBreakENum() {
        return breakENum;
    }

    /**
     * 设置E类违规数
     * @param breakENum E类违规数
     */
    public void setBreakENum(Long breakENum) {
        this.breakENum = breakENum;
    }

    /**
     * 获取开播主播数
     * @return live_uv 开播主播数
     */
    public Long getLiveUv() {
        return liveUv;
    }

    /**
     * 设置开播主播数
     * @param liveUv 开播主播数
     */
    public void setLiveUv(Long liveUv) {
        this.liveUv = liveUv;
    }

    /**
     * 获取违规占比
     * @return break_rt 违规占比
     */
    public Double getBreakRt() {
        return breakRt;
    }

    /**
     * 设置违规占比
     * @param breakRt 违规占比
     */
    public void setBreakRt(Double breakRt) {
        this.breakRt = breakRt;
    }

    /**
     * 获取违规占比环比上月增长数
     * @return break_rr 违规占比环比上月增长数
     */
    public Double getBreakRr() {
        return breakRr;
    }

    /**
     * 设置违规占比环比上月增长数
     * @param breakRr 违规占比环比上月增长数
     */
    public void setBreakRr(Double breakRr) {
        this.breakRr = breakRr;
    }

    /**
     * 获取往前一星违规占比中位数
     * @return per_break 往前一星违规占比中位数
     */
    public Double getPerBreak() {
        return perBreak;
    }

    /**
     * 设置往前一星违规占比中位数
     * @param perBreak 往前一星违规占比中位数
     */
    public void setPerBreak(Double perBreak) {
        this.perBreak = perBreak;
    }

    /**
     * 获取新主播运营能力
     * @return p1 新主播运营能力
     */
    public Double getP1() {
        return p1;
    }

    /**
     * 设置新主播运营能力
     * @param p1 新主播运营能力
     */
    public void setP1(Double p1) {
        this.p1 = p1;
    }

    /**
     * 获取新主播运营能力归一分
     * @return ps_1 新主播运营能力归一分
     */
    public Double getPs1() {
        return ps1;
    }

    /**
     * 设置新主播运营能力归一分
     * @param ps1 新主播运营能力归一分
     */
    public void setPs1(Double ps1) {
        this.ps1 = ps1;
    }

    /**
     * 获取新主播运营能力星级
     * @return ps_s_1 新主播运营能力星级
     */
    public Long getPsS1() {
        return psS1;
    }

    /**
     * 设置新主播运营能力星级
     * @param psS1 新主播运营能力星级
     */
    public void setPsS1(Long psS1) {
        this.psS1 = psS1;
    }

    /**
     * 获取新主播运营能力雷达图显示
     * @return ps_s_1_sh 新主播运营能力雷达图显示
     */
    public String getPsS1Sh() {
        return psS1Sh;
    }

    /**
     * 设置新主播运营能力雷达图显示
     * @param psS1Sh 新主播运营能力雷达图显示
     */
    public void setPsS1Sh(String psS1Sh) {
        this.psS1Sh = psS1Sh;
    }

    /**
     * 获取新主播运营能力得分按星级排名
     * @return ps_s_1_rn 新主播运营能力得分按星级排名
     */
    public Long getPsS1Rn() {
        return psS1Rn;
    }

    /**
     * 设置新主播运营能力得分按星级排名
     * @param psS1Rn 新主播运营能力得分按星级排名
     */
    public void setPsS1Rn(Long psS1Rn) {
        this.psS1Rn = psS1Rn;
    }

    /**
     * 获取人气主播
     * @return p2 人气主播
     */
    public Double getP2() {
        return p2;
    }

    /**
     * 设置人气主播
     * @param p2 人气主播
     */
    public void setP2(Double p2) {
        this.p2 = p2;
    }

    /**
     * 获取人气主播归一得分
     * @return ps_2 人气主播归一得分
     */
    public Double getPs2() {
        return ps2;
    }

    /**
     * 设置人气主播归一得分
     * @param ps2 人气主播归一得分
     */
    public void setPs2(Double ps2) {
        this.ps2 = ps2;
    }

    /**
     * 获取人气主播星级
     * @return ps_s_2 人气主播星级
     */
    public Long getPsS2() {
        return psS2;
    }

    /**
     * 设置人气主播星级
     * @param psS2 人气主播星级
     */
    public void setPsS2(Long psS2) {
        this.psS2 = psS2;
    }

    /**
     * 获取人气主播雷达图显示
     * @return ps_s_2_sh 人气主播雷达图显示
     */
    public String getPsS2Sh() {
        return psS2Sh;
    }

    /**
     * 设置人气主播雷达图显示
     * @param psS2Sh 人气主播雷达图显示
     */
    public void setPsS2Sh(String psS2Sh) {
        this.psS2Sh = psS2Sh;
    }

    /**
     * 获取人气主播得分按星级排名
     * @return ps_s_2_rn 人气主播得分按星级排名
     */
    public Long getPsS2Rn() {
        return psS2Rn;
    }

    /**
     * 设置人气主播得分按星级排名
     * @param psS2Rn 人气主播得分按星级排名
     */
    public void setPsS2Rn(Long psS2Rn) {
        this.psS2Rn = psS2Rn;
    }

    /**
     * 获取活跃主播
     * @return p3 活跃主播
     */
    public Double getP3() {
        return p3;
    }

    /**
     * 设置活跃主播
     * @param p3 活跃主播
     */
    public void setP3(Double p3) {
        this.p3 = p3;
    }

    /**
     * 获取活跃主播归一得分
     * @return ps_3 活跃主播归一得分
     */
    public Double getPs3() {
        return ps3;
    }

    /**
     * 设置活跃主播归一得分
     * @param ps3 活跃主播归一得分
     */
    public void setPs3(Double ps3) {
        this.ps3 = ps3;
    }

    /**
     * 获取活跃主播星级
     * @return ps_s_3 活跃主播星级
     */
    public Long getPsS3() {
        return psS3;
    }

    /**
     * 设置活跃主播星级
     * @param psS3 活跃主播星级
     */
    public void setPsS3(Long psS3) {
        this.psS3 = psS3;
    }

    /**
     * 获取活跃主播雷达图显示
     * @return ps_s_3_sh 活跃主播雷达图显示
     */
    public String getPsS3Sh() {
        return psS3Sh;
    }

    /**
     * 设置活跃主播雷达图显示
     * @param psS3Sh 活跃主播雷达图显示
     */
    public void setPsS3Sh(String psS3Sh) {
        this.psS3Sh = psS3Sh;
    }

    /**
     * 获取	活跃主播得分按星级排名
     * @return ps_s_3_rn 	活跃主播得分按星级排名
     */
    public Long getPsS3Rn() {
        return psS3Rn;
    }

    /**
     * 设置	活跃主播得分按星级排名
     * @param psS3Rn 	活跃主播得分按星级排名
     */
    public void setPsS3Rn(Long psS3Rn) {
        this.psS3Rn = psS3Rn;
    }

    /**
     * 获取营收能力
     * @return p4 营收能力
     */
    public Double getP4() {
        return p4;
    }

    /**
     * 设置营收能力
     * @param p4 营收能力
     */
    public void setP4(Double p4) {
        this.p4 = p4;
    }

    /**
     * 获取营收能力归一得分
     * @return ps_4 营收能力归一得分
     */
    public Double getPs4() {
        return ps4;
    }

    /**
     * 设置营收能力归一得分
     * @param ps4 营收能力归一得分
     */
    public void setPs4(Double ps4) {
        this.ps4 = ps4;
    }

    /**
     * 获取营收能力星级
     * @return ps_s_4 营收能力星级
     */
    public Long getPsS4() {
        return psS4;
    }

    /**
     * 设置营收能力星级
     * @param psS4 营收能力星级
     */
    public void setPsS4(Long psS4) {
        this.psS4 = psS4;
    }

    /**
     * 获取营收能力雷达图显示
     * @return ps_s_4_sh 营收能力雷达图显示
     */
    public String getPsS4Sh() {
        return psS4Sh;
    }

    /**
     * 设置营收能力雷达图显示
     * @param psS4Sh 营收能力雷达图显示
     */
    public void setPsS4Sh(String psS4Sh) {
        this.psS4Sh = psS4Sh;
    }

    /**
     * 获取营收能力得分按星级排名
     * @return ps_s_4_rn 营收能力得分按星级排名
     */
    public Long getPsS4Rn() {
        return psS4Rn;
    }

    /**
     * 设置营收能力得分按星级排名
     * @param psS4Rn 营收能力得分按星级排名
     */
    public void setPsS4Rn(Long psS4Rn) {
        this.psS4Rn = psS4Rn;
    }

    /**
     * 获取违规占比
     * @return p5 违规占比
     */
    public Double getP5() {
        return p5;
    }

    /**
     * 设置违规占比
     * @param p5 违规占比
     */
    public void setP5(Double p5) {
        this.p5 = p5;
    }

    /**
     * 获取违规占比得分
     * @return ps_5 违规占比得分
     */
    public Double getPs5() {
        return ps5;
    }

    /**
     * 设置违规占比得分
     * @param ps5 违规占比得分
     */
    public void setPs5(Double ps5) {
        this.ps5 = ps5;
    }

    /**
     * 获取违规占比星级
     * @return ps_s_5 违规占比星级
     */
    public Long getPsS5() {
        return psS5;
    }

    /**
     * 设置违规占比星级
     * @param psS5 违规占比星级
     */
    public void setPsS5(Long psS5) {
        this.psS5 = psS5;
    }

    /**
     * 获取违规占比雷达图显示
     * @return ps_s_5_sh 违规占比雷达图显示
     */
    public String getPsS5Sh() {
        return psS5Sh;
    }

    /**
     * 设置违规占比雷达图显示
     * @param psS5Sh 违规占比雷达图显示
     */
    public void setPsS5Sh(String psS5Sh) {
        this.psS5Sh = psS5Sh;
    }

    /**
     * 获取违规占比得分按星级排名
     * @return ps_s_5_rn 违规占比得分按星级排名
     */
    public Long getPsS5Rn() {
        return psS5Rn;
    }

    /**
     * 设置违规占比得分按星级排名
     * @param psS5Rn 违规占比得分按星级排名
     */
    public void setPsS5Rn(Long psS5Rn) {
        this.psS5Rn = psS5Rn;
    }

    /**
     * 获取汇总得分
     * @return ps_all 汇总得分
     */
    public Double getPsAll() {
        return psAll;
    }

    /**
     * 设置汇总得分
     * @param psAll 汇总得分
     */
    public void setPsAll(Double psAll) {
        this.psAll = psAll;
    }

    /**
     * 获取最终星级
     * @return ps_s_all_cor 最终星级
     */
    public Long getPsSAllCor() {
        return psSAllCor;
    }

    /**
     * 设置最终星级
     * @param psSAllCor 最终星级
     */
    public void setPsSAllCor(Long psSAllCor) {
        this.psSAllCor = psSAllCor;
    }

    /**
     * 获取环比上月升降星级数
     * @return ps_s_all_cor_rr 环比上月升降星级数
     */
    public String getPsSAllCorRr() {
        return psSAllCorRr;
    }

    /**
     * 设置环比上月升降星级数
     * @param psSAllCorRr 环比上月升降星级数
     */
    public void setPsSAllCorRr(String psSAllCorRr) {
        this.psSAllCorRr = psSAllCorRr;
    }

    /**
     * 获取违规占比星级
     * @return ps_s_all_rn_cor 违规占比星级
     */
    public Long getPsSAllRnCor() {
        return psSAllRnCor;
    }

    /**
     * 设置违规占比星级
     * @param psSAllRnCor 违规占比星级
     */
    public void setPsSAllRnCor(Long psSAllRnCor) {
        this.psSAllRnCor = psSAllRnCor;
    }

    /**
     * 获取同星级总分排名环比上月升降
     * @return ps_s_all_rn_cor_rr 同星级总分排名环比上月升降
     */
    public String getPsSAllRnCorRr() {
        return psSAllRnCorRr;
    }

    /**
     * 设置同星级总分排名环比上月升降
     * @param psSAllRnCorRr 同星级总分排名环比上月升降
     */
    public void setPsSAllRnCorRr(String psSAllRnCorRr) {
        this.psSAllRnCorRr = psSAllRnCorRr;
    }

    /**
     * 获取公会新主播数冲刺值
     * @return spr_new_uv 公会新主播数冲刺值
     */
    public Double getSprNewUv() {
        return sprNewUv;
    }

    /**
     * 设置公会新主播数冲刺值
     * @param sprNewUv 公会新主播数冲刺值
     */
    public void setSprNewUv(Double sprNewUv) {
        this.sprNewUv = sprNewUv;
    }

    /**
     * 获取公会有效开播新主播冲刺值
     * @return spr_valid_live_new_uv 公会有效开播新主播冲刺值
     */
    public Double getSprValidLiveNewUv() {
        return sprValidLiveNewUv;
    }

    /**
     * 设置公会有效开播新主播冲刺值
     * @param sprValidLiveNewUv 公会有效开播新主播冲刺值
     */
    public void setSprValidLiveNewUv(Double sprValidLiveNewUv) {
        this.sprValidLiveNewUv = sprValidLiveNewUv;
    }

    /**
     * 获取人气主播数冲刺值
     * @return spr_acu10_uv 人气主播数冲刺值
     */
    public Double getSprAcu10Uv() {
        return sprAcu10Uv;
    }

    /**
     * 设置人气主播数冲刺值
     * @param sprAcu10Uv 人气主播数冲刺值
     */
    public void setSprAcu10Uv(Double sprAcu10Uv) {
        this.sprAcu10Uv = sprAcu10Uv;
    }

    /**
     * 获取高活跃主播冲刺值
     * @return spr_h_act_uv 高活跃主播冲刺值
     */
    public Double getSprHActUv() {
        return sprHActUv;
    }

    /**
     * 设置高活跃主播冲刺值
     * @param sprHActUv 高活跃主播冲刺值
     */
    public void setSprHActUv(Double sprHActUv) {
        this.sprHActUv = sprHActUv;
    }

    /**
     * 获取公会旗下主播累计蓝钻收入冲刺值
     * @return spr_month_diamond 公会旗下主播累计蓝钻收入冲刺值
     */
    public Double getSprMonthDiamond() {
        return sprMonthDiamond;
    }

    /**
     * 设置公会旗下主播累计蓝钻收入冲刺值
     * @param sprMonthDiamond 公会旗下主播累计蓝钻收入冲刺值
     */
    public void setSprMonthDiamond(Double sprMonthDiamond) {
        this.sprMonthDiamond = sprMonthDiamond;
    }

    /**
     * 获取违规占比冲刺值
     * @return spr_p5 违规占比冲刺值
     */
    public Double getSprP5() {
        return sprP5;
    }

    /**
     * 设置违规占比冲刺值
     * @param sprP5 违规占比冲刺值
     */
    public void setSprP5(Double sprP5) {
        this.sprP5 = sprP5;
    }

    /**
     * 获取月份
     * @return dt 月份
     */
    public String getDt() {
        return dt;
    }

    /**
     * 设置月份
     * @param dt 月份
     */
    public void setDt(String dt) {
        this.dt = dt;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", sidOwnerid=").append(sidOwnerid);
        sb.append(", sidOwyyid=").append(sidOwyyid);
        sb.append(", newUv=").append(newUv);
        sb.append(", newUvRr=").append(newUvRr);
        sb.append(", validLiveNewUv=").append(validLiveNewUv);
        sb.append(", validLiveNewUvRr=").append(validLiveNewUvRr);
        sb.append(", perNewUv=").append(perNewUv);
        sb.append(", perValidLiveNewUv=").append(perValidLiveNewUv);
        sb.append(", newUvRt=").append(newUvRt);
        sb.append(", acu10Uv=").append(acu10Uv);
        sb.append(", acu10UvRr=").append(acu10UvRr);
        sb.append(", perAcu10Uv=").append(perAcu10Uv);
        sb.append(", acu1050Uv=").append(acu1050Uv);
        sb.append(", acu50300Uv=").append(acu50300Uv);
        sb.append(", acu300Uv=").append(acu300Uv);
        sb.append(", hActUv=").append(hActUv);
        sb.append(", hActUvRr=").append(hActUvRr);
        sb.append(", perHActUv=").append(perHActUv);
        sb.append(", monthDiamond=").append(monthDiamond);
        sb.append(", monthDiamondRr=").append(monthDiamondRr);
        sb.append(", perMonthDiamond=").append(perMonthDiamond);
        sb.append(", breakANum=").append(breakANum);
        sb.append(", breakBNum=").append(breakBNum);
        sb.append(", breakCNum=").append(breakCNum);
        sb.append(", breakENum=").append(breakENum);
        sb.append(", liveUv=").append(liveUv);
        sb.append(", breakRt=").append(breakRt);
        sb.append(", breakRr=").append(breakRr);
        sb.append(", perBreak=").append(perBreak);
        sb.append(", p1=").append(p1);
        sb.append(", ps1=").append(ps1);
        sb.append(", psS1=").append(psS1);
        sb.append(", psS1Sh=").append(psS1Sh);
        sb.append(", psS1Rn=").append(psS1Rn);
        sb.append(", p2=").append(p2);
        sb.append(", ps2=").append(ps2);
        sb.append(", psS2=").append(psS2);
        sb.append(", psS2Sh=").append(psS2Sh);
        sb.append(", psS2Rn=").append(psS2Rn);
        sb.append(", p3=").append(p3);
        sb.append(", ps3=").append(ps3);
        sb.append(", psS3=").append(psS3);
        sb.append(", psS3Sh=").append(psS3Sh);
        sb.append(", psS3Rn=").append(psS3Rn);
        sb.append(", p4=").append(p4);
        sb.append(", ps4=").append(ps4);
        sb.append(", psS4=").append(psS4);
        sb.append(", psS4Sh=").append(psS4Sh);
        sb.append(", psS4Rn=").append(psS4Rn);
        sb.append(", p5=").append(p5);
        sb.append(", ps5=").append(ps5);
        sb.append(", psS5=").append(psS5);
        sb.append(", psS5Sh=").append(psS5Sh);
        sb.append(", psS5Rn=").append(psS5Rn);
        sb.append(", psAll=").append(psAll);
        sb.append(", psSAllCor=").append(psSAllCor);
        sb.append(", psSAllCorRr=").append(psSAllCorRr);
        sb.append(", psSAllRnCor=").append(psSAllRnCor);
        sb.append(", psSAllRnCorRr=").append(psSAllRnCorRr);
        sb.append(", sprNewUv=").append(sprNewUv);
        sb.append(", sprValidLiveNewUv=").append(sprValidLiveNewUv);
        sb.append(", sprAcu10Uv=").append(sprAcu10Uv);
        sb.append(", sprHActUv=").append(sprHActUv);
        sb.append(", sprMonthDiamond=").append(sprMonthDiamond);
        sb.append(", sprP5=").append(sprP5);
        sb.append(", dt=").append(dt);
        sb.append("]");
        return sb.toString();
    }
}