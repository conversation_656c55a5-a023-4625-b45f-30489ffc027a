
server:
  port: 8084
  servlet:
    context-path: /zone/microservice/guild-rank

spring:
  application:
    name:  yyzoneguildrank
  main:
    allow-bean-definition-overriding:  true

dubbo:
  scan:
    base-packages: com.yy.yyzone.guildrank.api
  application:
    name: springboot-yrpc-client
    id : springboot-yrpc-client
  registry:
    id:  s2sRegister4yrpc
    address:  s2s://test-wudang-meta.yy.com
    username:  yyzone_guildrank
    password:  4a6c9425f900989a4c4d6b9e0b91ab83d745dbae734092cbf0769fd83bb8b0fa374ea9b4d5ad4b8a
    s2stype: 4096
  consumer:
    timeout: 3000
yy:
  env: dev
  app-name: xxx