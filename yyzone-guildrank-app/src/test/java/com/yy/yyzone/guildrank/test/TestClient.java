package com.yy.yyzone.guildrank.test;


import com.yy.yyzone.guildrank.api.AllGuildRank;
import com.yy.yyzone.guildrank.api.GuildRank;
import com.yy.yyzone.guildrank.api.GuildrankService;
import com.yy.yyzone.guildrank.api.GuildrankYYPService;
import com.yy.yyzone.guildrank.test.ClientApplication;
import org.apache.dubbo.config.annotation.Reference;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = ClientApplication.class)
@WebAppConfiguration
public class TestClient {

    @Reference(protocol = "yrpc", owner = "yyzone_guildrank")
    private GuildrankService guildrankService;

    @Reference(protocol = "yyp", owner = "yyzone_guildrank_yyp")
    private GuildrankYYPService guildrankYYPService;

    @Test
    public void hello() {
        GuildRank.QueryGuildRankResp resp = guildrankService.queryGuildRank(
                GuildRank.QueryGuildRankReq.newBuilder().setMonth("2020-02").addOwUids(1629370835).build());
        System.out.println(resp.toString());

    }


    @Test
    public void hello2() {
        GuildRank.CurrentRankResp resp = guildrankYYPService.queryCurrentRank(GuildRank.CurrentRankReq.newBuilder().setUid(2350662155L).build());
        System.out.println(resp.toString());

    }

    @Test
    public void hello3() {
        AllGuildRank.QueryAllGuildRankByOrderReq req = AllGuildRank.QueryAllGuildRankByOrderReq.newBuilder().setMonth("2020-02").setOrderType(1).build();
        AllGuildRank.QueryAllGuildRankByOrderResp resp = guildrankYYPService.queryAllGuildRankByOrder(req);
        System.out.println(resp.toString());

    }

    @Test
    public void hello4() {
        AllGuildRank.QueryAllGuildRankReq req = AllGuildRank.QueryAllGuildRankReq.newBuilder().setMonth("2020-03").build();
        AllGuildRank.QueryAllGuildRankResp resp = guildrankYYPService.queryAllGuildRank(req);
        System.out.println(resp.toString());

    }
}
