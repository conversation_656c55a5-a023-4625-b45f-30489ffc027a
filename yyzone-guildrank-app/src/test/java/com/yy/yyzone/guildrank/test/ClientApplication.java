package com.yy.yyzone.guildrank.test;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

import java.io.IOException;

@SpringBootApplication
@EnableDubbo
public class ClientApplication {
    public static void main(String[] args) throws IOException {
        SpringApplication.run(ClientApplication.class, args);
        /*AnnotationConfigApplicationContext context = new AnnotationConfigApplicationContext(ClientConfiguration.class); // #1
        context.start();
        TestClient client = context.getBean(TestClient.class);
        client.hello();
        System.exit(0);*/
    }
}
