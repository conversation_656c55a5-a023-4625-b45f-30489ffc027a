yy:
  env: DEV
  app-name: yyzoneguildrank
  datasource:
    yyzone-guildrank:
      primary: true
      basePackage: com.yy.yyzone.guildrank.db.gen.mapper,com.yy.yyzone.guildrank.db.custom.mapper
  webdb:
    enable: true

apollo:
  meta: http://nbcfg-dev.yy.com
  bootstrap:
    enabled: true
    namespaces: application,app.yml     #yy内部默认namespace为application
app:
  id: 4ca797c7d2196f5e0529d4777cb140c9
 #是否强制头加密，默认为true，生产环境必须开启，本地开发可以设置 false

logging:
  name: yyzone-guildrank
  config : classpath:logback-spring.xml
  sms: true
  level:
    root: info