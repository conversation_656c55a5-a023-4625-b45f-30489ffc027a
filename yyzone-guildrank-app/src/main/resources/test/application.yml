yy:
  env: test
  app-name: yyzoneguildrank
  datasource:
    yyzone-guildrank:
      primary: true
      basePackage: com.yy.yyzone.guildrank.db.gen.mapper,com.yy.yyzone.guildrank.db.custom.mapper
  webdb:
    enable: true

logging:
  name: yyzone-guildrank
  config : classpath:logback-spring.xml
  sms: false
  level:
    root: info

apollo:
  meta: http://nbcfg-dev.yy.com
  cluster: test
  bootstrap:
    enabled: true
    namespaces: application,app.yml     #yy内部默认namespace为application
app:
  id: 4ca797c7d2196f5e0529d4777cb140c9