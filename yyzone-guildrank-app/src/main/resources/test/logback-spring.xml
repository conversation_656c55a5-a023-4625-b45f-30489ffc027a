<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="true" scan="true">

	<springProperty scope="context" name="LOG_LEVEL" source="logging.level.root"/>
	<springProperty scope="context" name="APPNAME" source="logging.name"/>
	<property name="LOG_HOME" value="/data/weblog/java" />

	<jmxConfigurator />

	<appender name="error_file"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_HOME}/${APPNAME}/web_error.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_HOME}/${APPNAME}/web_error_%d{yyyy-MM-dd}_%i.log.zip
			</fileNamePattern>
			<maxHistory>7</maxHistory>
			<cleanHistoryOnStart>true</cleanHistoryOnStart>
			<timeBasedFileNamingAndTriggeringPolicy
				class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>100MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
		</rollingPolicy>

		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>ERROR</level>
		</filter>
		<encoder>
			<charset>UTF-8</charset>
			<pattern>[%date] [%-5level] [%thread] [%logger{0}_%method:%line] traceId=%X{trace_id} spanId=%X{span_id} - %msg%n
			</pattern>
		</encoder>
	</appender>

	<appender name="warn_file"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_HOME}/${APPNAME}/web_warn.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_HOME}/${APPNAME}/web_warn_%d{yyyy-MM-dd}_%i.log.zip
			</fileNamePattern>
			<maxHistory>7</maxHistory>
			<cleanHistoryOnStart>true</cleanHistoryOnStart>
			<timeBasedFileNamingAndTriggeringPolicy
				class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>100MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
		</rollingPolicy>

		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>WARN</level>
		</filter>
		<encoder>
			<charset>UTF-8</charset>
			<pattern>[%date] [%-5level] [%thread] [%logger{0}_%method:%line] traceId=%X{trace_id} spanId=%X{span_id} - %msg%n
			</pattern>
		</encoder>
	</appender>

	<appender name="info_file"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_HOME}/${APPNAME}/web_info.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_HOME}/${APPNAME}/web_info_%d{yyyy-MM-dd}_%i.log.zip
			</fileNamePattern>
			<maxHistory>7</maxHistory>
			<cleanHistoryOnStart>true</cleanHistoryOnStart>
			<timeBasedFileNamingAndTriggeringPolicy
				class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>100MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
		</rollingPolicy>

		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>${LOG_LEVEL}</level>
		</filter>
		<encoder>
			<charset>UTF-8</charset>
			<pattern>[%date] [%-5level] [%thread] [%logger{0}_%method:%line] traceId=%X{trace_id} spanId=%X{span_id} - %msg%n
			</pattern>
		</encoder>
	</appender>


	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<charset>UTF-8</charset>
			<pattern>%date [%thread] [%level %logger{0}:%line] traceId=%X{trace_id} spanId=%X{span_id} - %msg%n
			</pattern>
		</encoder>
	</appender>

	<!--异步输出-->
	<appender name="async_info_log" class="ch.qos.logback.classic.AsyncAppender">
		<discardingThreshold>0</discardingThreshold>
		<queueSize>512</queueSize>
		<includeCallerData>true</includeCallerData>
		<appender-ref ref="info_file"/>
	</appender>

	<root level="${LOG_LEVEL}">
		<appender-ref ref="async_info_log" />
		<appender-ref ref="warn_file" />
		<appender-ref ref="error_file" />
		${logback-console}
	</root>
</configuration>
