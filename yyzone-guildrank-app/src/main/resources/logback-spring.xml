<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="true" scan="true">
	<springProperty scope="context" name="LOG_LEVEL" source="logging.level.root"/>
	<appender name="console" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
            <pattern>[%date] [%-5level] [%thread] [%logger{0}_%method:%line] traceId=%X{trace_id} spanId=%X{span_id} - %msg%n</pattern>
		</encoder>
	</appender>
	<root level="${LOG_LEVEL}">
		<appender-ref ref="console" />
	</root>
</configuration>