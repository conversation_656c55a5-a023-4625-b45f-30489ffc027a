package com.yy.yyzone.guildrank.controller;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.java.webdb.WebdbUserInfo;
import com.yy.yyzone.guildrank.db.gen.mapper.DmYySidSegmentScoreAccumCm1DayMapper;
import com.yy.yyzone.guildrank.db.gen.mapper.DmYySidSegmentScoreAccumCm2DayMapper;
import com.yy.yyzone.guildrank.db.gen.model.*;
import com.yy.yyzone.guildrank.dto.Leaderboard1;
import com.yy.yyzone.guildrank.service.GuildRankService;
import com.yy.yyzone.guildrank.service.UserInfoService;
import com.yy.yyzone.guildrank.util.Resp;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.ibatis.mapping.MappedStatement;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Generated;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
* 公会段位排行榜活动接口
*<AUTHOR> gen
**/
@RequestMapping("/leaderboardActivity")
@RestController
@Generated("忽略代码规范检查")
public class LeaderboardActivityController {

    private static Logger logger = LoggerFactory.getLogger(LeaderboardActivityController.class);

    @Autowired
    private DmYySidSegmentScoreAccumCm1DayMapper dmYySidSegmentScoreAccumCm1DayMapper;

    @Autowired
    private DmYySidSegmentScoreAccumCm2DayMapper dmYySidSegmentScoreAccumCm2DayMapper;

    @Autowired
    private UserInfoService webdbUserInfoService;


    Cache<String, List<Leaderboard1>> leaderboardCache = CacheBuilder.newBuilder().expireAfterWrite(10, TimeUnit.MINUTES).build();

    Cache<String, Map<Long, Map<String,String>>> awardListCache = CacheBuilder.newBuilder().expireAfterWrite(30, TimeUnit.MINUTES).build();

    @Autowired
    private GuildRankService guildRankService;

    /**
     * 查询上个月获奖名单
     * @return
     */
    @RequestMapping("/awardList")
    public Resp<Map<Long, Map<String,String>>> awardList() throws Exception{
        Map<Long, Map<String,String>> result = new HashMap<>();

        Date endDate = DateUtils.parseDate("2020-02-01","yyyy-MM-dd");
        Date now = new Date();
        if (now.after(endDate)){
            return Resp.createBySuccess(result);
        }
        Date date = new Date();
        Calendar c = Calendar.getInstance();
        //设置为指定日期
        c.setTime(date);
        //指定日期月份减去一
        c.add(Calendar.MONTH, -1);
        //指定日期月份减去一后的 最大天数
        c.set(Calendar.DATE, c.getActualMaximum(Calendar.DATE));
        Date lastDateOfPrevMonth = c.getTime();
        String dateStr = DateFormatUtils.format(lastDateOfPrevMonth, "yyyy-MM-dd");
        Map<Long, Map<String,String>> cacheData = awardListCache.getIfPresent(dateStr);
        if (MapUtils.isNotEmpty(cacheData)){
            return Resp.createBySuccess(cacheData);
        }

        for (int i = 1; i<=6; i++){
            DmYySidSegmentScoreAccumCm1DayExample example = new DmYySidSegmentScoreAccumCm1DayExample();
            example.createCriteria().andGradeEqualTo(i+"").andDtEqualTo(dateStr).andPsAllGreaterThan(0d);
            example.setOrderByClause(" grade_rn ");
            example.setLimit(10);
            List<DmYySidSegmentScoreAccumCm1Day> list = dmYySidSegmentScoreAccumCm1DayMapper.selectByExample(example);
            for (DmYySidSegmentScoreAccumCm1Day item : list) {
                Map<String,String> map = Maps.newHashMap();
                map.put("paiming",item.getGradeRn()+"");
                int duanwei = 0;
                Long uid = item.getSidOwnerid();
                DmYySidSegmentScoreMonth data = guildRankService.currentRank(uid);
                if (data != null){
                    duanwei = data.getPsSAllCor().intValue();
                }
                map.put("duanwei", String.valueOf(duanwei));
                result.put(item.getSidOwnerid(), map);
            }
        }
        awardListCache.put(dateStr, result);
        return Resp.createBySuccess(result);
    }
    /**
     * 查询榜1信息
     * @param date 格式：2019-06-05
     * @param star 1 2 3 4 5 6
     * @return
     */
    @RequestMapping("/leaderboard1")
    public Resp<List<Leaderboard1>> leaderboard1(String date, int star) {

        List<Leaderboard1> cacheData = Lists.newArrayList();
        return Resp.createBySuccess(cacheData);


//        if (star <1 || star >6 || StringUtils.isBlank(date)){
//            return Resp.createByError("参数非法");
//        }
//        String key = star+"leaderboard1"+date;
//        List<Leaderboard1> cacheData = leaderboardCache.getIfPresent(key);
//        if (CollectionUtils.isNotEmpty(cacheData)){
//            return Resp.createBySuccess(cacheData);
//        }
//
//        boolean isAugust = date.contains("2019-08");
//        String grade = "";
//        if (isAugust){
//            if (star == 1){
//                grade = "一星";
//            }else if (star == 2){
//                grade = "二星";
//            }else if (star == 3){
//                grade = "三星";
//            }else if (star == 4){
//                grade = "四星";
//            }else if (star == 5){
//                grade = "五星";
//            }else if (star == 6){
//                grade = "六星";
//            }
//        }else {
//            grade = star+"";
//        }
//
//
//        logger.info("leaderboard1  查db" + key);
//        DmYySidSegmentScoreAccumCm1DayExample example = new DmYySidSegmentScoreAccumCm1DayExample();
//        example.createCriteria().andGradeEqualTo(grade).andDtEqualTo(date).andPsAllGreaterThan(0d);
//        example.setOrderByClause(" grade_rn ");
//        if (isAugust){
//            example.setLimit(15);
//        }else {
//            example.setLimit(20);
//        }
//
//        List<DmYySidSegmentScoreAccumCm1Day> list = dmYySidSegmentScoreAccumCm1DayMapper.selectByExample(example);
//        if (CollectionUtils.isEmpty(list)){
//            return Resp.createBySuccess(Lists.newArrayList());
//        }
//        List<Long> uids = Lists.newArrayList();
//
//        List<Leaderboard1> data = Lists.newArrayList();
//        for (DmYySidSegmentScoreAccumCm1Day item : list) {
//            uids.add(item.getSidOwnerid());
//        }
//        Map<String, WebdbUserInfo> userInfoMap = webdbUserInfoService.getUserInfo(uids);
//        for (DmYySidSegmentScoreAccumCm1Day item : list) {
//            Leaderboard1 leaderboard1 = new Leaderboard1();
//            WebdbUserInfo userInfo = userInfoMap.get(String.valueOf(item.getSidOwnerid()));
//            leaderboard1.setIcon(userInfo.getAvatar());
//            leaderboard1.setLevel(item.getGradeRn());
//            leaderboard1.setNick(userInfo.getNick());
//            leaderboard1.setValue(item.getPsAll());
//            leaderboard1.setYy(Long.parseLong(userInfo.getYyno()));
//            if (isAugust){
//                leaderboard1.setAllScore(item.getPsAll());
//            }else {
//                leaderboard1.setActiveAnchor(item.gethActUv());
//                leaderboard1.setActiveAnchorPer(item.getWeightB());
//                leaderboard1.setNewAnchor(item.getNewUvCm());
//                leaderboard1.setNewAnchorPer(item.getWeightA());
//                leaderboard1.setAllScore(item.getPsAllWeight());
//            }
//            data.add(leaderboard1);
//        }
//        leaderboardCache.put(key,data);
//        return Resp.createBySuccess(data);
    }

    /**
     * 查询榜2信息
     * @param date 格式：2019-06-05
     * @param star 1 2 3 4 5 6
     * @param type 1公会营收榜  2活跃主播榜  3新主播运营榜
     * @return
     */
    @RequestMapping("/leaderboard2")
    public Resp<List<Leaderboard1>> leaderboard2(String date, int star, int type) {

        if (star <1 || star >6 || StringUtils.isBlank(date) || type > 3 || type < 1){
            return Resp.createByError("参数非法");
        }

        if ("2019-08-22".equals(date) ||"2019-08-23".equals(date) || "2019-08-24".equals(date)){
            return Resp.createBySuccess(Lists.newArrayList());
        }

        String key = star+"leaderboard2"+date + "_" + type;
        List<Leaderboard1> cacheData = leaderboardCache.getIfPresent(key);
        if (CollectionUtils.isNotEmpty(cacheData)){
            return Resp.createBySuccess(cacheData);
        }

        String grade = "";
        if (star == 1){
            grade = "一星";
        }else if (star == 2){
            grade = "二星";
        }else if (star == 3){
            grade = "三星";
        }else if (star == 4){
            grade = "四星";
        }else if (star == 5){
            grade = "五星";
        }else if (star == 6){
            grade = "六星";
        }

        String kpiName = "";
        if (type == 1){
            kpiName = "公会营收能力";
        }else if (type == 2){
            kpiName = "活跃主播";
        }else if (type == 3){
            kpiName = "新主播运营能力";
        }
        logger.info("leaderboard2  查db" + key);
        DmYySidSegmentScoreAccumCm2DayExample example = new DmYySidSegmentScoreAccumCm2DayExample();
        example.createCriteria().andGradeEqualTo(grade).andDtEqualTo(date).andKpiNameEqualTo(kpiName).andPsNGreaterThan(0d);
        example.setOrderByClause(" ps_n_all_rn ");
        example.setLimit(5);
        List<DmYySidSegmentScoreAccumCm2Day> list = dmYySidSegmentScoreAccumCm2DayMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)){
            return Resp.createBySuccess(Lists.newArrayList());
        }

        List<Long> uids = Lists.newArrayList();
        List<Leaderboard1> data = Lists.newArrayList();
        for (DmYySidSegmentScoreAccumCm2Day item : list) {
            uids.add(item.getSidOwnerid());
        }
        Map<String, WebdbUserInfo> userInfoMap = webdbUserInfoService.getUserInfo(uids);
        for (DmYySidSegmentScoreAccumCm2Day item : list) {
            Leaderboard1 leaderboard1 = new Leaderboard1();
            WebdbUserInfo userInfo = userInfoMap.get(String.valueOf(item.getSidOwnerid()));
            leaderboard1.setIcon(userInfo.getAvatar());
            leaderboard1.setLevel(item.getPsNAllRn());
            leaderboard1.setNick(userInfo.getNick());
            leaderboard1.setValue(item.getPsN());
            leaderboard1.setYy(Long.parseLong(userInfo.getYyno()));
            data.add(leaderboard1);
        }
        leaderboardCache.put(key,data);
        return Resp.createBySuccess(data);
    }
}
