package com.yy.yyzone.guildrank.dto;

import com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentOriginStarDay;
import lombok.Data;
import org.springframework.beans.BeanUtils;

@Data
public class DmYySidSegmentOriginStarDayDTO extends DmYySidSegmentOriginStarDay {
    /**
     * 关联的SDK小火星公会YY
     */
    private String marsSidOwnerYy;

    /**
     * 关联的SDK小火星公会名
     */
    private String marsSidOwnerName;

    public DmYySidSegmentOriginStarDayDTO(DmYySidSegmentOriginStarDay day) {
        BeanUtils.copyProperties(day, this);
    }
}