package com.yy.yyzone.guildrank.schedule;

import com.alibaba.fastjson.JSONObject;
import com.yy.ent.platform.yyzone.api.yyp.message.constant.message.MsgContentField;
import com.yy.ent.platform.yyzone.api.yyp.message.constant.message.YyZoneMsgTpl;
import com.yy.java.webdb.WebdbUserInfo;
import com.yy.yyzone.guildrank.annotation.JobLock;
import com.yy.yyzone.guildrank.service.*;
import com.yy.yyzone.guildrank.util.MyDateUtil;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Generated;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 段位通知消息
 *
 * <AUTHOR>
 * @date 2020/5/15
 */
@Component
@Configurable
@EnableScheduling
@Generated("忽略代码规范检查")
public class RankNotifyJob {
    private static Logger logger = LoggerFactory.getLogger(RankNotifyJob.class);

    @Value("${yy.env}")
    private String env;

    @Value("${rank.job.enableRankNotifyJob:true}")
    private boolean enableRankNotifyJob;

    @Value("${rank.job.enableRankCheckJob:true}")
    private boolean enableRankCheckJob;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private RankCheckV3Service rankCheckV3Service;

    @Autowired
    private RankCacheV3Service cacheV3Service;

    @Autowired
    private YyzoneMsgService msgService;

    private static final String RANK_NOTIFY_KEY = "guildrank_notify_";
    private static final String TEST_ENV = "TEST";
    private static final String EXECUTED_FLAG = "1";
    private static final int TTL_LOCK = 3600;
    private static final String KEY_LOCK_RANCK = "guildrank:lock:rankCheck";
    private static final String KEY_LOCK_NOTIFY = "guildrank:lock:rankNotify";

    /**
     * 公会段位校验
     */
    @Scheduled(cron = "0 0/10 10-23 1-15 * ?")
    @JobLock
    public void rankCheck() {
        logger.info("enableRankCheckJob {}", enableRankCheckJob);
        if(!enableRankCheckJob) {
            return;
        }

        try {
            logger.info("start rankCheck ...");
            rankCheckV3Service.rankCheck(MyDateUtil.lastMonthEnd(), false);
            logger.info("end rankCheck ...");
        } catch (Exception e) {
            logger.error("rankCheck error", e);
        }
    }

    /**
     * 非月末数据同步
     */
    @JobLock
    @Scheduled(cron = "0 */10 10-23 * * ?")
    public void syncNotMonthEndData() {
        try {
            logger.info("start syncNotMonthEndData ...");
            rankCheckV3Service.syncNotMonthEndData();
            logger.info("end syncNotMonthEndData ...");
        } catch (Exception e) {
            logger.error("syncNotMonthEndData error", e);
        }
    }

    /**
     * 发送段位消息 每月1-3号10点起，每小时执行；若BI段位数据已同步，则发送段位消息
     */
    @Scheduled(cron = "0 0 10/1 1-3 * ?")
    @JobLock
    public void rankNotify() {
        logger.info("enableRankNotifyJob {}", enableRankNotifyJob);
        if(!enableRankNotifyJob) {
            return;
        }
        try {
            logger.info("start RankNotifyJob ...");
            String currentDate = DateFormatUtils.format(DateUtils.addMonths(new Date(), -1) ,"yyyy-MM");
            if(TEST_ENV.equalsIgnoreCase(env)){
                logger.info("not execute");
                return;
            }
            String redisKey = RANK_NOTIFY_KEY + currentDate;
            String redisResult = redisTemplate.opsForValue().get(redisKey);
            // 当月已执行
            if (EXECUTED_FLAG.equals(redisResult)){
                return;
            }
            // 已更新
            if(cacheV3Service.hasUpdate()){
                Map<Long, Integer> rankMap = cacheV3Service.allRank();
                logger.info("rankNotify currentDate:{},size:{}", currentDate, rankMap.size());

                List<Long> uids = new ArrayList<>(rankMap.keySet());
                Map<Long, String> nameMap = userInfoService.batchGetGuildName(uids);
                Map<String, WebdbUserInfo> userInfoMap = userInfoService.getUserInfo(uids);

                for (Map.Entry<Long, Integer> e : rankMap.entrySet()) {
                    JSONObject data = new JSONObject();
                    Long uid = e.getKey();
                    WebdbUserInfo userInfo = userInfoMap.get(uid.toString());
                    data.put(MsgContentField.GUILD_YY, userInfo == null ? Strings.EMPTY : userInfo.getYyno());
                    data.put(MsgContentField.GUILD_NAME, nameMap.get(uid));
                    data.put(MsgContentField.GUILD_RANK, e.getValue());
                    msgService.sendMsg(YyZoneMsgTpl.TPL_508.id, uid, JSONObject.toJSONString(data));
                }

                // 当月任务已执行
                redisTemplate.opsForValue().set(redisKey, EXECUTED_FLAG, Duration.ofDays(31));
            }
            logger.info("end RankNotifyJob ...");
        } catch (Exception e) {
            logger.error("RankNotifyJob error", e);
        }
    }

}
