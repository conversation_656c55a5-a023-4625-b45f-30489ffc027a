package com.yy.yyzone.guildrank.dto;

/**
 * <AUTHOR>
 * @since 2019/8/19
 */
public class Leaderboard1 {
    private Long yy;
    private String nick;
    private String icon;
    private int level; // 排名
    private double value; // 吊炸天值
    private double allScore; //总分值
    private Long newAnchor; //新签主播数
    private double newAnchorPer; //新签主播数加权百分比
    private Long activeAnchor; //活跃主播数
    private double activeAnchorPer; //新签主播数加权百分比



    public Long getYy() {
        return yy;
    }

    public void setYy(Long yy) {
        this.yy = yy;
    }

    public String getNick() {
        return nick;
    }

    public void setNick(String nick) {
        this.nick = nick;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public double getValue() {
        return value;
    }

    public void setValue(double value) {
        this.value = value;
    }

    public double getAllScore() {
        return allScore;
    }

    public void setAllScore(double allScore) {
        this.allScore = allScore;
    }

    public Long getNewAnchor() {
        return newAnchor;
    }

    public void setNewAnchor(Long newAnchor) {
        this.newAnchor = newAnchor;
    }

    public double getNewAnchorPer() {
        return newAnchorPer;
    }

    public void setNewAnchorPer(double newAnchorPer) {
        this.newAnchorPer = newAnchorPer;
    }

    public Long getActiveAnchor() {
        return activeAnchor;
    }

    public void setActiveAnchor(Long activeAnchor) {
        this.activeAnchor = activeAnchor;
    }

    public double getActiveAnchorPer() {
        return activeAnchorPer;
    }

    public void setActiveAnchorPer(double activeAnchorPer) {
        this.activeAnchorPer = activeAnchorPer;
    }
}
