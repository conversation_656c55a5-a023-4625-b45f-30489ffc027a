package com.yy.yyzone.guildrank.schedule;

import com.google.common.base.Stopwatch;
import com.yy.yyzone.guildrank.annotation.JobLock;
import com.yy.yyzone.guildrank.constant.RankConstant;
import com.yy.yyzone.guildrank.service.GuildRankWhitelistService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class RankWhitelistJob {

    @Autowired
    private GuildRankWhitelistService rankWhitelistService;

    /**
     * 操作段位白名单后通知yyzone-resources修改资源额度
     */
    @JobLock
    @Scheduled(cron = "10/20 * * * * ?")
    public void notifyResource() {
        log.info("notifyResource begin");
        Stopwatch sw = Stopwatch.createStarted();
        rankWhitelistService.notifyResource();
        log.info("notifyResource end elapsed:{}ms", sw.elapsed(TimeUnit.MILLISECONDS));
    }
}
