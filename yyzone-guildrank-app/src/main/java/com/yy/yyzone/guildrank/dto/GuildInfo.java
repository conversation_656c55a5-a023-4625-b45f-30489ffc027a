package com.yy.yyzone.guildrank.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.annotation.Generated;

/**
 * 公会信息
 * <AUTHOR>
 * @date 2019/11/1
 *
 */
@Generated("忽略代码规范检查")
public class GuildInfo {
    /**
     * 公会段位
     */
    private int rank;

    /**
     * 公会名称
     */
    private String name;

    /**
     * 公会类型 0-普通 1-星级 2-金牌
     */
    private int type;

    /**
     * 公会yy号
     */
    private String yy_num;
    /**
     * 公会uid
     */
    @JsonIgnore
    private Long uid;

    public String getYy_num() {
        return this.yy_num;
    }

    public void setYy_num(final String yy_num) {
        this.yy_num = yy_num;
    }

    public int getType() {
        return this.type;
    }

    public void setType(final int type) {
        this.type = type;
    }

    public int getRank() {
        return this.rank;
    }

    public void setRank(final int rank) {
        this.rank = rank;
    }

    public String getName() {
        return this.name;
    }

    public void setName(final String name) {
        this.name = name;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    @Override
    public String toString() {
        return "GuildInfo{" +
                "rank=" + rank +
                ", name='" + name + '\'' +
                ", type=" + type +
                ", yy_num='" + yy_num + '\'' +
                ", uid=" + uid +
                '}';
    }

}