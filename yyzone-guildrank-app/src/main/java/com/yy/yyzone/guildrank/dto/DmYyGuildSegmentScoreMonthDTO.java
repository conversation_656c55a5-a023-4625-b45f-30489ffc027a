package com.yy.yyzone.guildrank.dto;

import com.yy.yyzone.guildrank.db.gen.model.DmYyGuildSegmentScoreMonth;
import lombok.Data;
import org.springframework.beans.BeanUtils;

@Data
public class DmYyGuildSegmentScoreMonthDTO extends DmYyGuildSegmentScoreMonth {
    /**
     * 关联的SDK小火星公会YY
     */
    private String marsSidOwnerYy;

    /**
     * 关联的SDK小火星公会名
     */
    private String marsSidOwnerName;

    public DmYyGuildSegmentScoreMonthDTO(DmYyGuildSegmentScoreMonth month) {
        BeanUtils.copyProperties(month, this);
    }

}