package com.yy.yyzone.guildrank.aspect;

import com.google.common.base.Stopwatch;
import com.yy.ent.mobile.metrics.MetricsStopWatch;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2021/3/3
 *
 */
@Aspect
@Component
@Slf4j
@Order(Ordered.HIGHEST_PRECEDENCE)
public class ServerInterfaceAspect {
    @Around(value = "execution(* com.yy.yyzone.guildrank.yrpc.impl.*.*(..))")
    public Object metrics(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        String methodName = proceedingJoinPoint.getSignature().getName();
        Stopwatch sw = Stopwatch.createStarted();
        Object o = null;
        String[] ss = StringUtils.split(proceedingJoinPoint.getSignature().getDeclaringTypeName(), ".");
        String sign = ss[ss.length - 1] + "_" + proceedingJoinPoint.getSignature().getName();
        MetricsStopWatch timer = MetricsStopWatch.startServerWatch().uri("server/" + sign);
        try {
            log.info("{} args:{}", methodName, proceedingJoinPoint.getArgs());
            o = proceedingJoinPoint.proceed(proceedingJoinPoint.getArgs());
            log.info("{} return:{}", methodName, o);
            timer.successCode().markDurationAndCode();
        } catch (Exception ex) {
            timer.failCode().markDurationAndCode();
            log.error("{} exception", sign, ex);
        } finally {
            log.info("{} finish in {}ms", methodName, sw.elapsed(TimeUnit.MILLISECONDS));
        }

        return o;
    }
}