package com.yy.yyzone.guildrank.filter;

import com.google.common.collect.Lists;

import com.yy.ent.mobile.metrics.MetricsStopWatch;
import org.apache.commons.lang3.RandomStringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * ServletControllerContextFilter
 *
 * <AUTHOR>
 * @date 2015/7/23.
 */
@Component
@WebFilter(urlPatterns = "/*", filterName = "controllerFilter")
public class ControllerFilter implements Filter {
    protected Logger logger = LoggerFactory.getLogger(ControllerFilter.class);

    @Override
    public void destroy() {
    }

    private static List<String> ignoreUri = Lists.newArrayList(

    );

    @Override
    public void doFilter(ServletRequest req, ServletResponse resp, FilterChain chain) throws ServletException, IOException {
        MetricsStopWatch metricsStopWatch = MetricsStopWatch.startServerWatch();

        Thread thread = Thread.currentThread();
        String oldName = thread.getName();

        HttpServletRequest httpServletRequest = (HttpServletRequest) req;
        String servletPath = httpServletRequest.getServletPath();
        boolean isIgnore = false;
        for (String s : ignoreUri) {
            if (servletPath.contains(s)){
                isIgnore = true;
                break;
            }
        }
        try {
            String randomId = RandomStringUtils.randomAlphanumeric(10);
            thread.setName(servletPath + "-" + randomId);
            if (!isIgnore){
                logger.info("请求:{}", servletPath);
            }
            chain.doFilter(req, resp);
        } finally {
            metricsStopWatch.code(0).uri(servletPath.replaceAll("/", "_")).markDurationAndCode();
            if (!isIgnore) {
                logger.info("响应url:{}, time: {} ms", servletPath, metricsStopWatch.getTime());
            }
            thread.setName(oldName);
        }
    }

    @Override
    public void init(FilterConfig config) throws ServletException {

    }

}
