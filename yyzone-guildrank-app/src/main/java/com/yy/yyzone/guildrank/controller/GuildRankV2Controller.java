/**
 * Autogenerated by Thrift
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 */
package com.yy.yyzone.guildrank.controller;

import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Sheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.google.common.collect.Lists;
import com.yy.yyzone.guildrank.db.gen.model.DmYyGuildSegmentScoreMonth;
import com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentOriginStarDay;
import com.yy.yyzone.guildrank.dto.DmYyGuildSegmentScoreMonthDTO;
import com.yy.yyzone.guildrank.dto.DmYySidSegmentOriginStarDayDTO;
import com.yy.yyzone.guildrank.dto.LastTwelveMonthResp;
import com.yy.yyzone.guildrank.service.GuildRankV2Service;
import com.yy.yyzone.guildrank.service.MoneyInfoService;
import com.yy.yyzone.guildrank.service.ThriftAdaptorService;
import com.yy.yyzone.guildrank.service.UserInfoService;
import com.yy.yyzone.guildrank.util.Resp;
import com.yy.zone.shiro.user.UserContext;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.shiro.authz.annotation.RequiresUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
* 公会段位接口V2版本(2020-04-01上线使用)
*<AUTHOR> gen
**/
@RestController
@RequestMapping("/v2")
public class GuildRankV2Controller {

    private static Logger logger = LoggerFactory.getLogger(GuildRankV2Controller.class);

    @Autowired
    private GuildRankV2Service guildRankV2Service;

    @Autowired
    private MoneyInfoService moneyInfoService;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private ThriftAdaptorService thriftAdaptorService;
    public static final int IS_MARS = 1;

    /**
     * 查询上个月的数据生成没有
     * @return true表示已生成
     */
    @RequestMapping("/hasUpdate")
    @RequiresUser
    public Resp<Boolean> hasUpdate() {
        boolean hasUpdate = guildRankV2Service.hasUpdate();
        logger.info(UserContext.getCurrentLoginUserUid() + "====" + hasUpdate);
        return Resp.createBySuccess(hasUpdate);
    }

    /**
     * 根据月份查询公会段位信息
     * @param month 格式：2019-06
     * @re  turn
     */
    @RequestMapping("/guildRankInfo")
    @RequiresUser
    public Resp<DmYyGuildSegmentScoreMonthDTO> guildRankInfo(String month) {
        Long uid = UserContext.getCurrentLoginUserUid();
        if (uid<=0){
            return Resp.createByError("没登陆");
        }
        if (StringUtils.isBlank(month)){
            return Resp.createByError("请传入月份");
        }
        month = month+"-01";
        logger.info("uid:{}, month:{}", uid, month);
        DmYyGuildSegmentScoreMonth data = guildRankV2Service.guildRankInfo(uid, month);
        if (data!=null){
            if (month.contains("2020-03")){
                data.setPsSAllRr(null);
            }
            DmYyGuildSegmentScoreMonthDTO dto = new DmYyGuildSegmentScoreMonthDTO(data);
            if (data.getIsMarsGuild() != null && data.getIsMarsGuild() == IS_MARS && dto.getMarsSidOwnerid() != null) {
                dto.setMarsSidOwnerYy(userInfoService.getYyByUid(dto.getMarsSidOwnerid()));
                dto.setMarsSidOwnerName(thriftAdaptorService.getChannelName(dto.getMarsSidOwnerid()));
            }
            return Resp.createBySuccess(dto);
        }else {// 刚升级的公会db没有数据，查询一下构造等级
            String currentDate = DateFormatUtils.format(DateUtils.addMonths(new Date(), -1),"yyyy-MM")+"-01";
            if (currentDate.equals(month)){
                data = guildRankV2Service.buildData(uid);
                if (data.getPsSAll()>0){
                    return Resp.createBySuccess(new DmYyGuildSegmentScoreMonthDTO(data));
                }
            }
        }
        return Resp.createByError(2,"查无数据");
    }

    /**
     * 公会星级目标差值
     * @param month 与guildRankInfo参数一致
     * @return
     */
    @RequestMapping("/starTargetDiff")
    @RequiresUser
    public Resp<DmYySidSegmentOriginStarDayDTO> starTargetDiff(String month) {
        Long uid = UserContext.getCurrentLoginUserUid();
        if (uid <= 0 || StringUtils.isBlank(month)) {
            return Resp.createBySuccess(null);
        }


        DmYySidSegmentOriginStarDay diff = guildRankV2Service.getStarTargetDiff(uid);
        if (diff == null || StringUtils.isBlank(diff.getDt())) {
            return Resp.createBySuccess(null);
        }

        month = month + "-01";
        try {
            String ptn = "yyyy-MM-dd";
            Date m = DateUtils.parseDate(month, ptn);
            Date nextMonth = DateUtils.addMonths(m, 1);
            Date d = DateUtils.parseDate(diff.getDt(), ptn);
            if (nextMonth.equals(DateUtils.truncate(d, Calendar.MONTH))) {
                String monthStr = DateFormatUtils.format(nextMonth, "yyyyMM");
                Map<String, Long> blueDiamond = moneyInfoService.queryGuildBlueDiamond(uid, 2, monthStr, monthStr);
                Long blueCount = blueDiamond.get(monthStr);
                diff.setMonthDiamond((double) (blueCount == null ? 0 : blueCount));
                DmYySidSegmentOriginStarDayDTO dto = new DmYySidSegmentOriginStarDayDTO(diff);
                if (diff.getIsMarsGuild() != null && diff.getIsMarsGuild() == IS_MARS && dto.getMarsSidOwnerid() != null) {
                    dto.setMarsSidOwnerYy(userInfoService.getYyByUid(dto.getMarsSidOwnerid()));
                    dto.setMarsSidOwnerName(thriftAdaptorService.getChannelName(dto.getMarsSidOwnerid()));
                }
                return Resp.createBySuccess(dto);
            }
        } catch (Exception e) {
            logger.error("starTargetDiff parseDate exception month:{}", month, e);
        }

        return Resp.createBySuccess(null);
    }


    /**
     * 公会段位最近12个月信息
     * @return
     */
    @RequestMapping("/lastTwelveMonth")
    @RequiresUser
    public Resp<List<LastTwelveMonthResp>> lastTwelveMonth(){
        Long uid = UserContext.getCurrentLoginUserUid();
        List<LastTwelveMonthResp> resp = null;
        try {
            resp = guildRankV2Service.lastTwelveMonth(uid);
        }catch (Exception e){
            logger.error(e.getMessage(), e);
        }
        return Resp.createBySuccess(resp);
    }

    /**
     * 导出
     * @param month
     * @param response
     * @throws Exception
     */
    @RequestMapping("/download")
    @RequiresUser
    public void download(String month, HttpServletResponse response){
        try {
            Resp<DmYySidSegmentOriginStarDayDTO> diffResp = starTargetDiff(month);
            DmYySidSegmentOriginStarDay diff = diffResp.getData();

            Long uid = UserContext.getCurrentLoginUserUid();
            String srMonth = month;
            month = month+"-01";
            ServletOutputStream out = response.getOutputStream();
            response.setContentType("multipart/form-data");
            response.setCharacterEncoding("utf-8");
            String fileName = new String((new SimpleDateFormat("yyyy-MM-dd").format(new Date()))
                    .getBytes(), "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename="+fileName+".xlsx");
            ExcelWriter writer = new ExcelWriter(out, ExcelTypeEnum.XLSX, true);
            Sheet sheet1 = new Sheet(1, 0);
            sheet1.setSheetName("第一个sheet");

            String thisMonth = "本期";
            String lastMonth = "上期";
            String nextMonth = "";
            try {
                int m = Integer.parseInt(StringUtils.split(month, "-")[1]);
                if (m >= 1 && m <= 12) {
                    thisMonth = m + "月";
                    lastMonth = (m > 1 ? m - 1 : 12) + "月";
                    nextMonth = (m == 12 ? 1 : m + 1) + "月";
                }
            } catch (Exception ex) {
                logger.error("", uid, month, ex);
            }

            List<List<String>> head = Lists.newArrayList();
            head.add(Lists.newArrayList("考核维度"));
            head.add(Lists.newArrayList("重要指标"));
            head.add(Lists.newArrayList(thisMonth + "数据"));
            head.add(Lists.newArrayList(thisMonth + "最终数据"));
            head.add(Lists.newArrayList(nextMonth + "上一星冲刺目标"));
            head.add(Lists.newArrayList("对比" + lastMonth + "数据"));
            head.add(Lists.newArrayList(nextMonth + "距离目标差值"));
            sheet1.setHead(head);

            DmYyGuildSegmentScoreMonth rankInfo = guildRankV2Service.guildRankInfo(uid, month);
            if (rankInfo == null){
                String currentDate = DateFormatUtils.format(DateUtils.addMonths(new Date(), -1),"yyyy-MM")+"-01";
                if (currentDate.equals(month)){
                    DmYyGuildSegmentScoreMonth buildData = guildRankV2Service.buildData(uid);
                    if (buildData.getPsSAll()>0){
                        rankInfo = buildData;
                    }
                }
            }

            List<List<String>> data = Lists.newArrayList();
            List<String> list1 = Lists.newArrayList();
            List<String> list2 = Lists.newArrayList();
            List<String> list3 = Lists.newArrayList();
            List<String> list4 = Lists.newArrayList();
            List<String> list5 = Lists.newArrayList();
            List<String> list6 = Lists.newArrayList();
            List<String> list7 = Lists.newArrayList();
            list1.add("公会营收");
            list1.add("旗下主播蓝钻收入");
            list1.add(formatDiamond(rankInfo.getMonthDiamond()));
            list1.add(rankInfo.getPsSMonthDiamond()+"星 "+formatDiamond(rankInfo.getMonthDiamond()));
            Double chongci = getLanZuanChongCi(rankInfo.getSprMonthDiamond(), rankInfo.getMonthDiamond(), srMonth);
            Double d = chongci-rankInfo.getMonthDiamond();
            String t = " 继续保持";
            if(d>0){
                t = " (差:"+ formatDiamond(d) +")";
            }
            t = "";
            list1.add(formatDiamond(chongci) +t);
            list1.add(formatDiamond(rankInfo.getMonthDiamondRr()));
            if(diff == null) {
                list1.add("");
            } else {
                double monthDiamondDiff = diff.getMonthDiamond() - chongci;
                if(monthDiamondDiff >= 0) {
                    list1.add("继续保持");
                } else {
                    list1.add("差:" + formatDiamond(-monthDiamondDiff));
                }
            }

            list2.add("开播管理");
            list2.add("日均有效开播主播数");
            list2.add(rankInfo.getValidLiveUv()+"");
            list2.add(rankInfo.getPsSLiveUv()+"星 "+rankInfo.getValidLiveUv()+"("+ formatDoule(rankInfo.getPsAcu()) +")");
            Double c = rankInfo.getSprValidLiveUv()-rankInfo.getValidLiveUv();
            String tip = " 继续保持";
            if(c>0){
                tip = " (差:"+formatDoule(c)+")";
            }
            tip = "";
            list2.add(rankInfo.getSprValidLiveUv()+tip);
            list2.add(rankInfo.getValidLiveUvRr()+"");
            if(diff == null || diff.getAvgValidLiveUvDiff() == null) {
                list2.add("");
            } else if(diff.getAvgValidLiveUvDiff() >= 0) {
                list2.add("继续保持");
            } else {
                list2.add("差:" + formatDoule(-diff.getAvgValidLiveUvDiff()));
            }

            if (oldTest(month)) {
            String acuDiff = "";
            if(diff == null || diff.getAnchorTotalScoreDiff() == null) {
                acuDiff = "";
            } else if(diff.getAnchorTotalScoreDiff() >= 0) {
                acuDiff = "继续保持";
            } else {
                acuDiff = "差:" + formatDoule(-diff.getAnchorTotalScoreDiff());
            }
            list3.add("开播管理");
            list3.add("日均超高人气有效开播主播数");
            list3.add(rankInfo.getAcu300Uv()+"");
            list3.add(rankInfo.getPsSLiveUv()+"星 "+rankInfo.getValidLiveUv()+"("+ formatDoule(rankInfo.getPsAcu()) +")");
            Double c2 = rankInfo.getSprAcu()-rankInfo.getPsAcu();
            String tip2 = " 继续保持";
            if(c2>0){
                tip2 = " (差:"+formatDoule(c2)+")";
            }
            tip2 = "";
            list3.add(rankInfo.getSprAcu()+tip2);
            list3.add(rankInfo.getAcu300UvRr()+"");
            list3.add(acuDiff);

            list4.add("开播管理");
            list4.add("日均高人气有效开播主播数");
            list4.add(rankInfo.getAcu50300Uv()+"");
            list4.add(rankInfo.getPsSLiveUv()+"星 "+rankInfo.getValidLiveUv()+"("+ formatDoule(rankInfo.getPsAcu()) +")");
            list4.add(rankInfo.getSprAcu()+tip2);
            list4.add(rankInfo.getAcu50300UvRr()+"");
            list4.add(acuDiff);

            list5.add("开播管理");
            list5.add("日均中人气有效开播主播数");
            list5.add(rankInfo.getAcu1050Uv()+"");
            list5.add(rankInfo.getPsSLiveUv()+"星 "+rankInfo.getValidLiveUv()+"("+ formatDoule(rankInfo.getPsAcu()) +")");
            list5.add(rankInfo.getSprAcu()+tip2);
            list5.add(rankInfo.getAcu1050UvRr()+"");
            list5.add(acuDiff);

            list6.add("开播管理");
            list6.add("日均非人气有效开播主播数");
            list6.add(rankInfo.getAcu10Uv()+"");
            list6.add(rankInfo.getPsSLiveUv()+"星 "+rankInfo.getValidLiveUv()+"("+ formatDoule(rankInfo.getPsAcu()) +")");
            list6.add(rankInfo.getSprAcu()+tip2);
            list6.add(rankInfo.getAcu10UvRr()+"");
            list6.add(acuDiff);
            } else {
                String levelDiff = diff == null || diff.getAidValueScoreDiff() == null ? "" : diff.getAidValueScoreDiff() >= 0 ?
                        "继续保持" : "差:" + formatDoule(-diff.getAidValueScoreDiff());
                list3.add("开播管理");
                list3.add("日均高优有效开播主播数");
                list3.add(rankInfo.getAvgHighAidNum()+"");
                list3.add(rankInfo.getPsSLiveUv()+"星 "+rankInfo.getValidLiveUv()+"("+ formatDoule(rankInfo.getAidValueScore()) +")");
                list3.add(rankInfo.getAidValueScoreSprintValue()+"");
                list3.add(rankInfo.getAvgHighAidNumRr()+"");
                list3.add(levelDiff);

                list4.add("开播管理");
                list4.add("日均腰部有效开播主播数");
                list4.add(rankInfo.getAvgWaistAidNum()+"");
                list4.add(rankInfo.getPsSLiveUv()+"星 "+rankInfo.getValidLiveUv()+"("+ formatDoule(rankInfo.getAidValueScore()) +")");
                list4.add(rankInfo.getAidValueScoreSprintValue()+"");
                list4.add(rankInfo.getAvgWaistAidNumRr()+"");
                list4.add(levelDiff);

                list5.add("开播管理");
                list5.add("日均潜力有效开播主播数");
                list5.add(rankInfo.getAvgTailAidNum()+"");
                list5.add(rankInfo.getPsSLiveUv()+"星 "+rankInfo.getValidLiveUv()+"("+ formatDoule(rankInfo.getAidValueScore()) +")");
                list5.add(rankInfo.getAidValueScoreSprintValue()+"");
                list5.add(rankInfo.getAvgTailAidNumRr()+"");
                list5.add(levelDiff);
            }

            list7.add("健康分管理");
            list7.add("低健康分主播数占比");
            list7.add(formatDoule(rankInfo.getBreakUvPp() * 100)+"%");
            list7.add(rankInfo.getPsSBreakUvPp()+"星 "+ formatDoule(rankInfo.getBreakUvPp() * 100) + "%");
            Double c3 = rankInfo.getSprBreakUvPp()-rankInfo.getBreakUvPp();
            String tip3 = " 继续保持";
            if(c3>0){
                tip3 = " (差:"+formatDoule(c3 * 100)+")";
            }
            tip3 = "";
            list7.add(formatDoule(rankInfo.getSprBreakUvPp() * 100)+"%"+tip3);
            list7.add(formatDoule(rankInfo.getBreakUvPpRr() * 100)+"%");
            if (diff == null || diff.getBreakUvRateDiff() == null) {
                list7.add("");
            } else if (diff.getBreakUvRateDiff() >= 0) {
                list7.add("继续保持");
            } else {
                list7.add("差:" + formatDoule(-diff.getBreakUvRateDiff() * 100) + "%");
            }

            data.add(list1); data.add(list2); data.add(list3);
            data.add(list4); data.add(list5); if(oldTest(month)){data.add(list6);}data.add(list7);
            writer.write0(data, sheet1);
            writer.finish();
            out.flush();
        }catch (Exception e){
            logger.error(e.getMessage(), e);
        }
    }

    private static boolean oldTest(String dateStr){
        try {
            Date date = DateUtils.parseDate(dateStr, "yyyy-MM-dd");
            // 2023-08-01
            return date.before(new Date(1690819200000L));
        }catch (Exception e){
            return true;
        }
    }

    private static String formatDoule(Double input){
        if (input == null){
            return "0.00";
        }
//        input = input * 100;
        DecimalFormat df = new DecimalFormat("##0.00");
        return df.format(input);
    }

    private static String formatDiamond(Double input){
        if (input == null){
            return "0";
        }
        DecimalFormat df = new DecimalFormat("###,###");
        return df.format(input);
    }


    private Double getLanZuanChongCi(Double spr_month_diamond, Double month_diamond, String month){
        double i = 0;
        if(month.contains("-01")){
            i = 0.05;
        }else if(month.contains("-02")){
            i = 0.04;
        }else if(month.contains("-03")){
            i = 0.06;
        }else if(month.contains("-04")){
            i = 0.05;
        }else if(month.contains("-05")){
            i = 0.08;
        }else if(month.contains("-06")){
            i = 0.07;
        }else if(month.contains("-07")){
            i = 0.06;
        }else if(month.contains("-08")){
            i = 0.05;
        }else if(month.contains("-09")){
            i = 0.04;
        }else if(month.contains("-10")){
            i = 0.08;
        }else if(month.contains("-11")){
            i = 0.04;
        }else if(month.contains("-12")) {
            i = 0.07;
        }
        return spr_month_diamond + (month_diamond*i);
    }


}
