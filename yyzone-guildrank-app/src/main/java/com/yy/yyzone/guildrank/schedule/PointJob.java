package com.yy.yyzone.guildrank.schedule;

import com.yy.yyzone.guildrank.annotation.JobLock;
import com.yy.yyzone.guildrank.service.ScoreMallService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PointJob {
    @Autowired
    private ScoreMallService scoreMallService;
    @Value("${rank.job.enableGrantPointJob:true}")
    private boolean enableGrantPointJob;

    /**
     * 发放积分（暂不启用积分）
     *
     * @return
     */
    // @JobLock
    // @Scheduled(cron = "25 */15 * * * ?")
    public Integer grantPoint() {
        int count = 0;
        log.info("enableGrantPointJob {}", enableGrantPointJob);
        if (enableGrantPointJob) {
            count += scoreMallService.grantPoint();
        }

        return count;
    }

    /**
     * 有超时（5min）发放中的记录告警
     *
     * @return
     */
    @Scheduled(cron = "35 */20 9-19 * * ?")
    @JobLock
    public Integer hasGrantingAlarm() {
        return scoreMallService.hasGrantingAlarm();
    }

    /**
     * 有发放失败的记录告警
     *
     * @return
     */
    @Scheduled(cron = "45 2/20 9-19 * * ?")
    @JobLock
    public Integer hasGrantFailAlarm() {
        return scoreMallService.hasGrantFailAlarm();
    }
}
