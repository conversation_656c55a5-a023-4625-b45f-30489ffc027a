package com.yy.yyzone.guildrank.interceptor;


import com.yy.ent.mobile.metrics.MetricsStopWatch;
import com.yy.yyzone.guildrank.annotation.JobLock;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.time.Duration;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021/3/3
 *
 */
@Order(Ordered.HIGHEST_PRECEDENCE)
@Aspect
@Component
@Slf4j
public class JobAspect {
    private static final Logger logger = LoggerFactory.getLogger(JobAspect.class);

    @Value("#{'${job.disabled:}'.split('[,\\n]')}")
    private Set<String> disabledJob;

    @Autowired
    private StringRedisTemplate redisTemplate;
    public static final String PREFIX = "yyzone.guildrank.lock.";

    @Around(value = "execution(* com.yy.yyzone.guildrank.schedule.*.*(..))")
    public Object around(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        Thread thread = Thread.currentThread();
        String oldName = thread.getName();
        String[] ss = StringUtils.split(proceedingJoinPoint.getSignature().getDeclaringTypeName(), ".");
        String sign = ss[ss.length - 1] + "_" + proceedingJoinPoint.getSignature().getName();
        thread.setName(sign + "-" + RandomStringUtils.randomAlphanumeric(10));

        Object o = null;
        String lockKey = null;
        MetricsStopWatch metricsStopWatch = MetricsStopWatch.startServerWatch();
        metricsStopWatch.uri(ss[ss.length - 1] + "/" + proceedingJoinPoint.getSignature().getName());
        try {
            if (isDisabled(proceedingJoinPoint)) {
                logger.info("{} disabled,return", sign);
                return null;
            }

            lockKey = tryLock(proceedingJoinPoint, sign);
            if (lockKey == null) {
                return null;
            }

            logger.info("{} begin", sign);
            o = proceedingJoinPoint.proceed(proceedingJoinPoint.getArgs());
            metricsStopWatch.successCode().markDurationAndCode();
        } catch (Exception ex) {
            metricsStopWatch.failCode().markDurationAndCode();
            logger.error("{} error", sign, ex);
        } finally {
            logger.info("{} finish return {} in {}ms", sign, o, metricsStopWatch.getTime());
            thread.setName(oldName);

            if (StringUtils.isNotBlank(lockKey)) {
                redisTemplate.delete(lockKey);
            }
        }

        return o;
    }

    /**
     * 是否禁用job
     * @param joinPoint
     * @return
     */
    private boolean isDisabled(ProceedingJoinPoint joinPoint) {
        String sign = joinPoint.getSignature().getDeclaringTypeName() + "#" + joinPoint.getSignature().getName();
        return CollectionUtils.isNotEmpty(disabledJob) && disabledJob.contains(sign);
    }

    /**
     * 尝试锁
     * @param joinPoint
     * @param sign
     * @return 锁失败null
     */
    private String tryLock(ProceedingJoinPoint joinPoint, String sign) {
        String lockKey = "";
        int lockTimeout = getLockTimeout(joinPoint);
        if (lockTimeout > 0) {
            lockKey = PREFIX + sign;
            if (BooleanUtils.isFalse(redisTemplate.opsForValue().setIfAbsent(lockKey, String.valueOf(System.currentTimeMillis()),
                    Duration.ofSeconds(lockTimeout)))) {
                logger.info("{} lock failed return,timeout:{}", sign, lockTimeout);
                return null;
            }
        }
        return lockKey;
    }

    private int getLockTimeout(ProceedingJoinPoint joinPoint) {
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            if (method.getDeclaringClass().isInterface()) {
                try {
                    method = joinPoint.getTarget().getClass().getDeclaredMethod(joinPoint.getSignature().getName(),
                            method.getParameterTypes());
                } catch (Exception ex) {
                    logger.error("get method exception", ex);
                }
            }

            JobLock lock = method.getAnnotation(JobLock.class);
            if (lock != null) {
                return lock.timeout();
            }

        } catch (Exception ex) {
            logger.error("getLockTimeout exception", ex);
        }

        return 0;
    }
}