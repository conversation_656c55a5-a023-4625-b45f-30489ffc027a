package com.yy.yyzone.guildrank.controller;

import com.alibaba.excel.EasyExcel;
import com.yy.yyzone.guildrank.dto.export.ExportData;
import com.yy.yyzone.guildrank.util.Resp;
import com.yy.yyzone.guildrank.util.excel.ExcelReadUtil;
import com.yy.yyzone.guildrank.util.excel.ExcelWriteUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/1/22
 */
@Slf4j
public class BaseController {
    protected Resp getBindingResult(BindingResult result) {
        if (result.hasErrors()) {
            List<String> errs = result.getFieldErrors().stream().map(FieldError::getDefaultMessage).collect(Collectors.toList());
            return Resp.createByError(StringUtils.join(errs, "、"));
        }
        return Resp.createBySuccess();
    }

    protected void export(List<String> columns, List<List<String>> values, String fileName) {
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        export(response, fileName, Arrays.asList(new ExportData(columns, values, fileName, null)));
    }

    protected void export(String fileName, ExportData exportData) {
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        export(response, fileName, Arrays.asList(exportData));
    }

    protected void export(HttpServletResponse response, String fileName, List<ExportData> dataList) {
        OutputStream out = null;
        Workbook wb = new SXSSFWorkbook();
        try {
            out = response.getOutputStream();
            if (!fileName.endsWith(ExcelReadUtil.XLSX_SUFFIX)) {
                fileName += "_" + DateFormatUtils.format(new Date(), "yyyyMMddHHmmss") + ExcelReadUtil.XLSX_SUFFIX;
            }
            response.setContentType("application/octet-stream;charset=utf-8");
            // response.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes(StandardCharsets.UTF_8), "iso8859-1"));
            // response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ";filename*=utf-8''" + URLEncoder.encode(fileName, "utf-8"));

            response.setHeader("Cache-Control", "private");
            response.flushBuffer();
            for (ExportData d : dataList) {
                ExcelWriteUtil.exportExcel2007(wb, d.getSheetName(), d.getTitles(), d.getRows(), out, d.getMerges());
            }
        } catch (Exception e) {
            log.error("exportData exception datasize:{},fileName:{}", dataList.size(), fileName, e);
        } finally {
            try {
                wb.write(out);
            } catch (Exception e) {
                log.warn("exportData write exception datasize:{},fileName:{}", dataList.size(), fileName, e);
            }
            try {
                out.flush();
            } catch (Exception e) {
                log.warn("exportData flush exception datasize:{},fileName:{}", dataList.size(), fileName, e);
            }
        }
    }
}