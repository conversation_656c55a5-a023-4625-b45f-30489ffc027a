package com.yy.yyzone.guildrank.controller;

import com.yy.yyzone.guildrank.service.RankCheckV3Service;
import com.yy.yyzone.guildrank.util.MyDateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/01/07
 **/
@RestController
@ConditionalOnProperty(prefix = "yy",name = "env", havingValue = "TEST")
public class TestController {

    @Autowired
    private RankCheckV3Service rankCheckService;
    @RequestMapping("/test")
    public void test() {
        rankCheckService.rankCheck(MyDateUtil.lastMonthEnd(), false);
    }

    @RequestMapping("/test1")
    public boolean test1() {
        return rankCheckService.rankCheckDone(MyDateUtil.parseDate("2021-12"));
    }
}
