package com.yy.yyzone.guildrank.controller;

import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Sheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.yy.java.webdb.WebdbUserInfo;
import com.yy.yyzone.guildrank.api.ContractInfoService;
import com.yy.yyzone.guildrank.api.RoleService;
import com.yy.yyzone.guildrank.api.RoleType;
import com.yy.yyzone.guildrank.api.dto.QueryContractInfoByAnchorUidsReqV2;
import com.yy.yyzone.guildrank.api.dto.QueryContractInfoByAnchorUidsResV2;
import com.yy.yyzone.guildrank.db.gen.model.DmYySidSegmentScoreMonth;
import com.yy.yyzone.guildrank.dto.GuildInfo;
import com.yy.yyzone.guildrank.dto.LastTwelveMonthResp;
import com.yy.yyzone.guildrank.service.*;
import com.yy.yyzone.guildrank.util.Resp;
import com.yy.yyzone.guildrank.util.YyTicketUtil;
import com.yy.yyzone.guildrank.yrpc.dto.PGetGuildInfoResp64;
import com.yy.zone.shiro.user.UserContext;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.shiro.authz.annotation.RequiresUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Generated;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 公会段位接口
 *<AUTHOR> gen
 **/
@RestController
@Generated("忽略代码规范检查")
public class GuildRankController {

    private static Logger logger = LoggerFactory.getLogger(GuildRankController.class);

    private static final String YY_80 = "80";

    @Autowired
    private GuildRankService guildRankService;

    @Autowired
    private GuildRankV2Service guildRankV2Service;

    @Autowired
    private RankCacheService rankCacheService;

    @Reference(protocol = "yyp", owner = "contractInfo_2")
    private ContractInfoService contractInfoService;
    @Reference(protocol = "yyp", owner = "yyRole")
    private RoleService roleService;
    @Autowired
    private UserInfoService userInfoService;
    public static final Duration TIME = Duration.ofMinutes(5);

    @Autowired
    private GuildRankTransService transService;

    @Autowired
    private RankCacheV3Service rankCacheV3Service;

    /**
     * 查询上个月的数据生成没有
     * @return true表示已生成
     */
    @RequestMapping("/hasUpdate")
    @RequiresUser
    public Resp<Boolean> hasUpdate() {
        boolean hasUpdate = guildRankV2Service.hasUpdate();
        logger.info(UserContext.getCurrentLoginUserUid() + "====" + hasUpdate);
        return Resp.createBySuccess(hasUpdate);
    }

    /**
     * 根据月份查询公会段位信息
     * @param month 格式：2019-06
     * @re turn
     */
    @RequestMapping("/guildRankInfo")
    @RequiresUser
    public Resp<DmYySidSegmentScoreMonth> guildRankInfo(String month) {
        Long uid = UserContext.getCurrentLoginUserUid();
        if (uid <= 0) {
            return Resp.createByError("没登陆");
        }
        if (StringUtils.isBlank(month)) {
            return Resp.createByError("请传入月份");
        }
        month = month + "-01";
        logger.info("uid:{}, month:{}", uid, month);
        DmYySidSegmentScoreMonth data = guildRankService.guildRankInfo(uid, month);
        if (data != null) {
            return Resp.createBySuccess(data);
        } else {// 刚升级的公会db没有数据，查询一下构造等级
            String currentDate = DateFormatUtils.format(DateUtils.addMonths(new Date(), -1), "yyyy-MM") + "-01";
            if (currentDate.equals(month)) {
                data = guildRankService.buildData(uid);
                if (data.getPsSAllCor() > 0) {
                    return Resp.createBySuccess(data);
                }
            }
        }
        return Resp.createByError(2, "查无数据");
    }

    /**
     * 公会段位最近12个月信息
     * @return
     */
    @RequestMapping("/lastTwelveMonth")
    @RequiresUser
    public Resp<List<LastTwelveMonthResp>> lastTwelveMonth() {
        Long uid = UserContext.getCurrentLoginUserUid();
        List<LastTwelveMonthResp> resp = null;
        try {
            resp = guildRankService.lastTwelveMonth(uid);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return Resp.createBySuccess(resp);
    }

    /**
     * 获取临时会话
     * @return
     */
    @RequestMapping("/temporaryChat")
    @RequiresUser
    public Resp<String> temporaryChat() {
        String airPlane = "";
        try {
            Long uid = UserContext.getCurrentLoginUserUid();
            int type = userInfoService.getGuildType(uid);
            Long toUid = 0L;
            if (type == 0) {// 普通公会
                toUid = 210371277L;
            } else {
                Long serviceyy = guildRankService.getTemporaryChat(uid);
                if (serviceyy > 0) {
                    toUid = userInfoService.getUidByYy(String.valueOf(serviceyy));
                } else {
                    toUid = 1101217371L;
                }
            }
            airPlane = YyTicketUtil.getAirplaneTicket(String.valueOf(uid), String.valueOf(toUid));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return Resp.createBySuccess(airPlane);
    }

    /**
     * 联系官方
     * @return
     */
    @RequestMapping("/officialChat")
    @RequiresUser
    public Resp<String> officialChat() {
        String airPlane = "";
        try {
            Long uid = UserContext.getCurrentLoginUserUid();
            int type = userInfoService.getGuildType(uid);
            Long toUid = 0L;
            if (type == 0) {
                // 普通公会 10频道
                return Resp.createBySuccess("yy://pd-[sid=10&subid=845374350]");
            } else {
                Long serviceyy = guildRankService.getTemporaryChat(uid);
                if (serviceyy > 0) {
                    toUid = userInfoService.getUidByYy(String.valueOf(serviceyy));
                } else {
                    toUid = 1101217371L;
                }
            }
            airPlane = YyTicketUtil.getAirplaneTicket(String.valueOf(uid), String.valueOf(toUid));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return Resp.createBySuccess(airPlane);
    }

    /**
     * 获取临时会话运营昵称
     * @return
     */
    @RequestMapping("/temporaryChatNick")
    @RequiresUser
    public Resp<String> temporaryChatNick() {
        String nick = "";
        Long uid = UserContext.getCurrentLoginUserUid();

        try {
            int type = userInfoService.getGuildType(uid);
            if (type != 0) {
                nick = guildRankService.getTemporaryChatNick(uid);
            }
        } catch (Exception e) {
            logger.error("temporaryChatNick uid:{}", uid, e);
        }
        return Resp.createBySuccess(nick);
    }

    /**
     * 导出
     * @param month
     * @param response
     * @throws Exception
     */
    @RequestMapping("/download")
    @RequiresUser
    public void download(String month, HttpServletResponse response) {
        try {
            Long uid = UserContext.getCurrentLoginUserUid();
            month = month + "-01";
            ServletOutputStream out = response.getOutputStream();
            response.setContentType("multipart/form-data");
            response.setCharacterEncoding("utf-8");
            String fileName = new String((new SimpleDateFormat("yyyy-MM-dd").format(new Date()))
                    .getBytes(), "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            ExcelWriter writer = new ExcelWriter(out, ExcelTypeEnum.XLSX, true);
            Sheet sheet1 = new Sheet(1, 0);
            sheet1.setSheetName("第一个sheet");
            List<List<String>> head = Lists.newArrayList();
            head.add(Lists.newArrayList("考核维度"));
            head.add(Lists.newArrayList("重要指标"));
            head.add(Lists.newArrayList("本期数据"));
            head.add(Lists.newArrayList("对比上期"));
            head.add(Lists.newArrayList("上一星冲刺目标"));
            head.add(Lists.newArrayList("维度评分"));
            head.add(Lists.newArrayList("维度同段位排名"));
            sheet1.setHead(head);

            DmYySidSegmentScoreMonth rankInfo = guildRankService.guildRankInfo(uid, month);
            if (rankInfo == null) {
                String currentDate = DateFormatUtils.format(DateUtils.addMonths(new Date(), -1), "yyyy-MM") + "-01";
                if (currentDate.equals(month)) {
                    DmYySidSegmentScoreMonth buildData = guildRankService.buildData(uid);
                    if (buildData.getPsSAllCor() > 0) {
                        rankInfo = buildData;
                    }
                }
            }

            List<List<String>> data = Lists.newArrayList();
            List<String> list1 = Lists.newArrayList();
            List<String> list2 = Lists.newArrayList();
            List<String> list3 = Lists.newArrayList();
            List<String> list4 = Lists.newArrayList();
            List<String> list5 = Lists.newArrayList();
            List<String> list6 = Lists.newArrayList();
            list1.add("公会营收");
            list1.add("旗下主播蓝钻收入");
            list1.add(formatDiamond(rankInfo.getMonthDiamond()));
            list1.add(formatDiamond(rankInfo.getMonthDiamondRr()));
            list1.add(formatDiamond(rankInfo.getSprMonthDiamond()));
            list1.add(formatDoule(rankInfo.getPs4()));
            list1.add(rankInfo.getPsS4Rn() + "");

            list2.add("人气主播");
            list2.add("人气主播个数");
            list2.add(rankInfo.getAcu10Uv() + "");
            list2.add(rankInfo.getAcu10UvRr() + "");
            list2.add(Math.ceil(rankInfo.getSprAcu10Uv()) + " ");
            list2.add(formatDoule(rankInfo.getPs2()));
            list2.add(rankInfo.getPsS2Rn() + "");

            list3.add("活跃主播");
            list3.add("活跃主播个数");
            list3.add(rankInfo.gethActUv() + "");
            list3.add(rankInfo.gethActUvRr() + "");
            list3.add(Math.ceil(rankInfo.getSprHActUv()) + " ");
            list3.add(formatDoule(rankInfo.getPs3()));
            list3.add(rankInfo.getPsS3Rn() + "");

            list4.add("新主播运营");
            list4.add("新主播数");
            list4.add(rankInfo.getNewUv() + "");
            list4.add(rankInfo.getNewUvRr() + "");
            list4.add(Math.ceil(rankInfo.getSprNewUv()) + " ");
            list4.add(formatDoule(rankInfo.getPs1()));
            list4.add(rankInfo.getPsS1Rn() + "");

            list5.add("新主播运营");
            list5.add("有效开播新主播数");
            list5.add(rankInfo.getValidLiveNewUv() + "");
            list5.add(rankInfo.getValidLiveNewUvRr() + "");
            list5.add(Math.ceil(rankInfo.getSprValidLiveNewUv()) + " ");
            list5.add(formatDoule(rankInfo.getPs1()));
            list5.add(rankInfo.getPsS1Rn() + "");

            list6.add("违规管理");
            list6.add("违规占比");
            list6.add(formatDoule(rankInfo.getBreakRt()) + "%");
            list6.add(formatDoule(rankInfo.getBreakRr()) + "%");
            list6.add(formatDoule(rankInfo.getSprP5()) + "%");
            list6.add(formatDoule(rankInfo.getPs5()));
            list6.add(rankInfo.getPsS5Rn() + "");

            data.add(list1);
            data.add(list2);
            data.add(list3);
            data.add(list4);
            data.add(list5);
            data.add(list6);
            writer.write0(data, sheet1);
            writer.finish();
            out.flush();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }


    /**
     * 查询公会段位最新段位
     * @return
     */
    @RequestMapping("/query")
    public Resp<Integer> query(long uid){
        if(transService.useV3("GuildRankController.query")) {
            return Resp.createBySuccess(rankCacheV3Service.queryCurrentRank(uid));
        }

        return Resp.createBySuccess(rankCacheService.queryCurrentRank(uid));
    }

    private static String formatDoule(Double input) {
        if (input == null) {
            return "0.00";
        }
        input = input * 100;
        DecimalFormat df = new DecimalFormat("##0.00");
        return df.format(input);
    }

    private static String formatDiamond(Double input) {
        if (input == null) {
            return "0";
        }
        DecimalFormat df = new DecimalFormat("###,###");
        return df.format(input);
    }

    public static void main(String[] args) {
        System.out.println(300000000 + (242983420*0.04));
    }


    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * 获取公会段位信息
     * @param yyno YY号 自动判断公会（获取自身信息）或主播（获取签约公会信息）优先uid
     * @param uid uid 自动判断公会（获取自身信息）或主播（获取签约公会信息）优先uid
     * @return
     */
    @RequestMapping("/queryGuildInfo")
    public Resp<GuildInfo> queryGuildInfo(String yyno, Long uid) {
        Resp<GuildInfo> resp = doQueryGuildInfo(yyno, uid);
        if (resp != null) {
            setRank(resp.getData());
        }
        return resp;
    }

    /**
     * 获取公会段位信息
     * @param yyno YY号 自动判断公会（获取自身信息）或主播（获取签约公会信息）优先uid
     * @param uid uid 自动判断公会（获取自身信息）或主播（获取签约公会信息）优先uid
     * @return
     */
    public Resp<GuildInfo> doQueryGuildInfo(String yyno, Long uid) {
        logger.info("queryGuildInfo yyno:{} uid:{}", yyno, uid);
        try {
            if (StringUtils.isNotBlank(yyno)) {
                long l = Long.parseLong(yyno);
                if (l <= 0) {
                    return Resp.createByError("error yyno");
                }
            }
        } catch (Exception ex) {
            logger.warn("queryGuildInfo yyno fail yyno:{} uid:{}", yyno, uid);
            return null;
        }

        String cacheKey;
        if (uid != null) {
            if (uid <= 0) {
                return Resp.createByError("error uid");
            }
            cacheKey = MessageFormat.format("GuildRank_queryGuildInfo_uid_{0}", String.valueOf(uid));
        } else if (StringUtils.isNotBlank(yyno)) {
            cacheKey = MessageFormat.format("GuildRank_queryGuildInfo_yyno_{0}", yyno);
        } else {
            return null;
        }

        String cacheVal = redisTemplate.opsForValue().get(cacheKey);
        if (StringUtils.isNotEmpty(cacheVal)) {
            return Resp.createBySuccess(JSON.parseObject(cacheVal, GuildInfo.class));
        }

        GuildInfo info = new GuildInfo();
        if (uid == null) {
            // 根据yy号获取uid
            uid = userInfoService.getUidByYy(yyno);
            logger.info("queryGuildInfo.getUidByYy yyno:{} uid:{}", yyno, uid);
            if (uid == null || uid < 1) {
                // 未获取到
                redisTemplate.opsForValue().set(cacheKey, JSON.toJSONString(info), TIME);
                return Resp.createBySuccess(info);
            }
        }

        // 当做公会uid尝试去缓存查询
        GuildInfo fromCache = getFromCache(uid);
        if (fromCache != null) {
            redisTemplate.opsForValue().set(cacheKey, JSON.toJSONString(fromCache), TIME);
            return Resp.createBySuccess(fromCache);
        }

        Long guildUid = 0L;
        String guildYyno = "";

        // 判断用户类型
        RoleType.GetRoleTypeReq req = RoleType.GetRoleTypeReq.newBuilder().setUid(uid).setType(2).build();
        RoleType.GetRoleTypeResp resp = roleService.getRoleType(req);
        int userType = resp.getRole();
        logger.info("queryGuildInfo.RoleType yyno:{} userType:{}", yyno, userType);
        boolean isAnchor = true;
        if (userType == 2 || userType == 4) {
            // 2公会（新） 或 4主播公会（过渡期角色）
            isAnchor = false;
            guildUid = uid;
            guildYyno = yyno;
        }

        if (isAnchor) {
            QueryContractInfoByAnchorUidsReqV2 req2 = QueryContractInfoByAnchorUidsReqV2.builder()
                    .uids(Arrays.asList(uid)).build();
            QueryContractInfoByAnchorUidsResV2 res = contractInfoService.queryContractInfoByAnchorUids(req2);
            if (MapUtils.isNotEmpty(res.getData())) {
                guildUid = res.getData().get(uid);
            }

            String ext = res.getExtendData().get("onlyshow");
            if (StringUtils.isNotBlank(ext)) {
                try {
                    JSONObject obj = JSONObject.parseObject(ext);
                    Long showGuildUid = obj.getLong(String.valueOf(uid));
                    if (showGuildUid != null && showGuildUid > 0) {
                        guildUid = showGuildUid;
                    }
                } catch (Exception e) {
                    logger.error("queryContractInfoByAnchorUids showGuild exception uid={} res={}", uid, res, e);
                }
            }
        }

        if (guildUid != null && guildUid > 0) {
            fromCache = getFromCache(guildUid);
            if (fromCache != null) {
                redisTemplate.opsForValue().set(cacheKey, JSON.toJSONString(fromCache), TIME);
                return Resp.createBySuccess(fromCache);
            }

            //获取公会名称
            String guildName = userInfoService.getGuildName(guildUid);
            logger.info("queryGuildInfo.GuildName yyno:{} guildUid:{} guildName:{}", yyno, guildUid, guildName);
            if (StringUtils.isBlank(guildName)) {
                // 获取公会昵称
                WebdbUserInfo userInfo = userInfoService.getUserInfo(guildUid);
                logger.info("queryGuildInfo.userInfo yyno:{} guildUid:{} nick:{}", yyno, guildUid,
                        userInfo == null ? "getUserInfo returns null" : userInfo.getNick());
                if (userInfo != null) {
                    guildName = userInfo.getNick();
                }
            }
            info.setName(guildName);

            info.setType(getGuildType(guildUid));

            if (StringUtils.isBlank(guildYyno)) {
                WebdbUserInfo userInfo = userInfoService.getUserInfo(guildUid);
                if (userInfo != null) {
                    guildYyno = userInfo.getYyno();
                }
            }
            info.setYy_num(guildYyno);
            info.setUid(guildUid);

            redisTemplate.opsForValue().set(getCacheKey(guildUid), JSON.toJSONString(info), TIME);
        }

        redisTemplate.opsForValue().set(cacheKey, JSON.toJSONString(info), TIME);
        return Resp.createBySuccess(info);
    }

    private void setRank(GuildInfo info) {
        if (info == null || info.getUid() == null) {
            return;
        }
        if (transService.useV3("GuildRankController.query")) {
            info.setRank(rankCacheV3Service.queryCurrentRank(info.getUid()));
        } else {
            info.setRank(rankCacheService.queryCurrentRank(info.getUid()));
        }
    }

    private int getGuildType(Long guildUid) {
        PGetGuildInfoResp64 rsp = userInfoService.getGuildInfoFromCache(guildUid);
        logger.info("getGuildType guildUid:{} goldenDate:{} starDate:{}", guildUid, rsp.getGoldenDate(), rsp.getStarDate());
        if (StringUtils.isNotEmpty(rsp.getGoldenDate())) {
            return 2;
        }
        if (StringUtils.isNotEmpty(rsp.getStarDate())) {
            return 1;
        }
        return 0;
    }

    private String getCacheKey(long guildUid) {
        String key = MessageFormat.format("GuildRank_queryGuildInfo_guilduid_{0}", String.valueOf(guildUid));
        return key;
    }

    private GuildInfo getFromCache(long guildUid) {
        String cacheValByGuildUid = redisTemplate.opsForValue().get(getCacheKey(guildUid));
        if (StringUtils.isNotEmpty(cacheValByGuildUid)) {
            return JSON.parseObject(cacheValByGuildUid, GuildInfo.class);
        }

        return null;
    }


    /**
     1. 当前页面登录是主播号
     2. 只能调起yy号为80开头的账号
     * @return
     */
    /**
     * 获取临时会话
     * @return
     */
    @RequestMapping("/chat80")
    @RequiresUser
    public Resp<String> chat80(long yy) {
        String airPlane = "";
        try {
            Long uid = UserContext.getCurrentLoginUserUid();
            if (yy <=0 || !"80".equals(String.valueOf(yy).substring(0,2))){
                return Resp.createByError("yy号不正确"+yy);
            }
            Long toUid = userInfoService.getUidByYy(String.valueOf(yy));
            // 判断用户类型
            RoleType.GetRoleTypeReq req = RoleType.GetRoleTypeReq.newBuilder().setUid(uid).setType(2).build();
            RoleType.GetRoleTypeResp resp = roleService.getRoleType(req);
            int userType = resp.getRole();
            if (userType == 2 || userType == 4) {
                return Resp.createByError("不是主播");
            }
            airPlane = YyTicketUtil.getAirplaneTicket(String.valueOf(uid), String.valueOf(toUid));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return Resp.createBySuccess(airPlane);
    }

    /**
     * 获取临时会话: 只能调起yy号为80开头的账号，不限制身份
     * @return
     */
    @RequestMapping("/chatAll80")
    @RequiresUser
    public Resp<String> chatAll80(long yy) {
        String airPlane = "";
        try {
            Long uid = UserContext.getCurrentLoginUserUid();
            if (yy <= 0 || !String.valueOf(yy).startsWith(YY_80)) {
                return Resp.createByError("yy号不正确"+yy);
            }
            Long toUid = userInfoService.getUidByYy(String.valueOf(yy));
            airPlane = YyTicketUtil.getAirplaneTicket(String.valueOf(uid), String.valueOf(toUid));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return Resp.createBySuccess(airPlane);
    }
}
