package com.yy.yyzone.guildrank.schedule;

import com.yy.yyzone.guildrank.annotation.JobLock;
import com.yy.yyzone.guildrank.constant.BigdaSyncConstant;
import com.yy.yyzone.guildrank.db.custom.mapper.BiExtMapper;
import com.yy.yyzone.guildrank.db.gen.mapper.YyDmEntityGuildCmpHealthAnalysisExptDiMapper;
import com.yy.yyzone.guildrank.db.gen.model.YyDmEntityGuildCmpHealthAnalysisExptDiExample;
import com.yy.yyzone.guildrank.service.BigdaSyncDetailService;
import com.yy.yyzone.guildrank.util.MyDateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class DataCleanJob {
    private static final int DEL_LIMIT = 1000;
    private static final int KEEP_LATEST_COUNT = 5;
    @Autowired
    private BiExtMapper biExtMapper;
    @Autowired
    private YyDmEntityGuildCmpHealthAnalysisExptDiMapper exptDiMapper;
    @Autowired
    private BigdaSyncDetailService syncDetailService;
    @Value("${rank.job.enableCleanCmpHealthDi:true}")
    private boolean enableCleanCmpHealthDi;
    @Value("${rank.job.enableCleanCmpHealthDiTmp:true}")
    private boolean enableCleanCmpHealthDiTmp;

    /**
     * 定时删除yy_dm_entity_guild_cmp_health_analysis_di 保留月最后一天以及最新5日数据
     *
     * @return
     */
    @JobLock
    @Scheduled(cron = "55 20 1/2 * * ?")
    public Integer cleanCmpHealthDi() {
        log.info("enableCleanCmpHealthDi {}", enableCleanCmpHealthDi);
        if (!enableCleanCmpHealthDi) {
            return 0;
        }
        String tbl = "yy_dm_entity_guild_cmp_health_analysis_di";
        List<Date> dates = biExtMapper.selectDt(tbl);
        dates = dates.stream().sorted(Comparator.reverseOrder()).skip(KEEP_LATEST_COUNT).collect(Collectors.toList());
        log.info("cleanCmpHealthDi dates:{}", MyDateUtil.fmtDate(dates));
        int i = 0;
        for (Date d : dates) {
            if (MyDateUtil.isMonthEnd(d)) {
                log.info("cleanCmpHealthDi month end dt:{}", MyDateUtil.fmt(d));
                continue;
            }
            log.info("cleanCmpHealthDi dt:{}", MyDateUtil.fmt(d));
            int delCount = 0;
            int count = 0;
            do {
                delCount = biExtMapper.deleteByDt(tbl, d, DEL_LIMIT);
                count += delCount;
            } while (delCount > 0);
            log.info("cleanCmpHealthDi del dt:{},count:{}", MyDateUtil.fmt(d), count);
            i++;
        }

        return i;
    }

    /**
     * 定时删除yy_dm_entity_guild_cmp_health_analysis_di_tmp
     *
     * @return
     */
    @JobLock
    @Scheduled(cron = "55 20 */2 * * ?")
    public Integer cleanCmpHealthDiTmp() {
        log.info("enableCleanCmpHealthDiTmp {}", enableCleanCmpHealthDiTmp);
        if (!enableCleanCmpHealthDiTmp) {
            return 0;
        }
        String tbl = "yy_dm_entity_guild_cmp_health_analysis_di_tmp";
        List<Date> dates = biExtMapper.selectDt(tbl);
        dates = dates.stream().sorted(Comparator.reverseOrder()).skip(KEEP_LATEST_COUNT).collect(Collectors.toList());
        log.info("cleanCmpHealthDiTmp dates:{}", MyDateUtil.fmtDate(dates));
        int i = 0;
        for (Date d : dates) {
            boolean b = syncDetailService.hasCheckDone(BigdaSyncConstant.TableName.CMP_HEALTH_DI, d);
            log.info("cleanCmpHealthDiTmp hasCheckDone dt:{},result:{}", MyDateUtil.fmtDate(d), b);
            if (!b) {
                continue;
            }
            log.info("cleanCmpHealthDiTmp dt:{}", MyDateUtil.fmt(d));
            int delCount = 0;
            int count = 0;
            do {
                delCount = biExtMapper.deleteByDt(tbl, d, DEL_LIMIT);
                count += delCount;
            } while (delCount > 0);
            log.info("cleanCmpHealthDiTmp del dt:{},count:{}", MyDateUtil.fmt(d), count);
            i++;
        }

        return i;
    }

    @JobLock
    @Scheduled(cron = "22 22 2 * * ?")
    public Integer cleanCmpHealthExptDiTmp() {
        Date max = DateUtils.addDays(new Date(), -5);
        YyDmEntityGuildCmpHealthAnalysisExptDiExample example = new YyDmEntityGuildCmpHealthAnalysisExptDiExample();
        example.createCriteria().andDtLessThan(max);
        int count = exptDiMapper.deleteByExample(example);
        log.info("cleanCmpHealthExptDiTmp max:{},count:{}", MyDateUtil.fmt(max), count);
        return count;
    }
}
