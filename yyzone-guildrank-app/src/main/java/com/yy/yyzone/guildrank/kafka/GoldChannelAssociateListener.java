package com.yy.yyzone.guildrank.kafka;

import com.yy.ent.mobile.metrics.MetricsStopWatch;
import com.yy.yyzone.guildrank.constant.RankConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

/**
 * 关新增联或解除关联YY号到经纪公司时kafka消息通知
 */
@Slf4j
@Component
public class GoldChannelAssociateListener {
    @Autowired
    private StringRedisTemplate redisTemplate;

    @KafkaListener(topics = {"AssoicateCompanyNotifty"}, groupId = "${kafka.goldchannel.group-id}", containerFactory = "goldChannelListenerFactory")
    public void processMessage(ConsumerRecord<String, String> record, Acknowledgment ack) {
        log.info("GoldChannelAssociateListener processMessage record:{}", record);
        MetricsStopWatch metricsStopWatch = MetricsStopWatch.startServerWatch();
        boolean flag = false;
        try {
            redisTemplate.convertAndSend(RankConstant.RedisConstants.ASSOICATE_COMPANY_CHANNEL, record.value());
            ack.acknowledge();
            log.info("GoldChannelAssociateListener processMessage end");
            flag = true;
        } finally {
            metricsStopWatch = flag ? metricsStopWatch.successCode() : metricsStopWatch.failCode();
            metricsStopWatch.uri("kafka/AssoicateCompanyNotifty").markDurationAndCode();
        }
    }
}