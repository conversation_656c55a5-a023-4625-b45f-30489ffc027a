package com.yy.yyzone.guildrank.controller;

import com.google.common.collect.Lists;
import com.yy.yyzone.guildrank.constant.RankConstant;
import com.yy.yyzone.guildrank.dto.*;
import com.yy.yyzone.guildrank.dto.export.ExportData;
import com.yy.yyzone.guildrank.dto.guildrankconfig.TaskIndexConfig;
import com.yy.yyzone.guildrank.dto.guildranktaskconfig.GuildRankTaskDTO;
import com.yy.yyzone.guildrank.dto.guildranktaskconfig.GuildRankTaskDetailsDTO;
import com.yy.yyzone.guildrank.service.*;
import com.yy.yyzone.guildrank.util.MyDateUtil;
import com.yy.yyzone.guildrank.util.Resp;
import com.yy.zone.shiro.user.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.shiro.authz.annotation.RequiresUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.lang.reflect.Method;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.yy.yyzone.guildrank.util.StrUtils.toCamelCase;
import static com.yy.yyzone.guildrank.constant.RankConstant.DataIndex.*;
import static com.yy.yyzone.guildrank.constant.RankConstant.TaskIndex.*;

/**
 * 公会段位接口V3版本(2024-10-01上线使用)
 **/
@Slf4j
@RestController
@RequestMapping("/v3")
public class GuildRankV3Controller extends BaseController {
    @Autowired
    private GuildRankV3Service guildRankV3Service;

    @Autowired
    private RankCacheV3Service rankCacheV3Service;

    @Autowired
    private GuildRankConfigService configService;

    @Autowired
    private GoldChannelService goldChannelService;
    @Autowired
    private GuildRankTaskDistributionService guildRankTaskDistributionService;
    /**
     * 查询数据是否生成
     *
     * @return true表示已生成
     */
    @RequiresUser
    @GetMapping("/hasUpdate")
    public Resp<Boolean> hasUpdate() {
        boolean hasUpdate = rankCacheV3Service.hasUpdate();
        return Resp.createBySuccess(hasUpdate);
    }

    /**
     * 根据月份查询公会段位信息
     *
     * @param month 月份 yyyy-MM
     * @return
     */
    @RequiresUser
    @RequestMapping("/guildRankInfo")
    public Resp<GuildRankInfoDTO> guildRankInfo(@DateTimeFormat(pattern = "yyyy-MM") Date month) {
        GuildRankInfoDTO dto = guildRankV3Service.guildRankInfo(getCompanyUid(), month);
        return Resp.createBySuccess(dto);
    }

    /**
     * 雷达图数据
     *
     * @return
     */
    @RequiresUser
    @GetMapping("radarChartData")
    public Resp<RadarChartData> radarChartData() {
        RadarChartData dto = guildRankV3Service.getRadarChartData(getCompanyUid());
        return Resp.createBySuccess(dto);
    }

    /**
     * 获取【任务指标】分值设置
     *
     * @return
     */
    @RequiresUser
    @GetMapping("/getTaskIndexConfig")
    public Resp<TaskIndexConfig> getTaskIndexConfig() {
        return Resp.createBySuccess(configService.getTaskIndexConfig(true));
    }

    /**
     * 公会段位最近12个月信息
     *
     * @return
     */
    @RequiresUser
    @GetMapping("/lastTwelveMonth")
    public Resp<List<LastTwelveMonthV3Resp>> lastTwelveMonth() {
        List<LastTwelveMonthV3Resp> resp = guildRankV3Service.lastTwelveMonth(getCompanyUid());
        return Resp.createBySuccess(resp);
    }

    /**
     * 导出公会等级报表
     * @param month 格式yyyy-MM
     */
    @GetMapping("/guid-rank-task/export")
    public void exportGuildRankTask(@RequestParam(required = true) @DateTimeFormat(pattern = "yyyy-MM") Date month){
        int dt  =Integer.parseInt(DateFormatUtils.format(month,"yyyyMM"));
        GuildRankTaskDTO guildRankTaskDTO = guildRankTaskDistributionService.getGuildTasks(getCompanyUid(), dt);
        List<List<String>> datas = Collections.emptyList();
        if(guildRankTaskDTO != null){
           datas = guildRankTaskDTO.getAllTaskDetails().stream().map(task ->{return Arrays.asList(task.getTaskName(),task.getDetail(),task.getCompletion(),String.valueOf(task.getScore()));}).collect(Collectors.toList());
        }
        int toDayDt = Integer.parseInt(DateFormatUtils.format(new Date(),"yyyyMM"));
        String monthStr = DateFormatUtils.format(month,"M");
        List<String> columns = dt < toDayDt ? Arrays.asList("任务名称", "任务详情", monthStr+ "月完成情况",monthStr+"月得分") : Arrays.asList("任务名称", "任务详情", monthStr+"月预计完成情况", monthStr+"月预计得分") ;
        export(columns, datas, "公会等级任务-" + MyDateUtil.fmtMonth(month));
    }

    /**
     * 导出报表
     *
     * @param month 月份 yyyy-MM
     * @param type  类型 0数据指标 1任务指标
     */
    @GetMapping("/export")
    public void export(@DateTimeFormat(pattern = "yyyy-MM") Date month, @RequestParam(required = false, defaultValue = "0") int type) throws Exception {
        // 隐藏任务指标
        type = 0;
        GuildRankInfoDTO dto = guildRankV3Service.guildRankInfo(getCompanyUid(), month);
        YyDmEntityGuildCmpHealthAnalysisDiDTO self = dto.getThisMonthDi() == null
                ? new YyDmEntityGuildCmpHealthAnalysisDiDTO()
                : dto.getThisMonthDi();
        dto.setThisMonthDi(self);
        YyDmEntityGuildCmpHealthAnalysisDiDTO last = dto.getLastMonthDi() == null
                ? new YyDmEntityGuildCmpHealthAnalysisDiDTO()
                : dto.getLastMonthDi();
        dto.setLastMonthDi(last);
        YyDmEntityGuildCmpHealthAnalysisDiDTO next = dto.getNextMonthDi();
        int i = 10;
        List<String> columns = new ArrayList<>(i);
        List<List<String>> values = new ArrayList<>(i);
        String name = MyDateUtil.fmtMonth(month);
        String ptn = "M月";
        String thisMonthStr = DateFormatUtils.format(month, ptn);
        String lastMonthStr = DateFormatUtils.format(DateUtils.addMonths(month, -1), ptn);
        String nextMonthStr = DateFormatUtils.format(DateUtils.addMonths(month, 1), ptn);
        boolean hasNext = next != null;
        List<CellRangeAddress> ranges = null;

        if (type == 0) {
            name += "数据指标";
            columns.addAll(Arrays.asList("考核维度", "考核指标", thisMonthStr + "得分", "环比" + lastMonthStr + "得分"));
            if (hasNext) {
                columns.add(nextMonthStr + "预计得分");
            }
            columns.addAll(Arrays.asList(thisMonthStr + "数据", "环比" + lastMonthStr + "数据"));
            if (hasNext) {
                columns.add(nextMonthStr + "预计数据");
            }
            if (dto.getExptDi() != null) {
                columns.add(nextMonthStr + "距离下一目标分差值");
            }
            values.add(getDataRow(STAR34_ANCHOR_SIGN_COUNT, Arrays.asList("招募（10分）", "3&4星主播签约数量（7分）"), dto));
            values.add(getDataRow(STAR34_ANCHOR_SIGN_COUNT_DIFF, Arrays.asList("招募（10分）", "3&4星主播签约数量趋势（3分）"), dto));
            values.add(getDataRow(HEAD_ANCHOR_COUNT_DIFF, Arrays.asList("孵化（30分）", "升级高优主播数趋势（3分）"), dto));
            values.add(getDataRow(WAIST_ANCHOR_COUNT_DIFF, Arrays.asList("孵化（30分）", "升级腰部主播数趋势（3分）"), dto));
            values.add(getDataRow(M_DA_HQ_VL_ANCHOR_COUNT, Arrays.asList("孵化（30分）", "月日均高优有效开播主播数（9分）"), dto));
            values.add(getDataRow(M_DA_WAIST_VL_ANCHOR_COUNT, Arrays.asList("孵化（30分）", "月日均腰部有效开播主播数（9分）"), dto));
            values.add(getDataRow(M_DA_HQ_VL_ANCHOR_COUNT_DIFF, Arrays.asList("孵化（30分）", "月日均高优有效开播主播数趋势（3分）"), dto));
            values.add(getDataRow(M_DA_WAIST_VL_ANCHOR_COUNT_DIFF, Arrays.asList("孵化（30分）", "月日均腰部有效开播主播数趋势（3分）"), dto));
            values.add(getDataRow(N2S_LIVE_RATIO, Arrays.asList("留存（25分）", "月日均新转存主播有效开播率（5分）"), dto));
            values.add(getDataRow(STOCK_ANCHOR_LIVE_RATIO, Arrays.asList("留存（25分）", "月日均存量主播有效开播率（7分）"), dto));
            values.add(getDataRow(NEW_SIGN_GOLD_ARTIST_COUNT, Arrays.asList("留存（25分）", "新授权金牌艺人（13分）"), dto));
            values.add(getDataRow(NEW_ANCHOR_BLUEDIAMOND, Arrays.asList("营收（30分）", "新主播蓝钻收入（5分）"), dto));
            values.add(getDataRow(STOCK_ANCHOR_BLUEDIAMOND, Arrays.asList("营收（30分）", "存量主播蓝钻收入（15分）"), dto));
            values.add(getDataRow(NEW_ANCHOR_BLUEDIAMOND_DIFF, Arrays.asList("营收（30分）", "新主播蓝钻收入趋势（3分）"), dto));
            values.add(getDataRow(STOCK_ANCHOR_BLUEDIAMOND_DIFF, Arrays.asList("营收（30分）", "存量主播蓝钻收入趋势（3分）"), dto));
            values.add(getDataRow(STOCK_ANCHOR_BLUEDIAMOND_RATIO, Arrays.asList("营收（30分）", "存量主播蓝钻收入占比（4分）"), dto));
            values.add(getDataRow(GUILD_HEALTH_POINT, Arrays.asList("综合（5分）", "公会健康分（5分）"), dto));

            ranges = Lists.newArrayList(new CellRangeAddress(1, 2, 0, 0),
                    new CellRangeAddress(3, 8, 0, 0),
                    new CellRangeAddress(9, 11, 0, 0),
                    new CellRangeAddress(12, 16, 0, 0));
        } else {
            name += "任务指标";
            columns.addAll(Arrays.asList("考核指标", thisMonthStr + "得分", "环比" + lastMonthStr + "得分"));
            if (hasNext) {
                columns.add(nextMonthStr + "预计得分");
            }
            columns.addAll(Arrays.asList(thisMonthStr + "获得积分", thisMonthStr + "数据", "环比" + lastMonthStr + "数据"));
            if (hasNext) {
                columns.add(nextMonthStr + "预计数据");
            }

            values.add(getTaskRow(NEW_SIGN_STAR32_ANCHOR_COUNT, dto));
            values.add(getTaskRow(NEW_HEAD_ANCHOR_COUNT, dto));
            values.add(getTaskRow(NEW_WAIST_ANCHOR_COUNT, dto));
            values.add(getTaskRow(RENEW_16_GOLD_ARIST_COUNT, dto));

            //region 礼物流水占比总流水是否≥70%
            List<String> row = new ArrayList<>(i);
            row.add(GIFT_FLOW_RATIO_GE70.getDesc());
            row.add(fmt(self.getActvProdPayAmtRateScore()));
            row.add(diff(self.getActvProdPayAmtRateScore(), last.getActvProdPayAmtRateScore()));
            if (hasNext) {
                StringBuilder sb = new StringBuilder(fmt(next.getActvProdPayAmtRateScore()));
                String tip = next.getMoreScoreTips().get((GIFT_FLOW_RATIO_GE70.getCode()));
                if (StringUtils.isNotBlank(tip)) {
                    sb.append("（ ").append(tip).append("）");
                }
                row.add(sb.toString());
            }
            row.add(fmt(self.getActvProdPayAmtRateItg()));
            row.add(self.getActvProdPayAmtRate() == null
                    ? ""
                    : self.getActvProdPayAmtRate() >= RankConstant.GIFT_FLOW_RATIO_GE ? "是" : "否");
            row.add("-");
            if (hasNext) {
                row.add(next.getActvProdPayAmtRate() == null
                        ? ""
                        : next.getActvProdPayAmtRate() >= RankConstant.GIFT_FLOW_RATIO_GE ? "是" : "否");
            }
            values.add(row);
            //endregion
        }

        export(name, new ExportData(columns, values, name, ranges));
    }

    private List<String> getDataRow(RankConstant.DataIndex index, List<String> preCols, GuildRankInfoDTO dto) throws Exception {
        YyDmEntityGuildCmpHealthAnalysisDiDTO self = dto.getThisMonthDi();
        YyDmEntityGuildCmpHealthAnalysisDiDTO last = dto.getLastMonthDi();
        YyDmEntityGuildCmpHealthAnalysisDiDTO next = dto.getNextMonthDi();
        YyDmEntityGuildCmpHealthAnalysisExptDiDTO expt = dto.getExptDi();
        List<String> row = new ArrayList<>(10);
        if (CollectionUtils.isNotEmpty(preCols)) {
            row.addAll(preCols);
        }
        Method dataGet = YyDmEntityGuildCmpHealthAnalysisDiDTO.class.getMethod("get" + toCamelCase(index.getDataCol()));
        Method scoreGet = YyDmEntityGuildCmpHealthAnalysisDiDTO.class.getMethod("get" + toCamelCase(index.getScoreCol()));
        row.add(fmt(scoreGet.invoke(self)));
        row.add(diff(scoreGet.invoke(self), scoreGet.invoke(last)));
        if (next != null) {
            row.add(fmt(scoreGet.invoke(next)));
        }
        boolean flag = RankConstant.DATA_PERCENT_INDEX.contains(index);
        row.add(fmt(dataGet.invoke(self), flag));
        row.add(diff(dataGet.invoke(self), dataGet.invoke(last), flag));
        if (next != null) {
            row.add(fmt(dataGet.invoke(next), flag));
        }
        if (expt != null && index.getNextDiffTipFunc() != null) {
            row.add(StringUtils.defaultString(index.getNextDiffTipFunc().apply(expt)));
        }

        return row;
    }

    private List<String> getTaskRow(RankConstant.TaskIndex index, GuildRankInfoDTO dto) throws Exception {
        YyDmEntityGuildCmpHealthAnalysisDiDTO self = dto.getThisMonthDi();
        YyDmEntityGuildCmpHealthAnalysisDiDTO last = dto.getLastMonthDi();
        YyDmEntityGuildCmpHealthAnalysisDiDTO next = dto.getNextMonthDi();
        List<String> row = new ArrayList<>(10);
        Method dataCol = YyDmEntityGuildCmpHealthAnalysisDiDTO.class.getMethod("get" + toCamelCase(index.getDataCol()));
        Method scoreGet = YyDmEntityGuildCmpHealthAnalysisDiDTO.class.getMethod("get" + toCamelCase(index.getScoreCol()));
        Method itgGet = YyDmEntityGuildCmpHealthAnalysisDiDTO.class.getMethod("get" + toCamelCase(index.getItgCol()));
        row.add(index.getDesc());
        row.add(fmt(scoreGet.invoke(self)));
        row.add(diff(scoreGet.invoke(self), scoreGet.invoke(last)));
        if (next != null) {
            StringBuilder sb = new StringBuilder(fmt(scoreGet.invoke(next)));
            String tip = next.getMoreScoreTips().get(index.getCode());
            if (StringUtils.isNotBlank(tip)) {
                sb.append("（ ").append(tip).append("）");
            }
            row.add(sb.toString());
        }
        row.add(fmt(itgGet.invoke(self)));
        row.add(fmt(dataCol.invoke(self)));
        row.add(diff(dataCol.invoke(self), dataCol.invoke(last)));
        if (next != null) {
            row.add(fmt(dataCol.invoke(next)));
        }
        return row;
    }

    /**
     * 获取主体uid
     *
     * @return
     */
    private Long getCompanyUid() {
        Long uid = UserContext.getCurrentLoginUserUid();
        if (uid == null) {
            return null;
        }
        Long companyUid = goldChannelService.getAssociatedCompany(uid);
        Long mainUid = companyUid == null || companyUid == 0L ? uid : companyUid;
        log.info("getCompanyUid loginUid:{},companyUid:{},mainUid:{}", uid, companyUid, mainUid);
        return mainUid;
    }

    private static String fmt(Object o) {
        return fmt(o, false);
    }

    private static String fmt(Object o, boolean isPercent) {
        if (o == null) {
            return "-";
        }
        DecimalFormat fmt = new DecimalFormat("#0.00");
        if (isPercent) {
            if (o instanceof Double) {
                return fmt.format(((Double) o) * 100) + "%";
            }
            if (o instanceof Float) {
                return fmt.format(((Float) o) * 100) + "%";
            }
        }
        if (o instanceof Double || o instanceof Float) {
            return fmt.format(o);
        }
        return o.toString();
    }

    private String diff(Object self, Object that) {
        return diff(self, that, false);
    }

    private String diff(Object self, Object that, boolean isPercent) {
        if (self == null && that == null) {
            return "-";
        }

        if (self != null && that != null && self.getClass() != that.getClass()) {
            return "-";
        }

        if (self instanceof Long || self instanceof Integer || that instanceof Long || that instanceof Integer) {
            return diffInt((Number) self, (Number) that);
        }
        if (self instanceof Double || self instanceof Float || that instanceof Double || that instanceof Float) {
            return diffDouble((Number) self, (Number) that, isPercent);
        }

        return "-";
    }

    private String diffDouble(Number self, Number that, boolean isPercent) {
        double diff = (self == null ? 0D : self.doubleValue()) - (that == null ? 0 : that.doubleValue());
        if (diff > 0) {
            return "↑" + fmt(diff, isPercent);
        }
        if (diff < 0) {
            return "↓" + fmt(Math.abs(diff), isPercent);
        }
        return String.valueOf(diff);
    }

    private String diffInt(Number self, Number that) {
        long diff = (self == null ? 0L : self.longValue()) - (that == null ? 0L : that.longValue());
        if (diff > 0) {
            return "↑" + diff;
        }
        if (diff < 0) {
            return "↓" + Math.abs(diff);
        }
        return String.valueOf(diff);
    }
}
